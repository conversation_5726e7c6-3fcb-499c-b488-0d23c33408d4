<?php
declare (strict_types = 1);

namespace app\command;

use app\service\ElasticSearch;
use think\cache\driver\Redis;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;
use app\service\Periods as PeriodsSer;

// 更新期数浏览量
class UpdatePeriodPageviews extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('UpdatePeriodPageviews')
            ->setDescription('the UpdatePeriodPageviews command');
    }

    protected function execute(Input $input, Output $output)
    {
        $s = getmicrotime();
        $redis_config = config('cache.stores.redis');
        $redis_config['select'] = 0;
        $conn = new \think\cache\driver\Redis($redis_config);

        $update = [];
        $num = 10000;
        $i = 0;
        while (true) {
            $period = $conn->lrange('vinehoo.period.pageviews',0,intval($num-1));
            if (!empty($period)) {
                $conn->ltrim('vinehoo.period.pageviews', count($period), -1);
            }
            if (empty($period)) {
                break;
            }
            foreach ($period as $v) {
                $update[$v] = $update[$v] ?? 0;
                $update[$v]++;
            }
            $i++;
            if ($i >= 10) {
                break;
            }
        }

        $ser = [
            0 => new PeriodsSer(0),
            1 => new PeriodsSer(1),
            2 => new PeriodsSer(2),
            3 => new PeriodsSer(3),
            4 => new PeriodsSer(4),
            5 => new PeriodsSer(5),
            9 => new PeriodsSer(9),
        ];
        foreach ($update as $k => $v) {
            $info = explode('_', $k);
            $periods_ser = $ser[intval($info[1])] ?? null;
            if (!empty($periods_ser)) {
                $periods_ser->updatePageviews(intval($info[0]), intval($v));
            }
            
        }
        echo 'ok';exit;

    }
}
