<?php
declare (strict_types=1);

namespace app\command;

use app\model\PeriodsCross;
use app\model\PeriodsLeftover;
use app\model\PeriodsSecond;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\facade\Db;

class SortShuffleAndUpdate extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('SortShuffleAndUpdate')
            ->setDescription('SortShuffleAndUpdate command: Shuffle and update sort values of PeriodsLeftover');
    }

    protected function execute(Input $input, Output $output)
    {
        $types = [
            //尾货
            'PeriodsLeftover' => new PeriodsLeftover(),
            //秒发
            'PeriodsSecond' => new PeriodsSecond(),
            //跨境
            'PeriodsCross' => new PeriodsCross(),
        ];
        foreach ($types as $type => $model) {
            // 查询符合条件的记录的ID
            $ids = $model::where([
                ['onsale_status', '=', 2],
                //将排序值小于500的数据重新排序
                ['sort', '<=', 500],
                ['is_channel', '=', 0],
            ])->column('id');

            if (empty($ids)) {
                $output->writeln($type . ':未找到要更新的记录。');
                continue;
            }

            // 打乱ID顺序
            shuffle($ids);

            // 开始事务
            $model::startTrans();
            try {
                // 将sort值从1依次填写
                for ($i = 0; $i < count($ids); $i++) {
                    $model::where('id', $ids[$i])
                        ->update(['sort' => $i + 1]);
                    usleep(100000);
                }

                // 提交事务
                $model::commit();
                $output->writeln('Successfully updated periods ' . $type . ' sort values. ' . date('Y-m-d H:i:s'));
            } catch (\Exception $e) {
                // 发生异常，回滚事务
                $model::rollback();
                $output->writeln('Failed to update periods ' . $type . ' sort values: ' . $e->getMessage() . ' ' . date('Y-m-d H:i:s'));
            }
        }

        // 秒级任务超时补偿
        $this->autoTaskOvertimeCompensation();

    }

    // 秒级任务超时补偿
    public function autoTaskOvertimeCompensation()
    {
        $task_log = Db::name('periods_task_log')
            ->where("task_trigger_time < " . time() . " and is_exec = 0 and params is not null and params != '' and type in(0,1,2)")
            ->column('task_id,params');
        if (!empty($task_log)) {
            $re_url = env('item.COMMODITIES_URL') . '/commodities/v3/periods/systemPutShelf';
            foreach ($task_log as $v) {
                // 秒级自动任务回调
                curlRequest($re_url, json_decode($v['params'], true));
            }
        }
    }
}
