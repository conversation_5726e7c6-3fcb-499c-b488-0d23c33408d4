<?php
declare (strict_types = 1);

namespace app\command;

use app\service\ElasticSearch;
use app\service\elasticsearch\ElasticSearchService;
use think\cache\driver\Redis;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;

class CreateIndexRedis extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('createIndexRedis')
            ->setDescription('the createIndexRedis command');
    }

    protected function execute(Input $input, Output $output)
    {


        // 查询 es 数据
        $es_ser = new ElasticSearch();

        // 查询闪购
        $flash = $es_ser->getPeriodsTypeList(0, 0, 1000);

        // 查询秒发
        $second = $es_ser->getPeriodsTypeList(1, 0, 1000);

        // 查询跨境
        $cross = $es_ser->getPeriodsTypeList(2, 0, 1000);

        // 查询尾货
        $leftover = $es_ser->getPeriodsTypeList(3, 0, 1000);

//        // 跨境秒发插入初始位置
//        $cInit = 4;
//        // 尾货插入初始位置
//        $lInit = 6;
//        // 插入次数
//        $count = 1;
//        $all = [];
//        // 规则 4 个闪购后面跟一个跨境，一个秒发，六个闪购后跟一个尾货
//        // 2022/5/5 修改规则 两个闪购后出现第一个尾货，其余规则不变。相当于尾货的第一个商品永远出现在列表的第三个。但是后面的间隔规则不变
//        // 取出第一个尾货
//        $first_wh = array_shift($leftover);
//        foreach ($flash as $key => $v) {
//            if ($key == $cInit) {
//                if (!empty($cross)) {
//                    array_push($all, array_shift($cross));
//                }
//                if (!empty($second)) {
//                    array_push($all, array_shift($second));
//                }
//                $cInit = $count * 6 + 4;
//            }
//            if ($key == $lInit) {
//                if (!empty($leftover)) {
//                    array_push($all, array_shift($leftover));
//                }
//                $lInit = $count * 6 + 6;
//                $count++;
//            }
//            array_push($all, $v);
//        }
//
//        // 尾货的第一个商品永远出现在列表的第三个
////        $all[2] = $first_wh;
//        $new_all = [];
//        foreach ($all as $key => $val) {
//            if ($key == 2) {
//                if (!empty($first_wh)) {
//                    $new_all[] = $first_wh;
//                }
//            }
//            $new_all[] = $val;
//        }
        // 跨境秒发插入初始位置
        $cInit = 5;
        // 尾货插入初始位置
        $sInit = 9;
        // 插入次数
        $count = 1;
        $all = [];
        // 规则 5 个闪购后面跟一个跨境，，9 个闪购后跟一个秒发
        foreach ($flash as $key => $v) {
            if ($key == $cInit) {
                if (!empty($cross)) {
                    array_push($all, array_shift($cross));
                }
                $cInit = $count * 9 + 5;
            }
            if ($key == $sInit) {
                if (!empty($second)) {
                    array_push($all, array_shift($second));
                }
                $sInit = $count * 9 + 9;
                $count++;
            }
            array_push($all, $v);
        }
//        $redis = new Redis(Config('cache')['stores']['redis']);
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(3);
        $key = 'index_periods_sort';
        $redis->del($key);
        foreach ($all as $k => $v) {
            $redis->zadd($key, $k, json_encode($v));
        }

        // 更新期数异常马甲数量
        $this->updatePeriodsMajia();

        print_r(count($all));
        echo '完成';
        exit();
    }

    // 更新期数异常马甲数量
    public function updatePeriodsMajia()
    {
        $period_table_arr = ['periods_flash', 'periods_second', 'periods_cross', 'periods_leftover'];

        $url = env('ITEM.COMMODITIES_URL') . '/commodities/v3/package/examinationPI';
        foreach ($period_table_arr as $periods_type => $period_table) {
            $period_ids = Db::name($period_table)
                ->where("`onsale_status` = '2' and (purchased + vest_purchased)=limit_number")
                ->column('id');
            if (!empty($period_ids)) {
                foreach ($period_ids as $period_id) {
                    $data = json_encode([
                        "period" => $period_id,
                        "periods_type" => $periods_type,
                        "table" => "vh_periods_product_inventory"
                    ]);
                    curl_request($url, $data);
                }
            }
        }

    }
}
