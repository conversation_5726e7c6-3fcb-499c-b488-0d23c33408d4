<?php

declare(strict_types=1);

namespace app\command;

use app\model\PeriodsFlash;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\facade\Db;

class PeriodsDepositSms extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('PeriodsDepositSms')
            ->addArgument('params', Argument::OPTIONAL, "param")
            ->setDescription('the PeriodsDepositSms command');
    }

    /**
     * 订金消息推送（定金商品开售时间在【9点-21点】之间立即发送对定金用户发送短信，如果开售时间在【21点-次日9点】的，次日早上十点如果定金商品还有库存，需对未支付尾款的定金用户发送短信通知，只发一次。）
     * @param Input $input
     * @param Output $output
     * @return int|void|null
     */
    protected function execute(Input $input, Output $output)
    {
        $table = [
            'periods_flash',
        ];

        foreach ($table as $v) {
            echo $v . PHP_EOL;
            $periods_type = 0;

            $package_ser = new \app\service\Package($periods_type);
            $periods_ser = new \app\service\Periods($periods_type);
            $periods = Db::name($v)
                ->where([
                    ['onsale_status', '=', 2],
                    ['is_deposit_period', '=', 1],
                ])
                ->order('sort', 'asc')
                ->column('id,title,sell_time');
            $count = count($periods);
            foreach ($periods as $i => $v) {
                echo ($i + 1) . '/' . $count . PHP_EOL;
                $currentHour = date('H', $v['sell_time']);
                if (intval($currentHour) < 9 || intval($currentHour) >= 21) {
                    //根据期数查询期数订金套餐列表
                    $list = $package_ser->depositPackageList($v['id'],'id,deposit_coupon_id',[
                        ['deposit_coupon_id', '>', 0]
                    ]);
                    if (!empty($list)) {
                        // 发送订金信息推送
                        $periods_ser->SendDepositSms($v, $list);
                    }
                }
            }
        }

        echo 'OK';
    }
}
