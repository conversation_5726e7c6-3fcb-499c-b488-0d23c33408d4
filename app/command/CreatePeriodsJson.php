<?php
declare (strict_types = 1);

namespace app\command;

use app\model\PeriodsCross;
use app\model\PeriodsFlash;
use app\model\PeriodsSecond;
use app\service\Periods;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;

class CreatePeriodsJson extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('createPeriodsJson')
            ->setDescription('the createPeriodsJson command');
    }

    protected function execute(Input $input, Output $output)
    {
        // 查询代售重，在售中闪购
        $flash = PeriodsFlash::whereIn('onsale_status', [1,2])->column('id');
        $periods_f_ser = new Periods(0);
        foreach ($flash as $f_v) {
            $periods_f_ser->create_period_json((int)$f_v);
            echo $f_v."\n";
        }
        // 查询代售重，在售中秒发
        $periods_s_ser = new Periods(1);
        $second = PeriodsSecond::whereIn('onsale_status', [1,2])->column('id');
        foreach ($second as $s_v) {
            $periods_s_ser->create_period_json((int)$s_v, 1);
            echo $s_v."\n";
        }
        // 查询代售重，在售中跨境
        $periods_c_ser = new Periods(2);
        $cross = PeriodsCross::whereIn('onsale_status', [1,2])->column('id');
        foreach ($cross as $c_v) {
            $periods_c_ser->create_period_json((int)$c_v, 2);
            echo $c_v."\n";
        }
        // 查询代售重，在售中尾货
        $periods_l_ser = new Periods(3);
        $leftover = PeriodsCross::whereIn('onsale_status', [1,2])->column('id');
        foreach ($leftover as $l_v) {
            $periods_l_ser->create_period_json((int)$l_v, 3);
            echo $l_v."\n";
        }
        // 查询代售重，在售中兔头
        $periods_r_ser = new Periods(4);
        $rabbit = PeriodsCross::whereIn('onsale_status', [1,2])->column('id');
        foreach ($rabbit as $r_v) {
            $periods_r_ser->create_period_json((int)$r_v, 4);
            echo $r_v."\n";
        }
        echo '完成';
        exit();
    }
}
