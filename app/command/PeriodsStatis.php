<?php
declare (strict_types = 1);

namespace app\command;

use app\model\PeriodsComment;
use app\model\PeriodsFlash;
use app\model\PeriodsProductInventory;
use app\model\PeriodsSecond;
use app\model\PeriodsVest;
use app\service\ElasticSearch;
use app\service\elasticsearch\ElasticSearchService;
use think\cache\driver\Redis;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;

class PeriodsStatis extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('PeriodsStatis')
            ->addArgument('params', Argument::OPTIONAL, "param")
            ->setDescription('the PeriodsStatis command');
    }

    protected function execute(Input $input, Output $output)
    {
        $params = trim($input->getArgument('params'));
        $params = explode('-', $params);
        $min = $params[0];
        $max = $params[1];
        $periods_list = PeriodsSecond::where([['id', '>=', $min], ['id', '<=', $max]])->column('id');
        $vest = [];
        foreach ($periods_list as $val) {
            $es_period = esGetOne((int) $val, 'vinehoo.periods');
            // 不存在瓶数，增加马甲触发统计
            if (!$es_period['saled_count']) {
                $vest['period_id'] = $val;
                $vest['periods_type'] = 0;
                $vest['nums'] = 0;
                $vest['start_time'] = 20220905;
                $vest['duration'] = 1;
                $vest['is_repeat'] = 0;
                $vest['repeat_nums'] = 0;
                $vest['repeat_interval'] = 0;
                $vest['status'] = 1;
                $vest['created_time'] = 20220906;
                $vest['operator'] = 0;
                PeriodsVest::create($vest);
                unset($vest);
                print_r($val . PHP_EOL);
            }
        }
        print_r('更新完毕');
        exit();
    }
}
