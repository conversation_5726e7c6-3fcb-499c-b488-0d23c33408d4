<?php
declare (strict_types = 1);

namespace app\command;

use app\service\ElasticSearch;
use app\service\elasticsearch\ElasticSearchService;
use think\cache\driver\Redis;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;

class CreateIndexRedis41161 extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('createIndexRedis')
            ->setDescription('the createIndexRedis command');
    }

    protected function execute(Input $input, Output $output)
    {


        // 查询 es 数据
        $es_ser = new ElasticSearch();

        // 查询闪购
        $flash = $es_ser->getPeriodsTypeList(0, 0, 1000);

        // 查询秒发
        $second = $es_ser->getPeriodsTypeList(1, 0, 1000);

        // 查询跨境
        $cross = $es_ser->getPeriodsTypeList(2, 0, 1000);

        // 查询尾货
        $leftover = $es_ser->getPeriodsTypeList(3, 0, 1000);

        // 跨境秒发插入初始位置
        $cInit = 4;
        // 尾货插入初始位置
        $lInit = 6;
        // 插入次数
        $count = 1;
        $all = [];
        // 规则 4 个闪购后面跟一个跨境，一个秒发，六个闪购后跟一个尾货
        // 2022/5/5 修改规则 两个闪购后出现第一个尾货，其余规则不变。相当于尾货的第一个商品永远出现在列表的第三个。但是后面的间隔规则不变
        // 取出第一个尾货
        $first_wh = array_shift($leftover);
        foreach ($flash as $key => $v) {
            if ($key == $cInit) {
                if (!empty($cross)) {
                    array_push($all, array_shift($cross));
                }
                if (!empty($second)) {
                    array_push($all, array_shift($second));
                }
                $cInit = $count * 6 + 4;
            }
            if ($key == $lInit) {
                if (!empty($leftover)) {
                    array_push($all, array_shift($leftover));
                }
                $lInit = $count * 6 + 6;
                $count++;
            }
            array_push($all, $v);
        }

        // 尾货的第一个商品永远出现在列表的第三个
//        $all[2] = $first_wh;
        $new_all = [];
        foreach ($all as $key => $val) {
            if ($key == 2) {
                if (!empty($first_wh)) {
                    $new_all[] = $first_wh;
                }
            }
            $new_all[] = $val;
        }
//        $redis = new Redis(Config('cache')['stores']['redis']);
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(3);
        $key = 'index_periods_sort';
        $redis->del($key);
        foreach ($new_all as $k => $v) {
            $redis->zadd($key, $k, json_encode($v));
        }
        print_r(count($new_all));
        echo '完成';
        exit();
    }
}
