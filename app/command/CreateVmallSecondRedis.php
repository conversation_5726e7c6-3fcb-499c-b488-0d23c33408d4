<?php
declare (strict_types=1);

namespace app\command;

use app\model\PeriodsSecondMerchants;
use app\service\ElasticSearch;
use app\service\elasticsearch\ElasticSearchService;
use think\cache\driver\Redis;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;

class CreateVmallSecondRedis extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('createVmallSecondRedis')
            ->setDescription('the createIndexRedis command');
    }

    protected function execute(Input $input, Output $output)
    {

        // 查询 es 数据
//        $es_ser = new ElasticSearch();

        // 查询闪购
//        $flash = $es_ser->getPeriodsTypeList(0, 0, 1000);

        // 商家秒发列表
        $vmall_second_list = PeriodsSecondMerchants::where('onsale_status', 2)
            ->field('id, title, brief, banner_img, horizontal_img, product_img, is_hidden_price, sold_out_time, 
            supplier, supplier_id, stores, product_channel')
            ->select()
            ->toArray();
        if (empty($vmall_second_list)) {
            echo '未查询到数据';
            exit();
        }

        // 查看有多少商家
        $vmall_second_supplier = PeriodsSecondMerchants::where('onsale_status', 2)
            ->group('supplier_id')
            ->column('supplier_id');
        if (empty($vmall_second_supplier)) {
            echo '未查询到商户';
            exit();
        }

        $new_all = $vmall_second_list;
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(3);
        foreach ($vmall_second_supplier as $val) {
            $area = $redis->hGet('vmall.merchants.districe_ids', (string)$val);
            $area_arr = [];
            if ($area) {
                $area_arr = explode(',', $area);
            }
            if (!empty($area_arr)) {
                foreach ($area_arr as $av) {
                    $key = 'vmall_second_'.$av. '_'.$val;
                    // 清除 key
                    $redis->del($key);
                    // 新建数据
                    foreach ($new_all as $k => $v) {
                        if ($v['supplier_id'] == $val) {
                            $redis->zadd($key, $k, json_encode($v));
                        }
                    }
                }
            }
        }
        print_r(count($new_all));
        echo '完成';
        exit();
    }
}
