<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;

class PutShelf extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('putshelf')
            ->setDescription('the putshelf command');
    }

    protected function execute(Input $input, Output $output)
    {

        $where[] = ['onsale_status', '=', 1];
        $where[] = ['sell_time', '<=', time()];
        // 查询闪购已到指定上架时间的期数，并上架
        $flash = Db::name('periods_flash')->where($where)->update(['onsale_status' => 2]);
        $output->writeln('闪购上架： '. $flash . ' 件商品');
        // 查询闪购已到指定上架时间的期数，并上架
        $flash = Db::name('periods_cross')->where($where)->update(['onsale_status' => 2]);
        $output->writeln('跨境上架： '. $flash . ' 件商品');
        // 查询闪购已到指定上架时间的期数，并上架
        $flash = Db::name('periods_leftover')->where($where)->update(['onsale_status' => 2]);
        $output->writeln('尾货上架： '. $flash . ' 件商品');
        // 查询闪购已到指定上架时间的期数，并上架
        $flash = Db::name('periods_second')->where($where)->update(['onsale_status' => 2]);
        $output->writeln('秒发上架： '. $flash . ' 件商品');

        // 指令输出
        $output->writeln('上架完毕');
    }
}
