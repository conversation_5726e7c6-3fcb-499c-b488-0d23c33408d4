<?php
declare (strict_types = 1);

namespace app\command;

use app\service\ElasticSearch;
use think\cache\driver\Redis;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;
use app\service\Periods as PeriodsSer;

/**
 * 在售期数、萌牙库存0或萌牙有库存且有推送萌牙失败订单、备货数量为0、预计发货时间小于当前时间+4天的期数
 * 把发货时间设置为当前时间+6，超卖发货时间设置为当前时间+8
 * 如果当前时间是周五，则把发货时间设置为当前时间+7，超卖发货时间设置为当前时间+9
 * https://devops.aliyun.com/projex/project/8ab3146db7bf4a79876e3a5333/task#viewIdentifier=1945bcda00c0cf935ebdd8a5&openWorkitemIdentifier=42f2fb754ee53db76c70114293
 */
class UpdatePeriodPredictShipmentTime extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('UpdatePeriodPredictShipmentTime')
            ->setDescription('the UpdatePeriodPredictShipmentTime command');
    }

    protected function execute(Input $input, Output $output)
    {
        $dtime = time();
        //获取当前周几
        $week = date('w');
        // 当前时间+4天
        $time = strtotime(date('Y-m-d 23:59:59')) + (86400 * 4);
        $periods = Db::name('periods_flash')
            ->where([
                ['onsale_status','=',2],
                ['predict_shipment_time', '<=', $time],
                ['is_supplier_delivery', '=', 0],
                ['import_type', '=', 1],
                ['is_channel', '=', 0],
            ])
            ->column('id,predict_shipment_time,supplier');
        if (empty($periods)) {
            echo 'ok';exit;
        }

        $period_id = array_column($periods,'id');
        // 获取期数推送失败订单
        $order_map = $this->getPushFailOrder($period_id);
        
        // 查询产品
        $periods_product = Db::name('periods_product_inventory')
            ->where([
                ['period', 'in', $period_id],
                ['is_use_comment', '=', 0],
            ])
            ->column('id,period,bar_code,short_code,inventory,warehouse_id,product_id');
        if (empty($periods_product)) {
            echo 'ok';exit;
        }
        $products = [];
        foreach ($periods_product as $v) {
            $products[$v['period']][] = $v;
        }

        $short_code = array_values(array_unique(array_column($periods_product, 'short_code')));
        $s_short_code = $my_inventory_data = [];
        foreach ($short_code as $s) {
            $s_short_code[] = $s;
            if (count($s_short_code) >= 150) {
                // 查询萌牙库存
                $my_inventory = \Curl::goodsGetFictitiousCount(['short_code' => $s_short_code]);
                foreach ($my_inventory as $k => $v) {
                    foreach ($v as $vv) {
                        $my_inventory_data["{$k}_{$vv['fictitious_id']}"] = $vv;
                    }
                }
                $s_short_code = [];
            }
        }
        if (count($s_short_code) > 0) {
            // 查询萌牙库存
            $my_inventory = \Curl::goodsGetFictitiousCount(['short_code' => $s_short_code]);
            foreach ($my_inventory as $k => $v) {
                foreach ($v as $vv) {
                    $my_inventory_data["{$k}_{$vv['fictitious_id']}"] = $vv;
                }
            }
            $s_short_code = [];
        }
        
        // 查询备货量
        $prepare_purchase = \Curl::PreparePurchaseList(['short_code' => implode(',', $short_code)]);
        $prepare_purchase_data = $prepare_purchase['list'] ?? [];
        $purchases = [];
        foreach ($prepare_purchase_data as $v) {
            $purchases["{$v['short_code']}_{$v['customer_name']}"] = $v;
        }

        $inventory_order = [];
        foreach ($periods as $v) {
            $is_update = true;
            $product = $products[$v['id']] ?? [];
            foreach ($product as $p) {
                $warehouse_id = intval($p['warehouse_id']);
                // 萌牙库存
                $my_inventory_count = $my_inventory_data["{$p['short_code']}_{$warehouse_id}"]['goods_count'] ?? 0;
                // 备货量
                $prepare_purchase_count = $purchases["{$p['short_code']}_{$v['supplier']}"]['number'] ?? 0;
                // 推送失败订单
                $fail_order = $order_map["{$v['id']}:{$p['product_id']}"] ?? [];

                //萌牙库存0或萌牙>0且有推送萌牙失败订单、备货数量为0
                if (($my_inventory_count > 0 && empty($fail_order)) || $prepare_purchase_count > 0) {
                    $is_update = false;
                }
            }
            // 当前预计发货时间
            // $predict_shipment_time = $v['predict_shipment_time'];
            if ($is_update) {
                //把发货时间设置为当前时间+6，超卖发货时间设置为当前时间+8
                $predict_shipment_time = $dtime + (86400 * 6);
                $surpass_shipment_time = $dtime + (86400 * 8);
                //如果当前时间是周五，则把发货时间设置为当前时间+7，超卖发货时间设置为当前时间+9
                if ($week == 5) {
                    $predict_shipment_time = $dtime + (86400 * 7);
                    $surpass_shipment_time = $dtime + (86400 * 9);
                }
                Db::name('periods_flash')
                    ->where('id', $v['id'])
                    ->update([
                        'predict_shipment_time' => $predict_shipment_time,
                        'update_time' => time(),
                    ]);
                foreach ($product as $p) {
                    $inventory_order[] = [
                        'period' => $v['id'],
                        'short_code' => $p['short_code'],
                        'type' => 1,
                        'order' => 0,
                        'operator' => '每天早上8点修改闪购在售期数的发货时间',
                        'operator_id' => 0,
                        'predict_shipment_time' => $surpass_shipment_time,
                        'created_time' => $dtime,
                    ];
                }
            }
        }
        if (!empty($inventory_order)) {
            // 添加订货记录/超卖发货预计发货时间
            Db::name('periods_product_inventory_order')->insertAll($inventory_order);
        }
        
        echo 'ok';exit;

    }

    /**
     * @方法描述: 获取期数推送失败订单
     * <AUTHOR>
     * @Date 2024/12/16
     * @param array $period_ids 期数
     * @return array
     */
    public function getPushFailOrder($period_ids = [])
    {
        // 查询推送失败订单
        $flash_order = Db::table('vh_orders.vh_flash_order')
            ->alias('o')
            ->leftJoin('vh_orders.vh_order_main om','om.id=o.main_order_id')
            ->where([
                ['o.period', 'in', $period_ids],
                ['o.sub_order_status', '=', 1],
                ['o.push_wms_status', '=', 2],
            ])
            ->column('o.id,o.sub_order_no,o.period,o.package_id,om.main_order_no');
        $package_ids = array_column($flash_order, 'package_id');
        $main_order_no = array_column($flash_order, 'main_order_no');
        // 查询盲盒套餐
        $mystery_box_log = Db::table('vh_orders.vh_order_mystery_box_log')
            ->whereIn('main_order_no', $main_order_no)
            ->column('main_order_no,product_info', 'main_order_no');
        // 查询套餐
        $order_package =  Db::name('periods_flash_set')
            ->where([
                ['id', 'in', $package_ids],
                ['is_mystery_box', '=', 0]
            ])
            ->column('id,associated_products', 'id');
        $order_map = [];
        foreach ($flash_order as $v) {
            $product_str = '';
            if (!empty($mystery_box_log[$v['main_order_no']])) {// 盲盒
                $product_str = $mystery_box_log[$v['main_order_no']]['product_info'];

            } else if (!empty($order_package[$v['package_id']])) {
                $product_str = $order_package[$v['package_id']]['associated_products'];
            }
            $product_info = json_decode($product_str, true);
            foreach ($product_info as $vv) {
                if (!empty($vv['product_id']) && !is_array($vv['product_id'])) {
                    $order_map["{$v['period']}:{$vv['product_id']}"][] = $v;
                }
            }
        }
        return $order_map;
    }
}
