<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;

class SalesVolumeTop extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('salesVolumeTop')
            ->addArgument('type', Argument::OPTIONAL, '类型：月（month）、周（week）')
            ->setDescription('销量TOP统计');
    }

    protected function execute(Input $input, Output $output)
    {
        $type = trim($input->getArgument('type') ?? '') ?: '';
        if (!$type) {
            exit('输入参数[type]');
        }
        if (!in_array($type, ['month', 'week'])) {
            exit('参数[type]有误');
        }

        // 更新TOP
        list($res, $msg) = (new \app\service\SalesVolumeTop())->updateTop($type);
        exit($res ? '执行成功' : '执行失败：' . $msg);
    }
}
