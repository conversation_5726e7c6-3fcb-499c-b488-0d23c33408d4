<?php

declare(strict_types=1);

namespace app\command;

use app\model\PeriodsFlash;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\facade\Db;

class PeriodsAutoSort extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('PeriodsAutoSort')
            ->addArgument('params', Argument::OPTIONAL, "param")
            ->setDescription('the PeriodsAutoSort command');
    }

    /**
     * 期数从低到高自动排序
     * @param Input $input
     * @param Output $output
     * @return int|void|null
     */
    protected function execute(Input $input, Output $output)
    {
        $table = [
            'periods_flash',
            'periods_cross',
            'periods_second',
            'periods_leftover',
        ];

        foreach ($table as $v) {
            echo $v . PHP_EOL;
            $periods = Db::name($v)->whereIn('onsale_status', [1, 2])->order('sort', 'asc')->column('id');
            $count = count($periods);
            foreach ($periods as $i => $period) {
                echo ($i + 1) . '/' . $count . PHP_EOL;
                Db::name($v)->where('id', $period)->update(['sort' => $i]);
                usleep(100000);
            }
        }

        echo 'OK';
    }
}
