<?php
declare (strict_types = 1);

namespace app\command;

use app\model\PeriodsPageviews;
use app\service\Periods;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;

class PeriodsPageviewHoursOrders extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('ppho')
            ->addArgument('params', Argument::OPTIONAL, "param")
            ->setDescription('the ppho command');
    }

    protected function execute(Input $input, Output $output)
    {
        $hourago = strtotime("-1 hour");
        $ha = strtotime(date('Y-m-d H:00:00', $hourago));
        $st = date('Y-m-d H:00:00', $hourago);
        $et = date('Y-m-d H:59:59', $hourago);
        $ta = ['st' => $st, 'et' => $et];
        $p = PeriodsPageviews::where('time', $ha)->field('id, periods_type, period, pageviews')->select()->toArray();
        $ps = new Periods(0);
        foreach ($p as $value) {
            switch ($value['periods_type']) {
                case 0:
                    $order_sta = $ps->orderSta('vh_flash_order', $value['period'], $ta);
                    break;
                case 1:
                    $order_sta = $ps->orderSta('vh_second_order', $value['period'], $ta);
                    break;
                case 2:
                    $order_sta = $ps->orderSta('vh_cross_order', $value['period'], $ta);
                    break;
                case 3:
                    $order_sta = $ps->orderSta('vh_tail_order', $value['period'], $ta);
                    break;
                default :
                    break;
            }
            if (!empty($order_sta) && $value['pageviews'] > 0) {
                // 更新订单
                $rate = 0;
                if ($order_sta['order_user'] > 0 && $value['pageviews'] > 0) {
                    $rate =  round($order_sta['order_user'] / $value['pageviews'], 2);
                }
                PeriodsPageviews::where('id', $value['id'])->update([
                    'order_user' => $order_sta['order_user'] ?? 0,
                    'order_price_sum' => $order_sta['order_price_sum'] ?? 0,
                    'rate' => $rate
                ]);
            }
        }
        print_r('更新完毕');
        exit();
    }
}
