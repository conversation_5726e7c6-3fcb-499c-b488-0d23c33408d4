<?php
declare (strict_types = 1);

namespace app\command;

use app\service\ElasticSearch;
use think\cache\driver\Redis;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;

class UpdateESPeriodsPackagePrice extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('updateESPPP')
            ->setDescription('the UpdateESPeriodsPackagePrice command');
    }

    protected function execute(Input $input, Output $output)
    {
        $es_ser = new ElasticSearch();
        // 更新闪购套餐价格
        $flash = Db::table('vh_periods_flash')->field('id')->where('onsale_status', '<>',2)->select()->toArray();
        foreach ($flash as $val) {
            $flash_set = Db::table('vh_periods_flash_set')
                ->where(['period_id' => $val['id'], 'is_hidden' => 0])
                ->order('price', 'asc')
                ->column('price');
            if (!empty($flash_set)) {
                $flash_set_str = implode('/', $flash_set);
                $es_ser->updatePeriodsById((int)$val['id'], ['package_prices' => $flash_set_str]);
            }
            print_r('flash: '. $val['id']. "\n");
        }
        unset($val);
        // 更新跨境套餐价格
        $cross = Db::table('vh_periods_cross')->field('id')->where('onsale_status', '<>',2)->select()->toArray();
        foreach ($cross as $val) {
            $cross_set = Db::table('vh_periods_cross_set')
                ->where(['period_id' => $val['id'], 'is_hidden' => 0])
                ->order('price', 'asc')
                ->column('price');
            if (!empty($cross_set)) {
                $cross_set_str = implode('/', $cross_set);
                $es_ser->updatePeriodsById((int)$val['id'], ['package_prices' => $cross_set_str]);
            }
            print_r('cross: '. $val['id']. "\n");
        }
        unset($val);
        // 更新秒发套餐价格
        $second = Db::table('vh_periods_second')->where('onsale_status', '<>',2)->field('id')->select()->toArray();
        foreach ($second as $val) {
            $second_set = Db::table('vh_periods_second_set')
                ->where(['period_id' => $val['id'], 'is_hidden' => 0])
                ->order('price', 'asc')
                ->column('price');
            if (!empty($second_set)) {
                $second_set_str = implode('/', $second_set);
                $es_ser->updatePeriodsById((int)$val['id'], ['package_prices' => $second_set_str]);
            }
            print_r('second: '. $val['id']. "\n");
        }
        unset($val);
        // 更新尾货套餐价格
        $leftover = Db::table('vh_periods_leftover')->where('onsale_status', '<>',2)->field('id')->select()->toArray();
        foreach ($leftover as $val) {
            $leftover_set = Db::table('vh_periods_leftover_set')
                ->where(['period_id' => $val['id'], 'is_hidden' => 0])
                ->order('price', 'asc')
                ->column('price');
            if (!empty($leftover_set)) {
                $leftover_set_str = implode('/', $leftover_set);
                $es_ser->updatePeriodsById((int)$val['id'], ['package_prices' => $leftover_set_str]);
            }
            print_r('leftover: '. $val['id']. "\n");
        }
        unset($val);
        // 更新兔头套餐价格
        $rabbit = Db::table('vh_periods_rabbit')->where('onsale_status', '<>',2)->field('id')->select()->toArray();
        foreach ($rabbit as $val) {
            $rabbit_set = Db::table('vh_periods_rabbit_set')
                ->where(['period_id' => $val['id'], 'is_hidden' => 0])
                ->order('price', 'asc')
                ->column('price');
            if (!empty($rabbit_set)) {
                $rabbit_set_str = implode('/', $rabbit_set);
                $es_ser->updatePeriodsById((int)$val['id'], ['package_prices' => $rabbit_set_str]);
            }
            print_r('rabbit: '. $val['id']. "\n");
        }
    }
}
