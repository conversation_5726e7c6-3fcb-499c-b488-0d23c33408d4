<?php
declare (strict_types = 1);

namespace app\command;

use app\model\PeriodsComment;
use app\model\PeriodsFlash;
use app\model\PeriodsProductInventory;
use app\model\PeriodsSecond;
use app\service\ElasticSearch;
use app\service\elasticsearch\ElasticSearchService;
use think\cache\driver\Redis;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;

class CommentCount extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('CommentCount')
            ->setDescription('the CommentCount command');
    }

    protected function execute(Input $input, Output $output)
    {
        // 小编id
        $xba = [1804,1807,1834,1889,5314,6062,7777,12079,14000,14145,16923,16939,16951,17385,19148,325244,335507,810684];
        // 统计闪购总评论
        $flash = PeriodsFlash::where('id', '>', 40000)->column('id');
//        $flash = PeriodsSecond::where([['id', '=', 100705]])->column('id');
        $file = fopen('commentCount.sql', 'a+');
        foreach ($flash as $val) {
            // 当期总评论数
            $c = PeriodsComment::where('period', $val)->column('uid');
            $cc = count($c);
            // 小编评论数
            $xb = 0;
            foreach ($c as $v) {
                if (in_array($v, $xba)) {
                    $xb++;
                }
            }
            // 总评论数
            $is_show_count = 0;
            $c_c = PeriodsProductInventory::where('b.period', $val)
                ->alias('a')
                ->join('periods_comment b', 'a.period = b.period')
                ->field('a.short_code, a.period')
                ->group('a.short_code')
                ->select()
                ->map(function ($item) use (&$is_show_count) {
                    $is_show_count += PeriodsComment::where(['b.short_code' => $item->short_code, 'is_show' => 1])
                        ->alias('a')
                        ->join('periods_product_inventory b', 'a.period = b.period')
                        ->count();
                    // 产品已购瓶数
                    return $item;
                })->toArray();
            $count = ($cc + $is_show_count);
            $sql = 'update vh_statistics_period set notes_user_this = '. $cc . ', notes_admin_this = '. $xb
                . ', notes_all = '. $count . ' where period = '. $val. ';'. "\n";
            fwrite($file, $sql);
            echo $val. PHP_EOL;
        }
        fclose($file);
        echo 'sql 生成完毕';
    }
}
