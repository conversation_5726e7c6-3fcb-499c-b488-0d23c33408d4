<?php
declare (strict_types = 1);

namespace app\command;


use app\model\PeriodsFlash;
use app\model\PeriodsStatusChangeRecord;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;

class DataCleaning extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('DataCleaning')
            ->addArgument('params', Argument::OPTIONAL, "param")
            ->setDescription('the DataCleaning command');
    }

    protected function execute(Input $input, Output $output)
    {
        $sevenDaysAgo = strtotime('-7 days');  // 获取七天前的时间戳
        $ids = PeriodsFlash::where([
            ['onsale_status', '=', 0],
            ['buyer_review_status', '=', 3],
            ['copywriting_review_status', '=', 2],
            ['created_time', '<' , $sevenDaysAgo],
            ['is_delete', '=', 0],
        ])->column('id');
        foreach ($ids as $val) {
            $creator_name = PeriodsFlash::where('id', $val)->value('creator_name');
            $sr = PeriodsStatusChangeRecord::where([
                ['period', '=', $val],
                ['type', '=', 4],
                ['status_type', '=', 1]
            ])->find()->toArray();
            if (empty($sr) || $sr['operator_name'] != $creator_name) {
                PeriodsFlash::where('id', $val)->update(['is_delete' => 1]);
                sleep(1);
            }
        }
    }
}
