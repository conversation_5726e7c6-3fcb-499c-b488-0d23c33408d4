<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use app\service\lib\WorkWeixinService;
use think\facade\Cache;
use app\service\Leftover as PeriodsLeftover;

//批量上架尾货商品（需要打上渠道标识）
class BatchOnSaleLeftoverChannelCommand extends Command
{
    protected $docid = 'dcBmIgCMXzTz1WSRF4bDG3y8DP18neu1zVCmo1J3sbii3y0OHKnwuxBmafo04eJYIPmoTFeBSVUieVSyS3HSQCkw';
    protected $sheet_id = 'jWRwPK';

    protected function configure()
    {
        // 指令配置
        $this->setName('BatchOnSaleLeftoverChannelCommand')
            ->setDescription('the BatchOnSaleLeftoverChannelCommand command');
    }

    protected function execute(Input $input, Output $output)
    {
        $WxService = new WorkWeixinService();
        $WxService->title = '企微智能文档批量上架尾货商品';

        $leftover = new PeriodsLeftover();
        try{
            $str = Cache::get('BatchOnSaleLeftoverChannelCommand');
            if (empty($str)) {
                throw new \Exception("no data");
            }
            // $str = '{"creator_id":1,"creator_name":"甘高寒"}';

            $param = json_decode($str, true);
            $WxService->uid = $param['creator_id'];
            // 用户信息
            $user = $WxService->queryMiddleAdminInfo();
            $param['creator_name'] = $user['realname'];
            // 企微ID
            $WxService->uid = $user['userid'];
            

        
            $fields = ['供应商', '进口类型', '采购', '仓库', '简码', '名称', '成本', '套餐', '售价', '库存', '图片', '处理结果'];
            $data = $WxService->GetSmartTableRecord ($this->docid, $this->sheet_id, $fields);
            if (!empty($data)) {
                foreach ($data as $v) {
                    if (empty($v['values']['简码'][0]['text'])) {
                        continue;
                    }
                    // 上次处理结果
                    $last_results = $v['values']['处理结果'][0]['text'] ?? '';
                    if (strpos($last_results, '成功') !== false) {
                        continue;
                    }

                    try {
                        $imageUrl = [];
                        //图片上传OSS
                        $image = $v['values']['图片'] ?? [];
                        foreach ($image as $f) {
                            $milliseconds = round(microtime(true) * 1000);
                            $file_path = $f['image_url'];
                            $file_title = $milliseconds.'-'.$f['title'];
                            $upload_path = 'vinehoo/client/commodities/batchonsale/' . date('Y-m-d') . '/' . $file_title;
                            $res = uploadFile($file_path, $upload_path);
                            if ($res !== true) {
                                throw new \Exception($res);
                            }
                            $imageUrl[] = '/' . $upload_path;
                        }
                        $import_type = 1;
                        $import_type_name = $v['values']['进口类型'][0]['text'] ?? '';
                        if (!empty($import_type_name) && $import_type_name == '自进口') {
                            $import_type = 0;
                        }
                        $info = [
                            'creator_id' => $param['creator_id'],
                            'creator_name' => $param['creator_name'],
                            'record_id' => $v['record_id'],
                            'image_url' => $imageUrl,
                            'supplier' => trim($v['values']['供应商'][0]['text']),
                            'short_code' => trim($v['values']['简码'][0]['text']),
                            'title' => trim($v['values']['名称'][0]['text']),
                            'cost' => trim($v['values']['成本'][0]['text']),
                            'package_name' => trim($v['values']['套餐'][0]['text']),
                            'price' => trim($v['values']['售价'][0]['text']),
                            'buyer_name' => trim($v['values']['采购'][0]['text']),
                            'inventory' => trim($v['values']['库存'][0]['text']),
                            'warehouse' => trim($v['values']['仓库'][0]['text']),
                            'import_type' => $import_type,
                        ];
                        $preiod = $leftover->batchOnSaleChannelCreate($info);

                        $result = '成功，期数：' . $preiod;

                    } catch (\Exception $e) {
                        $result = $e->getMessage();
                    }

                    // 写入结果
                    $this->updateRecordsResult ($v['record_id'], $result);
                }
            }
        } catch (\Throwable $e) {
            $WxService->sendNotify($e->getMessage(), false);
        }
        
        // 删除缓存
        Cache::delete('BatchOnSaleLeftoverChannelCommand');
        
        echo 'ok';exit;
    }

    /**
     * 更新智能表格记录
     * @param string $title 文档标题
     * @param array $sheets 文档表格字段
     * @return array
     */
    public function updateRecordsResult ($record_id, $result)
    {
        $records = [
            [
                'record_id' => $record_id,
                'values' => [
                    '处理结果' => [
                        [
                            'text' => $result,
                            "type" => "text",
                        ]
                    ]
                ]
            ]
        ];
        (new WorkWeixinService())->updateRecords ($this->docid, $this->sheet_id, $records);
    }
}
