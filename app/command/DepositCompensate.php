<?php

declare(strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use app\service\Periods;

class DepositCompensate extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('DepositCompensate')
            ->setDescription('the DepositCompensate command');
    }

    protected function execute(Input $input, Output $output)
    {
        $periods_data = Db::name('periods_flash')
            ->where([
                ['onsale_status', 'in', [1, 2]],
                ['is_deposit_period', '=', 1],
            ])
            ->select()->toArray();
        $period_id = array_column($periods_data, 'id');

        // 查询套餐
        $periods_set = Db::name('periods_flash_set')
            ->where([
                ['is_hidden', '=', 0],
                ['period_id', 'in', $period_id],
            ])
            ->select()->toArray();
        $package_data = [];
        foreach ($periods_set as $v) {
            $package_data[$v['period_id']][] = $v;
        }

        $periods_ser = new Periods(0);
        foreach ($periods_data as $v) {
            $is_retry = false;
            $package_list = $package_data[$v['id']] ?? [];
            foreach ($package_list as $p) {
                if ($v['onsale_status'] == 1 && $p['is_deposit'] == 1 && $p['deposit_coupon_id'] == 0)
                {
                    $is_retry = true;
                }

                if ($v['onsale_status'] == 2 && $p['is_deposit'] == 1 )
                {
                    $is_retry = true;
                }
            }

            if ($is_retry) {
                echo $v['id'].PHP_EOL;
                $v['sell_time'] = date('Y-m-d H:i:s', $v['sell_time']);
                $v['sold_out_time'] = date('Y-m-d H:i:s', $v['sold_out_time']);
                // 定金期数处理
                $periods_ser->DepositPackageHandle($v, $package_list, 0);
            }
        }

        echo 'ok';
        exit;
    }
}
