<?php
namespace app;


use app\exception\BusinessException;

trait ApiResponse
{
    /**
     * @var string
     */
    protected $errorCode;
    protected $msg;
    /**
     * @param $errorCode
     * @return $this
     */
    public function setErrorCode($errorCode)
    {
        $this->errorCode = $errorCode;
        return $this;
    }

    /**
     * @param $message
     * @return mixed
     */
    public function setMessage($message)
    {
        $this->msg = $message;
        return $this;
    }


    /**
     * @param $status
     * @param array $data
     * @return mixed
     */
    public function status1($data = [])
    {
        $result = [
            'error_code' => $this->errorCode,
            'error_msg'  => empty($this->msg) ? 'ok' : $this->msg,
            'data'       => empty($data) && is_array($data) ? (object)[] : $data
        ];

        return json($result,200);
    }


    /**
     * @param string $message
     * @param string $code
     * @return mixed
     */
    public function failed($msg,$code = -1)
    {
        return $this->setMessage($msg)->setErrorCode($code)->status1([]);
    }

    /**
     * @param $data
     * @param int $status
     * @return mixed
     */
    public function success($data = [], $code = 0)
    {
        return $this->setErrorCode($code)->status1($data);
    }


    public function throwError($msg = "",$errorCode = -1,$code = 200) {

        $data = [
            'error_code' => $errorCode,
            'error_msg'  => $msg,
            'code'       => $code
        ];

        throw new BusinessException($data);
    }

}