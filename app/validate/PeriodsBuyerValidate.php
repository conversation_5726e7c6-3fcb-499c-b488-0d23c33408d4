<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class PeriodsBuyerValidate
 * @package app\validate
 */
class PeriodsBuyerValidate extends Validate
{

    protected $rule = [
        'period|期数'                         => 'require|number|>:0',  //期数
        'periods_type|频道'              => 'require|number|>=:0',  //频道
        'param|参数'              => 'require|array',  //参数
        'buyer_id|采购ID'              => 'require|number|>=:0',  //采购ID
        'buyer_name|采购人'              => 'require',  //采购人
        'distribution_ratio|分账比率'              => 'require|float|>:0|<=:100',  //分账比率
        'vh_uid|操作人ID'              => 'require|number|>=:0',  //操作人ID

    ];


    protected $scene = [
        'list'       => ['period', 'periods_type'],
        'save'       => ['period', 'periods_type', 'param', 'vh_uid'],
        'save_param' => ['buyer_id', 'buyer_name', 'distribution_ratio'],
    ];



}