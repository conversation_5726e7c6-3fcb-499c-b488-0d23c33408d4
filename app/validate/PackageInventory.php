<?php

namespace app\validate;

use think\Validate;

class PackageInventory extends Validate {

    protected $rule = [
        'id' => 'require|integer',
        'periods_type' => 'require|integer',
        'count' => 'require|integer',
    ];

    protected $message  =   [
        'id.require' => '请选择套餐',
        'id.integer' => '套餐格式错误',
        'periods_type.require' => '请选择套餐所属频道',
        'periods_type.integer' => '频道格式错误',
        'count.require' => '请选择数量',
        'count.integer' => '数量格式错误',

    ];

}
