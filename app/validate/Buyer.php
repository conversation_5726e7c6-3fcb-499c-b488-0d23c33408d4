<?php

namespace app\validate;

use think\Validate;

class Buyer extends Validate {

    protected $rule = [
//        'title'  => 'require|max:100',
//        'brief'  => 'require|max:100',
//        'banner_img'  => 'require|max:80',
//        'product_img'  => 'require|max:350',
//        'detail'  => 'require',
        'period' => 'require|integer',
        'supplier_id' => 'require|integer',
        'buyer_id' => 'require|integer',

    ];

    protected $message  =   [
//        'title.require' => '标题必填',
//        'title.max'     => '标题最多不能超过 100 个字符',
//        'brief.require' => '一句话介绍必填',
//        'brief.max'     => '一句话介绍最多不能超过 100 个字符',
//        'banner_img.require' => '题图必填',
//        'banner_img.max' => '题图链接超长',
//        'product_img.require' => '产品图必填',
//        'product_img.max' => '产品图链接超长',
//        'detail.require' => '商品详细必须',
        'period.require' => '请选择商品',
        'period.integer' => '商品格式错误',
        'supplier_id.require' => '请选择供应商',
        'supplier_id.integer' => '供应商格式错误',
        'buyer_id.require' => '请选择采购人',
        'buyer_id.integer' => '采购人格式错误',

    ];

}
