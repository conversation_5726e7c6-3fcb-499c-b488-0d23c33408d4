<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class PurchaseOrdernoItemsValidate
 * @package app\validate
 */
class PurchaseOrdernoItemsValidate extends Validate
{

    protected $rule = [
        'limit|返回条数'          => 'number|gt:0',
        'page|当前页'             => 'number|gt:0',

        'id|id' => 'require|number',  //id
        'purchase_orderno_id|采购单ID' => 'number',  //采购单ID
        'short_code|简码' => 'max:255',  //简码
        'billing_name|开票名称' => 'max:255',  //开票名称
        'en_product_name|英文名' => 'max:255',  //英文名
        'unit|单位' => 'max:255',  //单位
        'capacity|规格' => 'max:255',  //规格
        'remark|备注' => 'max:2048',  //备注
        'period|期数,多个之间/分割' => 'max:2048',  //期数,多个之间/分割
        'number|数量' => 'max:255',  //数量
        'price|含税单价' => 'float|>=:0',  //含税单价
        'tax_rate|税率' => 'float|>=:0',  //税率
        'is_gift|是否赠品' => 'number',  //是否赠品:0=否,1=是

    ];


    protected $scene = [
        'add'    => ['vh_uid', 'vh_vos_name', 'purchase_orderno_id', 'short_code', 'billing_name', 'en_product_name', 'unit', 'capacity', 'remark', 'period', 'number', 'price', 'tax_rate', 'is_gift',],
        'edit'   => [ 'id', 'purchase_orderno_id', 'short_code', 'billing_name', 'en_product_name', 'unit', 'capacity', 'remark', 'period', 'number', 'price', 'tax_rate', 'is_gift',],
        'detail' => [ 'id',],
        'del'    => [ 'id',],
    ];

    public function scenelist()
    {
        return $this->only(['limit', 'page', 'fields', 'id', 'purchase_orderno_id', 'short_code', 'billing_name', 'en_product_name', 'unit', 'capacity', 'remark', 'period', 'number', 'price', 'tax_rate', 'is_gift', ])->remove('id', 'require')
            ;
    }




}