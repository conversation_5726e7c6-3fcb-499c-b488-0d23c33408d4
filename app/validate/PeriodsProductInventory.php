<?php

namespace app\validate;

use think\Validate;

class PeriodsProductInventory extends Validate {

    protected $rule = [
        'period' => 'require|integer',
        'product_id' => 'require|integer',
        'short_code' => 'require|max:32',
        'inventory' => 'require|integer|max:6',
    ];

    protected $message  =   [
        'period.require' => '请选择商品',
        'period.integer' => '商品数据类型错误',
        'product_id.require' => '请选择产品',
        'product_id.integer' => '产品数据类型错误',
        'short_code.require' => '请选择简码',
        'short_code.integer' => '用户数据类型错误',
        'inventory.require' => '请输入库存',
        'inventory.integer' => '库存数据类型错误',
        'inventory.max' => '库存最大设置为 6 位正整数',
    ];

}
