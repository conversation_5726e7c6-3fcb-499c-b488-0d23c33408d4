<?php

namespace app\validate;

use think\Validate;

class Products extends Validate {

    protected $rule = [
        'cn_product_name'  => 'require|max:200|unique:products,cn_product_name',
        'en_product_name'  => 'require|max:200|unique:products,en_product_name',
        'short_code'  => 'require|max:32|unique:products,short_code',
        'product_category'  => 'require|integer',
        'product_category_code'  => 'require|max:10',
        'product_type'  => 'require|integer',
        'product_type_code'  => 'require|max:10',
        'product_unit'  => 'require|integer',
        'product_form'  => 'require|integer',
        'capacity'  => 'require',
        'country'  => 'require',
        'is_gift'  => 'require',
        'costprice'  => 'require',
        'is_addition'  => 'require',
    ];

    protected $message  =   [
        'cn_product_name.require' => '中文名称必须',
        'cn_product_name.max'     => '中文名称最多不能超过 200 个字符',
        'en_product_name.require' => '英文名称必须',
        'en_product_name.max'     => '英文名称最多不能超过 200 个字符',
        'product_unit.require'     => '产品单位必须',
        'product_form.require'     => '产品形态必须',
        'capacity.require'     => '规格必须',
        'country.require'     => '国家必须',
        'is_gift.require'     => '是否赠品必须',
        'costprice.require'     => '成本必须',
        'is_addition.require'     => '是否附加物必须',
    ];

}
