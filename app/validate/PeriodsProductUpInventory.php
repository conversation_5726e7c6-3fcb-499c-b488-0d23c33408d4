<?php

namespace app\validate;

use think\Validate;

class PeriodsProductUpInventory extends Validate {

    protected $rule = [
        'id' => 'require|integer',
        'inventory' => 'require|integer|max:6',
    ];

    protected $message  =   [
        'id.require' => '请选择修改库存',
        'id.integer' => '库存 id 数据类型错误',
        'inventory.require' => '请输入库存',
        'inventory.integer' => '库存数据类型错误',
        'inventory.max' => '库存最大设置为 6 位正整数',
    ];

}
