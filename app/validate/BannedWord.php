<?php

namespace app\validate;

use think\Validate;

class BannedWord extends Validate {

    protected $rule = [
        'word'  => 'require|max:30|unique:banned_word,word',
        'creator_id'  => 'require|integer',

    ];

    protected $message  =   [
        'word.require' => '请选择违禁词',
        'word.max' => '违禁词最多不能超过 30 个字符',
        'word.unique' => '违禁词已存在',
        'creator_id.require' => '请选择违禁词创建人',

    ];

}
