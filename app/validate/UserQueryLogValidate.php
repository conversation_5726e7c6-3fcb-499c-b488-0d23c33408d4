<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class UserQueryLogValidate
 * @package app\validate
 */
class UserQueryLogValidate extends Validate
{

    protected $rule = [
        'limit|返回条数'          => 'number|gt:0',
        'page|当前页'             => 'number|gt:0',

        'id|主键id' => 'require|number',  //主键id
        'keywords|搜索关键词' => 'require|max:255',  //搜索关键词
        'satisfaction|满意度' => 'require|number',  //满意度:0=未评分,1=满意,2=一般,3=不满意
        'uid|用户ID' => 'number',  //用户ID
        'vh_uid|用户ID' => 'number',  //用户ID
        'user_phone|用户手机号' => 'mobile|max:255',  //用户手机号
        'nickname|昵称' => 'max:255',  //昵称
        'user_unique_code|用户唯一编码' => 'require|max:255',  //用户唯一编码

    ];


    protected $scene = [
        'add'    => ['vh_uid', 'vh_vos_name', 'keywords', 'satisfaction', 'vh_uid', 'user_unique_code', 'user_phone', 'nickname',],
        'edit'   => [ 'id', 'keywords', 'satisfaction', 'uid', 'user_phone', 'nickname',],
        'detail' => [ 'id',],
        'del'    => [ 'id',],
    ];

    public function scenelist()
    {
        return $this->only(['limit', 'page', 'fields', 'id', 'keywords', 'satisfaction', 'uid', 'user_phone', 'nickname', ])->remove('id', 'require')
            ->remove('keywords', 'require')
            ->remove('satisfaction', 'require')
            ->remove('uid', 'require')
            ;
    }


    public function sceneuserList()
    {
        return $this->only(['limit', 'page', 'fields', 'id', 'keywords', 'satisfaction', 'vh_uid', 'user_phone', 'user_unique_code', 'nickname', ])->remove('id', 'require')
            ->remove('keywords', 'require')
            ->remove('satisfaction', 'require');
    }




}