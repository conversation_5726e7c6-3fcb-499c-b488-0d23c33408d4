<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class UserPortraitValidate
 * @package app\validate
 */
class UserPortraitValidate extends Validate
{

    protected $rule = [
        'limit|返回条数' => 'number|gt:0',
        'page|当前页'    => 'number|gt:0',

        'id|主键id'      => 'require|number',  //主键id
        'user_portrait_id|用户画像ID'      => 'require|number',  //用户画像ID
        'user_portrait_labels_id|标签ID'      => 'require',  //标签ID
        'name|标题'      => 'require|max:255',  //标题
        'labels|标签'    => 'require|array',  //标题
        'subhead|副标题' => 'max:255',  //副标题
        'status|状态'    => 'number',  //状态:0=禁用,1=启用
        'uid|用户ID'     => 'require|number',  //用户ID
        'genre|类型'             => 'require',  //
        'feedback_type|反馈行为' => 'require',  //
        'item_id|项目ID'         => 'require',  //

    ];


    protected $scene = [
        'add'                => ['name', 'labels',],
        'edit'               => ['id', 'name', 'subhead', 'status',],
        'detail'             => ['id',],
        'del'                => ['id',],
        'activeUserPortrait' => ['uid',],
        'postUserPortrait' => ['uid','user_portrait_id','user_portrait_labels_id',],
        'feedback' => ['uid', 'genre', 'feedback_type', 'item_id'],
    ];

    public function scenelist()
    {
        return $this->only(['limit', 'page', 'fields', 'id', 'name', 'subhead', 'status',])
            ->remove('id', 'require')
            ->remove('name', 'require');
    }


}