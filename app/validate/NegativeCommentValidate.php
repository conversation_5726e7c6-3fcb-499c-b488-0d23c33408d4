<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class NegativeCommentValidate
 * @package app\validate
 */
class NegativeCommentValidate extends Validate
{

    protected $rule = [
        'limit|返回条数'          => 'number|gt:0',
        'page|当前页'             => 'number|gt:0',

        'id|自增id' => 'require|number',  //自增id
        'period|期数' => 'require|number',  //期数
        'period_name|期数名称' => 'max:2048',  //期数名称
        'buyer_id|采购人ID' => 'number',  //采购人ID
        'buyer_name|采购人名称' => 'max:255',  //采购人名称
        'vh_uid|操作人ID' => 'require|number',  //操作人ID
        'vh_vos_name|操作人姓名' => 'require|max:255',  //操作人姓名
        'comment_id|评论ID' => 'require|max:2048',  //评论ID

    ];


    protected $scene = [
        'add'    => ['vh_uid', 'vh_vos_name', 'period', 'period_name', 'buyer_id', 'buyer_name', 'vh_uid', 'vh_vos_name', 'comment_id',],
        'edit'   => [ 'id', 'period', 'period_name', 'buyer_id', 'buyer_name', 'vh_uid', 'vh_vos_name', 'comment_id',],
        'detail' => [ 'id',],
        'del'    => [ 'id',],
    ];

    public function scenelist()
    {
        return $this->only(['limit', 'page', 'fields', 'id', 'period', 'period_name', 'buyer_id', 'buyer_name', 'vh_uid', 'vh_vos_name', 'comment_id', ])->remove('id', 'require')
            ->remove('period', 'require')
            ->remove('vh_uid', 'require')
            ->remove('vh_vos_name', 'require')
            ->remove('comment_id', 'require')
            ;
    }




}