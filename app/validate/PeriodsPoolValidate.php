<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class PeriodsPoolValidate
 * @package app\validate
 */
class PeriodsPoolValidate extends Validate
{

    protected $rule = [
        'limit|返回条数' => 'number|gt:0',
        'page|当前页'    => 'number|gt:0',

        'id|期数'                                  => 'require|number',  //期数
        'periods_type|频道'                        => 'require|number',  //频道
        'import_type|进口类型'                     => 'require|number',  //进口类型:0=自采,1=地采,2=跨境
        'payee_merchant_id|收款商户id'             => 'require|number',  //收款商户id
        'payee_merchant_name|收款商户名称'         => 'require|max:255',  //收款商户名称
        'is_gift_box|礼盒发货'                     => 'require|number',  //礼盒发货:0=不带,1=代
        'is_supplier_delivery|是否代发'            => 'require|number',  //是否代发:0=否,1=是
        'is_presell|是否预售'                      => 'require|number',  //是否预售:0=否,1=是
        'shipping_conditions|发货条件'             => 'require|number',  //发货条件:0=常温,1=冷链
        'storage_conditions|存储条件'              => 'require|number',  //存储条件:0=常温,1=冰冻
        'delivery_time_limit|发货时效'             => 'require|number',  //发货时效:0=24小时,1=72小时,2=72小时以上
        'purchase_remake|采购备注'                 => 'require|max:255',  //采购备注
        'selling_point|卖点介绍'                   => 'require|max:255',  //卖点介绍
        'demand_grade|写作难易程度'                => 'number',  //写作难易程度:1=低,2=中,3=高
        'status|当前状态'                          => 'require|max:255',  //当前状态
        'delay_time|运营审核截止时间'              => 'date',  //运营审核截止时间
        'copy_periods_id|复制的期数id:0=未复制'    => 'require|number',  //复制的期数id:0=未复制
        'expect_done_time|期望完成日期'            => 'date',  //是否需要设计:0=否,1=是
        'is_design|是否需要设计'                   => 'number',  //是否需要设计:0=否,1=是
        'demand|文案需求'                          => 'requireCallback:check_copy_periods_id|max:255',  //文案需求
        'expect_time|期望完成时间'                 => 'date',  //期望完成时间
        'purchase_expect_time|期望上架时间'        => 'date',  //期望完成时间
        'title|期数标题（名称）'                     => 'max:350',  //期数标题（名称）
        'brief|一句话介绍（副标题）'                 => 'max:350',  //一句话介绍（副标题）
        'banner_img|题图'                          => 'max:350',  //题图
        'product_img|产品图'                       => 'max:65535',  //产品图
        'video|视频'                               => 'max:350',  //视频
        'video_cover|视频封面'                     => 'max:350',  //视频封面
        'detail|题图详情'                          => 'max:65535',  //题图详情
        'purchase_uid|采购人uid'                   => 'require|number',  //采购人uid
        'purchase_name|采购人'                     => 'require|max:255',  //采购人
        'creator_uid|文案uid'                      => 'number',  //文案uid
        'creator_name|文案'                        => 'max:10',  //文案
        'design_title|设计酒款名'                  => 'max:255',  //设计酒款名
        'design_cn_highlights|中文亮点'            => 'max:255',  //中文亮点
        'design_score|评分'                        => 'max:255',  //评分
        'design_style|设计风格'                    => 'max:255',  //设计风格
        'design_website|官网'                      => 'max:255',  //官网
        'attachment|附件,多个逗号分隔'             => 'max:65535',  //附件,多个逗号分隔
        'is_postpone|是否延期'                     => 'require|number',  //是否延期:0=否,1=是
        'planned_completion_time|文案计划完成时间' => 'date',  //文案计划完成时间

        'product_ids|产品'  => 'require',  //产品表IDs ,多个,分割
        'products|产品'     => 'require|array',  //产品表IDs ,多个,分割
        'items|产品'        => 'require|array',
        'sku_ids|SKUID'     => 'require',
        'sku_id|SKUID'      => 'require',
        'product_id|产品ID' => 'require',

        'vh_uid|用户ID'        => 'require',
        'vh_vos_name|用户姓名' => 'require',

        'periods_id|期数'                   => 'require|number',  //期数
        'sku_id|skuid'                      => 'require|number',  //skuid
        'warehouse|仓库'                    => 'require|max:255',  //仓库
        'warehouse_id|虚拟仓id（虚拟仓编码）' => 'require',  //虚拟仓id（虚拟仓编码）
        'product_name|产品名称'             => 'require|max:255',  //产品名称
        'en_product_name|产品英文名称'      => 'require|max:255',  //产品英文名称
        'short_code|产品简码'               => 'require|max:32',  //产品简码
        'product_category|商品类别'         => 'require|max:255',  //商品类别
        'supplier|供应商'                   => 'max:500',  //供应商
        'supplier_id|供应商id'              => 'number',  //供应商id
        'country|国家'                      => 'require|max:200',  //国家
        'capacity|容量'                     => 'require|max:100',  //容量
        'costprice|成本价'                  => 'require|float|>=:0',  //成本价
        'price|售卖价格'                    => 'require|float|>=:0',  //售卖价格
        'num|售卖数量'                      => 'require|number',  //售卖数量
        'actual_num|实际库存'               => 'require|number',  //实际库存

        'periods_id|期数id'            => 'require|number',  //期数id
        'uid|操作人id'                 => 'require|number',  //操作人id
        'name|操作人名称'              => 'require|max:255',  //操作人名称
        'create_time|创建时间'         => 'date',  //创建时间
        'operation|操作'               => 'require|max:255',  //操作
        'remarks|备注'                 => 'require|max:255',  //备注
        'reject_cause|驳回原因'        => 'requireIf:status,4|max:255',  //备注
        'current_node_id|操作的节点ID' => 'require',  //备注
//        'current_node_name|操作的节点名称' => 'require',  //备注
//        'current_node_customer_id|操作的节点自定义ID' => 'require',  //操作的节点自定义ID

    ];

    function check_copy_periods_id($value, $data)
    {
        if ($data['copy_periods_id'] > 0) {
            return false;
        }
        return true;
    }

    protected $scene = [
        'add'                   => ['vh_uid', 'vh_vos_name', 'periods_type', 'import_type', 'payee_merchant_id', 'payee_merchant_name', 'is_gift_box', 'is_supplier_delivery', 'is_presell', 'shipping_conditions', 'storage_conditions', 'delivery_time_limit', 'purchase_remake', 'selling_point', 'demand_grade', 'status', 'delay_time', 'copy_periods_id', 'is_design', 'demand', 'expect_time', 'title', 'brief', 'banner_img', 'product_img', 'video', 'video_cover', 'detail', 'purchase_uid', 'purchase_name', 'creator_uid', 'creator_name', 'design_title', 'design_cn_highlights', 'design_score', 'design_style', 'design_website', 'attachment', 'is_postpone',],
        'edit'                  => ['id', 'periods_type', 'import_type', 'payee_merchant_id', 'payee_merchant_name', 'is_gift_box', 'is_supplier_delivery', 'is_presell', 'shipping_conditions', 'storage_conditions', 'delivery_time_limit', 'purchase_remake', 'selling_point', 'demand_grade', 'status', 'delay_time', 'copy_periods_id', 'is_design', 'demand', 'expect_time', 'title', 'brief', 'banner_img', 'product_img', 'video', 'video_cover', 'detail', 'purchase_uid', 'purchase_name', 'creator_uid', 'creator_name', 'design_title', 'design_cn_highlights', 'design_score', 'design_style', 'design_website', 'attachment', 'is_postpone',],
        'detail'                => ['id',],
        'del'                   => ['id',],
        'supplierProductList'   => ['short_code',],
        'onSale'                => ['products', 'vh_uid', 'vh_vos_name'],
        'onSaleProducts'        => ['sku_id', 'product_id', 'supplier', 'supplier_id'],
        'purchaseSubmit'        => ['id', 'vh_uid', 'vh_vos_name', 'periods_type', 'import_type', 'payee_merchant_id', 'payee_merchant_id', 'is_gift_box', 'is_supplier_delivery', 'is_presell', 'shipping_conditions', 'storage_conditions', 'delivery_time_limit', 'copy_periods_id', 'is_design', 'demand_grade', 'demand', 'expect_time', 'items', 'warehouse', 'warehouse_id', 'current_node_id', 'current_node_name', 'current_node_customer_id'],
        'purchaseSubmitItems'   => ['product_id', 'costprice', 'price', 'num', 'actual_num',],
        'qualification'         => ['id', 'vh_uid', 'vh_vos_name', 'product_id', 'frontal_label_img', 'cn_back_label_img', 'en_back_label_img', 'package_img', 'health', 'customs_pass', 'current_node_id', 'current_node_name',],
        'qualificationSubmit'   => ['id', 'vh_uid', 'vh_vos_name', 'current_node_id', 'current_node_name'],
        'editor'                => ['id', 'vh_uid', 'vh_vos_name', 'title', 'brief', 'banner_img', 'product_img', 'video_cover', 'video', 'detail', 'current_node_id', 'current_node_name',],
        'editorUpdate'          => ['id', 'vh_uid', 'vh_vos_name'],
        'editorSubmit'          => ['id', 'vh_uid', 'vh_vos_name', 'current_node_id', 'current_node_name',],
        'pictureSubmit'         => ['id', 'vh_uid', 'vh_vos_name', 'current_node_id', 'current_node_name',],
        'pictureDistribution'   => ['id', 'vh_uid', 'vh_vos_name', 'design_title', 'design_cn_highlights', 'design_score', 'design_style', 'design_website', 'current_node_id', 'current_node_name',],
        'documentDistribution'  => ['id', 'vh_uid', 'vh_vos_name', 'creator_name', 'demand_grade', 'creator_uid', 'planned_completion_time', 'current_node_id', 'current_node_name',],
        'documentPersonnel'     => ['id', 'vh_uid', 'vh_vos_name', 'creator_name', 'creator_uid', 'current_node_id', 'current_node_name',],
        'uiPersonnel'           => ['id', 'vh_uid', 'vh_vos_name', 'ui_name', 'ui_uid', 'current_node_id', 'target_node_id', 'current_node_name',],
        'auditPurchase'         => ['id', 'vh_uid', 'vh_vos_name', 'reject_cause', 'status', 'expect_time', 'current_node_id', 'current_node_name',],
        'auditLastPurchase'     => ['id', 'vh_uid', 'vh_vos_name', 'reject_cause', 'status', 'expect_time', 'current_node_id', 'current_node_name',],
        'document'              => ['id', 'vh_uid', 'vh_vos_name', 'reject_cause', 'status', 'current_node_id', 'current_node_name',],
        'purchaseExec'          => ['id', 'vh_uid', 'vh_vos_name', 'reject_cause', 'status', 'current_node_id', 'current_node_name',],
        'operation'             => ['id', 'vh_uid', 'vh_vos_name', 'delay_time', 'status',],
        'shortCodesList'        => ['vh_uid', 'vh_vos_name', 'short_code',],
        'resend'                => ['id', 'vh_uid', 'vh_vos_name', 'purchase_expect_time',],
        'uiPictureDistribution' => ['id', 'vh_uid', 'vh_vos_name', 'design_title', 'design_cn_highlights', 'design_score', 'design_style', 'design_website', 'attachment', 'current_node_id', 'current_node_name',],
        'backlogQuantity'       => ['vh_uid', 'vh_vos_name'],
        'logList'               => ['id'],
        'findPeriods'           => ['id'],
        'getCompanyByProducts'  => ['product_ids'],
    ];


    public function scenelist()
    {
        return $this->only(['limit', 'page', 'fields', 'id', 'periods_type', 'import_type', 'payee_merchant_id', 'payee_merchant_name', 'is_gift_box', 'is_supplier_delivery', 'is_presell', 'shipping_conditions', 'storage_conditions', 'delivery_time_limit', 'purchase_remake', 'selling_point', 'status', 'delay_time', 'expect_time', 'title', 'brief', 'banner_img', 'product_img', 'video', 'video_cover', 'detail', 'purchase_uid', 'purchase_name', 'creator_uid', 'creator_name', 'design_title', 'design_cn_highlights', 'design_score', 'design_style', 'design_website', 'attachment', 'is_postpone',])->remove('id', 'require')
            ->remove('periods_type', 'require')
            ->remove('import_type', 'require')
            ->remove('payee_merchant_id', 'require')
            ->remove('payee_merchant_name', 'require')
            ->remove('is_gift_box', 'require')
            ->remove('is_supplier_delivery', 'require')
            ->remove('is_presell', 'require')
            ->remove('shipping_conditions', 'require')
            ->remove('storage_conditions', 'require')
            ->remove('delivery_time_limit', 'require')
            ->remove('purchase_remake', 'require')
            ->remove('selling_point', 'require')
            ->remove('status', 'require')
            ->remove('delay_time', 'require')
            ->remove('expect_time', 'require')
            ->remove('title', 'require')
            ->remove('brief', 'require')
            ->remove('banner_img', 'require')
            ->remove('product_img', 'require')
            ->remove('video', 'require')
            ->remove('detail', 'require')
            ->remove('purchase_uid', 'require')
            ->remove('purchase_name', 'require')
            ->remove('creator_uid', 'require')
            ->remove('creator_name', 'require')
            ->remove('design_title', 'require')
            ->remove('design_cn_highlights', 'require')
            ->remove('design_score', 'require')
            ->remove('design_style', 'require')
            ->remove('design_website', 'require')
            ->remove('attachment', 'require')
            ->remove('is_postpone', 'require');
    }


}