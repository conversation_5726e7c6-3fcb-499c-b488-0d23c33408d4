<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class UserPortraitLogsValidate
 * @package app\validate
 */
class UserPortraitLogsValidate extends Validate
{

    protected $rule = [
        'limit|返回条数'          => 'number|gt:0',
        'page|当前页'             => 'number|gt:0',

        'id|主键id' => 'require|number',  //主键id
        'uid|用户ID' => 'require|number',  //用户ID
        'user_portrait_id|用户画像ID' => 'require|number',  //用户画像ID
        'user_portrait_labels_id|标签ID' => 'require|number',  //标签ID
        'label|标签' => 'require|max:255',  //标签

    ];


    protected $scene = [
        'add'    => ['vh_uid', 'vh_vos_name', 'uid', 'user_portrait_id', 'user_portrait_labels_id', 'label',],
        'edit'   => [ 'id', 'uid', 'user_portrait_id', 'user_portrait_labels_id', 'label',],
        'detail' => [ 'id',],
        'del'    => [ 'id',],
        'listByUserPortrait'    => [ 'user_portrait_id',],
    ];

    public function scenelist()
    {
        return $this->only(['limit', 'page', 'fields', 'id', 'uid', 'user_portrait_id', 'user_portrait_labels_id', 'label', ])->remove('id', 'require')
            ->remove('uid', 'require')
            ->remove('user_portrait_id', 'require')
            ->remove('user_portrait_labels_id', 'require')
            ->remove('label', 'require')
            ;
    }




}