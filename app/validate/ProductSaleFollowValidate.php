<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class ProductSaleFollowValidate
 * @package app\validate
 */
class ProductSaleFollowValidate extends Validate
{

    protected $rule = [
        'limit|返回条数' => 'number|gt:0',
        'page|当前页'    => 'number|gt:0',

        'vh_vos_name|操作人'                         => 'require',  //id
        'vh_uid|操作人ID'                         => 'require|number',  //id
        'id|id'                         => 'require|number',  //id
        'short_code|简码'              => 'require|max:50',
        'product_id|产品ID'             => 'require|number',
        'bar_code|条码'      => 'require',
        'operation_name|运营'               => 'max:255',
        'operation_id|运营ID'  => 'number',
        'planned_completion_time|计划完成时间'      => 'require|dateFormat:Y-m-d',
        'purchase_nums|采购数量'    => 'require|number|>:0',
        'main_period|最后一期期数'           => 'require|number|>:0',
        'period|期数'  => 'require|number|>:0',
        'periods_type|频道'            => 'require|number',
        'remark|备注' => 'require',
        'file|文件路径' => 'require',

        'A|简码' => 'require',
        'B|运营' => 'require',
        'C|采购量' => 'require|number|>:0',
        'D|文件路径' => 'require',
    ];

    protected $scene = [
        'shortCodeSearch' => ['short_code'],
        'add'       => ['vh_uid', 'vh_vos_name', 'short_code', 'product_id', 'bar_code',  'operation_name', 'operation_id', 'planned_completion_time', 'purchase_nums', 'main_period'],
        'add_items' => ['period','periods_type'],
        'addRemark' => ['id','vh_uid', 'vh_vos_name', 'remark'],
        'remarkList' => ['id'],
        'import' => ['vh_uid','vh_vos_name','file'],
        'remove' => ['id'],
        'remove_itme' => ['A','B','C','D'],
    ];

    public function scenelist()
    {
        return $this->only(['limit', 'page',])->remove('id', 'require');
    }


    // 自定义验证规则
    protected function checkFloatLen($value, $rule, $data = [])
    {
        if (preg_match('/\.\d{9,}/', $value)) {
            return "含税单价小数点后长度不超过8位";
        } else {
            return true;
        }
    }


}