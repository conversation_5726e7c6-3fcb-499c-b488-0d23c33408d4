<?php

namespace app\validate;

use think\Validate;

class Package extends Validate {

    protected $rule = [
        'period_id'  => 'require|integer',
        'periods_type' => 'require|integer',
        'package_name'  => 'require|max:50',
        'price'  => 'require|float',
        'market_price'  => 'require|float',
        'associated_products'  => 'require',
    ];

    protected $message  =   [
        'package_name.max'  => '套餐名称长度不能超过50个字符',
        'package_name.require'  => '套餐名称不能为空',
    ];

}
