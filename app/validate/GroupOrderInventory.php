<?php

namespace app\validate;

use think\Validate;

class GroupOrderInventory extends Validate {

    protected $rule = [
        'group_id' => 'require|integer',
        'group_join_nums' => 'require|integer',
        'group_limit_nums' => 'require|integer',
    ];

    protected $message  =   [
        'group_id.require' => '请选择拼团id',
        'group_id.integer' => '拼团数据类型错误',
        'group_join_nums.require' => '已参与拼单人数不能为空',
        'group_join_nums.integer' => '已参与拼单人数数据类型错误',
        'group_limit_nums.require' => '拼单人数限制不能为空',
        'group_limit_nums.integer' => '拼单人数数据类型错误',
    ];

}
