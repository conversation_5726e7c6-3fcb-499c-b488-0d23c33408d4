<?php

namespace app\validate;

use think\Validate;

class ProductMinority extends Validate {

    protected $rule = [
        'cn_product_name'  => 'require|max:200|unique:products,cn_product_name',
        'short_code'  => 'require|max:32|unique:products,short_code',
        'product_category'  => 'require|integer',
        'product_category_code'  => 'require|max:10',
        'product_type'  => 'require|integer',
        'product_type_code'  => 'require|max:10',

    ];

    protected $message  =   [
        'cn_product_name.require' => '中文名称必须',
        'cn_product_name.max'     => '中文名称最多不能超过 200 个字符',
        'short_code.require'     => '简码必须',
    ];

}
