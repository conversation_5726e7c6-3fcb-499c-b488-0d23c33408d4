<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class PeriodsPurchasedSyncValidate
 * @package app\validate
 */
class PeriodsPurchasedSyncValidate extends Validate
{

    protected $rule = [
        'limit|返回条数'          => 'number|gt:0',
        'page|当前页'             => 'number|gt:0',

        'id|主键id' => 'require|number',  //主键id
        'sub_order_no|子单号' => 'require|max:255',  //子单号
        'vmall_period|商家期数' => 'require|number',  //商家期数
        'second_period|秒发期数' => 'require|number',  //秒发期数
        'purchased|销量' => 'require|number',  //销量

    ];


    protected $scene = [
        'add'    => ['vh_uid', 'vh_vos_name', 'sub_order_no', 'vmall_period', 'second_period', 'purchased',],
        'edit'   => [ 'id', 'sub_order_no', 'vmall_period', 'second_period', 'purchased',],
        'detail' => [ 'id',],
        'del'    => [ 'id',],
    ];

    public function scenelist()
    {
        return $this->only(['limit', 'page', 'fields', 'id', 'sub_order_no', 'vmall_period', 'second_period', 'purchased', ])->remove('id', 'require')
            ->remove('sub_order_no', 'require')
            ->remove('vmall_period', 'require')
            ->remove('second_period', 'require')
            ->remove('purchased', 'require')
            ;
    }




}