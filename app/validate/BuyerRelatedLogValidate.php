<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class BuyerRelatedLogValidate
 * @package app\validate
 */
class BuyerRelatedLogValidate extends Validate
{

    protected $rule = [
        'limit|返回条数'          => 'number|gt:0',
        'page|当前页'             => 'number|gt:0',

        'id|主键id' => 'require|number',  //主键id
        'operation_review_id|商品运营审核者id' => 'require|number',  //商品运营审核者id
        'operation_review_name|商品运营审核者名称' => 'require|max:10',  //商品运营审核者名称
        'buyer_id|采购人id' => 'require|number',  //采购人id
        'type|操作类型' => 'require|number',  //操作类型:1=添加关联,2=解除关联
        'buyer_name|采购人名称' => 'require|max:15',  //采购人名称
        'vh_uid|操作用户id' => 'number',  //操作用户id
        'vh_vos_name|VOS用户姓名' => 'max:255',  //VOS用户姓名

    ];


    protected $scene = [
        'add'    => ['vh_uid', 'vh_vos_name', 'operation_review_id', 'operation_review_name', 'buyer_id', 'type', 'buyer_name', 'vh_uid', 'vh_vos_name',],
        'edit'   => [ 'id', 'operation_review_id', 'operation_review_name', 'buyer_id', 'type', 'buyer_name', 'vh_uid', 'vh_vos_name',],
        'detail' => [ 'id',],
        'del'    => [ 'id',],
    ];

    public function scenelist()
    {
        return $this->only(['limit', 'page', 'fields', 'id', 'operation_review_id', 'operation_review_name', 'buyer_id', 'type', 'buyer_name', 'vh_uid', 'vh_vos_name', ])->remove('id', 'require')
            ->remove('operation_review_id', 'require')
            ->remove('operation_review_name', 'require')
            ->remove('buyer_id', 'require')
            ->remove('type', 'require')
            ->remove('buyer_name', 'require')
            ;
    }




}