<?php

namespace app\validate;

use think\Validate;

class AuctionBid extends Validate {

    protected $rule = [
//        'title'  => 'require|max:100',
//        'brief'  => 'require|max:100',
//        'banner_img'  => 'require|max:80',
//        'product_img'  => 'require|max:350',
//        'detail'  => 'require',
        'period' => 'require|integer',
        'uid' => 'require|integer',
        'bid_price' => 'require',
//        'bid_status' => 'require|integer',

    ];

    protected $message  =   [
//        'title.require' => '标题必填',
//        'title.max'     => '标题最多不能超过 100 个字符',
//        'brief.require' => '一句话介绍必填',
//        'brief.max'     => '一句话介绍最多不能超过 100 个字符',
//        'banner_img.require' => '题图必填',
//        'banner_img.max' => '题图链接超长',
//        'product_img.require' => '产品图必填',
//        'product_img.max' => '产品图链接超长',
//        'detail.require' => '商品详细必须',
        'period.require' => '拍卖期数必传',
        'period.integer' => '期数格式错误',
        'uid.require' => '用户id必传',
        'uid.integer' => '用户id格式错误',
        'bid_price.require' => '出价金额必传',
//        'bid_price.integer' => '出价金额格式错误',
//        'bid_status.require' => '出价状态必传',
//        'bid_status.integer' => '出价状态格式错误',

    ];

}
