<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class UserPortraitLabelsValidate
 * @package app\validate
 */
class UserPortraitLabelsValidate extends Validate
{

    protected $rule = [
        'limit|返回条数'          => 'number|gt:0',
        'page|当前页'             => 'number|gt:0',

        'id|主键id' => 'require|number',  //主键id
        'user_portrait_id|用户画像ID' => 'number',  //用户画像ID
        'name|标签' => 'max:255',  //标签

    ];


    protected $scene = [
        'add'    => ['vh_uid', 'vh_vos_name', 'user_portrait_id', 'name',],
        'edit'   => [ 'id', 'user_portrait_id', 'name',],
        'detail' => [ 'id',],
        'del'    => [ 'id',],
    ];

    public function scenelist()
    {
        return $this->only(['limit', 'page', 'fields', 'id', 'user_portrait_id', 'name', ])->remove('id', 'require')
            ;
    }




}