<?php

namespace app\validate;

use think\Validate;

class Copywriter extends Validate {

    protected $rule = [
        'title'  => 'require|max:300',
        'brief'  => 'require|max:300',
        'banner_img'  => 'require',
        'product_img'  => 'require',
        'detail'  => 'require',
//        'product_id' => 'require',
//        'creator_name' => 'require',
//        'creator_id' => 'require',
    ];

    protected $message  =   [
        'title.require' => '标题必填',
        'title.max'     => '标题最多不能超过 300 个字符',
        'brief.require' => '一句话介绍必填',
        'brief.max'     => '一句话介绍最多不能超过 300 个字符',
        'banner_img.require' => '题图必填',
        'product_img.require' => '产品图必填',
        'detail.require' => '商品详细必须',
//        'product_id.require' => '请选择产品',
//        'creator_name.require' => '文案添加人必须',
//        'creator_name.id' => '文案添加人必须',

    ];

}
