<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class PurchaseOrdernoValidate
 * @package app\validate
 */
class PurchaseOrdernoValidate extends Validate
{

    protected $rule = [
        'limit|返回条数' => 'number|gt:0',
        'page|当前页'    => 'number|gt:0',

        'id|id'                         => 'require|number',  //id
        'orderno|采购单号'              => 'require|max:50',  //采购单号
        'operator|采购人id'             => 'number',  //采购人id
        'operator_name|采购人名称'      => 'require|max:10',  //采购人名称
        'period|期数'                   => 'max:1024',  //期数
        'source|来源：0=手动,1=自动,2=机器人自动下单'     => 'number',  //来源：0=手动，1=自动，2=机器人自动下单
        'warehouse|入库仓库'            => 'require|max:255',  //入库仓库
        'warehouse_code|入库仓erp编码'  => 'require|max:20',  //入库仓erp编码
        'supplier|供应商'               => 'require|max:255',  //供应商
        'supplier_code|供应商编码'      => 'require|max:20',  //供应商编码
        'operator_code|采购员工编码'    => 'require|max:20',  //采购员工编码
        'department|采购部门'           => 'require|max:255',  //采购部门
        'department_code|采购部门编码'  => 'require|max:20',  //采购部门编码
        'created_uid|操作人'            => 'number',  //操作人
        'created_name|操作人名称'       => 'max:10',  //操作人名称
        'status|采购单状态'             => 'number',  //采购单状态:0=自由(缺省),1=未用,2=正在审批,3=审批通过,4=审批未通过,5=输出,6=冻结,7=执行完毕
        'setttlement|付款方式'          => 'max:255',  //付款方式
        'setttlement_code|付款方式编码' => 'max:20',  //付款方式编码
        'remark|备注'                   => 'max:2048',  //备注
        'corp_code|公司编码'            => 'max:255',  //公司编码

        'price|含税单价' => 'float|>=:0|checkFloatLen',  //含税单价
        'items|产品数据' => 'require|array',  //明细
        'payee_merchant_id|收款商户id' => 'require|number',
        'payee_merchant_name|收款商户名称' => 'require|max:255',
    ];


    protected $scene = [
        'add'       => ['vh_uid', 'vh_vos_name', 'orderno', 'operator', 'operator_name', 'period', 'source', 'warehouse', 'warehouse_code', 'supplier', 'supplier_code', 'operator_code', 'department', 'department_code', 'created_uid', 'created_name', 'status', 'setttlement', 'setttlement_code', 'remark', 'corp_code', 'items'],
        'add_items' => ['price',],
        'edit'      => ['id', 'orderno', 'operator', 'operator_name', 'period', 'source', 'warehouse', 'warehouse_code', 'supplier', 'supplier_code', 'operator_code', 'department', 'department_code', 'created_uid', 'created_name', 'status', 'setttlement', 'setttlement_code', 'remark', 'corp_code', 'items'],
        'detail'    => ['id',],
        'del'       => ['id',],
    ];

    public function scenelist()
    {
        return $this->only(['limit', 'page', 'fields', 'id'])->remove('id', 'require');
    }


    // 自定义验证规则
    protected function checkFloatLen($value, $rule, $data = [])
    {
        if (preg_match('/\.\d{9,}/', $value)) {
            return "含税单价小数点后长度不超过8位";
        } else {
            return true;
        }
    }


}