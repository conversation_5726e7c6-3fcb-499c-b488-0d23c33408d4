<?php

namespace app\model;

class PeriodsPool extends BaseModel
{
    protected $connection = 'mongodb';

    const IMPORT_TYPE = [ '0' => '自采', '1' => '地采', '2' => '跨境']; //进口类型
    const IS_GIFT_BOX = [ '0' => '不带', '1' => '代']; //礼盒发货
    const IS_SUPPLIER_DELIVERY = [ '0' => '否', '1' => '是']; //是否代发
    const IS_PRESELL = [ '0' => '否', '1' => '是']; //是否预售
    const SHIPPING_CONDITIONS = [ '0' => '常温', '1' => '冷链']; //发货条件
    const STORAGE_CONDITIONS = [ '0' => '常温', '1' => '冰冻']; //存储条件
    const DELIVERY_TIME_LIMIT = [ '0' => '24小时', '1' => '72小时', '2' => '72小时以上']; //发货时效
    const DEMAND_GRADE = [ '1' => '低', '2' => '中', '3' => '高']; //写作难易程度
    const IS_DESIGN = [ '0' => '否', '1' => '是']; //是否需要设计
    const IS_POSTPONE = [ '0' => '否', '1' => '是']; //是否延期


    
    #region 进口类型 获取器
    public function getImportTypeTextAttr($value)
    {
        return self::IMPORT_TYPE[$value] ?? null;
    }
    #endregion
    
    #region 礼盒发货 获取器
    public function getIsGiftBoxTextAttr($value)
    {
        return self::IS_GIFT_BOX[$value] ?? null;
    }
    #endregion
    
    #region 是否代发 获取器
    public function getIsSupplierDeliveryTextAttr($value)
    {
        return self::IS_SUPPLIER_DELIVERY[$value] ?? null;
    }
    #endregion
    
    #region 是否预售 获取器
    public function getIsPresellTextAttr($value)
    {
        return self::IS_PRESELL[$value] ?? null;
    }
    #endregion
    
    #region 发货条件 获取器
    public function getShippingConditionsTextAttr($value)
    {
        return self::SHIPPING_CONDITIONS[$value] ?? null;
    }
    #endregion
    
    #region 存储条件 获取器
    public function getStorageConditionsTextAttr($value)
    {
        return self::STORAGE_CONDITIONS[$value] ?? null;
    }
    #endregion
    
    #region 发货时效 获取器
    public function getDeliveryTimeLimitTextAttr($value)
    {
        return self::DELIVERY_TIME_LIMIT[$value] ?? null;
    }
    #endregion
    
    #region 写作难易程度 获取器
    public function getDemandGradeTextAttr($value)
    {
        return self::DEMAND_GRADE[$value] ?? null;
    }
    #endregion
    
    #region 运营审核截止时间 获取器和编辑器
    public function setDelayTimeAttr($value)
    {
        if (!$value) return null;
        if (is_numeric($value)) return $value;
        return $value ? strtotime($value) : null;
    }

    public function getDelayTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s',$value) : null;
    }
    #endregion
    
    #region 是否需要设计 获取器
    public function getIsDesignTextAttr($value)
    {
        return self::IS_DESIGN[$value] ?? null;
    }
    #endregion
    
    #region 期望完成时间 获取器和编辑器
    public function setExpectTimeAttr($value)
    {
        if (!$value) return null;
        if (is_numeric($value)) return $value;
        return $value ? strtotime($value) : null;
    }

    public function getExpectTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s',$value) : null;
    }
    #endregion
    
    #region 是否延期 获取器
    public function getIsPostponeTextAttr($value)
    {
        return self::IS_POSTPONE[$value] ?? null;
    }
    #endregion
    

}