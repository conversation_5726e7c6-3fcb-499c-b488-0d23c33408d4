<?php

namespace app\model;

class PurchaseOrdernoApprove extends BaseModel
{
    protected $name = 'purchase_orderno_approve';

    const STATUS = ['0' => '审批中', '1' => '同意', '2' => '驳回'];

    const APPROVE_TYPE = ['0' => '预下单', '1' => '确认下单', '2' => '采购审核', '3' => '财务审核', '4' => '延期下单', '5' => '财务复审', '6' => '作废', '7' => '财务主管审核'];


    #region 状态 获取器
    public function getStatusAttr($value)
    {
        return self::STATUS[$value] ?? '';
    }

     #region 类型 获取器
     public function getApproveTypeAttr($value)
     {
         return self::APPROVE_TYPE[$value] ?? '';
     }

    // 时间戳转换
    public function getSendTimeAttr($value)
    {
        return !empty($value) ? date('Y-m-d H:i:s', $value) : '';
    }

    // reviewe_time字段访问器
    public function getRevieweTimeAttr($value)
    {
        return !empty($value) ? date('Y-m-d H:i:s', $value) : '';
    }
}