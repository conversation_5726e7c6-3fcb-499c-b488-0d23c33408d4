<?php

namespace app\model;

use think\model\concern\SoftDelete;

class BuyerRelatedLog extends BaseModel
{
    use SoftDelete;

    protected $autoWriteTimestamp = true;
    protected $createTime         = 'created_time';
    protected $deleteTime = 'delete_time';

    const TYPE = [ '1' => '添加关联', '2' => '解除关联']; //操作类型


    
    #region 操作类型 获取器
    public function getTypeTextAttr($value)
    {
        return self::TYPE[$value] ?? null;
    }
    #endregion
    

}