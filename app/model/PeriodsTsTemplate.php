<?php

namespace app\model;

use think\Model;

class PeriodsTsTemplate extends Model {
    // 主键
    protected $pk = 'id';

    public function getStartTimeAttr($val) {
        return date('Y-m-d H:i:s', $val);
    }
    public function getEndTimeAttr($val) {
        return date('Y-m-d H:i:s', $val);
    }
    public function getCreatedTimeAttr($val) {
        return date('Y-m-d H:i:s', $val);
    }
    public function getTsLongtimeAttr($val) {
        return date('Y-m-d', $val);//
    }
}