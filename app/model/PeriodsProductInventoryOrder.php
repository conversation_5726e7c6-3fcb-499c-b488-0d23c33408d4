<?php

namespace app\model;

use think\Model;

class PeriodsProductInventoryOrder extends Model {
    // 主键
    protected $pk = 'id';

    // 时间戳转换
    public function getCreatedTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    // 预计发货时间戳转换
    public function getPredictShipmentTimeAttr($value) {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

}