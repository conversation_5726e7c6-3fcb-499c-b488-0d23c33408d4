<?php

namespace app\model;

use think\facade\Db;
use think\Model;

class PeriodsRabbitCoupon extends Model {
    // 主键
    protected $pk = 'id';


    // 上架时间戳转换
    public function getOnsaleTimeAttr($value) {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    // 下架时间戳转换
    public function getSoldOutTimeAttr($value) {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    // 预计发货时间戳转换
    public function getPredictShipmentTimeAttr($value) {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    // 预计发货时间戳转换
    public function getSellTimeAttr($value) {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    // 图片 oss 域名拼接
    public function getBannerImgAttr($value) {
        $value = explode(",", $value);
        foreach ($value as &$v) {
            $v = env('ALIURL').$v;
        }
        return $value;
    }

    // 图片 oss 域名拼接
    public function getProductImgAttr($value) {
        $value = explode(",", $value);
        foreach ($value as &$v) {
            $v = env('ALIURL').$v;
        }
        return $value;
    }
    public function reduceNums($id, $field)
    {
        $value = Db::name('periods_rabbit_coupon')->where('id', $id)->value($field);
        if ($value > 0) {
            Db::name('periods_rabbit_coupon')->where('id', $id)->dec($field)->update();
        }
    }

}