<?php

namespace app\model;

use think\Model;

class PeriodsSecondMerchants extends Model {

    //商家状态
    const ONSALE_STATUS = [0 => '待上架', 1 => '待售中', 2 => '在售中', 3 => '已下架', 4 => '已驳回'];

    // 主键
    protected $pk = 'id';

    // 上架时间戳转换
    public function getOnsaleTimeAttr($value) {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    // 下架时间戳转换
    public function getSoldOutTimeAttr($value) {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    // 下架操作时间戳转换
    public function getOffSellTimeAttr($value) {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    // 预计发货时间戳转换
    public function getPredictShipmentTimeAttr($value) {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    // 预计发货时间戳转换
    public function getSellTimeAttr($value) {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    // 图片 oss 域名拼接
    public function getBannerImgAttr($value) {
        if ($value) {
            $value = explode(",", $value);
            foreach ($value as &$v) {
                if (strpos($v,'vinehoo/goods-imageshttp://img.vinehoo.com')) {
                    $v = str_replace('vinehoo/goods-imageshttp://img.vinehoo.com', '', $v);
                }
                $v = env('ALIURL').$v;
            }
        }
        return $value;
    }

    // 图片 oss 域名拼接
    public function getProductImgAttr($value) {
        if ($value) {
            $value = explode(",", $value);
            foreach ($value as &$v) {
                $v = env('ALIURL').$v;
            }
        }
        return $value;
    }

    // 图片 oss 域名拼接
    public function getHorizontalImgAttr($value) {
        if ($value) {
            $value = explode(",", $value);
            if (!empty($value)) {
                foreach ($value as &$v) {
                    $v = env('ALIURL').$v;
                }
            }
        }
        return $value;
    }

    // 图片 oss 域名拼接
    public function getVideoCoverAttr($value) {
        if ($value != '') {
            $value = env('ALIURL').$value;
        }
        return $value;
    }

}