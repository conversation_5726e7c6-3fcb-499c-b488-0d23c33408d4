<?php

namespace app\model;

use think\Model;

class Products extends Model {
    // 主键
    protected $pk = 'id';

    // 产品类型名称
    public function getProductTypeNameAttr($value, $data): string
    {
        return ProductType::where('id', $data['product_type'])->value('name');
    }

    // 产品类别名称
    public function getProductCategoryNameAttr($value, $data): string
    {
        return ProductCategory::where('id', $data['product_category'])->value('name');
    }
}