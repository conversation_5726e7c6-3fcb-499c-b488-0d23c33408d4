<?php

namespace app\model;

use think\Model;

class UserPortrait extends Model
{
    
    protected $autoWriteTimestamp = true;
    protected $updateTime         = 'update_time';
    protected $createTime         = 'created_time';

    const STATUS = [ '0' => '禁用', '1' => '启用']; //状态


    
    #region 状态 获取器
    public function getStatusTextAttr($value)
    {
        return self::STATUS[$value] ?? null;
    }
    #endregion


    public function labels()
    {
        return $this->hasMany(UserPortraitLabels::class,'user_portrait_id', 'id');
    }


}