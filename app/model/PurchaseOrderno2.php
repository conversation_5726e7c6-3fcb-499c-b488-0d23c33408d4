<?php

namespace app\model;

use think\Model;
use think\facade\Db;

class PurchaseOrderno2 extends BaseModel
{

    protected $name               = 'purchase_orderno';
    protected $autoWriteTimestamp = true;
    protected $updateTime         = 'update_time';
    protected $createTime         = 'created_time';

    const STATUS = ['0' => '自由(缺省)', '1' => '未用', '2' => '正在审批', '3' => '审批通过', '4' => '审批未通过', '5' => '输出', '6' => '冻结', '7' => '执行完毕']; //采购单状态

    //中台状态
    const PUSH_ERP_STATUS = ['0' => '待确认', '1' => '下单已确认', '2' => '下单失败', '3' => '已驳回', '4' => '采购审核', '5' => '财务审核', '6' => '预下单', '7' => '自动下单', '8' => '延期下单', '9' => '作废采购单', '10' => '财务主管审核'];


    #region 采购单状态 获取器
    public function getStatusTextAttr($value, $data)
    {
        return self::STATUS[$data['status']] ?? '';
    }
    #endregion

    #region 中台状态 获取器
    public function getPushErpStatusTextAttr($value, $data)
    {
        return self::PUSH_ERP_STATUS[$data['push_erp_status']] ?? '';
    }
    #endregion

    public function items()
    {
        return $this->hasMany(PurchaseOrdernoItems::class, 'purchase_orderno_id', 'id');

    }

    public function period()
    {
        return $this->hasMany(PurchaseOrdernoPeriod::class, 'purchase_orderno_id', 'id');

    }

    public function approve()
    {
        return $this->hasMany(PurchaseOrdernoApprove::class, 'purchase_orderno_id', 'id');

    }

    public function waybill()
    {
        return $this->hasMany(PurchaseOrdernoWaybill::class, 'purchase_orderno_id', 'id');

    }

    public function setBillDateAttr($value)
    {
        if (!$value) return null;
        if (is_numeric($value)) return $value;
        return $value ? strtotime($value) : null;
    }

    public function getBillDateAttr($value)
    {
        return $value ? date('Y-m-d', $value) : null;
    }

    public function getAnnexAttr($value)
    {
        if (!empty($value)) {
            $value = explode(',', $value);
            // 拼接域名
            foreach ($value as $k => $v) {
                $value[$k] = env('ALIURL') . $v;
            }
        } else {
            $value = [];
        }
        return $value;
    }

    public function setAnnexAttr($value)
    {
        if (empty($value)) {
            return '';
        }
        if (!is_array($value)) {
            return $value;
        }
        $value = array_values(array_unique(array_filter($value)));
        // 去掉域名
        foreach ($value as $k => $v) {
            $value[$k] = str_replace(env('ALIURL'), '', $v);
        }

        return !empty($value) ? implode(',', $value) : '';
    }

    public function getWaybillNoAttr($value, $data)
    {
        return Db::name('purchase_orderno_waybill')->where('purchase_orderno_id', $data['id'])->column('waybill_no');
    }
}