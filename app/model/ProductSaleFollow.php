<?php

namespace app\model;

use think\Model;

class ProductSaleFollow extends BaseModel
{

    // 指定当前模型使用的数据库连接
    // protected $connection         = 'mysql_follow';
    protected $name               = 'product_sale_follow';
    protected $autoWriteTimestamp = true;
    protected $updateTime         = 'update_time';
    protected $createTime         = 'created_time';

    #endregion

    public function remark()
    {
        return $this->hasMany(ProductSaleFollowRemark::class, 'fid', 'id');

    }

    public function period()
    {
        return $this->hasMany(PeriodsProductInventory::class, 'product_id', 'product_id');

    }

    public function follow()
    {
        $this->connection = 'mysql_follow';
        return $this;
    }

    #region  获取器和编辑器
    public function setBillDateAttr($value)
    {
        if (!$value) return null;
        if (is_numeric($value)) return $value;
        return $value ? strtotime($value) : null;
    }

    public function getBillDateAttr($value)
    {
        return $value ? date('Y-m-d', $value) : null;
    }

    #endregion


}