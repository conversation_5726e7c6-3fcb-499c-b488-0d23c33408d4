<?php

namespace app\model;

class UserQueryLog extends BaseModel
{
    
    protected $autoWriteTimestamp = true;
    protected $createTime         = 'created_time';
    
    const SATISFACTION = [ '0' => '未评分', '1' => '满意', '2' => '一般', '3' => '不满意']; //满意度


    
    #region 满意度 获取器
    public function getSatisfactionTextAttr($value)
    {
        return self::SATISFACTION[$value] ?? null;
    }
    #endregion
    

}