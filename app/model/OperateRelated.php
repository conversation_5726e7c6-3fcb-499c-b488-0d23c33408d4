<?php

namespace app\model;

use think\model\concern\SoftDelete;

class OperateRelated extends BaseModel
{

    use SoftDelete;
    protected $autoWriteTimestamp = true;
    protected $createTime         = 'created_time';
    protected $deleteTime = 'delete_time';


    public function buyer()
    {
        return $this->hasMany(BuyerRelated::class, 'operation_review_id', 'operation_review_id');
    }

    public function log()
    {
        return $this->hasMany(BuyerRelatedLog::class, 'operation_review_id', 'operation_review_id');
    }
}