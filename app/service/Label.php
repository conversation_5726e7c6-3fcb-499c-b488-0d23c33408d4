<?php


namespace app\service;


use app\ErrorCode;
use think\facade\Db;

class Label
{
    /**
     * Description:推荐标签列表
     * Author: zrc
     * Date: 2023/4/24
     * Time: 17:34
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getLabelList($params)
    {
        $result['list']  = [];
        $result['total'] = 0;
        $offset          = ($params['page'] - 1) * $params['limit'];
        $where           = [];
        $where[]         = ['is_delete', '=', 0];
        if (isset($params['type']) && in_array($params['type'], [1, 2])) {
            $where[] = ['type', '=', $params['type']];
        }
        if (!empty($params['name'])) {
            $where[] = ['name', 'like', "%{$params['name']}%"];
        }
        $totalNum = Db::name('recommend_label')->where($where)->count();
        $list     = Db::name('recommend_label')->where($where)->order('id desc')->limit($offset, $params['limit'])->select()->toArray();
        if (count($list) > 0) {
            $admin_id = array_unique(array_column($list, 'operator'));
            foreach ($admin_id as $key => $val) {
                if ($val == 0) unset($admin_id[$key]);
            }
            $adminInfo = curlRequest(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => implode(',', $admin_id), 'field' => 'realname'], [], 'GET');
            foreach ($list as &$val) {
                $val['update_time']  = !empty($val['update_time']) ? date('Y-m-d H:i:s', $val['update_time']) : '';
                $val['created_time'] = !empty($val['created_time']) ? date('Y-m-d H:i:s', $val['created_time']) : '';
                if ($val['operator'] == 0) $val['operator'] = '系统';
                if (empty($val['auto_add_content'])){
                    $val['auto_add_content'] = [];
                }else{
                    $val['auto_add_content'] = json_decode($val['auto_add_content'],'true');
                }
                if (empty($val['auto_add_type'])){
                    $val['auto_add_type'] = [];
                }else{
                    $val['auto_add_type'] = explode(',', $val['auto_add_type']);
                }
                if (isset($adminInfo['data'][$val['operator']])) $val['operator'] = $adminInfo['data'][$val['operator']];
            }
        }
        $result['list']  = $list;
        $result['total'] = $totalNum;
        return serviceReturn(true, $result, '');
    }

    /**
     * Description:新增推荐标签
     * Author: zrc
     * Date: 2023/4/24
     * Time: 17:34
     * @param $params
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function labelAddData($params)
    {
        $count = Db::name('recommend_label')->where(['name' => $params['name'], 'type' => $params['type'], 'is_delete' => 0])->count();
        if ($count > 0) return serviceReturn(false, '', '标签已存在，请勿重复添加');

        if (!empty($params['add_method']) && $params['add_method'] == 1) {
            if (empty($params['auto_add_type'])) {
                return serviceReturn(false, '', '自动添加类型不能为空');
            }
            $auto_add_content = $params['auto_add_content'] ?? [];
            $auto_content = [];
            foreach ($params['auto_add_type'] as $v) {
                if (empty($auto_add_content[$v])) {
                    return serviceReturn(false, '', '自动添加定义内容不能为空');
                }
                $auto_content[strval($v)] = $auto_add_content[$v];
            }
            $params['auto_add_content'] = $auto_content;
        }
        
        $insertData = array(
            'name'         => $params['name'],
            'type'         => $params['type'],
            'operator'     => $params['operator'],
            'created_time'     => time(),
            'update_time'     => time(),
            'add_method'     => $params['add_method'] ?? 0,
            'auto_add_type'     => !empty($params['auto_add_type']) ? implode(',', $params['auto_add_type']): '0',
            'auto_add_content' => empty($params['auto_add_content']) ? '' : json_encode($params['auto_add_content'])
        );
        $result     = Db::name('recommend_label')->insertGetId($insertData);
        if (empty($result)) return serviceReturn(false, '', '添加失败');

        if ($insertData['add_method'] == 1) {
            // 把所有的在售商品按照规则添加标签
            addLabelByRule($result);
        }

        return serviceReturn(true, $result, '');
    }

    /**
     * Description:修改推荐标签
     * Author: zrc
     * Date: 2023/4/24
     * Time: 17:31
     * @param $params
     * @return array|\think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function labelEditData($params)
    {
        $labelData = Db::name('recommend_label')->where(['id' => $params['id']])->find();
        if (empty($labelData)) return throwResponse([], ErrorCode::EXEC_ERROR, '未获取到标签数据');
        $updateData                = [];
        $updateData['update_time'] = time();
        $updateData['operator']    = $params['operator'];
        if (isset($params['is_delete']) && in_array($params['is_delete'], [0, 1])) {
            $updateData['is_delete'] = $params['is_delete'];
        }
        if (!empty($params['name'])) {
            $count = Db::name('recommend_label')->where('id','<>', $params['id'])->where([ 'name' => $params['name'], 'type' => $labelData['type'], 'is_delete' => 0])->count();
            if ($count > 0) return serviceReturn(false, '', '标签已存在，请勿重复添加');
            $updateData['name'] = $params['name'];
        }
        
        if (!empty($params['add_method']) && $params['add_method'] == 1) {
            if (empty($params['auto_add_type'])) {
                return serviceReturn(false, '', '自动添加类型不能为空');
            }
            $auto_add_content = $params['auto_add_content'] ?? [];
            $auto_content = [];
            foreach ($params['auto_add_type'] as $v) {
                if (empty($auto_add_content[$v])) {
                    return serviceReturn(false, '', '自动添加定义内容不能为空');
                }
                $auto_content[strval($v)] = $auto_add_content[$v];
            }
            $params['auto_add_content'] = $auto_content;
        }

        if (isset($params['add_method'])) {
            $updateData['add_method'] = $params['add_method'];
        }

        if (isset($params['auto_add_type'])) {
            $updateData['auto_add_type'] = implode(',', $params['auto_add_type']);
        }

        if (isset($params['auto_add_content'])) {
            $updateData['auto_add_content'] = empty($params['auto_add_content']) ? '' : json_encode($params['auto_add_content']);
        }

        $result = Db::name('recommend_label')->where(['id' => $params['id']])->update($updateData);

        if ($labelData['add_method'] == 1) {
            // 把所有的在售商品按照规则添加标签
            addLabelByRule($params['id']);
        }

        return serviceReturn(true, $result, '');
    }

    /**
     * 查询标签名称
     * @param array $params
     * @return array|\think\Collection|Db[]
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getLabelByName(array $params)
    {
        $where = [];
        if (!empty($params['name'])) {
            $where[] = ['name', 'in', $params['name']];
        }
        $where[] = ['type', '=', 1];
        $where[] = ['is_delete', '=', 0];
        return Db::name('recommend_label')->where($where)->select();
    }

    /**
     * 根据 id 查询标签
     * @param string $ids
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getLabelNameByIds(string $ids): array
    {
        return Db::name('recommend_label')->field('id, name')
            ->where('is_delete', 0)
            ->whereIn('id', $ids)
            ->select()
            ->toArray();
    }
}