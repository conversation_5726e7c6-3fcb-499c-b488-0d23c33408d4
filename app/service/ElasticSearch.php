<?php

namespace app\service;

use Elasticsearch\ClientBuilder;
use app\service;

class ElasticSearch
{

    private $hosts = [];

    public function __construct()
    {
        $this->hosts[] = [
            'host' => env('ES.HOST','127.0.0.1'),
            'port' => env('ES.PORT',9200),
            'user' => env('ES.USER','root'),
            'pass' => env('ES.PASS','vinehoo666')
        ];
    }

    /**
     * 获取期数操作员
     */
    public function getPeriodOne(int $period)
    {
        $esWhere['bool']['must'][] = ['match' => ['id' => $period]];

        $params = [
            'index' => 'vinehoo.periods',
            'type' => '_doc',
//            '_source' => $fields,
            'body' => [
                'query' => $esWhere,
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($this->hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);
        $list = [];
        if (isset($data['list'][0])) {
            $list = $data['list'][0];
        }
        return $list;
    }

    /**
     * 更新 es 数据
     * @param int $period 期数
     * @param array $data 更新数据
     * @return array|callable
     */
    public function updatePeriodsById(int $period, array $data) {
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($this->hosts)->build();
        $params = [
            'index' => 'vinehoo.periods',
            'type' => '_doc',
            'id' => $period, // 通过期数更新
            'body' => [
                'doc' => $data
            ]
        ];
        return $client->update($params);
    }

    /**
     * 获取前端展示期数
     * @param int $periods_type 频道
     * @param int $page 页码
     * @param int $limit 每页大小
     * @param array $field 取值字段
     * @return array|mixed
     */
    public function getPeriodsTypeList(int $periods_type = 0, int $page = 0, int $limit = 15, array $field = []) {
        $from = $page;
        $size = $limit;

        if ($from > 0) {
            $from = ($from - 1) * $size;
        } else {
            $from = 0;
        }

        // 默认查询所有
        $esWhere['bool']['must'] = [];
        // 未删除
        $esWhere['bool']['must'][] = ['match' => ['is_delete' => 0]];


        $esWhere['bool']['must'][] = ['match' => ['periods_type' => $periods_type]];
        // 非渠道商品
        if ($periods_type < 5) {
            $esWhere['bool']['must'][] = ['match' => ['is_channel' => 0]];
        }

        // 默认查询，待售中，在售中，售完不下架
//        $esWhere['bool']['must'][] = ['match' => ['onsale_status' => 2]];
        $esWhere['bool']['should'][] = ['match' => ['onsale_status' => 1]];
        $esWhere['bool']['should'][] = ['match' => ['onsale_status' => 2]];
        $esWhere['bool']['should'][] = ['match' => ['sellout_sold_out' => 1]];
        $esWhere['bool']['minimum_should_match'] = 1;

        // 排序
        $sort = [['sort' => 'desc'], ['onsale_time' => 'desc']];

        $params = [
            'index' => 'vinehoo.periods',
            'type' => '_doc',
            'body' => [
                'query' => $esWhere,
                '_source' => $field,
                'sort' => $sort,
                'size' => $size,
                'from' => $from
            ]
        ];

        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($this->hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);

        // 遍历列表取出限购数量
        if (!empty($data['list'])) {
            return $data['list'];
        }
        return [];
    }

    /**
     * 根据期数查询订单
     * @param int $period
     * @return array|mixed
     */
    public function getOrderByPeriod(int $period) {
        $esWhere['bool']['must'][] = ['match' => ['period' => $period]];
        $esWhere['bool']['must'][] = ['terms' => ['sub_order_status' => [1,2,3]]];

        $params = [
            'index' => 'vinehoo.orders',
            'type' => '_doc',
//            '_source' => $fields,
            'body' => [
                'query' => $esWhere,
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($this->hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);
        $list = [];
        if (isset($data['list'][0])) {
            $list = $data['list'][0];
        }
        return $list;
    }

    /**
     * 获取磐石产品信息
     * @param string $short_code 简码
     * @return array|mixed
     */
    public function getProductByShortCode(string $short_code) {
        $esWhere['bool']['must'][] = ['match' => ['short_code' => $short_code]];

        $params = [
            'index' => 'vinehoo.panshi.products',
            'type' => '_doc',
//            '_source' => $fields,
            'body' => [
                'query' => $esWhere,
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($this->hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);
        $list = [];
        if (isset($data['list'][0])) {
            $list = $data['list'][0];
        }
        return $list;
    }

}