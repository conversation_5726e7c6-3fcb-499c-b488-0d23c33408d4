<?php

namespace app\service;

use AlibabaCloud\SDK\Cdn\V20180510\Cdn;
use AlibabaCloud\SDK\Cdn\V20180510\Models\RefreshObjectCachesRequest;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use app\ElasticSearchConnection;
use app\model\InterfaceCallLog;
use app\model\PeriodsAuction;
use app\model\PeriodsComment;
use app\model\PeriodsCommentLike;
use app\model\PeriodsCross;
use app\model\PeriodsFlash;
use app\model\PeriodsGroup;
use app\model\PeriodsLeftover;
use app\model\PeriodsNewcomer;
use app\model\PeriodsOffsaleProductRecord;
use app\model\PeriodsPageviews;
use app\model\PeriodsPraiseRecord;
use app\model\PeriodsProductInventory;
use app\model\PeriodsProductInventoryOrder;
use app\model\PeriodsRabbit;
use app\model\PeriodsRabbitCoupon;
use app\model\PeriodsRemark;
use app\model\PeriodsReservation;
use app\model\PeriodsReviewLog;
use app\model\PeriodsSecond;
use app\model\PeriodsSecondMerchants;
use app\model\PeriodsStatusChangeRecord;
use app\model\PeriodsTaskLog;
use app\model\PeriodsUserAuction;
use app\model\PeriodsUserCollection;
use app\model\PeriodsVest;
use app\model\PeriodsVestRecord;
use app\model\VirtualWarehouse;
use app\service\elasticsearch\ElasticSearchService;
use app\service\es\Es;
use app\service\Other as ServiceOther;
use app\service\Package as PeriodsPackage;
use Darabonba\OpenApi\Models\Config;
use OSS\OssClient;
use think\db\Query;
use think\facade\Db;
use think\facade\Log;
use think\Model;


class Periods
{

    // 数据模型
    protected $model = null;

    // 期数类型 （0：闪购，1：秒发，2：跨境，3：尾货）
    protected $type = 0;

    // 期数类型 （0：闪购，1：秒发，2：跨境，3：尾货）
    public $type_name = [0 => '闪购', 1 => '秒发', 2 => '跨境', 3 => '尾货', 4 => '兔头', 5 => '兔头优惠券',
        9 => '商家秒发', 10 => '商家闪购', 11 => '拍卖'];

    // 进口类型
    protected $import_type_name = [0 => '自采', 1 => '地采', 2 => '跨境'];

    public function getImportTypeName(): array
    {
        return $this->import_type_name;
    }

    // 采购审核状态
    protected $buyer_review_status = [0 => '待提交', 1 => '已提交', 2 => '待采购审核', 3 => '采购勉强通过', 4 => '采购已驳回', 5 => '采购加分通过', 6 => '时间紧迫而通过'];
    // 采购审核通过状态
    public $buyer_review_tg_status = [3, 5, 6];
    // 运营审核状态
    protected $onsale_review_status = [0 => '待运营绑定', 1 => '待运营审核', 2 => '审批中', 3 => '运营已通过', 4 => '运营已驳回'];
    // 上下架状态
    protected $onsale_status = [0 => '待上架', 1 => '待售中', 2 => '在售中', 3 => '已下架', 4 => '已驳回'];
    // 文案主管审核状态
    protected $copywriting_review_status = [0 => '待提交', 1 => '文案已提交', 2 => '文案主管已通过', 3 => '文案主管已驳回'];

    public $period_table_arr = ['periods_flash', 'periods_second', 'periods_cross', 'periods_leftover'];

    // 设置数据模型
    function __construct(int $periods_type)
    {
        // 期数类型
        $this->type = $periods_type;

        switch ($periods_type) {
            case 0:
                // 闪购
                $this->model = new PeriodsFlash();
                break;
            case 1:
                // 秒发
                $this->model = new PeriodsSecond();
                break;
            case 2:
                // 跨境
                $this->model = new PeriodsCross();
                break;
            case 3:
                // 尾货
                $this->model = new PeriodsLeftover();
                break;
            case 4:
                // 兔头
                $this->model = new PeriodsRabbit();
                break;
            case 5:
                // 兔头优惠券
                $this->model = new PeriodsRabbitCoupon();
                break;
            case 9:
                // 商家秒发
                $this->model = new PeriodsSecondMerchants();
                break;
            case 11:
                $this->model = new PeriodsAuction();
                break;
        }
    }

    /**
     * 商品内容采购审核
     * @param array $params
     * @return PeriodsCross|PeriodsFlash|PeriodsLeftover|PeriodsSecond
     */
    public function review(array $params)
    {
        $info        = $this->model::where('id', $params['period'])->find();
        $buyer_id    = $info['buyer_id'];
        $oerate_info = Db::name('buyer_related')->alias('t1')
            ->join('operate_related t2', 't1.operation_review_id=t2.operation_review_id')
            ->where('t1.delete_time', null)
            ->where('t2.delete_time', null)
            ->where('t1.buyer_id', $buyer_id)
            ->field('t2.operation_review_id,t2.operation_review_name')
            ->find();
        if (!empty($oerate_info)) {
//            $periods_remark[] = [
//                'period'        => $params['period'],
//                'periods_type'  => $params['periods_type'],
//                'remark'        => "由采购：-更换到采购：" . ($oerate_info['operation_review_name'] ?? '') . "。",
//                'operator'      => request()->header('vinehoo-uid',0),
//                'operator_name' => base64_decode(request()->header('vinehoo-vos-name', '')),
//                'created_time'  => time(),
//            ];
//            Db::name('periods_remark')->insertAll($periods_remark);
            $r_data['operation_review_id']   = $oerate_info['operation_review_id'];
            $r_data['operation_review_name'] = $oerate_info['operation_review_name'];
        }

        $r_data['buyer_review_status'] = $params['buyer_review_status'];
        // 如果通过采购审核添加采购信息
        if (in_array($params['buyer_review_status'], $this->buyer_review_tg_status)) {
            $r_data['buyer_review_status'] = 3;
            // 验证供应商收款商户
            $res = $this->VerifySupplierReceiptMerchant($params['supplier_id'], $params['payee_merchant_id'], $params['periods_type']);
            if ($res !== true) {
                return serviceReturn(false, [], $res);
            }

            if (!empty($params['is_supplier_delivery']) && $params['is_supplier_delivery'] == 1 && empty($params['supplier_delivery_address'])) {
                return serviceReturn(false, [], '代发商品请选择发货地');
            }

            $r_data['supplier'] = $params['supplier'] ?? '';
            $r_data['import_type'] = $params['import_type'] ?? 1;
            $r_data['supplier_id'] = $params['supplier_id'] ?? '';
            $r_data['is_presell'] = $params['is_presell'] ?? '';
            $r_data['is_supplier_delivery'] = $params['is_supplier_delivery'] ?? '';
            $r_data['payee_merchant_id'] = $params['payee_merchant_id'];
            $r_data['payee_merchant_name'] = $params['payee_merchant_name'];
            if (!empty($params['is_supplier_delivery']) && $params['is_supplier_delivery'] == 1) {
                $r_data['supplier_delivery_address'] = $params['supplier_delivery_address'];
            }
            $result = $this->model::where('id', $params['period'])
            ->update($r_data);
            if ($result) {
                // 更新收款账户推企微
                $this->updatePayeeMerchantDingdingPush($params['period']);
            }
            
        } else {
            $this->model::where('id', $params['period'])
            ->update($r_data);
            // 如果驳回发送钉钉通知
            if ($params['buyer_review_status'] == 4) {
                $remark = $params['remark'] ?? '';
                $remark = $params['remark'] ?? '';
                $other_ser = new Other();
                $send_text = '您的期数： ' . $params['period'] . ' 已被驳回，' . '驳回原因：' . "\"$remark\"。" .
                    ' 驳回时间：' . date('Y-m-d H:i:s', time());
                $get_period = $this->getOne((int)$params['period'], 'creator_id');
                if (isset($get_period['creator_id']) && !empty($get_period['creator_id'])) {
                    $other_ser->sendDingText($get_period['creator_id'], $send_text);
                }

            }
        }

        // 通过方式
        $sub_status_map = [3 => 2, 5 => 1, 6 => 3];
        $sub_status = $sub_status_map[$params['buyer_review_status']] ?? 0;
        
        // 添加状态操作日志
        $scr_data['type'] = 1;
        $scr_data['status_type'] = $r_data['buyer_review_status'];
        $scr_data['status_name'] = $this->buyer_review_status[$params['buyer_review_status']];
        $scr_data['operator_id'] = $params['user_id'];
        $scr_data['operator_name'] = $params['user_name'];
        $scr_data['period'] = $params['period'];
        $scr_data['periods_type'] = $this->type;
        $scr_data['describe'] = $params['reject_reason'] ?? '';
        $scr_data['remark'] = $params['remark'] ?? '';
        $scr_data['created_time'] = date('Y-m-d H:i:s', time());
        $scr_data['sub_status'] = $sub_status;
        PeriodsStatusChangeRecord::create($scr_data);

        return serviceReturn(true, [], '操作成功');
    }

    /**
     * 查询期数驳回备注
     * @param array $params
     * @return PeriodsReviewLog|array|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getReviewLog(array $params)
    {
        return PeriodsReviewLog::where('periods_id', $params['period'])
            ->order('id', 'desc')
            ->find();
    }

    /**
     * 运营上架内容审核
     * @param array $params
     * @return PeriodsCross|PeriodsFlash|PeriodsLeftover|PeriodsSecond
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function onSale(array $params)
    {
        $update_data = [
            'onsale_review_status' => $params['onsale_review_status'],
            'onsale_review_time' => time(),
        ];

        $buyer_id = $this->model::where('id', $params['period'])->value('buyer_id');
        $oerate_info = Db::name('buyer_related')->alias('t1')
            ->join('operate_related t2', 't1.operation_review_id=t2.operation_review_id')
            ->where('t1.delete_time', null)
            ->where('t2.delete_time', null)
            ->where('t1.buyer_id', $buyer_id)
            ->field('t2.operation_review_id,t2.operation_review_name')
            ->find();
        if(empty($oerate_info)){
            $update_data['operation_review_id'] = $params['user_id'];
            $update_data['operation_review_name'] = $params['user_name'];
        }
        $result = $this->model::where('id', $params['period'])
            ->update($update_data);
        // 获取期数详细
//        $periods_info = $this->getOne(($params['period']));

        if ($params['onsale_review_status'] == '3') {
            $package = new Package($this->type);
            $custom_package = $package->getPackage([
                'period_id' => $params['period'],
                'is_custom_package' => 1
            ]);
            if (!empty($custom_package)) {
                $package->createCustomPackage($custom_package);
            }
            
            // 更新现有商品套餐已上架
            if ($this->type != 5) {
                $package->updateAllByPeriod((int)$params['period'], ['is_onsale' => 1]);
            }
            // 生成json文件
            $this->create_period_json((int)$params['period'], $this->type,0,-1);
            // 刷新CDN
            self::CDNrefreshObject((int)$params['period']);

        }
        // 添加状态操作日志
        $scr_data['type'] = 2;
        $scr_data['status_type'] = $params['onsale_review_status'];
        $scr_data['status_name'] = $this->onsale_review_status[$params['onsale_review_status']];
        $scr_data['operator_id'] = $params['user_id'];
        $scr_data['operator_name'] = $params['user_name'];
        $scr_data['period'] = $params['period'];
        $scr_data['periods_type'] = $this->type;
        $scr_data['created_time'] = date('Y-m-d H:i:s', time());
        $scr_data['describe'] = $params['reject_reason'] ?? '';
        $scr_data['remark'] = $params['remark'] ?? '';
        PeriodsStatusChangeRecord::create($scr_data);
        return $result;
    }

    /**
     * 非渠道【二次确认】上架时，需要按照期数仓库查询萌牙库存，如果有库存需要设置订货量。
     *
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function confirmOnSaleSetPurchase(array $params)
    {
        $time = time();
        $periods_product = Db::name('periods_product_inventory')
            ->where('period', $params['period'])
            ->column('id,period,periods_type,bar_code,short_code,warehouse_id');

        $short_code = array_column($periods_product, 'short_code');
        // 查询萌牙库存
        $url = env('ITEM.DISTRIBUTE_URL') . '/query/goodsGetFictitiousCount';
        $body = json_encode([
            'short_code' => $short_code,
            'store_code' => 'xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa',
        ]);
        $res = curlRequest($url, $body);
        $wms_data = $res['data'] ?? [];
        foreach ($wms_data as $k => $v) {
            if (!empty($v) && is_array($v)) {
                foreach ($v as $vv) {
                    $my_inventory_data["{$k}:{$vv['fictitious_id']}"] = $vv;
                }
            }
        }

        $periods_remark = [];
        foreach ($periods_product as $v) {
            $key = $v['short_code'] . ':' . intval($v['warehouse_id']);
            $wms_inventory = $my_inventory_data[$key]['goods_count'] ?? 0;
            if ($wms_inventory > 0) {
                #增加订货量
                Db::name('periods_product_inventory')
                    ->where('id', $v['id'])
                    ->inc('order', $wms_inventory)
                    ->update();

                $periods_remark[] = [
                    'period' => $v['period'],
                    'periods_type' => $v['periods_type'],
                    'remark' => "【{$v['short_code']}】二次确认上架萌牙有库存，初始订货量：{$wms_inventory}",
                    'operator' => $params['user_id'],
                    'operator_name' => $params['user_name'],
                    'created_time' => $time,
                ];
            }
        }

        // 添加商品备注
        Db::name('periods_remark')->insertAll($periods_remark);

        return true;
    }
    
    /**
     * 期数上下架
     *
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function offSale(array $params)
    {

        // 更新参数
        $up_data = [];
        // 商品上架
        if ($params['onsale_status'] == 2) {
            // 验证是否存在可售套餐
            if ($this->type < 5) {
                $package_ser = new Package($this->type);
                $package_list = $package_ser->packageList(['period' => $params['period']]);
                if (count($package_list) < 1) {
                    return serviceReturn(false, $package_list, '未查询到可用套餐');
                }
                // 验证套餐是否绑定产品
                $is_product = 1;
                $custom_package = [];
                foreach ($package_list as $val) {
                    $product_json = json_decode($val['associated_products'], true);
                    if (empty($product_json)) {
                        $is_product = 0;
                    }
                    if (!empty($val['is_custom_package']) && $val['is_custom_package'] == 1){
                        $custom_package[] = $val;
                    }
                }
                if ($is_product == 0) {
                    return serviceReturn(false, $package_list, '检测到未绑定产品简码的套餐');
                }
                if (!empty($custom_package)) {
                    //创建自选套餐所有匹配套餐
                    $package_ser->createCustomPackage($custom_package);
                }

                // 验证产品关单卫检
                $resu = $this->vCustomsOrderHealthInspect($params['period'], 1, $package_list);
                if ($resu !== true) {
                    return serviceReturn(false, $package_list, $resu);
                }
            }

            // 验证是否通过审核
            $field = 'id,import_type,onsale_review_status,onsale_time,sell_time,sold_out_time,supplier_id,supplier,predict_shipment_time,buyer_id,operation_review_id';
//            if ($this->type == 0 or $this->type == 3) {
//                $field .= ', payee_merchant_id,payee_merchant_name';
//            }
            if ($this->type < 4) {
                $field .= ',is_channel, payee_merchant_id,payee_merchant_name,is_cold_chain,is_cold_free_shipping';
            }
            // 拍卖查询截拍时间
            if ($this->type == 11) {
                $field .= ', closing_auction_time, price, markup, delay_period';
            }
            $period_info = $this->model::where('id', $params['period'])
                ->field($field)
                ->find();
            if ($period_info['onsale_review_status'] != 3) {
                return serviceReturn(false, $period_info, '审核未通过');
            }

            // 商品管理 - 商品上架时，如果采购没有对应运营，则根据配置自动获取，如未配置则提示：采购没有对应运营！
            //https://devops.aliyun.com/projex/project/8ab3146db7bf4a79876e3a5333/task#viewIdentifier=1945bcda00c0cf935ebdd8a5&openWorkitemIdentifier=9cd7498f43536d5ca23d4bcf37
            if (empty($period_info['operation_review_id']) || $period_info['operation_review_id'] == 0) {
                $operate_info = Db::name('buyer_related')->alias('t1')
                    ->join('operate_related t2', 't1.operation_review_id=t2.operation_review_id')
                    ->where('t1.delete_time', null)
                    ->where('t2.delete_time', null)
                    ->where('t1.buyer_id', $period_info['buyer_id'])
                    ->field('t2.operation_review_id,t2.operation_review_name')
                    ->find();
                if (empty($operate_info)) {
                    return serviceReturn(false, $period_info, '采购没有对应运营！');

                } else {
                    $up_data['operation_review_name'] = $operate_info['operation_review_name'];
                    $up_data['operation_review_id'] = $operate_info['operation_review_id'];
                }
            }

            //二次确认上架时需要保证所有绑定的简码仓库一致
            $period_product = PeriodsProductInventory::where('period', $params['period'])
                ->where('is_use_comment', 0)
                ->order('id', 'desc')
                ->column('id,period,short_code,erp_id,inventory,is_use_comment');
            $inv_list = array_values(array_unique(array_column($period_product, 'erp_id')));
            if (count($inv_list) != 1) {
                return serviceReturn(false, $period_info, '所有绑定的简码仓库必须一致');
            }
            //二次上架时判断【微醺、一花一世界】主体不能绑定034代发仓:https://devops.aliyun.com/projex/project/8ab3146db7bf4a79876e3a5333/task#viewIdentifier=7f5f68b23b9da320b7b70a1f3b&openWorkitemIdentifier=6cae31104a99f26be688603a61
            if (!empty($period_info['payee_merchant_id']) && in_array(intval($period_info['payee_merchant_id']), [5, 10])) {
                if (in_array('034', $inv_list) || in_array('34', $inv_list)) {
                    return serviceReturn(false, $period_info, '微醺\一花一世界主体不能绑定034代发仓');
                }
            }

            //所有【烈酒\白酒】全部改为【不支持冷链】
            if (!empty($period_info['is_cold_chain']) || !empty($period_info['is_cold_free_shipping'])) {
                if (!empty(is_baijueLiejiu($params['period']))) {
                    return serviceReturn(false, $period_info, '烈酒\白酒不支持冷链');
                }
            }

            $vh_supplier = Db::table('vh_wiki.vh_supplier')
                ->where('id', $period_info['supplier_id'] ?? 0)
                ->field('contract_end,corp')
                ->find();

            if ($period_info['import_type'] == 1) {
                $contract_end = $vh_supplier['contract_end'] ?? null;
                if ($contract_end != null) {
                    if (substr($contract_end, 0, 4) != '1970') {
                        if (time() >= strtotime($contract_end)) {
                            //如果到期了需要判断售卖的简码在萌牙是否有库存，如果所有存货都有库存才能上架。不然不能上架。
                            $inv_short_codes = Db::name('periods_product_inventory')
                                ->where('period', $period_info['id'])
                                ->where('is_use_comment', 0)
                                ->column('warehouse_id', 'short_code');
                            $wms_stock       = \Curl::goodsGetFictitiousCount(['short_code' => array_keys($inv_short_codes)]);
                            foreach ($inv_short_codes as $inv_scode => $inv_wh_id) {
                                $inv_sc_wms = $wms_stock[$inv_scode] ?? [];
                                if (empty($inv_sc_wms)) return serviceReturn(false, $period_info, '供应商 ' . ($period_info['supplier'] ?? '') . ' 合同已到期且' . $inv_scode . '萌芽无库存,不能上架!');
                                $inv_sc_wms = array_column($inv_sc_wms, 'goods_count', 'fictitious_id');
                                if (empty($inv_sc_wms[$inv_wh_id])) return serviceReturn(false, $period_info, '供应商 ' . ($period_info['supplier'] ?? '') . ' 合同已到期且' . $inv_scode . '萌芽无库存,不能上架!');
                            }
                        }
                    }
                }
            }
            
            if ($this->type < 4 && !empty($period_info['payee_merchant_id'])) {
                $virtual_warehouse = \think\facade\Db::name('virtual_warehouse')->column('company_ids', 'erp_id');

                foreach ($inv_list as $inv_erp_id) {
                    if (empty($inv_erp_id)) return serviceReturn(false, $period_info, '简码未绑定发货仓');
                    if (!in_array($period_info['payee_merchant_id'], explode(',', $virtual_warehouse[$inv_erp_id] ?? ''))) {
                        return serviceReturn(false, $period_info, '收款公司 与 产品发货仓库不匹配!');
                    }
                }

                // 商品二次确认上架时需要再次判断期数绑定的主体是否和磐石的供应商主体是否一致
                $vh_corp = payeeMerchantIdCodeExchange($period_info['payee_merchant_id']);
                $supplier_corp = explode(',', $vh_supplier['corp'] ?? '');
                if ($this->type != 2 && !in_array($vh_corp, $supplier_corp)) {
                    return serviceReturn(false, $period_info, '供应商主体已变更，请前往磐石核实');
                }

            }

            if (($params['periods_type'] == 2)) {
                $packages = Es::name(Es::PERIODS_PACKAGE)->where([['period_id', '==', $period_info['id']], ['is_hidden', '==', 0]])->select()->toArray();
                $pids     = [];
                foreach ($packages as $package) {
                    $associated_products = json_decode($package['associated_products'], true);
                    foreach ($associated_products as $associated_product) {
                        $pids[] = $associated_product['product_id'];
                        if (!is_array($associated_product['product_id']) && !isset($up_data['is_leftover'])) {
                            $product = (new ServiceOther())->getPackageProductInfo((int)$val['period_id'], (int)$associated_product['product_id'], 'inventory');
                            if ($product['inventory'] <= 2) {
                                $up_data['is_leftover'] = 1;
                            }
                        }
                    }
                }
                $short_codes  = Db::connect('wiki')->name('products')->where('id', 'in', $pids)->column('short_code');
                if($period_info['is_channel'] == 0){
                $sales_period = Es::name(Es::PERIODS)->where([
                    ['short_code', 'in', $short_codes],
                    ['periods_type', 'in', [2]],
                    ['is_channel', '==', 0],
                    ['onsale_status', 'in', [1, 2]],
                    ['id', '<>', $params['period']],
                ])->find();
                if ($sales_period) {
                    return serviceReturn(false, $period_info, '相同的跨境商品不能同时在售!');
                }
                }
            }

            // 非渠道销售不能存在同产品同时售卖，判断依据是【商品售卖区间】开始时间-下架时间。不能存在相同存货
            if (empty($period_info['is_channel'])) {
                $res = $this->VerifySimultaneousSales($period_info['id'], strtotime($period_info['sell_time']), strtotime($period_info['sold_out_time']), [], 2);
                if ($res !== true) {
                    return serviceReturn(false, $period_info, $res);
                }
            }

            // 验证上架时参数
            if ($this->type == 0 or $this->type == 3) {
                $ver = self::verifyOnSaleParam($period_info);
                if ($ver['status'] == 0) {
                    return serviceReturn(false, $period_info, $ver['msg']);
                }
            }
            // 跨境验证上架简码库存
            if ($this->type == 2) {
                $inv_info = $period_product[0];
                if ($inv_info && (($inv_info['is_use_comment'] ?? 0) != 1)) {
                    $o_u = env('ITEM.ORDERS_URL') . '/orders/v3/cross/verifyCrossStock';
                    $o_d['goods_barcode'] = $inv_info['short_code'];
                    $o_d['warehouse_code'] = $inv_info['erp_id'];
                    $o_d['inventory_nums'] = $inv_info['inventory'];
                    $o_d['period'] = $inv_info['period'];
                    $o_re = post_url($o_u, $o_d);
                    if ($o_re) {
                        $re_j = json_decode($o_re, true);
                        if ($re_j['error_code'] != 0) {
                            return serviceReturn(false, $re_j, '跨境简码库存验证失败：' . $re_j['error_msg']);
                        }
                    }
                }

            }
            // 生成json文件
            $es_info = $this->create_period_json((int)$params['period'], $this->type, 1,-1);
            // 刷新CDN
            self::CDNrefreshObject((int)$params['period']);

//            $up_data['onsale_time'] = time();
            $up_data['onsale_status'] = 0;
            $up_data['onsale_verify_status'] = 1;

            if (strtotime($period_info['sell_time']) < time()) {
                $up_data['onsale_status'] = 2;
            }
            // 检查上架及开售时间
            if (!empty($period_info)) {
                // 上架时间
                if (strtotime($period_info['onsale_time']) > time()) {
                    if (strtotime($period_info['onsale_time']) == strtotime($period_info['sell_time'])) {
                        $this->task($period_info['id'], strtotime($period_info['onsale_time']), 1, 2);
                    } else {
                        $this->task($period_info['id'], strtotime($period_info['onsale_time']), 0, 1);
                    }
                }
                // 开售时间
                if (strtotime($period_info['sell_time']) > time()) {
                    $this->task($period_info['id'], strtotime($period_info['sell_time']), 1, 2);
                }
                // 下架时间
                if (strtotime($period_info['sold_out_time']) > time()) {
                    if ($this->type != 1) {
                        $this->task($period_info['id'], strtotime($period_info['sold_out_time']),
                            2, 3);
                    }
                }
                // 拍卖
                if ($this->type == 11) {
                    // 截拍时间
                    if (strtotime($period_info['closing_auction_time']) > time()) {
                        $this->task($period_info['id'],
                            strtotime($period_info['closing_auction_time']),
                            5,
                            4);
                    }
                    // 开始前 30 分钟推送任务
                    $before = strtotime($period_info['sell_time']) - 1800;
                    if ($before > time()) {
                        $this->task($period_info['id'],
                            $before,
                            6,
                            2);
                    }
                    // 截止拍卖前 30 分钟推送
                    $end_before = strtotime($period_info['closing_auction_time']) - 1800;
                    if ($before > time()) {
                        $this->task($period_info['id'],
                            $end_before,
                            8,
                            2);
                    }
                    // 写入 redis 时间
                    $auction_ser = new Auction();
                    $set_key = ["auction_{$params['period']}_start_time" => strtotime($period_info['onsale_time']),
                        "auction_{$params['period']}_end_time" => strtotime($period_info['closing_auction_time']),
                        "auction_{$params['period']}_bid_price" => $period_info['price'],
                        "auction_{$params['period']}_min_bid_price" => $period_info['markup'],
                        "auction_{$params['period']}_delay_time" => $period_info['delay_period'],
                        "auction_{$params['period']}_strike_time" => 120,
                    ];
                    $auction_ser->setAuctionRedis($set_key);
                    $url = env('ITEM.AUCTION_SERVER_URL') . '/auction/v3/notice?id=' . $period_info['id'] .
                        '&notice=' . 'start';
                    get_url($url);
                }
            }

            // 发送上架钉钉推送，兔头优惠券暂不推送
            if ($this->type != 5) {
                $this->onSaleDingdingPush((int)$params['period']);
            }

        }
        // 商品下架
        if ($params['onsale_status'] == 3) {
            // 删除该商品产品记录
            PeriodsOffsaleProductRecord::where('period', $params['period'])
                ->delete();
            $up_data['sold_out_time'] = time();
            //
            if ($this->type < 5) {
                $period_info = $this->model::where('id', $params['period'])
                    ->field('id,sellout_sold_out')
                    ->find();
                if (isset($period_info['sellout_sold_out'])) {
                    if ($period_info['sellout_sold_out'] == 1) {
                        return serviceReturn(false, [], '此期数已设置售完不下架，请先取消设置');
                    }
                }
            }
            // 查看是否有马甲
            $vest_id = PeriodsVest::where(['period_id' => $params['period'], 'status' => 0])->column('id');
            if (!empty($vest_id)) {
                // 马甲结束执行
                $url = env('ITEM.GOODS_VEST_URL') . '/vest/v3/vest/stopVest';
                $vest_data['vest_id'] = implode(',', $vest_id);
                $vest_headers[] = 'vinehoo-uid:' . $params['user_id'];
                post_url($url, $vest_data, $vest_headers);
            }
            $up_data['onsale_status'] = 3;
            // 手动下架，取消未售完自动延期设置
            $up_data['is_postpone'] = 0;

            if (!empty($params['remark'])) {
                // 手动下架记录备注
                $this->createRemark([
                    'period' => $params['period'],
                    'periods_type' => $this->type,
                    'remark' => '手动下架：' . $params['remark'],
                    'operator' => $params['user_id'] ?? 0,
                    'operator_name' => $params['user_name'] ?? '',
                    'created_time' => time(),
                ]);
            }
            
            // 拍卖
            if ($this->type == 11) {
                // 拍卖删除拍卖 redis 记录
                $auction_ser = new Auction();
                $set_key = ["auction_{$params['period']}_start_time" => 0,
                    "auction_{$params['period']}_end_time" => 0,
                    "auction_{$params['period']}_bid_price" => 0,
                    "auction_{$params['period']}_min_bid_price" => 0,
                    "auction_{$params['period']}_delay_time" => 0,
                    "auction_{$params['period']}_strike_time" => 0,
                ];
                $auction_ser->delAuctionRedis($set_key);
            }
            $url = env('ITEM.AUCTION_SERVER_URL') . '/auction/v3/notice?id=' . $params['period'] . '&notice=' . 'end';
            get_url($url);

            //秒发 手动下架商家期数同步下架
//            if ($this->type == 1) {
//                $m_period_ids = PeriodsSecondMerchants::where('join_period_id', $params['period'])->column('id'); //还拥有其他可售套餐的期数ID
//                $m_up_data    = [
//                    'onsale_status'   => 3,
//                    'off_sell_remark' => '关联酒云期数同步下架',
//                    'off_sell_type'   => 5,
//                    'off_sell_time'   => time(),
//                ];
//                (new \app\service\SecondMerchants())->batchSoldOut([
//                    "periods" => $m_period_ids,
//                    "data"    => $m_up_data,
//                ]); //批量下架
//            }
        }

        if ($params['onsale_status'] == 2) {
            //非渠道【二次确认】上架时，需要按照期数仓库查询萌牙库存，如果有库存需要设置订货量。
            if ($period_info['is_channel'] == 0) {
                $this->confirmOnSaleSetPurchase($params);
            }

            //卡片
            $card_id = $params['marketing_section']['card'] ?? [];
            //卡片筛选项
            $card_filter = $params['marketing_section']['card_filter'] ?? [];
            //栏目
            $column_id = $params['marketing_section']['column'] ?? [];
            //栏目筛选项
            $column_filter = $params['marketing_section']['column_filter'] ?? [];
            //标题
            $title = $params['marketing_section']['title'] ?? '';
            if (!empty($card_id)) {
                //商品需要加入到【卡片】，且卡片类型不为【秒发】时，必填【主标题】
                $not_mf = Db::table('vh_marketing.vh_card')
                    ->where([
                        ['id', 'in', $card_id],
                        ['channel', '<>', 2]
                    ])->value('id');
                if (!empty($not_mf) && empty($title)) {
                    return serviceReturn(false, [], '勾选非【秒发】卡片时标题不能为空。');
                }
            }
            //自动添加期数到营销版块
            AutomaticallyAddPeriod($params['period'], $this->type, $card_id, $column_id, $title, $card_filter, $column_filter);
        }

        $result = $this->model::where('id', $params['period'])->update($up_data);

        // 添加状态操作日志
        $scr_data['type'] = 3;
        $scr_data['status_type'] = $params['onsale_status'];
        $scr_data['status_name'] = $this->onsale_status[$params['onsale_status']];
        $scr_data['operator_id'] = $params['user_id'] ?? 0;
        $scr_data['operator_name'] = $params['user_name'] ?? 0;
        $scr_data['period'] = $params['period'];
        $scr_data['period'] = $this->type;
        $scr_data['created_time'] = date('Y-m-d H:i:s', time());
        PeriodsStatusChangeRecord::create($scr_data);
        return serviceReturn(true, $result, '操作成功');
    }


    /**
     * 拍卖商品验证
     * @param object $period_info
     * @return array
     */
    static function verifyOnSaleParam(object $period_info): array
    {
        if ($period_info['supplier_id'] == '' || $period_info['supplier'] == '') {
            return ['status' => 0, 'msg' => '供应商不能为空'];
        }
        if ($period_info['payee_merchant_id'] == '' || $period_info['payee_merchant_name'] == '') {
            return ['status' => 0, 'msg' => '收款商户不能为空'];
        }
        if ($period_info['onsale_time'] == '' || $period_info['sold_out_time'] == '') {
            return ['status' => 0, 'msg' => '上下架时间不能为空'];
        }
        if ($period_info['sell_time'] == '') {
            return ['status' => 0, 'msg' => '开售时间不能为空'];
        }
        if ($period_info['predict_shipment_time'] == '') {
            return ['status' => 0, 'msg' => '预计发货时间不能为空'];
        }
        return ['status' => 1, 'msg' => '验证通过'];
    }

    /**
     * 期数上下架
     *
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function vmallOffSale(array $params)
    {
        // 更新参数
        $up_data = [];
        // 商品上架
        if ($params['onsale_status'] == 2) {
            // 验证是否存在可售套餐
            if ($this->type < 5) {
                $package_ser = new Package($this->type);
                $package_list = $package_ser->packageList(['period' => $params['period']]);
                if (count($package_list) < 1) {
                    return serviceReturn(false, $package_list, '未查询到可用套餐');
                }
            }

            // 验证是否通过审核
            $period_info = $this->model::where('id', $params['period'])
                ->field('id,onsale_review_status,onsale_time,sell_time,sold_out_time')
                ->find();
            if ($period_info['onsale_review_status'] != 3) {
                return serviceReturn(false, $period_info, '审核未通过');
            }
            // 生成json文件
            $this->create_period_json((int)$params['period'], $this->type,0,-1);
            // 刷新CDN
            self::CDNrefreshObject((int)$params['period']);

//            $up_data['onsale_time'] = time();
            $up_data['onsale_status'] = 0;
            $up_data['onsale_verify_status'] = 1;
            if (strtotime($period_info['sell_time']) < time()) {
                $up_data['onsale_status'] = 2;
            }
            // 检查上架及开售时间
            if (!empty($period_info)) {
                // 上架时间
                if (strtotime($period_info['onsale_time']) > time()) {
                    if (strtotime($period_info['onsale_time']) == strtotime($period_info['sell_time'])) {
                        $this->task($period_info['id'], strtotime($period_info['onsale_time']), 1, 2);
                    } else {
                        $this->task($period_info['id'], strtotime($period_info['onsale_time']), 0, 1);
                    }
                }
                // 开售时间
                if (strtotime($period_info['sell_time']) > time()) {
                    $this->task($period_info['id'], strtotime($period_info['sell_time']), 1, 2);
                }
                // 下架时间
                if (strtotime($period_info['sold_out_time']) > time()) {
                    if ($this->type != 1) {
                        $this->task($period_info['id'], strtotime($period_info['sold_out_time']), 2, 3);
                    }
                }
            }
            // 发送上架钉钉推送，兔头优惠券暂不推送
            if ($this->type != 5) {
                $this->onSaleDingdingPush((int)$params['period']);
            }
        }
        // 商品下架
        if ($params['onsale_status'] == 3) {
            // 删除该商品产品记录
            PeriodsOffsaleProductRecord::where('period', $params['period'])
                ->delete();
//            $up_data['sold_out_time'] = time();
            //
            if ($this->type < 5) {
                $period_info = $this->model::where('id', $params['period'])
                    ->field('id,sellout_sold_out')
                    ->find();
                if (isset($period_info['sellout_sold_out'])) {
                    if ($period_info['sellout_sold_out'] == 1) {
                        return serviceReturn(false, [], '此期数已设置售完不下架，请先取消设置');
                    }
                }
            }
            // 查看是否有马甲
            $vest_id = PeriodsVest::where(['period_id' => $params['period'], 'status' => 0])->column('id');
            if (!empty($vest_id)) {
                // 马甲结束执行
                $url = env('ITEM.GOODS_VEST_URL') . '/vest/v3/vest/stopVest';
                $vest_data['vest_id'] = implode(',', $vest_id);
                $vest_headers[] = 'vinehoo-uid:' . $params['user_id'];
                post_url($url, $vest_data, $vest_headers);
            }
            $up_data['onsale_status'] = 3;
            // 下架说明
            if ($this->type == 9) {
                $up_data['off_sell_type'] = 0;
                $up_data['off_sell_time'] = time();
                $up_data['off_sell_remark'] = $params['off_sell_remark'] ?? '';
                if ($up_data['off_sell_remark']) {
                    $up_data['off_sell_type'] = 1;
                }
            }
        }

        $result = $this->model::where('id', $params['period'])->update($up_data);

        // 添加状态操作日志
        $scr_data['type'] = 3;
        $scr_data['status_type'] = $params['onsale_status'];
        $scr_data['status_name'] = $this->onsale_status[$params['onsale_status']];
        $scr_data['operator_id'] = $params['user_id'] ?? 0;
        $scr_data['operator_name'] = $params['user_name'] ?? 0;
        $scr_data['period'] = $params['period'];
        $scr_data['period'] = $this->type;
        $scr_data['created_time'] = date('Y-m-d H:i:s', time());
        PeriodsStatusChangeRecord::create($scr_data);
        return serviceReturn(true, $result, '操作成功');
    }

    /**
     * 生成 json 文件
     *
     * @param int $period
     * @param int $periods_type
     * @return bool|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function create_period_json(int $period, int $periods_type = 0, $req_type = 0, $is_new = 0)
    {
        $period_fields = 'id,title,brief,banner_img,product_img,is_hidden_price,price,market_price,quota_rule,
        sold_out_time,predict_shipment_time,onsale_time,sell_time,is_support_ts,onsale_status,video,video_cover,operation_review_id,product_id';
        if ($periods_type == 0) {
            $period_fields .= ',marketing_attribute';
        }
        if ($periods_type == 4) {
            $period_fields .= ',rabbit';
        }
        if ($periods_type == 5) {
            $period_fields = '*';
        }
        // 商品详细信息 mysql
//        $data = $this->model::where('id', $period)
//            ->field($period_fields)
//            ->find();
        // 商品信息 ES
//        $data = esGetOne($period, 'vinehoo.periods');
        $es_ser = new ElasticSearch();
        $data = $es_ser->getPeriodOne($period);
        if ($period == 155135) {
            $data['product_id'] = trim(str_replace('113596', '', $data['product_id']), ',');
        }

        if (empty($data)) {
            $icl_data['function_name'] = 'create_period_json';
            $icl_data['model'] = 'ES vinehoo.periods';
            $icl_data['method'] = 'get';
            $icl_data['url'] = $period;
            $icl_data['params'] = $period;
            $icl_data['response'] = json_encode($data);
            $icl_data['remark'] = '获取ES期数信息失败，会造成无法生成期数 json 文件';
            $icl_data['create_time'] = date('Y-m-d H:i:s');
            InterfaceCallLog::create($icl_data);
            return false;
        }
        $mysql_period = $this->getOne($period, 'detail');
        $data['detail'] = $mysql_period['detail'] ?? '';
        if ($data['detail'] != '') {
            if (strpos($data['detail'], 'img.wine-talk.cn')) {
                $data['detail'] = str_replace('img.wine-talk.cn', 'images.vinehoo.com', $data['detail']);
            }
            if (strpos($data['detail'], 'winetalk.oss-cn-qingdao.aliyuncs.com')) {
                $data['detail'] = str_replace(
                    'winetalk.oss-cn-qingdao.aliyuncs.com',
                    'images.vinehoo.com',
                    $data['detail']
                );
            }
            if (strpos($data['detail'], 'img.wine-talk.cn')) {
                $data['detail'] = str_replace(
                    'img.wine-talk.cn',
                    'images.vinehoo.com',
                    $data['detail']
                );
            }
//            if ($periods_type == 2) {
//                $data['detail'] .= '<p style="margin-top: 10px; display: flex; justify-content: center" >
//					    <img src="https://images.vinehoo.com/data/ueditor/image/20200203/1580723156312243.jpg" alt="">
//				</p>';
//            }
            if ($periods_type == 1) {
                // 起泡酒加上注意事项图片
                if (!empty($data['product_category'])) {
                    $is_ = false;
                    foreach ($data['product_category'] as $v) {
                        if (strpos(strval($v), '起泡酒') !== false) {
                            $is_ = true;
                        }
                    }
                    if ($is_) {
                        $data['detail'] .= '<p><strong><img style="display: block; margin-left: auto; margin-right: auto; 
                        margin-top: 10px;" src="https://images.vinehoo.com/mall/detail/sparking_attention.jpeg" 
                        alt="" width="750" /></strong></p>';
                    }
                }

                $data['detail'] .= '<p><strong><img style="display: block; margin-left: auto; margin-right: auto; 
                margin-top: 10px;" src="https://images.vinehoo.com/mall/detail/houseware_delivery.jpg" 
                alt="" width="750" /></strong></p>';
            }
            if ($periods_type == 2) {
                $data['detail'] .= '<p><strong><img style="display: block; margin-left: auto; margin-right: auto; 
                margin-top: 10px;" src="https://images.vinehoo.com/mall/detail/kj.jpg" 
                alt="" width="750" /></strong></p>';
            }
            if ($periods_type == 0) {
                if (isset($data['is_presell']) && $data['is_presell'] == 1) {
                    $data['detail'] .= '<p><strong><img style="display: block; margin-left: auto; margin-right: auto; 
            margin-top: 10px;" src="https://images.vinehoo.com/mall/detail/6E04D243-FEBD-4654-B95F-984F623D3405.png" 
            alt="" width="960" /></strong></p>';
                }
            }
        }
        $data['periods_type'] = $this->type;
        $data['quota_number'] = '';
        if (isset($data['quota_rule']) && $data['quota_rule'] != '') {
            $data['quota_number'] = json_decode($data['quota_rule'], true);
            $data['quota_number'] = $data['quota_number']['quota_number'] ?? '';
        }
//        $data['sold_out_time'] = strtotime($data['sold_out_time']);
        $data['shipment_time'] = date('Y-m-d', strtotime($data['predict_shipment_time']));
        $data['gold_area_img'] = 'https://images.vinehoo.com/vinehoomini/v3/goods_detail/goods_intro.png';
        $periods_types = [0 => 'goods_flash_purchase.png', 1 => 'tail_cargo.png', 2 => 'cross_border.png',
            3 => 'tail_cargo.png', 4 => '', 5 => '', 9 => '', 10 => '', 11 => ''];
        $data['channel_bg'] = 'https://images.vinehoo.com/vinehoomini/v3/goods_detail/' . $periods_types[$this->type];
        if ($periods_type == 0) {
            if (isset($data['marketing_attribute'])) {
                if (strpos($data['marketing_attribute'], '1') !== false) {
                    $data['channel_bg'] = 'https://images.vinehoo.com/vinehoomini/v3/goods_detail/spell_order.png';
                }
            }
            // 查询套餐价格
//            $group = $this->getGroupInfo([], ['period' => $period]);
//            if (!empty($group)) {
//                $data['group_price'] = $group['group_price'];
//            }
        }
        // 专题边框图
        $data['title_map'] = '';
        if (isset($data['special_activity_data'])) {
            if (isset($data['special_activity_data']['title_map']) &&
                $data['special_activity_data']['title_map'] != '') {
                if ($data['special_activity_data']['title_map']) {
                    $title_map = env('ALIURL') . $data['special_activity_data']['title_map'];
                    $data['special_activity_data']['title_map'] = $title_map;
                    $data['title_map'] = $title_map;
                }
                if (!empty($data['special_activity_data']['list_back'])) {
                    $list_back = env('ALIURL') . $data['special_activity_data']['list_back'];
                    $data['special_activity_data']['list_back'] = $list_back;
                    $data['list_map'] = $list_back;
                }
            }
        }
        // 题图
        if (isset($data['banner_img']) && $data['banner_img'] != '') {
            $data['banner_img'] = explode(",", $data['banner_img']);
            foreach ($data['banner_img'] as &$v) {
                $v = env('ALIURL') . $v;
            }
        }
        // 产品图
        if (isset($data['product_img']) && $data['product_img'] != '') {
            $data['product_img'] = explode(",", $data['product_img']);
            foreach ($data['product_img'] as &$v) {
                $v = env('ALIURL') . $v;
            }
        }
        // 秒发竖图
        if (isset($data['horizontal_img']) && $data['horizontal_img'] != '') {
            $data['horizontal_img'] = explode(",", $data['horizontal_img']);
            foreach ($data['horizontal_img'] as &$v) {
                $v = env('ALIURL') . $v;
            }
        }
        $data['service_policy_img'] = 'https://images.vinehoo.com/vinehoomini/v3/goods_detail/vh_ser_pol.png';
        // 视频封面图增加域名
        if (isset($data['video_cover']) && $data['video_cover'] != '') {
            $data['video_cover'] = env('ALIURL') . $data['video_cover'];
        }
        // 商品套餐信息
        $package_ser = new Package($this->type);
        $package_where = [
            'period_id' => $period,
            'is_hidden' => 0
        ];
        if ($periods_type != 5) {
            $package_list = $package_ser->getPackage($package_where);
            $data['package'] = $package_list;
            // 产品信息
            if (!empty($package_list)) {
                // 定金套餐处理
                $data = $this->DepositPackageHandle($data, $package_list);
            }

            $product_infos = PeriodsProductInventory::where([
                'period' => $period,
                // 'is_use_comment' => 0,
                // 'is_hidden_param' => 0,
                ])->column('product_id,is_use_comment,is_hidden_param', 'product_id');
            $product_ids = array_column($product_infos, 'product_id');
            if (empty($product_ids)) {
                $product_ids = array_values(array_filter(explode(',', $data['product_id'])));
            }
            // $product = json_decode($package_list[0]['associated_products'], true);
            $product_url = env('ITEM.WINE_WIKI_URL') . '/wiki/v3/productwikibodyinfo?ids=' . implode(',', $product_ids);
//            $fields = 'country,product_keywords_id,product_type,country,product_keywords_id,product_type,country_id,
//                       grape,producing_area_id';
//            $product_url .= '?fields='.$fields;
            $product_info = get_url($product_url);
            $data['is_lj'] = false;
            $data['product_info'] = [];
            if (!is_null(json_decode($product_info))) {
                $result_pro = json_decode($product_info, true);

                if ($result_pro['error_code'] == 0 && !empty($result_pro['data'])) {
                    // 烈酒类型
                    $lj_type= $this->getProductType(22);
                    $lj_type_ids = array_column($lj_type, 'id');
                    foreach ($result_pro['data'] as $val) {
                        // 判断烈酒
                        if (in_array($val['product_type'] ?? 0, $lj_type_ids)) {
                            $data['is_lj'] = true;
                        }
                        if (empty($product_infos)) {
                            $data['product_info'][] = $val;
                            continue;
                        }
                        
                        $product_info =  $product_infos[$val['id']] ?? [];
                        if (empty($product_info)) {
                            continue;
                        }
                        // 过滤仅用于评论、详情不显示参数
                        if ($product_info['is_use_comment'] == 0 && $product_info['is_hidden_param'] == 0 ) {
                            $data['product_info'][] = $val;
                        }
                    }
                    // 查询期数产品评价详细
                    if (!empty($data['product_info'])) {
                        // 按照id重新排序
                        $product_ids = explode(',', $data['product_id']);
                        $data['product_info'] = arrayByarraySort($data['product_info'], $product_ids);

                        $other_ser = new Other();
                        foreach ($data['product_info'] as $key => &$val) {
                            $product_ev = $other_ser->getProductEvaluate(['period' => $period,
                                'product_id' => $val['id']]);
//                                if (isset($val['alcohol']) && !empty($val['alcohol'])) {
//                                    $val['alcohol'] = $val['alcohol'] . '（仅供参考）';
//                                }
                            if (!empty($product_ev)) {
                                $val['tasting_notes'] = $product_ev['tasting_notes'];
                                $val['score'] = $product_ev['score'];
                                $val['prize'] = $product_ev['prize'];
                                $val['drinking_suggestion'] = $product_ev['drinking_suggestion'];
                            }
                            // $is_use_comment = PeriodsProductInventory::where(['period' => $period,
                            //     'product_id' => $val['id']])->value('is_use_comment');
                            // if ($is_use_comment == 1) {
                            //     unset($data['product_info'][$key]);
                            // }
                        }
                        unset($val);
                        $data['product_info'] = array_values($data['product_info']);
                    }

                } else {
                    $icl_data['function_name'] = 'create_period_json';
                    $icl_data['model'] = 'WINE_WIKI_URL';
                    $icl_data['method'] = 'get';
                    $icl_data['url'] = $product_url;
                    $icl_data['params'] = implode(",", $product_ids);
                    $icl_data['response'] = json_encode($result_pro);
                    $icl_data['remark'] = '获取产品信息失败，会造成无法生成期数 json 文件';
                    $icl_data['create_time'] = date('Y-m-d H:i:s');
                    InterfaceCallLog::create($icl_data);
                    return '获取产品信息失败，会造成无法生成期数 json 文件';
                }
            }
            
        }

        $re['error_code'] = 0;
        $re['data'] = $data;
        $re['error_msg'] = '';
        $json_data = json_encode($re);

        $onsale_status = $data['onsale_status'] ?? 0;
        // 上传到 oss
        $oss_re = $this->uploadFile($period, $json_data, $onsale_status, $is_new);

        if ($req_type == 1) {
            return $data;
        }

        return true;
    }

    /**
     * 创建订金套餐处理
     * @param array $data 期数信息
     * @param array $package_list 套餐信息
     * @param int $is_query 是否重新查询订单信息：0否，1是
     * @return false
     */
    public function DepositPackageHandle($data, $package_list, $is_query = 1) {
        if ($is_query == 1) {
            $periods = Db::name('periods_flash')
                ->field('onsale_status,sell_time,sold_out_time')
                ->where('id', $data['id'])
                ->findOrEmpty();
            if (!empty($periods)) {
                $data['onsale_status'] = $periods['onsale_status'];
                $data['sell_time'] = date('Y-m-d H:i:s', $periods['sell_time']);
                $data['sold_out_time'] = date('Y-m-d H:i:s', $periods['sold_out_time']);
            }
        }

        if (!empty($data['onsale_status']) && in_array($data['onsale_status'], [1, 2])) {
            $is_deposit = 0;
            $deposit_price = 0;
            foreach ($package_list as $v) {
                if (!empty($v['is_deposit']) && intval($v['is_deposit']) === 1 ) {
                    $is_deposit = 1;
                    empty($deposit_price) && $deposit_price = $v['deposit_price'];
                    if (empty($v['deposit_coupon_id'])) {
                        // 订单套餐创建优惠券
                        $res = $this->addDepositCoupon($data, $v);
                        if ($res !== true) {
                            Log::error("订金套餐创建优惠券失败，响应：{$res}，期数：".$data['id']);
                        }
                    }
                }
            }

            if ($is_deposit == 1) {
                if ($data['onsale_status'] == 1) {//待售
                    if (floatval($data['price']) != floatval($deposit_price)) {
                        //修改期数价格为定金金额
                        $r = $this->updateInfo($data['id'],[
                            'price' => $deposit_price,
                            'is_deposit' => 1,
                            'update_time' => time()
                        ]);
                        $r && $data['price'] = $deposit_price;
                    }
                    // 增加定金边框
                    $newBorder = env('ALIURL') . '/vinehoo/goods-images/203401/169882960624428YcGa2tf_F8EPAH4zf.png';
                    $data['special_activity_data'] = UpdatePeriodsBorder($data['id'], $data['special_activity_data'] ?? [], $newBorder);


                } else if ($data['onsale_status'] == 2) {//在售
                    $package_ser = new Package($data['periods_type']);
                    //商品开售取消定金标识
                    $package_ser->updatePackage(['is_deposit' => 0], ['period_id'  => $data['id'],'is_deposit' => 1]);
                    $update = [
                        'is_deposit' => 0,
                        'update_time' => time()
                    ];
                    //修改期数价格为套餐金额
                    if (isset($package_list[0]['price'])) {
                        if (floatval($data['price']) != floatval($package_list[0]['price'])) {
                            $update['price'] = $package_list[0]['price'];
                            $data['price'] = $package_list[0]['price'];
                        }
                    }
                    $this->updateInfo($data['id'], $update);
                }
            }
        }

        // 取消定金边框
        if ($data['onsale_status'] != 1) {
            $data['special_activity_data'] = UpdatePeriodsBorder($data['id'], $data['special_activity_data'] ?? []);
        }

        return $data;
    }

    /**
     * 生成订金优惠券
     * @param int $period_id 期数
     * @param array $period_info 期数信息
     * @return bool|string
     */
    public function generateDepositCoupon($period_id, $periods_type, $period_info = []) {
        if (empty($period_info)) {
            $period_info = $this->getOne($period_id, 'id,onsale_review_status,onsale_status,sell_time,sold_out_time,operation_review_id');
        }
        if (empty($period_info)) {
            return true;
        }
        if ($period_info['onsale_status'] != 1) {
            return true;
        }
        $package_ser = new Package($periods_type);
        $package_list = $package_ser->depositPackageList($period_id, 'id,deposit_coupon_value,deposit_coupon_threshold',[
            ['is_deposit', '=', 1],
            ['deposit_coupon_id', '=', 0],
        ]);
        if (empty($package_list)) {
            return true;
        }
        foreach ($package_list as $v) {
            #创建订金优惠券
            $result = $this->addDepositCoupon($period_info, $v);
            if ($result !== true) {
                return $result;
            }
        }

        return true;
    }
    /**
     * 创建订金优惠券
     * @param array $period_info 期数信息
     * @param array $package_info 套餐信息
     * @return false
     */
    public function addDepositCoupon($period_info, $package_info) {
        $uid = $period_info['operation_review_id'] ?? 0;
        if (empty($uid)) {
            return '操作人ID获取失败';
        }
        $package_ser = new Package($this->type);
        $url = env('ITEM.COUPON_URL').'/coupon/v3/coupon/add';
        $body = json_encode([
            'coupon_name' => '定金膨胀优惠券',
            'validity_days' => 0,
            'effected_time' => strval($period_info['sell_time']),
            'invalidate_time' => strval($period_info['sold_out_time']),
            'coupon_face_value' => intval($package_info['deposit_coupon_value']),
            'threshold_price' => intval($package_info['deposit_coupon_threshold']),
            'coupon_scope' => 1000,
            'coupon_type' => '1008',
            'admin_id' => strval($uid),
            'relation_id' => strval($period_info['id']),
            'remark' => '订金优惠券',
            'is_deposit' => 1,
        ]);
        $header = ['vinehoo-uid:'.$uid];
        $res = curlRequest($url, $body, $header);
        if (empty($res['data']['coupon_id'])) {
            $error_msg = $res['error_msg'] ?? '优惠券服务未响应';
            $content = '【' . $period_info['id'] . '】订金优惠券创建失败：' . $error_msg;
            SendWeChatRobot($content);

            return empty($res) ? '订金优惠券创建失败' : json_encode($res);
        }
        //保存优惠券ID
        $res = $package_ser->updatePackage([
            'id' => $package_info['id'],
            'deposit_coupon_id' => $res['data']['coupon_id'],
        ]);
        if (empty($res)) {
            return '订金优惠券保存失败';
        }

        return true;
    }

    /**
     * 新建时创建 json 文件
     * @param int $period
     * @param int $periods_type
     * @return false
     * @throws \OSS\Core\OssException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function createNewJsonFile(int $period, $periods_type = 0)
    {
        $period_fields = 'id,title,brief, detail, banner_img,product_img,is_hidden_price,price,market_price,quota_rule,
        sold_out_time,predict_shipment_time,onsale_time,sell_time,is_support_ts,onsale_status,video,video_cover';
        if ($periods_type == 0) {
            $period_fields .= ',marketing_attribute';
        }
        if ($periods_type == 4) {
            $period_fields .= ',rabbit';
        }
        if ($periods_type == 5) {
            $period_fields = '*';
        }
        // 商品详细信息 mysql
        $data = $this->model::where('id', $period)
            ->field($period_fields)
            ->find();
        // 商品信息 ES
//        $data = esGetOne($period, 'vinehoo.periods');
//        $es_ser = new ElasticSearch();
//        $data = $es_ser->getPeriodOne($period);
        if ($data['detail'] != '') {
            if (strpos($data['detail'], 'img.wine-talk.cn')) {
                $data['detail'] = str_replace('img.wine-talk.cn', 'images.vinehoo.com', $data['detail']);
            }
            if ($periods_type == 1) {
                // 起泡酒加上注意事项图片
                if (!empty($data['product_category'])) {
                    if (strpos(strval($data['product_category']), '起泡酒') !== false) {
                        $data['detail'] .= '<p><strong><img style="display: block; margin-left: auto; margin-right: auto; 
                        margin-top: 10px;" src="https://images.vinehoo.com/mall/detail/sparking_attention.jpeg" 
                        alt="" width="750" /></strong></p>';
                    }
                }

                $data['detail'] .= '<p><strong><img style="display: block; margin-left: auto; margin-right: auto; 
                margin-top: 10px;" src="https://images.vinehoo.com/mall/detail/houseware_delivery.jpg" 
                alt="" width="750" /></strong></p>';
            }
            if ($periods_type == 0) {
                if (isset($data['is_presell']) && $data['is_presell'] == 1) {
                    $data['detail'] .= '<p><strong><img style="display: block; margin-left: auto; margin-right: auto; 
            margin-top: 10px;" src="https://images.vinehoo.com/mall/detail/6E04D243-FEBD-4654-B95F-984F623D3405.png" 
            alt="" width="960" /></strong></p>';
                }
            }
        }
        $data['periods_type'] = $this->type;
        $data['quota_number'] = '';
        if (isset($data['quota_rule']) && $data['quota_rule'] != '') {
            $data['quota_number'] = json_decode($data['quota_rule'], true);
            $data['quota_number'] = $data['quota_number']['quota_number'] ?? '';
        }

//        $data['sold_out_time'] = strtotime($data['sold_out_time']);
        $data['shipment_time'] = date('Y-m-d', strtotime($data['predict_shipment_time']));
        $data['gold_area_img'] = 'https://images.vinehoo.com/vinehoomini/v3/goods_detail/goods_intro.png';
        $periods_types = [0 => 'goods_flash_purchase.png', 1 => 'tail_cargo.png', 2 => 'cross_border.png',
            3 => 'tail_cargo.png', 4 => '', 5 => '', 9 => '', 10 => ''];
        $data['channel_bg'] = 'https://images.vinehoo.com/vinehoomini/v3/goods_detail/' . $periods_types[$this->type];
        if ($periods_type == 0) {
            if (isset($data['marketing_attribute'])) {
                if (strpos($data['marketing_attribute'], '1') !== false) {
                    $data['channel_bg'] = 'https://images.vinehoo.com/vinehoomini/v3/goods_detail/spell_order.png';
                }
            }
        }
        $data['service_policy_img'] = 'https://images.vinehoo.com/vinehoomini/v3/goods_detail/vh_ser_pol.png';
        // 视频封面图增加域名
        if (isset($data['video_cover']) && $data['video_cover'] != '') {
//            $data['video_cover'] = env('ALIURL') . $data['video_cover'];
        }

        // 商品套餐信息
        $package_ser = new Package($this->type);
        $package_where = [
            'period_id' => $period,
            'is_hidden' => 0
        ];

        if ($periods_type != 5) {
            $package_list = $package_ser->getPackage($package_where);
            $data['package'] = $package_list;

            // 产品信息
            if (!empty($package_list)) {
                $product = json_decode($package_list[0]['associated_products'], true);
                $product_url = env('ITEM.WINE_WIKI_URL') . '/wiki/v3/productwikibodyinfo?ids=' . $data['product_id'];
//            $fields = 'country,product_keywords_id,product_type,country,product_keywords_id,product_type,country_id,
//                       grape,producing_area_id';
//            $product_url .= '?fields='.$fields;
                $product_info = get_url($product_url);
                if (!is_null(json_decode($product_info))) {
                    $result_pro = json_decode($product_info, true);
                    if ($result_pro['error_code'] == 0) {
                        $data['product_info'] = $result_pro['data'];
                        // 查询期数产品评价详细
                        if (!empty($data['product_info'])) {
                            $other_ser = new Other();
                            foreach ($data['product_info'] as &$val) {
                                $product_ev = $other_ser->getProductEvaluate(['period' => $period,
                                    'product_id' => $val['id']]);
//                                if (isset($val['alcohol']) && !empty($val['alcohol'])) {
//                                    $val['alcohol'] = $val['alcohol'] . '（仅供参考）';
//                                }
                                if (!empty($product_ev)) {
                                    $val['tasting_notes'] = $product_ev['tasting_notes'];
                                    $val['score'] = $product_ev['score'];
                                    $val['prize'] = $product_ev['prize'];
                                    $val['drinking_suggestion'] = $product_ev['drinking_suggestion'];
                                }
                            }
                            unset($val);
                        }

                    } else {
                        $icl_data['function_name'] = 'create_period_json';
                        $icl_data['model'] = 'WINE_WIKI_URL';
                        $icl_data['method'] = 'get';
                        $icl_data['url'] = $product_url;
                        $icl_data['params'] = implode(",", $product[0]['product_id']);
                        $icl_data['response'] = json_encode($result_pro);
                        $icl_data['remark'] = '获取产品信息失败，会造成无法生成期数 json 文件';
                        $icl_data['create_time'] = date('Y-m-d H:i:s');
                        InterfaceCallLog::create($icl_data);
                        return false;
                    }
                }
            }
        }
        $re['error_code'] = 0;
        $re['data'] = $data;
        $re['error_msg'] = '';
        $json_data = json_encode($re);
//        $path = App::getRuntimePath() . '/periods/';
//        $file = file_put_contents($path . $period, $json_data);
//        if ($file != false) {
//            // 上传到 oss
//            $this->uploadFile($period, $json_data);
//        }
        $onsale_status = $data['onsale_status'] ?? 0;
        $oss_re = $this->uploadFile($period, $json_data, $onsale_status, 1);
    }

    /**
     * oss上传图片
     *
     * @param int $period 期数
     * @param string $json_data 期数
     * @return array
     * @throws \OSS\Core\OssException
     */
    public function uploadFile(int $period, string $json_data, $onsale_status = 0, $is_new = 0): array
    {
        // 阿里云 oss 账号
        $accessKeyId = env('OSS.ACCESSKEYID');
        $accessKeySecret = env('OSS.ACCESSKEYSECRET');
        // oss 数据中心域名
        $endpoint = env('OSS.ENDPOINT');
        // oss 存储空间名称
        $bucket = env('OSS.BUCKET');
        // 上传到 oss 目录地址
        $object = 'vinehoo/client/commodities/periods/' . $period . '.json';
        // 本地文件路径
//        $filePath = App::getRuntimePath() . '/periods/' . $period;
        $ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint);
//        $result = $ossClient->uploadFile($bucket, $object, $filePath);
        $result = $ossClient->putObject($bucket, $object, $json_data);
        if (!$result) {
            return [];
        }
        // 刷新 CDN 文件
        if (in_array($is_new, [0, 1])) {
            if ($onsale_status == 2) {
                self::CDNrefreshObject($period, $is_new);
            }
        }
//        $refre = self::CDNrefreshObject($period);

        return $result;
    }

    /**
     * 检测 oss 文件是否存在
     * @param int $period
     * @return bool
     * @throws \OSS\Core\OssException
     */
    public function objectExist(int $period): bool
    {
        // 阿里云 oss 账号
        $accessKeyId = env('OSS.ACCESSKEYID');
        $accessKeySecret = env('OSS.ACCESSKEYSECRET');
        // oss 数据中心域名
        $endpoint = env('OSS.ENDPOINT');
        // oss 存储空间名称
        $bucket = env('OSS.BUCKET');
        // 上传到 oss 目录地址
        $object = 'vinehoo/client/commodities/periods/' . $period . '.json';
        // 本地文件路径
//        $filePath = App::getRuntimePath() . '/periods/' . $period;
        $ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint);

        return $ossClient->doesObjectExist($bucket, $object);
    }

    /**
     * 使用AK&SK初始化账号Client
     * @param string $accessKeyId
     * @param string $accessKeySecret
     * @return Cdn Client
     */
    public static function createClient()
    {
        $config = new Config([
            // AccessKey ID
            "accessKeyId" => 'LTAI5tA399hBWprfBqq9m2hj',
            // AccessKey Secret
            "accessKeySecret" => '******************************'
        ]);
        // 访问的域名
        $config->endpoint = 'cdn.aliyuncs.com';

        return new Cdn($config);
    }

    /**
     * @param int $period
     * @return void
     */
    public static function CDNrefreshObject(int $period, $is_new = 0)
    {
        if ($is_new != 1) {
            $cdn_refresh_key = "cdn_refresh_urls";
            $objectPath      = env('OSS.ALIURL') . '/vinehoo/client/commodities/periods/' . $period . '.json' . '?t=1&isJson=true&id=' . $period;

            $redis_config           = config('cache.stores.redis');
            // $redis_config['host'] = 'r-8vbyf9qn03iwmy7w5npd.redis.zhangbei.rds.aliyuncs.com';
            // $redis_config['password'] = 'NRDSYa5e6EWZuJ3d';
            $redis_config['select'] = 5;
            $conn                   = new \think\cache\driver\Redis($redis_config);

            $re = $conn->rpush($cdn_refresh_key, $objectPath);

            return boolval($re);
        } else {
            $client                     = self::createClient();
            $refreshObjectCachesRequest = new RefreshObjectCachesRequest([
//            'objectPath' => 'images.vinehoo.com'.'vinehoo/client/commodities/periods/' . $period . '.json'
                'objectPath' => env('OSS.ALIURL') . '/vinehoo/client/commodities/periods/' . $period . '.json' .
                    '?t=1&isJson=true&id=' . $period
            ]);
            $runtime                    = new RuntimeOptions([]);
            try {
                // 复制代码运行请自行打印 API 的返回值
                $result = $client->refreshObjectCachesWithOptions($refreshObjectCachesRequest, $runtime);
            } catch (\Exception $error) {
                if (!($error instanceof TeaError)) {
                    $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
                }
                // 如有需要，请打印 error
                Utils::assertAsString($error->message);
            }
        }
    }

    /**
     * 根据 id 获取期数某个字段值g
     * @param int $period 期数
     * @param string $value 字段
     * @return mixed
     */
    public function getPeriodsValue(int $period, string $value)
    {
        return $this->model::where('id', $period)->value($value);
    }

    /**
     * 查询商品是否以上过架
     *
     * @param int $period 期数
     * @param int $periods_type 期数频道
     * @return mixed
     */
    public function getOnSaleRecord(int $period, int $periods_type): ?int
    {
        return PeriodsOffsaleProductRecord::where(['period' => $period, 'periods_type' => $periods_type])
            ->value('period');
    }

    /**
     * 删除期数
     * @param int $period
     * @return PeriodsCross|PeriodsFlash|PeriodsLeftover|PeriodsSecond
     */
    public function delete(int $period)
    {
        return $this->model::where('id', $period)
            ->update([
                'is_delete' => 1,
            ]);
    }

    /**
     * 更新商品信息
     * @param int $period
     * @param array $data
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update(int $period, array $data)
    {
        $field = 'onsale_time,sell_time,sold_out_time,onsale_review_status,onsale_verify_status,is_channel, onsale_status';
        if ($this->type == 0 || $this->type == 2) {
            $field .= ' ,is_presell, predict_shipment_time';
        }
        if ($this->type == 0) {
            $field .= ' ,is_seckill';
        }
        if ($this->type == 0 || $this->type == 3) {
            $field .= ' ,payee_merchant_id';
        }
        $period_info = $this->getOne($period, $field);
        if (empty($period_info)) {
            return '商品信息查询失败';
        }

        // 渠道判断是否设置专题活动价格
        if (!empty($data['is_channel']) && $data['is_channel'] == 1) {
            $exist = Db::table('vh_marketing.vh_special_activity_package')
                ->where('periods', $period)
                ->value('id');
            if (!empty($exist)) {
                return '无法设置为渠道商品，因为该期数已配置专题活动的自定义价格。';
            }
        }

        //开售时间（预约短信提醒）
        $sell_timestamp = strtotime($period_info['sell_time']);
        if (isset($data['sell_time']) && $data['sell_time'] != $sell_timestamp) {
            //获取定时任务
            $is_exists = PeriodsTaskLog::where([
                'period' => $period,
                'type' => 11,
                'is_exec' => 0
            ])->value('task_id');

            //判断是否满足条件，满足就直接新增或修改时间, 不满足就删除task
            if(($data['sell_time'] >= time()+3600) && ($data['sell_time'] - $data['onsale_time']) >= 86400){
                $this->task($period,$data['sell_time']-600,11);
            }else{
                if(!empty($is_exists)){
                    $task_url = env('item.SLS_URL') . '/services/v3/task/delete';
                    $task_data['task_id'] = $is_exists;
                    $task_data = json_encode($task_data);
                    $url_header[] = 'vinehoo-client: tp6-commodities';
                    $url_header[] = 'vinehoo-client-version: v3';
                    $url_header[] = 'Content-Type: application/json;charset=utf-8';
                    $url_header[] = 'Content-Length: ' . strlen($task_data);
                    $t1 = post_url($task_url, $task_data, $url_header);
                    // 删除任务日志
                    PeriodsTaskLog::where('task_id', $is_exists)->delete();
                }
            }
        }

        // 初始值设置
        if (isset($data['limit_number']) && !empty($data['limit_number'])) {
            $data['invariant_number'] = $data['limit_number'];
        }
        // 确认上架后不可更改初始值
        if ($period_info['onsale_verify_status'] == 1) {
            unset($data['invariant_number'], $data['limit_number']);
        }
        $data['onsale_status'] = $period_info['onsale_status'];
        // 如果下架时间大于当前时间，此期数直接上架
        if (isset($data['sold_out_time']) && $period_info['onsale_verify_status'] == 1) {
//            if ($data['sold_out_time'] > time() && $period_info['onsale_review_status'] == 3) {
//                $data['onsale_status'] = 2;
//            }

            if (($this->type == 2) && isset($data['is_channel']) && $data['is_channel'] == 0 && ($period_info['onsale_status'] >= 2)) {
                $packages = Es::name(Es::PERIODS_PACKAGE)->where([['period_id', '==', $period], ['is_hidden', '==', 0]])->select()->toArray();
                $pids     = [];
                foreach ($packages as $package) {
                    $associated_products = json_decode($package['associated_products'], true);
                    foreach ($associated_products as $associated_product) {
                        $pids[] = $associated_product['product_id'];
                    }
                }
                $short_codes  = Db::connect('wiki')->name('products')->where('id', 'in', $pids)->column('short_code');
                $sales_period = Es::name(Es::PERIODS)->where([
                    ['id', '<>', $period],
                    ['short_code', 'in', $short_codes],
                    ['periods_type', 'in', [2]],
                    ['is_channel', '==', 0],
                    ['onsale_status', 'in', [1, 2]],
                ])->field('id')->find();

                if (!empty($sales_period)) {
                    return '相同的跨境商品不能同时在售!';
                }
            }

            // 上架时间
            if (isset($data['onsale_time']) && $data['onsale_time'] > time()) {
                if ($data['onsale_time'] == $data['sell_time']) {
                    $this->task($period, $data['sell_time'], 1, 2);
                } else {
                    $this->task($period, $data['onsale_time'], 0, 1);
                }
                $data['onsale_status'] = 0;
            } elseif ($data['onsale_time'] < time()) {
                $data['onsale_status'] = 1;
            }
            // 开售时间
            if (isset($data['sell_time']) && $data['sell_time'] > time()) {
                $this->task($period, $data['sell_time'], 1, 2);
            } elseif ($data['sell_time'] < time()) {
                // 验证库存是否存在
                $periods_inventory = $this->getPeriodsInventory($period, $this->type);
                // 商家商品秒发库存验证 TODO...
                if ($this->type == 9) {
                    // TODO.... 调用接口验证库存
                    $periods_inventory = 1;
                }
                if ($periods_inventory) {
                    $data['onsale_status'] = 2;
                } else {
                    // 不存在库存直接下架
                    $data['onsale_status'] = 3;
                }
            }
            // 下架时间
            if (isset($data['sold_out_time']) && $data['sold_out_time'] > time()) {
                $this->task($period, $data['sold_out_time'], 2, 3);
            } elseif ($data['sold_out_time'] < time() && $this->type != 1) {
                // 超过下架时间自动下架，秒发除外
                if ($this->type != 9) {
                    $data['onsale_status'] = 3;
                }
            }
        }

        // 所有【烈酒\白酒】全部改为【不支持冷链】
        if (!empty($data['is_cold_chain']) || !empty($data['is_cold_free_shipping'])) {
            if (!empty(is_baijueLiejiu($period))) {
                return '烈酒\白酒不支持冷链';
            }
        }

        // 期数上架验证产品关单卫检
        if ($this->type < 5 && in_array($data['onsale_status'], [1, 2])) {
            $resu = $this->vCustomsOrderHealthInspect($period, 1);
            if ($resu !== true) {
                return $resu;
            }
        }

        // 跨境尾货
        if ($this->type != 2 && isset($data['is_leftover'])) {
            unset($data['is_leftover']);
        }
        // 上架判断初始值限量
        if ($data['onsale_status'] == 2) {
            // 判断已购限量
            $this->limitNumberInc($period);
        }
        // 刷新 CDN
        self::CDNrefreshObject(intval($period));
        //秒发 更新
        if ($this->type == 1) {
            $info = $this->model::where('id', $period)->field('id,is_support_reduction,is_support_coupon')->find();
            $is_support_reduction = $data['is_support_reduction'] ?? $info['is_support_reduction'];
            $is_support_coupon    = $data['is_support_coupon'] ?? $info['is_support_coupon'];
            if ((intval($info['is_support_reduction']) !== intval($is_support_reduction))
                || (intval($info['is_support_coupon']) !== intval($is_support_coupon))) {
                //同步更新
                $res = PeriodsSecondMerchants::where('join_period_id', 'in', $period)->update([
                    'is_support_reduction' => $is_support_reduction,
                    'is_support_coupon'    => $is_support_coupon,
                ]);
            }
        }

        $redis_config = config('cache.stores.redis');
        $redis_config['select'] = 0;
        $conn = new \think\cache\driver\Redis($redis_config);
        if (isset($data['is_channel']) && intval($data['is_channel']) === 1) {
            if (isset($data['is_enc_link']) && $data['is_enc_link'] == 1) {
                $conn->sadd('vinehoo.details.channel', intval($period));
            } else {
                // $conn->srem('vinehoo.details.channel', intval($period));
            }
        } else if (isset($data['is_channel']) && intval($data['is_channel']) === 0) {
            $conn->srem('vinehoo.details.channel', intval($period));
        }
        unset($data['is_enc_link']);

        if ($this->type == 0) {
            $is_seckill = isset($data['is_seckill']) ? $data['is_seckill'] : ($period_info['is_seckill'] ?? 0) ;
            // 秒杀期数写入redis
            $redis_config['select'] = 11;
            $conn                   = new \think\cache\driver\Redis($redis_config);
            if (isset($data['is_seckill'])) {
                if (!empty($data['is_seckill'])) {
                    $conn->sadd('vinehoo.periods.seckill', intval($period));
                } else {
                    $conn->srem('vinehoo.periods.seckill', intval($period));
                }
            }
            $vpsskey = "periods.seckill.saletime_{$period}";
            if ($is_seckill) {
                //判断是否订金
                if (Db::name('periods_flash_set')
                    ->where('period_id', $period)
                    ->where('is_hidden', 0)
                    ->where('is_deposit', 1)->count()) {
                    $sell_timestamp = strtotime($period_info['sell_time']);
                    if (!empty($data['sell_time'])) {
                        $sell_timestamp = $data['sell_time'];
                    }
                    $conn->set($vpsskey, $sell_timestamp);
                } else {
                    $conn->delete($vpsskey);
                }
            } else {
                $conn->delete($vpsskey);
            }
        }
        
        $data['update_time'] = time();
        $result = $this->model::where('id', $period)->inc('version')->update($data);
        // 查看是否预售
        if ($this->type == 0 || $this->type == 2) {
            if (isset($period_info['is_presell']) && $period_info['is_presell'] == 1) {
                // 推送发送邮件队列
                // 推送地址
                $queue_url = env('item.QUEUE_URL');
                // 请求头
                $url_header[] = 'vinehoo-client: tp6-commodities';
                $url_header[] = 'Content-Type: application/json;charset=utf-8';
                // 推送请求参数
                $push_data['exchange_name'] = 'commodities';
                $push_data['routing_key'] = 'buyer.sendemail';
                $d_json['period'] = $period;
                $d_json['periods_type'] = $this->type;
                $d_str = base64_encode(json_encode($d_json));
                $push_data['data'] = $d_str;
                $p_data = json_encode($push_data);
                $re = post_url($queue_url, $p_data, $url_header);
            }
        }
        // 闪购更新收款账户推钉钉
        if ($this->type == 0 && isset($data['payee_merchant_id'])) {
            if ((int)$period_info['payee_merchant_id'] !== (int)$data['payee_merchant_id']) {
                $this->updatePayeeMerchantDingdingPush($period);
            }
        }

        // 尾货更新收款账户推钉钉
        if ($this->type == 3 && isset($data['payee_merchant_id'])) {
            if ((int)$period_info['payee_merchant_id'] !== (int)$data['payee_merchant_id']) {
                $this->updatePayeeMerchantDingdingPush($period);
            }
        }
        return $result;
    }

    /**
     * 更新期数收款商户
     * @param array $params
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updatePayeeMerchant($params)
    {
        $period_info = $this->getOne($params['period'], 'id,supplier_id,onsale_status,onsale_review_status,payee_merchant_id');
        if (empty($period_info)) {
            return serviceReturn(false, [], '期数信息查询失败');
        }
        if (intval($period_info['payee_merchant_id']) === intval($params['payee_merchant_id'])) {
            return serviceReturn(true, [], '操作成功');
        }
        if (empty($period_info['supplier_id'])) {
            return serviceReturn(false, [], '请先选择供应商');
        }
        if (!empty($period_info['onsale_review_status']) && intval($period_info['onsale_review_status']) == 3) {
            return serviceReturn(false, [], '该期数已审核通过不允许修改收款商户');
        }

        // 验证供应商收款商户
        $res = $this->VerifySupplierReceiptMerchant($period_info['supplier_id'], $params['payee_merchant_id'], $params['periods_type']);
        if ($res !== true) {
            return serviceReturn(false, [], $res);
        }

        #启动事务
        Db::startTrans();
        try {
            $this->model::where('id', $params['period'])
                ->inc('version')
                ->update([
                    'payee_merchant_id'   => $params['payee_merchant_id'],
                    'payee_merchant_name' => $params['payee_merchant_name'],
                ]);
            // 更新收款账户推企微
            if (intval($period_info['payee_merchant_id']) !== intval($params['payee_merchant_id'])) {
                $this->updatePayeeMerchantDingdingPush($params['period']);
            }
            #提交事务
            Db::commit();
        } catch (\Exception $e) {
            #回滚事务
            Db::rollback();
            return serviceReturn(false, [], $e->getMessage());
        }

        return serviceReturn(true, [], '操作成功');
    }

    /**
     * 验证供应商收款商户
     * @param int $supplier_id
     * @param int $payee_merchant_id
     * @return bool
     */
    public function VerifySupplierReceiptMerchant($supplier_id, $payee_merchant_id, $periods_type)
    {
        // 秒发、跨境不验证
        if (in_array($periods_type, [1, 2])) {
            return true;
        }

        // 查询供应商信息
        $supplier = Db::Table('vh_wiki.vh_supplier')
            ->where('id', $supplier_id)
            ->field('id,corp')
            ->findOrEmpty();
        if (empty($supplier)) {
            return '供应商信息查询失败';
        }
        if (empty($supplier['corp'])) {
            return '先维护磐石【供应商管理】';
        }

        $corp_code = payeeMerchantIdCodeExchange($payee_merchant_id);

        $supplier_corp = explode(',',$supplier['corp']);
        if (!in_array($corp_code, $supplier_corp)) {
            return '只能选择供应商对应收款商户';
        }

        return true;
    }

    /**
     * 更新商家商品信息
     * @param int $period
     * @param array $data
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function vmallUpdate(int $period, array $data)
    {
        $field = 'onsale_time,sell_time,sold_out_time,onsale_review_status,onsale_verify_status, onsale_status';
        if ($this->type == 0 || $this->type == 2) {
            $field .= ' ,is_presell, predict_shipment_time';
        }
        if ($this->type == 9) {
            $field .= ' ,off_sell_type';
        }
        $period_info = $this->getOne($period, $field);
        if (empty($period_info)) {
            return 0;
        }

        // 初始值设置
        if (isset($data['limit_number']) && !empty($data['limit_number'])) {
            $data['invariant_number'] = $data['limit_number'];
        }
        // 确认上架后不可更改初始值
        if ($period_info['onsale_verify_status'] == 1) {
            unset($data['invariant_number'], $data['limit_number']);
        }
        $data['onsale_status'] = $period_info['onsale_status'];
        // 如果下架时间大于当前时间，此期数直接上架
        if ($period_info['onsale_verify_status'] == 1) {
            // 上架时间
            if (isset($data['onsale_time']) && $data['onsale_time'] > time()) {
                if ($data['onsale_time'] == $data['sell_time']) {
                    $this->task($period, $data['sell_time'], 1, 2);
                } else {
                    $this->task($period, $data['onsale_time'], 0, 1);
                }
                $data['onsale_status'] = 0;
            } elseif ($data['onsale_time'] < time()) {
                $data['onsale_status'] = 1;
            }
            // 开售时间
            if (isset($data['sell_time']) && $data['sell_time'] > time()) {
                $this->task($period, $data['sell_time'], 1, 2);
            } elseif ($data['sell_time'] <= time()) {
                // 验证库存是否存在
                $periods_inventory = $this->getPeriodsInventory($period, $this->type);
                // 商家商品秒发库存验证 TODO...
                if ($this->type == 9) {
                    // TODO.... 调用接口验证库存
                    $periods_inventory = 1;
                }
                if ($periods_inventory) {
                    $data['onsale_status'] = 2;
                } else {
                    // 不存在库存直接下架
                    $data['onsale_status'] = 3;
                }
            }
            // 下架时间
            if (isset($data['sold_out_time']) && $data['sold_out_time'] > time()) {
                $this->task($period, $data['sold_out_time'], 2, 3);
            } elseif ($data['sold_out_time'] < time() && $this->type != 1) {
                // 超过下架时间自动下架，秒发除外
                if ($this->type != 9) {
                    $data['onsale_status'] = 3;
                }
            }
        }
        // 跨境尾货
        if ($this->type != 2 && isset($data['is_leftover'])) {
            unset($data['is_leftover']);
        }
        // 上架判断初始值限量
        if ($data['onsale_status'] == 2) {
            // 如果是手动下架或系统下架，重新提交审核
            if ($this->type == 9) {
                if ($period_info['off_sell_type'] == '0' || $period_info['off_sell_type'] == '1') {
                    // 重新走提交流程
                    $data['onsale_status'] = 0;
                }
            }
            // 判断已购限量
            $this->limitNumberInc($period);
        }
        // 刷新 CDN
        self::CDNrefreshObject(intval($period));
        $result = $this->model::where('id', $period)->inc('version')->update($data);
        // 查看是否预售
        if ($this->type == 0 || $this->type == 2) {
            if (isset($period_info['is_presell']) && $period_info['is_presell'] == 1) {
                // 推送发送邮件队列
                // 推送地址
                $queue_url = env('item.QUEUE_URL');
                // 请求头
                $url_header[] = 'vinehoo-client: tp6-commodities';
                $url_header[] = 'Content-Type: application/json;charset=utf-8';
                // 推送请求参数
                $push_data['exchange_name'] = 'commodities';
                $push_data['routing_key'] = 'buyer.sendemail';
                $d_json['period'] = $period;
                $d_json['periods_type'] = $this->type;
                $d_str = base64_encode(json_encode($d_json));
                $push_data['data'] = $d_str;
                $p_data = json_encode($push_data);
                $re = post_url($queue_url, $p_data, $url_header);
            }
        }
        return $result;
    }

    /**
     *  验证是否存在库存
     * @param $period
     * @param $periods_type
     * @return int
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPeriodsInventory($period, $periods_type): int
    {
        $this->type = $periods_type;
        if ($periods_type == 5) {
            $period_info = $this->getOne(
                (int)$period,
                'onsale_status, inventory'
            );
            if ($period_info['inventory'] > 0) {
                return 1;
            } else {
                return 0;
            }
        }
        // 期数详细
        $period_info = $this->getOne(
            (int)$period,
            'onsale_status, onsale_verify_status, sold_out_time,sellout_sold_out,is_sold_out_lock, limit_number,
            purchased, vest_purchased, incremental, critical_value'
        );

        // 未进行二次上架确认不做任何操作
        if (empty($period_info['onsale_verify_status']) || $period_info['onsale_verify_status'] != 1) {
            return 0;
        }
        // 售完不下架
        if (!empty($period_info['sellout_sold_out']) && $period_info['sellout_sold_out'] == 1) {
            return 1;
        }

        $package = new PeriodsPackage($periods_type);
        // 套餐列表
        $package_list = $package->packageList(['period' => $period]);
        // 是否还存在套餐库存 0 不存在，1 存在
        $inventory = 0;
        // 是否不限量 0 限量，1 不限量
        $unlimited = 0;
        if (!empty($package_list)) {
            // 检查套餐库存
            foreach ($package_list as $val) {
                if ($val['unlimited'] == 0 && $val['inventory'] > 0) {
                    $inventory = 1;
                }
                $unlimited = $val['unlimited'];
            }
            // 不限量
            if ($unlimited == 1) {
                $unlimited = 1;
            }
        }
        unset($val);

        if (empty($period_info)) {
            return 0;
        }

        // 存在库存
        if ($inventory == 1) {
            return 1;
        }
        // 没有库存并且售完下架，并且套餐限量
        if ($inventory == 0 && $unlimited == 0) {
            return 0;
        }

        $other_ser = new \app\service\Other();
        // 套餐无库存并且套餐不限量，检查产品库存，套餐产品库存不足的写入以下数组
        $package_inventory = [];
        // 套餐id
        $package_id_arr = [];
        if ($inventory == 0 && $unlimited == 1) {
            $product_ids = [];
            foreach ($package_list as $key => $val) {
                array_push($package_id_arr, $val['id']);
                $product_arr = $package_list[$key]['product_info'] = json_decode($val['associated_products'], true);
                // 验证套餐下每个产品库存
                foreach ($product_arr as $v) {
                    if (!empty($v['product_id'])) {
                        if (is_array($v['product_id'])) {
                            $product_ids = array_merge($product_ids, $v['product_id']);
                        } else {
                            array_push($product_ids, $v['product_id']);
                        }
                    }
                }
            }
            $product_inventory = Db::name('periods_product_inventory')
                ->where('period', $period)
                ->where('product_id', 'in', $product_ids)
                ->column('inventory','product_id');

            // 验证每个套餐
            foreach ($package_list as $val) {
                // 验证套餐下每个产品库存
                foreach ($val['product_info'] as $v) {
                    $s_inv = false;
                    if (!empty($v['product_id'])) {
                        !is_array($v['product_id']) && $v['product_id'] = [$v['product_id']];
                        foreach ($v['product_id'] as $v1) {
                            $s_inventory = $product_inventory[$v1] ?? 0;
                            // 产品库存不足
                            if ($s_inventory >= $v['nums']) {
                                $s_inv = true;
                            }
                        }
                    }
                    // 产品库存不足
                    if (!$s_inv) {
                        array_push($package_inventory, $val['id']);
                    }
                }
            }
            unset($val, $v);
            $package_inventory = array_values(array_unique($package_inventory));
            $package_id_arr = array_values(array_unique($package_id_arr));
            // 所有套餐都存在库存不足的产品，判断商品售罄还是下架
            if ($package_inventory == $package_id_arr) {
                return 0;
            }
            // 更新商品状态在售中：存在库存并且已确认过上架，并未到下架时间
            if ($package_inventory != $package_id_arr && $period_info['onsale_verify_status'] == 1) {
                return 1;
            }
        }

        // 下架锁定后不允许上架
//        if ($onsale_status == 1 || $onsale_status == 2) {
//            // 下架后锁定不做任何操作
//            if ($period_info['is_sold_out_lock'] == 1) {
//                // 下架后锁定不做任何操作
//                return 0;
//            }
//        }
        return 0;
    }

    /**
     * @param int $period 期数
     * @param int $trigger_time 任务执行时间
     * @param int $type 任务类型
     * @param int $period_status 改变期数状态
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function task(int $period, int $trigger_time, int $type, int $period_status = 1)
    {
//        $field = 'onsale_time,sell_time,sold_out_time,onsale_review_status,onsale_verify_status';
//        $period_info = $this->getOne($period, $field);
        // 建立秒级任务地址
        $task_url = env('item.SLS_URL') . '/services/v3/task/add';
        // 更新秒级任务地址
        $up_task_url = env('item.SLS_URL') . '/services/v3/task/update';
        // 回调地址
        $re_url = env('item.COMMODITIES_URL') . '/commodities/v3/periods/systemPutShelf';
        // 查询是否已存在上架任务
        $is_exists = PeriodsTaskLog::where([
            'period' => $period,
            'type' => $type,
            'is_exec' => 0
        ])->value('task_id');
        $task_id = uuid();
        $task_data['task_trigger_time'] = $trigger_time;
        $task_data['task_url'] = $re_url;
        $task_data_json = (string)json_encode([
                'period' => $period,
                'onsale_status' => $period_status,
                'task_id' => $task_id,
                'periods_type' => $this->type,
                'type' => $type
            ]
        );
        $task_data['task_data'] = $task_data_json;

        if ($is_exists) {
            $task_data['old_task_id'] = $is_exists;
            $task_data['new_task_id'] = $task_id;
            $task_data = json_encode($task_data);
            $url_header[] = 'vinehoo-client: tp6-commodities';
            $url_header[] = 'vinehoo-client-version: v3';
            $url_header[] = 'Content-Type: application/json;charset=utf-8';
            $url_header[] = 'Content-Length: ' . strlen($task_data);
            $t1 = curl_request($up_task_url, $task_data, $url_header);
            // PeriodsTaskLog::where('task_id', $is_exists)->update(['task_trigger_time' => $trigger_time]);
        } else {
            $task_data['task_id'] = $task_id;
            $task_data = json_encode($task_data);
            $url_header[] = 'vinehoo-client: tp6-commodities';
            $url_header[] = 'vinehoo-client-version: v3';
            $url_header[] = 'Content-Type: application/json;charset=utf-8';
            $url_header[] = 'Content-Length: ' . strlen($task_data);
            $t1 = curl_request($task_url, $task_data, $url_header);
        }
        $t1 = json_decode($t1, true);
        // 记录任务日志
        $task_log_data['task_id'] = $task_id;
        $task_log_data['period'] = $period;
        $task_log_data['task_trigger_time'] = $trigger_time;
        $task_log_data['response'] = $t1['error_code'] ?? null;
        $task_log_data['remark'] = '期数：' . $period . ' 自动上架任务';
        $task_log_data['created_time'] = time();
        $task_log_data['type'] = $type;
        $task_log_data['params'] = $task_data_json;
        if ($is_exists) {
            PeriodsTaskLog::where('task_id', $is_exists)->update([
                'task_id' => $task_id,
                'params' => $task_data_json,
                'task_trigger_time' => $trigger_time,
            ]);
        } else {
            PeriodsTaskLog::create($task_log_data);
        }
    }

    /**
     * 创建商品备注
     * @param array $params
     * @return false|mixed
     */
    public function createRemark(array $params)
    {
        $result = PeriodsRemark::create($params);
        return (int)$result->id ?? false;
    }

    /**
     * 获取商品备注列表
     * @param int $period
     * @param int $limit
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     */
    public function remarkList(int $period, int $limit = 15)
    {
        return PeriodsRemark::where('period', $period)->order('id', 'desc')->paginate([
            'list_rows' => $limit,
            'var_page' => 'page',
        ]);
    }

    /**
     * 商品已购数量变更处理
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function periodsPlusMinus(array $params)
    {
        // 查询商品已购临界值等信息
        $field = 'id,purchased,vest_purchased,limit_number,critical_value,incremental,predict_shipment_time';
        if ($this->type == 5) {
            $field .= ',inventory';
        }
        $period_info = $this->model::where('id', $params['period'])->field($field)->find();
        if (empty($period_info)) {
            return serviceReturn(false, [], '商品不存在');
        }
        // 拍卖商品，更新已支付
        if ($this->type == 11 && $params['type'] == 'order') {
            PeriodsUserAuction::where('period', $params['period'])->update(['pay_status' => 2]);
        }
        // 马甲处理
        // 马甲增加后总数
        $count = $params['count'] + $period_info['purchased'] + $period_info['vest_purchased'];
        // 初始值应增量
        $o_n = 0;
        // 如果总数大于初始值 增加初始值
        if ($count > $period_info['limit_number']) {
            // 差值
            $y = $count - $period_info['limit_number'];
            // 如果差值大于增量值
            if ($y > $period_info['incremental']) {
                // 计算增量几次
                if ($y > 0 && $period_info['incremental'] > 0) {
                    $o_n = ceil($y / $period_info['incremental']) * $period_info['incremental'];
                }
            } else {
                $o_n = $period_info['incremental'];
            }
        }

        // 如果已购达到临界值
        if (($period_info['limit_number'] - $count) <= $period_info['critical_value']) {
            $o_n += $period_info['incremental'];
        }

        // 如果增量后到达了临界值继续增加
        if ((($period_info['limit_number'] - $count) + $o_n) <= $period_info['critical_value']) {
            $o_n += $period_info['incremental'];
        }
        // 增加初始值
        if ($o_n > 0 && $this->type < 5) {
            $package_ser = new Package($this->type);
            $package_list = $package_ser->packageProductList(['period' => $params['period']]);
            // 获取每个套餐最大库存
            $package_inventory = [];
            // 是否剩余库存1
            $is_one = false;
            if (!empty($package_list)) {
                foreach ($package_list as $value) {
                    $product_list = json_decode($value['associated_products'], true);
                    foreach ($product_list as &$val) {
                        if ($value['is_mystery_box'] == 0) {
                            // 查询套餐产品库存
                            $product_inventory = PeriodsProductInventory::where([
                                'period' => $value['period_id'],
                                'product_id' => $val['product_id']
                            ])->value('inventory');
                            if (intval($product_inventory) == 1) {
                                $is_one = true;
                            }
                            $val['inventory'] = $product_inventory;
                            // 套餐内产品最大购买数
                            array_push($package_inventory, floor($product_inventory / $val['nums']));
                        } elseif ($value['is_mystery_box'] == 1) {
                            foreach ($val['product_id'] as &$v) {
                                $product_inventory = PeriodsProductInventory::where([
                                    'period' => $value['period_id'],
                                    'product_id' => $v
                                ])->value('inventory');
                                $val['inventory'][$v] = $product_inventory;
                            }
                        }
                    }
                }
            }
            if (!empty($package_inventory)) {
                sort($package_inventory);
            }
            // 如果增量大于库存，则初始值增量等于仅剩库存
            if ($package_inventory[0] <= $period_info['incremental'] &&
                end($package_inventory) <= $period_info['incremental']) {
                $o_n = end($package_inventory);
            }

            // 套餐最小库存大于增量值才增加增量
//            if ($package_inventory[0] >= $o_n || $params['type'] == 'vest') {
            $min = $period_info['limit_number'] - ($period_info['purchased'] + $period_info['vest_purchased']);
//            var_dump($o_n);
//            var_dump(end($package_inventory));
//            var_dump($min);
//            exit();
            if (end($package_inventory) >= $min && empty($period_info['is_seckill'])) {
                $this->model::where('id', $params['period'])->inc('limit_number', $o_n)->update();
            }
//            $this->model::where('id', $params['period'])->inc('limit_number', $o_n)->update();
//            }
        }

        // 兔头优惠券增量
        if ($this->type == 5) {
            // 套餐最小库存大于增量值才增加增量
            if ($period_info['inventory'] > $o_n) {
                $this->model::where('id', $params['period'])->inc('limit_number', $o_n)->update();
            }
        }

        // 商家秒发增量
        if ($this->type == 9) {
            $this->model::where('id', $params['period'])->inc('limit_number', $o_n)->update();
        }

        switch ($params['type']) {
            case 'vest':
                if (empty($period_info['is_seckill'])) {
                    // 增加马甲已购值
                    $inc_vest = $this->model::where('id', $params['period'])
                    ->inc('vest_purchased', $params['count'])
                    ->update();
                    // 增加马甲记录
                    if ($inc_vest) {
                        $vest['period'] = $params['period'];
                        $vest['periods_type'] = $this->type;
                        $vest['vest_id'] = $params['vest_id'] ?? 0;
                        $vest['inc_count'] = $params['count'];
                        $vest['created_time'] = time();
                        PeriodsVestRecord::create($vest);
                        if(!empty($vest['vest_id'])){
                            //增加已执行次数
                            Db::name('periods_vest_log')->where('vest_id',$vest['vest_id'])->inc('exec_total',1)->update();
                        }
                    }
                }

                // 马甲是否结束
                if (isset($params['is_end']) && $params['is_end'] == true) {
                    PeriodsVest::where('id', $params['vest_id'])->update(['status' => 1]);
                }
                break;
            case 'order':
                // 增加订单真实已购值
//                $inc_order = $this->model::where('id', $params['period'])
//                    ->inc('purchased', $params['count'])
//                    ->update();
//                // 扣减套餐库存
//                $package_ser = new Package($this->type);
//                $package = $package_ser->getOne($params['package_id']);
//                // -1 不限量
//                if ($package['inventory'] != '-1') {
//                    if ($package['inventory'] < $params['count']) {
//                        return serviceReturn(false, [], '库存不足');
//                    }
//                    // 减少套餐实际库存
//                    $dec_package = $package_ser->decInventory((int)$params['package_id'], (int)$params['count']);
//                }
                // 增加商品真实已购
                $inc_purchased = $this->model::where('id', $params['period'])
                    ->inc('purchased', (int)$params['count'])
                    ->update();
                // 根据订货量自动更新期数发货日期
                try {
                    // 查询期数日志
                    $i_o = PeriodsProductInventoryOrder::where('period', $params['period'])
                        ->order('predict_shipment_time', 'desc')
                        ->find();
                    if (!empty($i_o)) {
                        if ($period_info['predict_shipment_time'] < $i_o['predict_shipment_time']) {
                            // 查询订货记录
                            $inventory_order = PeriodsProductInventoryOrder::where('period', $params['period'])
                                ->order('created_time desc')
                                ->column('short_code,created_time,predict_shipment_time as pstime');
                            $i_o = [];
                            if (!empty($inventory_order)) {
                                foreach ($inventory_order as $v) {
                                    if (empty($i_o[$v['short_code']])) {
                                        $i_o[$v['short_code']] = $v;
                                    }
                                }
                            }
                            // 查询订货量
                            $order_num = PeriodsProductInventory::where('period', $params['period'])->column('order','short_code');

                            // 获取简码已售
                            $url_resq_data = GetSaleBottleNums([['period' => (int)$params['period'], 'period_type' => $this->type]]);
                            if (!empty($url_resq_data)) {
                                $predict_shipment_time = strtotime($period_info['predict_shipment_time']);
                                // 查看简码订货量
                                foreach ($url_resq_data[$params['period']] as $key => $val) {
                                    if (!empty($order_num[$key]) && $val >= $order_num[$key]) {
                                        if (!empty($i_o[$key]) && $i_o[$key]['pstime'] > $predict_shipment_time) {
                                            $predict_shipment_time = $i_o[$key]['pstime'];
                                        }
                                    }
                                }
                                if ($predict_shipment_time > strtotime($period_info['predict_shipment_time'])) {
                                    // 已售大于订货更新期数发货时间
                                    $this->updateField(
                                        intval($params['period']),
                                        'predict_shipment_time',
                                        strval($predict_shipment_time)
                                    );
                                }
                                unset($val);
                            }
                        }
                    }
                } catch (\ErrorException $exception) {
                    $err = $exception->getMessage();
                }
                break;
        }
        $period_new_info = $this->model::where('id', $params['period'])->field($field)->find()->toArray();
        $purch = intval($period_new_info['purchased'] + $period_new_info['vest_purchased']);
        // 如果真实已购+马甲已购 大于 初始值进行差值增量
        if ($purch > (int)$period_new_info['limit_number'] && empty($period_info['is_seckill'])) {
//            $this->model::where('id', $params['period'])->update(['limit_number' => $purch]);
            $inc_num = ($purch - (int)$period_info['limit_number']) + $o_n;
//            var_dump($purch - (int)$period_info['limit_number']);
            if ((($inc_num + $period_new_info['limit_number']) - $purch) > end($package_inventory)) {
                $c = (($inc_num + $period_new_info['limit_number']) - $purch) - end($package_inventory);
                $inc_num -= $c;
            }
            $period_new_info['limit_number'] += $inc_num;
            $this->model::where('id', $params['period'])->inc('limit_number', $inc_num)->update();
        }
        // 如果已购和限量相等
        if ($purch == (int)$period_new_info['limit_number'] && empty($period_info['is_seckill'])) {
            $inc_num = 1;
            if (end($package_inventory) >= $period_new_info['incremental']) {
                $inc_num = $period_new_info['incremental'];
            } else {
                $inc_num = end($package_inventory);
            }
            $period_new_info['limit_number'] += $inc_num;
            $this->model::where('id', $params['period'])->inc('limit_number', $inc_num)->update();
        }

        // 当真实库存只剩1瓶时，限量值与已购值之差为1
        $interval = intval($period_new_info['limit_number'] - $purch);
        if (!empty($is_one) && $interval != 1 && empty($period_info['is_seckill'])) {
            $vest_purchased = intval(($period_new_info['limit_number'] - 1) - $period_new_info['purchased']);
            $this->model::where('id', $params['period'])->update(['vest_purchased' => $vest_purchased]);
        }

        return serviceReturn(true, (int)$params['count'], '操作成功');
    }

    /**
     * 马甲背心期数添加列表
     * @param array $where 查询条件
     * @return PeriodsVestRecord[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function vestRecordList(array $where)
    {
        return PeriodsVestRecord::where($where)->select();
    }

    /**
     * 添加商品评论
     * @param array $params
     * @return PeriodsComment|Model
     */
    public function createComment(array $params)
    {
        return PeriodsComment::create($params);
    }

    /**
     * 用户评论数据
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function commentList(array $params)
    {
        $start_time = microtime(true);

        $limit = $params['limit'] ?? 10;
        // 查询同简码期数
        $product_inventory = PeriodsProductInventory::where('period', $params['period'])->column('period,periods_type,product_id');
        $product_id = [];
        if (!empty($product_inventory)) {
            $product_id = array_column($product_inventory,'product_id');
            $periods_type = $product_inventory[0]['periods_type'] ?? '';
            // 商家秒发查询原期数评论
            if ($periods_type == 9) {
                $period_id = Db::name('periods_second_merchants')
                    ->where('id',$params['period'])
                    ->value('join_period_id');
                if (!empty($period_id)) {
                    $params['period'] = $period_id;
                }
            }
        }

        $monitor1 = (microtime(true) - $start_time);

        $periods_id = PeriodsProductInventory::whereIn('product_id', $product_id)->column('period');
        $list = PeriodsComment::alias('a')
            ->leftJoin('periods_product_inventory b', 'a.period = b.period')
            ->where([
                ['a.period', '=', $params['period']],
                ['a.pid', '=', 0],
                ['a.audit_status', '=', 1],
                ['a.is_recommend', '=', 1],
//                ['a.is_show', '=', 1],
            ])
            ->whereOr([[['a.is_show', '=', 1], ['a.period', 'in', $periods_id], ['a.audit_status', '=', 1],
                ['a.is_recommend', '=', 1]]])
            ->whereOr([[['a.uid', '=', $params['uid']], ['a.audit_status', '=', 0], ['a.period', '=', $params['period']]]])
            ->field('a.id, a.uid, a.first_id, a.pid, a.p_uid, a.period, a.content, a.likenums, a.audit_status, 
            a.emoji_image, a.created_time, a.hot_num, a.is_recommend,a.is_show')
            ->order(['hot_num' => 'desc', 'created_time' => 'desc'])
            ->group('a.id')
            ->paginate([
                'list_rows' => $limit,
                'var_page' => 'page',
            ]);
        // 分页
        $result['total'] = $list->total() ?? 0;
        $result['list'] = $list->getCollection()->toArray() ?? [];

        $monitor2 = (microtime(true) - $start_time);

        $uid = '';
        if (!empty($result['list'])) {
            foreach ($result['list'] as $val) {
                $uid .= $val['uid'] . ",";
            }
            unset($val);
            // 查询用户
            $user_url = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
            $user_url = $user_url . '?uid=' . $uid .
                '&field=uid,user_level,avatar_image,nickname,certified_info,type';
            $user_list = get_url($user_url);
            $user_list = get_interior_http_response($user_list);
            $user_list = $user_list['list'] ?? [];
            $users = [];
            if (!empty($user_list)) {
                foreach ($user_list as $v) {
                    if (!empty($v['uid'])) {
                        $users[$v['uid']] = $v;
                    }
                }
            }
            // 写入用户等级
            $under_uid = '';
            // 评论id
            $comment_id = '';
            // 回复用户
            foreach ($result['list'] as &$val) {
                $comment_id .= $val['id'] . ',';
                $val['user_level'] = 0;
                $val['created_time'] = mdate($val['created_time']);
                // 用户数据
                $user_info = $users[$val['uid']] ?? [];
                if (!empty($user_info)) {
                    $val['avatar_image'] = env('ALIURL') . $user_info['avatar_image'];
                    $val['nickname'] = $user_info['nickname'];
                    $val['user_level'] = $user_info['user_level'];
                    $val['certified_info'] = $user_info['certified_info'];
                    $val['user_type'] = $user_info['type'];
//                        $val['is_buy'] = $this->getESOrderBuy((int)$v['uid'], (int)$params['period']);
                }
                // 用户是否点赞
//                $like = PeriodsCommentLike::where([
//                    'uid' => $params['uid'],
//                    'period' => $params['period'],
//                    'comment_id' => $val['id']
//                ])
//                ->value('id');
                $val['is_like'] = empty($like) ? 0 : 1;
                // 回复数据
//                $val['under_comment'] = PeriodsComment::where(['first_id' => $val['id'], 'audit_status' => 1])
//                    ->field('id, uid, first_id, pid, p_uid, period, content, likenums, audit_status, emoji_image,
//                    created_time, hot_num')
//                    ->order('hot_num', 'desc')
//                    ->limit(3)
//                    ->select()
//                    ->toArray();
//                if (!empty($val['under_comment'])) {
//                    foreach ($val['under_comment'] as $value) {
//                        if ($value['uid']) {
//                            $under_uid .= $value['uid'] . ",";
//                            if ($value['p_uid'] > 0) {
//                                $under_uid .= $value['p_uid'] . ",";
//                            }
//                        }
//                    }
//                }
            }
            unset($val, $v, $value);
            if ($comment_id) {
                $comment_id = trim($comment_id, ',');
                // 查询评论回复
                $w = [['first_id', 'in', $comment_id]];
                //不推荐处理   没有登录的用户不推荐数据全部看不了
                if(empty($params['uid'])){
                    array_push($w,['audit_status', '=', 1]);
                    array_push($w,['is_recommend', '=', 1]);
                }else{
                    array_push($w,['audit_status', 'in', [0,1]]);
                }
                $reply = PeriodsComment::where($w)
                    ->field('id, uid, first_id, pid, p_uid, period, content, likenums, audit_status, emoji_image,
                    created_time, hot_num')
                    ->order('hot_num', 'desc')
                    ->select()
                    ->toArray();
                foreach ($result['list'] as &$value) {
                    $value['under_comment'] = [];
                    foreach ($reply as $val) {
                        // 回复用户id
                        if ($val['uid']) {
                            $under_uid .= $val['uid'] . ",";
                            if ($val['p_uid'] > 0) {
                                $under_uid .= $val['p_uid'] . ",";
                            }
                        }
                        if ($val['first_id'] == $value['id']) {
                            array_push($value['under_comment'], $val);
                        }
                    }
                }
            }


            // 下级评论用户
            $under_user_url = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
            $under_user_url = $under_user_url . '?uid=' . $under_uid .
                '&field=uid,user_level,avatar_image,nickname,certified_info,type';
            $under_user_list = get_url($under_user_url);
            $under_user_list = get_interior_http_response($under_user_list);
            $under_user_list = $under_user_list['list'] ?? [];
            $under_users = [];
            if (!empty($under_user_list)) {
                foreach ($under_user_list as $v) {
                    if (!empty($v['uid'])) {
                        $under_users[$v['uid']] = $v;
                    }
                }
            }
            unset($val, $v, $value);
            foreach ($result['list'] as &$val) {
                if (!isset($val['under_comment'])) {
                    continue;
                }
                foreach ($val['under_comment'] as &$v) {
                    $v['created_time'] = mdate($v['created_time']);
                    $v['user_level'] = 0;
                    $under_user_info =  $under_users[$v['uid']] ?? [];
                    if (!empty($under_user_info)) {
                        $v['avatar_image'] = env('ALIURL') . $under_user_info['avatar_image'];
                        $v['nickname'] = $under_user_info['nickname'];
                        $v['user_level'] = $under_user_info['user_level'];
                        $v['certified_info'] = $under_user_info['certified_info'];
                        $v['user_type'] = $under_user_info['type'];
                        // 是否已购
//                            $v['is_buy'] = $this->getESOrderBuy((int)$v['uid'], (int)$params['period']);
                    }
                    if (!empty($under_users[$v['p_uid']]['nickname'])) {
                        $v['reply_nickname'] = $under_users[$v['p_uid']]['nickname'];
                    }
                }
            }

            //上面内存地址使用导致数据混乱
            $result['list'] = json_decode(json_encode($result['list']),true);

            //不推荐处理   登录用户判断那些数据需要显示
            if($params['uid']){
                foreach ($result['list'] as &$val) {
                    if (empty($val['under_comment'])) {
                        continue;
                    }

                    // 补充上下级结构
                    $val['under_comment'] = $this->buildUids($val['under_comment'],$val['uid']);

                    // 遍历并根据条件删除不符合条件的评论
                    foreach ($val['under_comment'] as &$v) {
                        // 如果当前评论的 audit_status 为 0 且用户不在可见用户列表中
                        if ($v['audit_status'] == 0 && !in_array((int)$params['uid'], $v['uids'])) {
                            // 删除该评论及其所有子评论
                            foreach ($v['ids'] as $id) {
                                unset($val['under_comment'][$id]);
                            }
                        }
                    }

                    // 清理临时属性并删除不存在父级的节点
                    foreach ($val['under_comment'] as $key => &$v) {
                        unset($v['uids'], $v['ids']); // 删除临时属性

                        // 检查并删除没有父级的节点
                        if ($v['pid'] != $val['id'] && !isset($val['under_comment'][$v['pid']])) {
                            unset($val['under_comment'][$key]);
                        }
                    }

                    // 重新整理索引，保持顺序
                    $val['under_comment'] = array_values($val['under_comment']);
                }
            }else{
                //未登录用户清理上级被不推荐的
                foreach ($result['list'] as &$val) {
                    if (empty($val['under_comment'])) {
                        continue;
                    }

                    $val['under_comment'] = array_column($val['under_comment'], null, 'id');

                    // 清理临时属性并删除不存在父级的节点
                    foreach ($val['under_comment'] as $key => $v) {
                        // 检查并删除没有父级的节点
                        if ($v['pid'] != $val['id'] && !isset($val['under_comment'][$v['pid']])) {
                            unset($val['under_comment'][$key]);
                        }
                    }

                    // 重新整理索引，保持顺序
                    $val['under_comment'] = array_values($val['under_comment']);
                }
            }
            //end
        }
        $monitor3 = (microtime(true) - $start_time);
        //file_put_contents(app()->getRuntimePath() . '/log/mon.log', sprintf("时间:%s,monitor1:%s秒,monitor2:%s秒,monitor3:%s秒\n", date('Y-m-d H:i:s'), $monitor1,$monitor2,$monitor3), FILE_APPEND);
        return $result;
    }

    public function buildUids(array $elements,$firstUid = 0) {
        // 保存元素的原始顺序
        $original_order = array_column($elements, 'id');

        // 按id排序，确保父节点在前，子节点在后
        usort($elements, function($a, $b) {
            return $a['id'] - $b['id'];
        });

        $map = [];

        // 初始化所有节点，建立以id为键的映射
        foreach ($elements as &$element) {
            $element['uids'] = [$firstUid,$element['uid']]; // 初始只包含自身的uid
            $element['ids'] = [$element['id']];   // 初始只包含自身的id
            $map[$element['id']] = &$element;     // 使用引用，方便后续修改
        }

        // 从后向前遍历，确保子节点的uids先被处理
        for ($i = count($elements) - 1; $i >= 0; $i--) {
            $element = &$elements[$i];
            if (isset($map[$element['pid']])) {
                // 将当前元素的uids合并到父元素中
                $parent = &$map[$element['pid']];
                $parent['uids'] = array_unique(array_merge($parent['uids'], $element['uids']));
                $parent['ids'] = array_merge($parent['ids'], $element['ids']);
            }
        }

        // 将数组恢复为原始顺序
        usort($elements, function($a, $b) use ($original_order) {
            return array_search($a['id'], $original_order) - array_search($b['id'], $original_order);
        });

        // 返回以id为键的映射数组，同时保持顺序不变
        return array_column($elements, null, 'id');
    }


    /**
     * 期数评论用户相关状态
     * @param array $params
     * @return array
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\db\exception\DataNotFoundException
     */
    public function getCommentUserStatus(array $params): array
    {
        // 查询用户是否真实已购
        $params['comment_uid'] = $params['comment_uid'] ?? [];
        $params['comment_id'] = $params['comment_id'] ?? [];
        $params['period'] = $params['period'] ?? 0;
//        $user_buy_list = $this->getESOrderBuyAll((array)$params['comment_uid'], (int)$params['period']);
        // 商品频道
        $periods_type = PeriodsProductInventory::where('period', $params['period'])->value('periods_type');
        $m_period_id = 0;
        if (!empty($periods_type) && $periods_type == 9) {
            $period_id = Db::name('periods_second_merchants')
                ->where('id', $params['period'])
                ->value('join_period_id');
            if (!empty($period_id)) {
                //秒发商家期数
                $m_period_id = $params['period'];
                //关联秒发期数
                $params['period'] = $period_id;
            }
        }

        // 查询用户是否马甲已购
        $vest_buy = PeriodsComment::where('vest_is_buy', 1)
            ->whereIn('id', $params['comment_id'])
            ->field('id, uid, vest_is_buy')
            ->select()->toArray();
        // 查询用户是否点赞
        $like = [];
        if ($params['uid']) {
            $like = PeriodsCommentLike::where([
                ['uid', '=', $params['uid']],
                ['period', '=', $params['period']],
                ['comment_id', 'in', $params['comment_id']]
            ])
                ->field('comment_id')
                ->select()
                ->toArray();
        }
        $c_like = [];
        foreach ($params['comment_id'] as $key => &$val) {
            $c_like[$key]['is_like'] = 0;
            $c_like[$key]['comment_id'] = $val;
            if (!empty($like)) {
                foreach ($like as $v) {
                    if ($v['comment_id'] == $val) {
                        $c_like[$key]['is_like'] = 1;
                    }
                }
            }
        }
        unset($val, $v, $key);
        // 真实已购
        $buy = [];
        // 真实已订
        $deposit = [];
        // redis 链接
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(3);
        foreach ($params['comment_uid'] as $key => $val) {
            $buy[$key]['is_buy'] = 0;
            $buy[$key]['comment_uid'] = $val;
//            if (!empty($user_buy_list)) {
//                foreach ($user_buy_list as $v) {
//                    if ($v['uid'] == $val) {
//                        $buy[$key]['is_buy'] = 1;
//                    }
//                }
//            }
            // 查询是否已购
            $is_buy = $redis->zScore('vinehoo.hought.' . $params['period'], $val);
            //查询秒发商家商品是否已购
            if (!empty($m_period_id) && empty($is_buy)) {
                $is_buy = $redis->zScore('vinehoo.hought.' . $m_period_id, $val);
            }
            $buy[$key]['is_buy'] = $is_buy;

            // 查询是否已订
            $deposit[$key]['is_deposit'] = 0;
            $deposit[$key]['comment_uid'] = $val;
            $is_deposit = $redis->zScore('vinehoo.deposit.' . $params['period'], $val);
            if (!empty($is_deposit)) {
                $deposit[$key]['is_deposit'] = $is_deposit;
            }
        }
        unset($val, $key);

        // 马甲是否已购
        if (!empty($vest_buy)) {
            foreach ($buy as &$value) {
                foreach ($vest_buy as &$v) {
                    if ($value['comment_uid'] == $v['uid']) {
                        if ($value['is_buy'] == 0) {
                            $value['is_buy'] = $v['vest_is_buy'];
                        }
                    }
                }
            }
        }

        $data['like'] = $c_like;
        $data['buy'] = $buy;
        $data['deposit'] = $deposit;
        return $data;
    }

    public function getESOrderBuyAll(array $uid, int $period): ?array
    {
        $es = new ElasticSearchService();
        $query = [
            'index' => ['orders'],
            'match' => [
                ['period' => $period],
            ],
            'terms' => [['uid' => $uid], ['sub_order_status' => [1, 2, 3]]]
        ];
        $order = $es->getDocumentList($query);
        return empty($order['data']) ? [] : $order['data'];
    }

    /**
     * 查询 es 订单是否已购
     * @param int $uid 用户 id
     * @param int $period 期数
     * @return int
     */
    public function getESOrderBuy(int $uid, int $period): int
    {
        $es = new ElasticSearchService();
        $query = [
            'index' => ['orders'],
            'match' => [
                ['uid' => $uid],
                ['period' => $period],
            ],
            'terms' => [['sub_order_status' => [1, 2, 3]]]
        ];
        $order = $es->getDocumentList($query);
        return empty($order['data']) ? 0 : 1;
    }

    /**
     * 添加商品产品库存
     * @param array $params
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function createPeriodsProductInventory(array $params): \think\Collection
    {
        // 查询期数下产品id是否已经存在库存
        $exists = PeriodsProductInventory::where([
            'period' => $params['period'],
            'product_id' => $params['product_id']
        ])->find();
        // 删除数据
        if ($exists && !isset($params['id'])) {
            PeriodsProductInventory::where([
                'period' => $params['period'],
                'product_id' => $params['product_id']
            ])->delete();
        }
        // 重新添加
        $ppi_ser = new PeriodsProductInventory();
        return $ppi_ser->saveAll([$params]);
    }

    /**
     * 删除商品产品库存
     * @param array $params
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return bool|string
     */
    public function deletePeriodsProductInventory(array $params)
    {
        Db::startTrans();
        try {
            $exists = PeriodsProductInventory::where([
                'period' => $params['period'],
                'short_code' => $params['short_code']
            ])->find();
            if (!$exists) {
                return '该产品不存在';
            }

            $short_codes = PeriodsProductInventory::where([
                ['period', '=', $params['period']],
                ['is_use_comment', '=', 0],
                ['short_code', '<>', $params['short_code']],
            ])->column('short_code');
            if (count($short_codes) < 1) {
                return '只有一个简码时不能删除';
            }
            
            $package = new PeriodsPackage($this->type);
            // 查询是否存在套餐
            $exists_package = $package->ConditionalQueryPackageExists([
                ['period_id', '=', $params['period']],
                ['associated_products', 'like', '%' . $exists['product_id'] . '%'],
            ]);
            if ($exists_package) {
                return '该产品存在于套餐中，无法删除';
            }

            $exists->delete();

            // 更新期数产品信息
            $products = Db::table('vh_wiki.vh_products')
                ->alias('p')
                ->leftJoin('vh_wiki.vh_product_category pc', 'pc.id=p.product_category')
                ->leftJoin('vh_wiki.vh_product_type pt', 'pt.id=p.product_type')
                ->whereIn('p.short_code', $short_codes)
                ->column('p.id,p.short_code,p.capacity,pc.name as product_main_category,pt.name as product_category,p.product_keywords_id');
            $keywords_ids = array_column($products, 'product_keywords_id');
            $product_keyword = Db::table('vh_wiki.vh_product_keyword')
                ->whereIn('id', $keywords_ids)
                ->column('name');
            $this->model::where('id', $params['period'])->update([
                'product_keyword' => implode(',', $product_keyword ?? ''),
                'short_code' => implode(',', $short_codes),
                'capacity' => implode(',', array_values(array_unique(array_column($products, 'capacity')))),
                'product_main_category' => implode(',', array_values(array_unique(array_column($products, 'product_main_category')))),
                'product_category' => implode(',', array_values(array_unique(array_column($products, 'product_category')))),
                'product_id' => implode(',', array_column($products, 'id')),
            ]);
            
            Db::commit();
        } catch (\Exception $e) {
            Db::Rollback();
            return $e->getMessage();
        }
        return true;
    }

    /**
     * 根据期数 id 修改期数信息
     * @param int $id
     * @param array $data
     * @return PeriodsCross|PeriodsFlash|PeriodsLeftover|PeriodsSecond
     */
    public function updateInfoById(int $id, array $data, int $upLimitNumber = 1)
    {
        // 修改商品上下架状态时
        if (isset($data['onsale_status'])) {
            $notify_content = '';
            $is_postpone = false;
            if ($data['onsale_status'] == '3' || $data['onsale_status'] == '4') {
                // 删除该商品产品记录
                PeriodsOffsaleProductRecord::where('period', $id)
                    ->delete();
                // 查看是否存在执行中马甲
                $vest_id = PeriodsVest::where(['period_id' => $id, 'status' => 0])->column('id');
                if (!empty($vest_id)) {
                    // 马甲结束执行
                    $url = env('ITEM.GOODS_VEST_URL') . '/vest/v3/vest/stopVest';
                    $vest_data['vest_id'] = implode(',', $vest_id);
                    $vest_headers[] = 'vinehoo-uid:0';
                    $vest_result = post_url($url, $vest_data, $vest_headers);
                }
                if ($data['onsale_status'] == 3) {
                    // 查看是否自动延期
                    $period_info = $this->model::where('id', $id)->field('sold_out_time, is_postpone')->find();
                    if (!empty($period_info)) {
                        if ($period_info['is_postpone'] == 1) {
                            $is_postpone = true;
                            $inventory = $this->getPeriodsInventory($id, $this->type);
                            if ($inventory == 1) {
                                $data['sold_out_time'] = strtotime("+7 day");
                                $data['onsale_status'] = 2;
                                $this->task($id, $data['sold_out_time'], 2, 3);
                                $notify_content = "{$id}期因有相同商品在售，未售完自动延期失败，并取消自动延期勾选。";
                            }
                        }
                    }
                }
            }
            if ($data['onsale_status'] == '2') {
                // 验证产品相同售卖区间
                $resu = $this->VerifySimultaneousSales($id,0,0,[],0,$notify_content);
                if ($resu !== true) {// 相同售卖区间下架期数
                    $data['onsale_status'] = 3;
                    if ($is_postpone) {
                        $data['is_postpone'] = 0;
                    }
                    // return true;
                }
            }
            
            if ($data['onsale_status'] == '2') {
                // 更新现有商品套餐已上架
                $package = new Package($this->type);
                $package->updateAllByPeriod($id, ['is_onsale' => 1]);
                if ($upLimitNumber == 1) {
                    // 判断已购限量
                    $this->limitNumberInc($id);
                }

            }
            // 刷新 CDN
            self::CDNrefreshObject(intval($id));
        }

        return $this->model::where('id', $id)
            ->update($data);
    }

    /**
     * 如果真实已购+马甲已购 等于 初始值 触发增量
     * @param int $period
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function limitNumberInc(int $period)
    {
        $field = 'purchased, vest_purchased, limit_number, incremental';
        if ($this->type == 0) {
            $field .= ',is_seckill';
        }
        // 如果真实已购+马甲已购 等于 初始值 触发增量
        $period_info = $this->model::where('id', $period)
            ->field($field)->find();
        // 秒杀商品不操作
        if (!empty($period_info['is_seckill'])){
            return true;
        }

        $purch = (int)$period_info['purchased'] + (int)$period_info['vest_purchased'];
        if ($purch == (int)$period_info['limit_number']) {
            // 如果上架已购限量持平，增加限量
            $this->model::where('id', $period)->inc('limit_number', $period_info['incremental'])->update();
        } elseif ($purch > (int)$period_info['limit_number']) {
            // 如果已购大于限量，限量增加差值 + 增量，虽然通常不可能会出现这种情况
            $inc_num = ($purch - (int)$period_info['limit_number']) + (int)$period_info['incremental'];
            $this->model::where('id', $period)->inc('limit_number', $inc_num)->update();
        }
    }

    /**
     * 添加用户酒款收藏
     * @param array $data
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function periodsCollection(array $data): array
    {
        // 检查是否收藏
        $exists = PeriodsUserCollection::where([
            'user_id' => $data['user_id'],
            'period_id' => $data['period_id']
        ])->find();
        // 添加收藏
        $result = 0;
        if ($data['type'] == 1) {
            // 查询是否渠道商品，渠道商品不允许收藏
            /*$redis_config = config('cache.stores.redis');
            $redis_config['select'] = 0;
            $conn = new \think\cache\driver\Redis($redis_config);
            $exist = $conn->sismember('vinehoo.details.channel', intval($data['period_id']));*/
            //直接查询数据库是否为渠道
            $this->__construct($data['periods_type']);
            $exist = $this->model::where('id', $data['period_id'])->where('is_channel',1)->field('id')->find();
            if (!empty($exist)) {
                return serviceReturn(true, [], '操作成功');
            }

            if (!empty($exists)) {
                return serviceReturn(false, [], '该商品已收藏');
            }
            $result = PeriodsUserCollection::create($data);
        } else {
            // 取消收藏
            if (!empty($exists)) {
                $result = PeriodsUserCollection::where('id', $exists['id'])->delete();
            }
        }

        return serviceReturn(true, $result, '操作成功');
    }

    /**
     * 用户收藏列表
     * @param int $user_id
     * @param int $limit
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     */
    public function collectionList(int $user_id, int $limit): \think\Paginator
    {
        // 用户收藏列表
        return PeriodsUserCollection::where('user_id', $user_id)
            ->order('created_time', 'desc')
            ->paginate($limit)->map(function ($item) {
                // 查询期数详细
                $this->__construct($item->periods_type);
                $field = 'title,brief,banner_img,price,market_price,is_hidden_price,purchased,vest_purchased,limit_number,
                quota_rule,onsale_status';
                //过滤渠道商品
                $period_info = $this->model::where('id', $item->period_id)->where('is_channel',0)->field($field)->find();
                if (!empty($period_info)) {
                    $period_info = $period_info->toArray();
                    $period_info['banner_img_str'] = $period_info['banner_img'][0] ?? '';
                    $period_info['quota_rule'] = json_decode($period_info['quota_rule'], true);
                }
                $item->period_info = $period_info;
                return $item;
            });
    }

    /**
     * 删除用户收藏
     * @param int $user_id
     * @param string $id
     * @return bool
     */
    public function delCollection(int $user_id, string $id): bool
    {
        return PeriodsUserCollection::where([
            ['user_id', '=', $user_id],
            ['id', 'in', $id]
        ])->delete();
    }

    /**
     * 检查是否收藏
     * @param int $user_id 用户 id
     * @param int $period 期数 id
     * @return int | null
     */
    public function existsCollection(int $user_id, int $period): ?int
    {
        // 检查是否收藏
        return PeriodsUserCollection::where(['user_id' => $user_id, 'period_id' => $period])->value('id');
    }

    /**
     * 商品库存列表
     * @param int $period
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function periodsInventoryList(int $period): array
    {
        return PeriodsProductInventory::where('period', $period)->where('is_use_comment', 0)->select()->toArray();
    }

    /**
     * 更新期数产品库存
     * @param int $id 主键 id
     * @param int $inventory 库存
     * @return PeriodsProductInventory|int
     */
    public function updateInventory(int $id, int $inventory)
    {
        return PeriodsProductInventory::where('id', $id)->update(['inventory' => $inventory]);
    }

    /**
     * 更新商品采购
     * @param array $params
     * @return PeriodsCross|PeriodsFlash|PeriodsLeftover|PeriodsSecond
     */
    public function updatePurchaseInfo(array $params)
    {
        if (empty($params['supplier']) || empty($params['supplier_id'])) {
            return serviceReturn(false, [], '必须选择供应商');
        }
        if (!empty($params['is_supplier_delivery']) && $params['is_supplier_delivery'] == 1 && empty($params['supplier_delivery_address'])) {
            return serviceReturn(false, [], '代发商品请选择发货地');
        }
        $period_info = $this->getOne(intval($params['period']), 'supplier_id,payee_merchant_id,buyer_id,buyer_name');
        if (empty($period_info)) {
            return serviceReturn(false, [], '期数信息获取失败');
        }

        if (!empty($params['is_supplier_delivery']) && $params['is_supplier_delivery'] == 1) {
            $data['supplier_delivery_address'] = $params['supplier_delivery_address'];
            //发货时效
            $data['supplier_delivery_time'] = $params['supplier_delivery_time'];
            //周末是否发货
            $data['supplier_delivery_weekend'] = $params['supplier_delivery_weekend'];
        }

        $data['import_type'] = $params['import_type'] ?? null;
        $data['supplier'] = $params['supplier'];
        $data['supplier_id'] = $params['supplier_id'];
        $data['payee_merchant_name'] = $params['payee_merchant_name'];
        $data['payee_merchant_id'] = $params['payee_merchant_id'];
        $periods_remark = [];

        if (isset($params['buyer_id']) && ($period_info['buyer_id'] != $params['buyer_id'])) {
            $data['buyer_id'] = $params['buyer_id'];
            $oerate_info = Db::name('buyer_related')->alias('t1')
                ->join('operate_related t2', 't1.operation_review_id=t2.operation_review_id')
                ->where('t1.delete_time', null)
                ->where('t2.delete_time', null)
                ->where('t1.buyer_id', $data['buyer_id'])
                ->field('t2.operation_review_id,t2.operation_review_name')
                ->find();
            if (!empty($oerate_info)) {
                $data['operation_review_id']   = $oerate_info['operation_review_id'];
                $data['operation_review_name'] = $oerate_info['operation_review_name'];
            } else {
                return serviceReturn(false, [], '采购没有对应运营');
            }
        }
        if (isset($params['buyer_name']) && ($period_info['buyer_id'] != $params['buyer_id'])) {
            $periods_remark[] = [
                'period'        => $params['period'],
                'periods_type'  => $params['periods_type'],
                'remark'        => "由采购：{$period_info['buyer_name']}-更换到采购：" . ($params['buyer_name']) . "。",
                'operator'      => request()->header('vinehoo-uid',0),
                'operator_name' => base64_decode(request()->header('vinehoo-vos-name', '')),
                'created_time'  => time(),
            ];
            $data['buyer_name'] = $params['buyer_name'];
        }
        $data['is_supplier_delivery'] = $params['is_supplier_delivery'] ?? 0;
        $data['is_presell'] = $params['is_presell'] ?? 0;

        if ($params['supplier_id'] != $period_info['supplier_id'] || $params['payee_merchant_id'] != $period_info['payee_merchant_id']) {
            // 验证供应商收款商户
            $res = $this->VerifySupplierReceiptMerchant($params['supplier_id'], $params['payee_merchant_id'], $params['periods_type']);
            if ($res !== true) {
                return serviceReturn(false, [], $res);
            }
        }

        #启动事务
        Db::startTrans();
        try {
            Db::name('periods_remark')->insertAll($periods_remark);
            if (isset($params['buyer_id'])) {
                Db::name('periods_buyer')
                ->where([
                    'period' => $params['period'],
                    'buyer_id' => $period_info['buyer_id']
                ])
                ->update(['buyer_id' => $params['buyer_id']]);
            }

            $this->model::where('id', $params['period'])->update($data);
            #提交事务
            Db::commit();
        } catch (\Exception $e) {
            #回滚事务
            Db::rollback();
            return serviceReturn(false, [], $e->getMessage());
        }

        return serviceReturn(true, [], '操作成功');
    }

    /**
     * 根据 id 获取商品信息
     * @param int $id
     * @param string $field
     * @return array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOne(int $id, string $field = '*'): ?array
    {
        $result = $this->model::field($field)->find($id);
        return $result ? $result->toArray() : [];
    }

    /**
     * 添加商品
     * @param array $params
     * @return int
     */
    public function create(array $params): ?int
    {
        $result = $this->model::create($params);
        return $result->id ?? false;
    }

    /**
     *
     * 期数点赞
     * @param array $params
     * @return bool
     */
    public function praise(array $params): bool
    {
        // 增加期数点赞
        $inc = $this->model::where('id', $params['period'])->inc('praise_count')->update();
        // 添加点赞记录
        if ($inc) {
            $data['period'] = $params['period'];
            $data['periods_type'] = $params['periods_type'];
            $data['praise_uid'] = $params['user_id'];
            $data['praise_uname'] = $params['user_name'];
            $data['created_time'] = time();
            $result = PeriodsPraiseRecord::insert($data);
            if (!$result) {
                return false;
            }
        }
        return true;
    }

    /**
     * 随机查询商品
     * @param int $limit
     * @param string $field
     * @return PeriodsCross[]|PeriodsFlash[]|PeriodsLeftover[]|PeriodsSecond[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function recommendList(int $limit = 100, string $field = '*')
    {
        return $this->model::field($field)
            ->whereRaw('(purchased + vest_purchased) > 100')
            ->where([['onsale_status', '=', 2], ['is_channel', '<>', 1]])
            ->order('sell_time', 'desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }

    /**
     * 文案主管审核
     * @param array $params
     * @return PeriodsCross|PeriodsFlash|PeriodsLeftover|PeriodsSecond
     */
    public function copyWriterReview(array $params)
    {
        // 文案审核状态
        $r_data['copywriting_review_status'] = $params['copywriting_review_status'];
        if ($params['copywriting_review_status'] == 2) {
            $r_data['buyer_review_status'] = 2;
        }

        $result = $this->model::where('id', $params['period'])
            ->update($r_data);

        // 添加状态操作日志
        $scr_data['type'] = 4;
        $scr_data['status_type'] = $params['copywriting_review_status'];
        $scr_data['status_name'] = $this->copywriting_review_status[$params['copywriting_review_status']];
        $scr_data['operator_id'] = $params['user_id'];
        $scr_data['operator_name'] = $params['user_name'];
        $scr_data['period'] = $params['period'];
        $scr_data['periods_type'] = $this->type;
        $scr_data['describe'] = $params['reject_reason'] ?? '';
        $scr_data['remark'] = $params['remark'] ?? '';
        $scr_data['created_time'] = date('Y-m-d H:i:s', time());
        PeriodsStatusChangeRecord::create($scr_data);
        return $result;
    }

    /**
     * 文案提交主管审核
     * @param array $params
     * @return PeriodsCross|PeriodsFlash|PeriodsLeftover|PeriodsSecond
     */
    public function submitCopyWriter(array $params)
    {
        // 文案审核状态
        $r_data['copywriting_review_status'] = 1;
        $r_data['buyer_review_status'] = 2;
        if ($this->type == 9) {
            $r_data['onsale_review_status'] = 1;
            $r_data['buyer_review_status'] = 3;
        }

        $result = $this->model::where('id', $params['period'])
            ->update($r_data);

        // 添加状态操作日志
        $scr_data['type'] = 4;
        $scr_data['status_type'] = 1;
        $scr_data['status_name'] = $this->copywriting_review_status[1];
        $scr_data['operator_id'] = $params['user_id'];
        $scr_data['operator_name'] = $params['user_name'];
        $scr_data['period'] = $params['period'];
        $scr_data['periods_type'] = $this->type;
        $scr_data['describe'] = $params['reject_reason'] ?? '';
        $scr_data['remark'] = $params['remark'] ?? '';
        $scr_data['created_time'] = date('Y-m-d H:i:s', time());
        PeriodsStatusChangeRecord::create($scr_data);
        return $result;
    }

    /**
     * 添加拼团
     * @param array $data
     * @return array
     */
    public function addGroup(array $data): array
    {
        $group_price_arr = json_decode($data['group_price'], true);
        $data['group_price'] = json_encode($group_price_arr);
        if ($data['share_status'] == 1) {
            PeriodsGroup::where('share_status', 1)->update(['share_status' => 0]);
        }
        $result = PeriodsGroup::create($data);
        // 添加成功更新es期数数据
        if (empty($result)) {
            return serviceReturn(false, [], '添加失败');
        }
        // 更新es拼团价格
        try {
            $group_price_arr = json_decode($data['group_price'], true);
            $group_price = $group_price_arr[0]['price'] ?? 0;
            if ($group_price <= 0) {
                return serviceReturn(false, [], '拼团价格格式错误');
            }
            $es = new ElasticSearchConnection();
            $params = [
                'index' => 'vinehoo.periods',
                'type' => '_doc',
                'id' => (int)$data['period'],
                'body' => [
                    'doc' => [
                        'group_price' => $group_price
                    ]
                ]
            ];
            $es->connection()->update($params);
        } catch (Elasticsearch\Common\Exceptions\TransportException $e) {
            return serviceReturn(false, [], $e->getMessage());
        }
        return serviceReturn(true, $result, '添加成功');
    }

    /**
     * 添加新人
     * @param array $data
     * @return array
     */
    public function addNewcomer(array $data): array
    {
        $newcomer_price_arr = json_decode($data['newcomer_price'], true);
        if (!empty($newcomer_price_arr)) {
            foreach ($newcomer_price_arr as &$v) {
                if (!empty($v)) {
                    $v['price'] = (float)$v['price'];
                }
            }
        }
        $newcomer_price = $newcomer_price_arr[0]['price'] ?? 0;
        if ($newcomer_price <= 0) {
            return serviceReturn(false, [], '新人价格格式错误');
        }
        $data['newcomer_price'] = json_encode($newcomer_price_arr);
        $result = PeriodsNewcomer::create($data);
        // 添加成功更新es期数数据
        if (empty($result)) {
            return serviceReturn(false, [], '添加失败');
        }
        // 更新es新人价格
        try {
            $es = new ElasticSearchConnection();
            $params = [
                'index' => 'vinehoo.periods',
                'type' => '_doc',
                'id' => (int)$data['period'],
                'body' => [
                    'doc' => [
                        'newcomer_price' => $newcomer_price
                    ]
                ]
            ];
            $result = $es->connection()->update($params);
        } catch (Elasticsearch\Common\Exceptions\TransportException $e) {
            return serviceReturn(false, [], $e->getMessage());
        }
        return serviceReturn(true, $result, '添加成功');
    }

    /**
     * 更新拼团
     * @param array $data
     * @return PeriodsGroup
     */
    public function upGroup(array $data): PeriodsGroup
    {
        if ($data['share_status'] == 1) {
            PeriodsGroup::where('share_status', 1)->where('id','!=',$data['id'] )->update(['share_status' => 0]);
        }
        return PeriodsGroup::update($data);
    }

    /**
     * 更新新人
     * @param array $data
     * @return PeriodsNewcomer
     */
    public function upNewcomer(array $data): PeriodsNewcomer
    {
        return PeriodsNewcomer::update($data);
    }

    /**
     * 拼团列表
     * @param array $data
     * @return mixed
     */
    public function getGroupList(array $data)
    {
        $where = [];
        // 期数搜索
        if (isset($data['period']) && $data['period'] != '') {
            $where[] = ['b.period', '=', $data['period']];
        }
        // 标题搜索
        if (isset($data['title']) && $data['title'] != '') {
            $where[] = ['a.title', 'like', '%' . $data['title'] . '%'];
        }
        // 上下架搜索
        if (isset($data['onsale_status']) && $data['onsale_status'] != '') {
            $where[] = ['a.onsale_status', '=', $data['onsale_status']];
        }
        // 是否邀请新人
        if (isset($data['is_invite']) && $data['is_invite'] != '') {
            $where[] = ['b.is_invite', '=', $data['is_invite']];
        }
        // 期数频道搜索
        if (isset($data['periods_type']) && $data['periods_type'] != '') {
            $where[] = ['b.periods_type', '=', $data['periods_type']];
        }
        // 商家 id 搜索
        if (isset($data['mid']) && $data['mid'] != '') {
            $where[] = ['a.supplier_id', '=', $data['mid']];
        }
        return $this->model::alias('a')
            ->join('periods_group b', 'a.id = b.period')
            ->field('b.*, a.title, a.onsale_status, a.price')
            ->where($where)
            ->paginate([
                'list_rows' => $data['limit']
            ]);
    }

    /**
     * 新人列表
     * @param array $data
     * @return mixed
     */
    public function getNewcomerList(array $data)
    {
        $where = [];
        // 期数搜索
        if (isset($data['period']) && $data['period'] != '') {
            $where[] = ['b.period', '=', $data['period']];
        }
        // 标题搜索
        if (isset($data['title']) && $data['title'] != '') {
            $where[] = ['a.title', '=', $data['title']];
        }
        // 上下架搜索
        if (isset($data['onsale_status']) && $data['onsale_status'] != '') {
            $where[] = ['a.onsale_status', '=', $data['onsale_status']];
        }
        // 是否邀请新人
        if (isset($data['is_invite']) && $data['is_invite'] != '') {
            $where[] = ['b.is_invite', '=', $data['is_invite']];
        }
        // 模块搜索
        if (isset($data['module']) && $data['module'] != '') {
            $where[] = ['b.module', '=', $data['module']];
        }
        return $this->model::alias('a')
            ->join('periods_newcomer b', 'a.id = b.period')
            ->field('b.*, a.title, a.onsale_status, a.price, a.brief, a.banner_img, a.market_price')
            ->where($where)
            ->paginate();
    }

    /**
     * 查询拼团套餐详细
     * @param array $params
     * @param array $where
     * @return PeriodsGroup|array|Model
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getGroupInfo(array $params, array $where)
    {
        $group = PeriodsGroup::where($where)->find();
        if (empty($group)) {
            return [];
        }
        // 选择套餐，返回套餐数据
        if (isset($params['package_id']) && $params['package_id'] != '') {
            $package = json_decode($group['group_price'], true);
            foreach ($package as $v) {
                if ($v['package_id'] == $params['package_id']) {
                    $group['package_price'] = (float)$v['price'];
                }
            }
        }
        return $group;
    }

    /**
     * 查询新人套餐详细
     * @param array $params
     * @param array $where
     * @return PeriodsNewcomer|array|Model
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getNewcomerInfo(array $params, array $where)
    {
        $newcomer = PeriodsNewcomer::where($where)->find();
        if (empty($newcomer)) {
            return [];
        }
        // 选择套餐，返回套餐数据
        if (isset($params['package_id']) && $params['package_id'] != '') {
            $package = json_decode($newcomer['newcomer_price'], true);
            foreach ($package as $v) {
                if ($v['package_id'] == $params['package_id']) {
                    $newcomer['package_price'] = $v['price'];
                }
            }
        }
        return $newcomer;
    }

    /**
     * 删除新人
     * @param int $id
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function delNewComer(int $id): bool
    {
        $newcomer = PeriodsNewcomer::where('id', $id)->find();
        if ($newcomer) {
            // 更新期数营销状态
            $this->type = (int)$newcomer['periods_type'];
            $period_info = $this->getOne((int)$newcomer['period'], 'marketing_attribute');
            if (!empty($period_info)) {
                if (strpos($period_info['marketing_attribute'], ',2') !== false) {
                    $period_info['marketing_attribute'] = strtr($period_info['marketing_attribute'], array(',2' => ''));
                    // 更新期数营销属性
                    $this->update((int)$newcomer['period'], ['marketing_attribute' => $period_info['marketing_attribute']]);
                }
            }
        }
        // 删除新人
        return PeriodsNewcomer::where('id', $id)->delete();
    }

    /**
     * 删除拼团期数
     * @param int $id
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function delGroup(int $id): bool
    {
        $group = PeriodsGroup::where('id', $id)->find();
        if ($group) {
            // 更新期数营销状态
            $this->type = (int)$group['periods_type'];
            $period_info = $this->getOne((int)$group['period'], 'marketing_attribute');
            if (!empty($period_info)) {
                if (strpos($period_info['marketing_attribute'], ',1') !== false) {
                    $period_info['marketing_attribute'] = strtr($period_info['marketing_attribute'], array(',1' => ''));
                    // 更新期数营销属性
                    $this->update((int)$group['period'], ['marketing_attribute' => $period_info['marketing_attribute']]);
                }
            }
        }
        // 删除拼团
        return PeriodsGroup::where('id', $id)->delete();
    }

    /**
     * 更新期数单个字段
     * @param int $period
     * @param string $field
     * @param string $value
     * @return PeriodsCross|PeriodsFlash|PeriodsLeftover|PeriodsRabbit|PeriodsRabbitCoupon|PeriodsSecond
     */
    public function updateField(int $period, string $field, string $value)
    {
        return $this->model::where('id', $period)->update([$field => $value]);
    }

    /**
     * 更新期数信息
     * @param int $period
     * @param array $update
     * @return PeriodsCross|PeriodsFlash|PeriodsLeftover|PeriodsRabbit|PeriodsRabbitCoupon|PeriodsSecond
     */
    public function updateInfo(int $period,array $update)
    {
        return $this->model::where('id', $period)->update($update);
    }

    /**
     * 自增期数单个字段
     * @param int $period
     * @param string $field
     * @param string $value
     * @return PeriodsCross|PeriodsFlash|PeriodsLeftover|PeriodsRabbit|PeriodsRabbitCoupon|PeriodsSecond
     */
    public function incField(int $period, string $field, int $value)
    {
        return $this->model::where('id', $period)
            ->inc($field, $value)
            ->update();
    }


    /**
     * 期数上架获取期数详细钉钉消息消息推送
     * @param int $period
     * @return mixed|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function onSaleDingdingPush(int $period)
    {
        // 期数信息
        $period_info = $this->getOne($period);
        $url = env('ITEM.H5_URL');
        if (empty($period_info)) {
            return "";
        }
        // 产品信息
        $product = PeriodsProductInventory::where([
            ['period', '=', $period],
            ['product_id', 'in', $period_info['product_id']],
        ])
            ->field('id, product_name,en_product_name,inventory,warehouse')
            ->select()
            ->toArray();

        // 套餐信息
        $package_ser = new Package($this->type);
        $package = $package_ser->getPackage(['period_id' => $period, 'is_hidden' => 0]);

        //拿到卡片名称和栏目名称
        $cards = Db::table('vh_marketing.vh_card_goods_live')->alias('g')
            ->leftJoin('vh_marketing.vh_card c', 'g.cid = c.id')
            ->where([
                ['g.type', '=', 1],
                ['g.status', '=', 1],
                ['c.status', '=', 1],
                ['g.relation_id', '=', $period],
            ])
            ->field('g.*,c.card_name')
            ->column('card_name');
        $cardsStr = implode(',', $cards);
        $columns = Db::table('vh_marketing.vh_column_goods')->alias('g')
            ->leftJoin('vh_marketing.vh_column c', 'g.cid = c.id')
            ->where([
                ['g.status', '=', 1],
                ['c.status', '=', 1],
                ['g.period', '=', $period],
            ])
            ->field('g.*,c.name')
            ->column('name');
        $columnsStr = implode(',', $columns);
        $isChannel = $period_info['is_channel'] == 0 ? '否' : '是';

        // 推送内容字符串
        $str = "## **上架商品信息**
- 期数：{$period}/{$this->type_name[$this->type]}/{$this->import_type_name[$period_info['import_type']]}
- 人员：{$period_info['creator_name']}/{$period_info['buyer_name']}/{$period_info['operation_review_name']}
- 上架时间：{$period_info['onsale_time']}；发货时间：{$period_info['predict_shipment_time']}
- 标题：[{$period_info['title']}]({$url}/pages/goods-detail/goods-detail?id={$period}&from=3)
- 渠道销售：{$isChannel}
- 供应商：{$period_info['supplier']}，主体：{$period_info['payee_merchant_name']}";
        // 闪购新增收款商户
        if ($this->type == 0 || $this->type == 3) {
            if ($period_info['payee_merchant_id'] == 1) {
                $str .= "
- 收款账户： <font color=\"warning\">{$period_info['payee_merchant_name']}</font>";
            } else {
                $str .= "
- 收款账户： <font color=\"info\">{$period_info['payee_merchant_name']}</font>";
            }
        }
        // 产品信息
        foreach ($product as $key => $val) {
            $k = $key + 1;
            $str .= "
- 期数产品信息（{$k}）
  > - 中文名：{$val['product_name']}
  > - 英文名：{$val['en_product_name']}
  > - 库存：  {$val['inventory']}
  > - 仓库：  {$val['warehouse']}
 ";
        }
        unset($key, $val, $k);
        // 套餐信息
        foreach ($package as $key => $value) {
            $k = $key + 1;
            $limit = $value['inventory'];
            if ($value['unlimited'] == 1) {
                $limit = '不限量';
            }
            $str .= "
- 套餐信息（{$k}）
  > - 套餐名称：{$value['package_name']}
  > - 套餐价格：{$value['price']}
  > - 套餐数量：{$limit}
 ";
        }

        // markdown 详情
        $markdown['title'] = '期数上架通知';
        $markdown['text'] = $str;
        $markdown_str = base64_encode(json_encode($markdown));

        // -- 开始推送
        // 推送地址
        $queue_url = env('item.QUEUE_URL');
        // 请求头
        $url_header[] = 'vinehoo-client: tp6-commodities';
        $url_header[] = 'Content-Type: application/json;charset=utf-8';
        // 钉钉请求参数
        $test_access_token = '39632e0ac0429b55d1d1d9b5c3128a015cf31466fccac558dc2a1c81bfbf23c4';
        $d_json['access_token'] = env('COMMODITIES.ONSALE_DINGTALK_TOKEN') ?? $test_access_token;
        $d_json['type'] = 'markdown';
        $d_json['at'] = '';
        $d_json['content'] = $markdown_str;
        $d_str = base64_encode(json_encode($d_json));
        // 推送请求参数
        $push_data['exchange_name'] = 'dingtalk';
        $push_data['routing_key'] = 'dingtalk_sender';
        $push_data['data'] = $d_str;
        $p_data = json_encode($push_data);
        $result = post_url($queue_url, $p_data, $url_header);
        return json_decode($result, true);
    }

    /**
     * 期数更新收款账户
     * @param int $period
     * @return mixed|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updatePayeeMerchantDingdingPush(int $period)
    {
        // 期数信息
        $period_info = $this->getOne($period);
        $url = env('ITEM.H5_URL');
        if (empty($period_info)) {
            return "";
        }
        // 产品信息
        $product = PeriodsProductInventory::where([
            ['period', '=', $period],
            ['product_id', 'in', $period_info['product_id']],
        ])
            ->field('id, product_name,en_product_name,inventory,warehouse')
            ->select()
            ->toArray();

        //拿到卡片名称和栏目名称
        $cards = Db::table('vh_marketing.vh_card_goods_live')->alias('g')
            ->leftJoin('vh_marketing.vh_card c', 'g.cid = c.id')
            ->where([
                ['g.type', '=', 1],
                ['g.status', '=', 1],
                ['c.status', '=', 1],
                ['g.relation_id', '=', $period],
            ])
            ->field('g.*,c.card_name')
            ->column('card_name');
        $cardsStr = implode(',', $cards);
        $columns = Db::table('vh_marketing.vh_column_goods')->alias('g')
            ->leftJoin('vh_marketing.vh_column c', 'g.cid = c.id')
            ->where([
                ['g.status', '=', 1],
                ['c.status', '=', 1],
                ['g.period', '=', $period],
            ])
            ->field('g.*,c.name')
            ->column('name');
        $columnsStr = implode(',', $columns);

        // 套餐信息
        //$package_ser = new Package($this->type);
        //$package = $package_ser->getPackage(['period_id' => $period, 'is_hidden' => 0]);
        // 推送内容字符串
        $str = "## **商品添加/更新信息**
- 期数：{$period}/{$this->type_name[$this->type]}/{$this->import_type_name[$period_info['import_type']]}
- 人员：{$period_info['creator_name']}/{$period_info['buyer_name']}/{$period_info['operation_review_name']}
- 上架时间：{$period_info['onsale_time']}；发货时间：{$period_info['predict_shipment_time']}
- 标题：[{$period_info['title']}]({$url}/pages/goods-detail/goods-detail?id={$period}&from=3)
- 营销：卡片({$cardsStr})，栏目({$columnsStr})
- 供应商：{$period_info['supplier']}，主体：{$period_info['payee_merchant_name']}";
        // 闪购新增收款商户
        if ($this->type == 0 || $this->type == 3) {
            if ($period_info['payee_merchant_id'] == 1) {
                $str .= "
- 收款账户： <font color=\"warning\">{$period_info['payee_merchant_name']}</font>";
            } else {
                $str .= "
- 收款账户： <font color=\"info\">{$period_info['payee_merchant_name']}</font>";
            }
        }
        // 产品信息
        foreach ($product as $key => $val) {
            $k = $key + 1;
            $str .= "
- 期数产品信息（{$k}）
  > - 中文名：{$val['product_name']}
  > - 英文名：{$val['en_product_name']}
  > - 库存：  {$val['inventory']}
  > - 仓库：  {$val['warehouse']}
 ";
        }
        unset($key, $val, $k);
        // markdown 详情
        $markdown['title'] = '期数上架通知';
        $markdown['text'] = $str;
        $markdown_str = base64_encode(json_encode($markdown));

        // -- 开始推送
        // 推送地址
        $queue_url = env('item.QUEUE_URL');
        // 请求头
        $url_header[] = 'vinehoo-client: tp6-commodities';
        $url_header[] = 'Content-Type: application/json;charset=utf-8';
        // 钉钉请求参数
        $test_access_token = '97ef22ab-8330-47a7-ba5c-396fc91c53cf';
        $d_json['access_token'] = env('COMMODITIES.RECEIVER') ?? $test_access_token;
//        $d_json['access_token'] = $test_access_token;
        $d_json['type'] = 'markdown';
        $d_json['at'] = '';
        $d_json['content'] = $markdown_str;
        $d_str = base64_encode(json_encode($d_json));
        // 推送请求参数
        $push_data['exchange_name'] = 'dingtalk';
        $push_data['routing_key'] = 'dingtalk_sender';
        $push_data['data'] = $d_str;
        $p_data = json_encode($push_data);
        $result = post_url($queue_url, $p_data, $url_header);
        return json_decode($result, true);
    }

    /**
     * 自增期数浏览量
     * @param array $params
     * @return mixed
     */
    public function incPageviews(array $params)
    {
        // 添加每小时期数浏览记录
        // $period = $this->model::where('id', $params['period'])->field('title,onsale_time,price')->find();
        // if ($period) {
        //     $datetime = date('Y-m-d H:00:00'); // 获取当前时间，并将分钟和秒数设置为0
        //     $timestamp = strtotime($datetime); // 将日期时间字符串转换为时间戳
        //     // 当前小时是否已存在本期数浏览量
        //     $pv = PeriodsPageviews::where(['period' => $params['period'], 'time' => $timestamp])->value('id');
        //     if ($pv) {
        //         PeriodsPageviews::where('id', $pv)
        //             ->inc('pageviews')
        //             ->update([
        //                 'title' => $period['title'],
        //                 'onsale_time' => strtotime($period['onsale_time']),
        //                 'price' => $period['price']
        //             ]);
        //     } else {
        //         $p['period'] = $params['period'];
        //         $p['periods_type'] = $this->type;
        //         $p['time'] = $timestamp;
        //         $p['title'] = $period['title'];
        //         $p['pageviews'] = 1;
        //         $p['onsale_time'] = strtotime($period['onsale_time']);
        //         $p['price'] = $period['price'];
        //         PeriodsPageviews::insert($p);
        //     }
        // }
        //添加浏览记录
        $redis_config = config('cache.stores.redis');
        $redis_config['select'] = 0;
        $conn = new \think\cache\driver\Redis($redis_config);
        return $conn->rpush('vinehoo.period.pageviews', $params['period'] . '_' . $this->type);

        // return $this->model::where('id', $params['period'])->inc('pageviews')->update();
    }

    /**
     * 增加期数浏览量
     * @param array $params
     * @return mixed
     */
    public function updatePageviews($period_id, $pageviews)
    {
        // 添加每小时期数浏览记录
        $period = $this->model::where('id', $period_id)->field('title,onsale_time,price')->find();
        if ($period) {
            $datetime = date('Y-m-d H:00:00'); // 获取当前时间，并将分钟和秒数设置为0
            $timestamp = strtotime($datetime); // 将日期时间字符串转换为时间戳
            // 当前小时是否已存在本期数浏览量
            $pv = PeriodsPageviews::where(['period' => $period_id, 'time' => $timestamp])->value('id');
            if ($pv) {
                PeriodsPageviews::where('id', $pv)
                    ->inc('pageviews', $pageviews)
                    ->update([
                        'title' => $period['title'],
                        'onsale_time' => strtotime($period['onsale_time']),
                        'price' => $period['price']
                    ]);
            } else {
                $p['period'] = $period_id;
                $p['periods_type'] = $this->type;
                $p['time'] = $timestamp;
                $p['title'] = $period['title'];
                $p['pageviews'] = $pageviews;
                $p['onsale_time'] = strtotime($period['onsale_time']);
                $p['price'] = $period['price'];
                PeriodsPageviews::insert($p);
            }
        }
        
        //添加浏览记录
        return $this->model::where('id', $period_id)->inc('pageviews', $pageviews)->update();
    }

    /**
     * 浏览量查询
     * @param $params
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     */
    public function getPageviewsByHours($params): \think\Paginator
    {
        $where = [];
        // 查询时间
        if (isset($params['st']) && $params['st'] != '') {
            if (isset($params['et']) && $params['et'] != '') {
                $where[] = ['time', '>=', strtotime($params['st'])];
                $where[] = ['time', '<=', strtotime($params['et'])];
            }
        }
        // 查询频道
        if (isset($params['periods_type']) && $params['periods_type'] != '') {
            $where[] = ['periods_type', '=', $params['periods_type']];
        }
        // 销售状态
        if (!empty($params['sale_status'])) {
            //查询在售期数
            $es_period = Es::name('periods')
                ->where([['onsale_status','=','2']])
                ->field('id')
                ->select()->toArray();
            $sale_period = array_unique(array_column($es_period, 'id'));
            
            switch (intval($params['sale_status'])) {
                case 1://在售
                    $where[] = ['period', 'in', $sale_period];
                    break;
                default://非在售
                    $where[] = ['period', 'not in', $sale_period];
            }
        }

        $limit = 15;
        if (isset($params['limit']) && $params['limit'] != '') {
            $limit = $params['limit'];
        }
        $where[] = ['onsale_time', '<>', 0];
        $sort = [];
        if (isset($params['sort_key']) && $params['sort_key'] != '') {
            $sort = json_decode($params['sort_key'], true);
        }
        // 返回指定小时范围浏览量及订单数
        return PeriodsPageviews::where($where)
            ->field('id,period,periods_type, SUM(`pageviews`) as pageviews, time, title, onsale_time, price, 
            SUM(`order_user`) as order_user, SUM(`order_price_sum`) as order_price_sum, 
            ROUND(SUM(order_user) / SUM(pageviews), 4) * 100 as rate')
            ->group('period')
            ->order($sort)
            ->paginate([
                'list_rows' => $limit,
                'var_page' => 'page',
            ])->map(function($item){
                $item->rate = floatval($item->rate);
                return $item;
            });
    }

    /**
     * 浏览量按天查询
     * @param $params
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     */
    public function getPageviewsByDays($params)
    {
        $where = [];
        // 查询时间
        if (isset($params['st']) && $params['st'] != '') {
            if (isset($params['et']) && $params['et'] != '') {
                $where[] = ['time', '>=', strtotime($params['st'])];
                $where[] = ['time', '<=', strtotime($params['et'])];
            }
        }
        // 查询频道
        if (isset($params['period']) && $params['period'] != '') {
            $where[] = ['period', '=', $params['period']];
        }
        $limit = 15;
        if (isset($params['limit']) && $params['limit'] != '') {
            $limit = $params['limit'];
        }
        // 返回指定小时范围浏览量及订单数
        return PeriodsPageviews::where($where)
            ->field("DATE_FORMAT(FROM_UNIXTIME(time), '%Y-%m-%d') as time, SUM(`pageviews`) as pageviews, 
            periods_type, period, SUM(`order_user`) as order_user, order_price_sum, rate")
            ->group("DATE_FORMAT(FROM_UNIXTIME(time), '%Y-%m-%d')")
            ->order('time', 'asc')
            ->select();
    }

    /**
     * 统计期数小时浏览量
     * @param $params
     * @return \think\Paginator
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPeriodPageviewsByHours($params): \think\Paginator
    {
        $where = [];
        // 查询时间
        if (isset($params['st']) && $params['st'] != '') {
            if (isset($params['et']) && $params['et'] != '') {
                $where[] = ['time', '>=', strtotime($params['st'])];
                $where[] = ['time', '<=', strtotime($params['et'])];
            }
        }
        // 查询频道
        if (isset($params['period']) && $params['period'] != '') {
            $where[] = ['period', '=', $params['period']];
        }
        $limit = 15;
        if (isset($params['limit']) && $params['limit'] != '') {
            $limit = $params['limit'];
        }
        // 返回指定小时范围浏览量及订单数
        return PeriodsPageviews::where($where)
            ->field('id,period,periods_type, pageviews, time, title, onsale_time, price,order_user, 
            order_price_sum, rate')
            ->order('time')
            ->paginate([
                'list_rows' => $limit,
                'var_page' => 'page',
            ])->map(function ($item) {
                $item->time = date('Y-m-d H', $item->time);
                return $item;
            });
    }


    /**
     * 统计期数时间段订单数据
     * @param $table
     * @param $period
     * @param $params
     * @return array|mixed|\think\db\BaseQuery|Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function orderSta($table, $period, $params)
    {
        return Db::connect('orders')->table($table)
            ->field('COUNT(DISTINCT `uid`) as order_user, SUM(`payment_amount`) as order_price_sum')
            ->where([
                ['period', '=', $period],
                ['sub_order_status', 'in', '1,2,3']
            ])
            ->whereBetween('created_time', [strtotime($params['st']), strtotime($params['et'])])
            ->find();
    }
    
    /**
     * 添加分享期数访问记录
     * @param array $params
     * @return int|string
     */
    public function createShareRecord(array $params)
    {
        return Db::name('periods_share_record')->insert($params);
    }

    /**
     * 商家库存变更上下架
     * @param array $params
     * @return PeriodsCross|PeriodsFlash|PeriodsLeftover|PeriodsSecond
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function vmallInventorySaleStatus(array $params)
    {
        $params['inventory'] = (int)$params['inventory'];
        $period_info = $this->getOne($params['period'], 'onsale_status,sellout_sold_out,sold_out_time,
            off_sell_type');
        $sold_out_time = strtotime($period_info['sold_out_time']);
        $onsale_status = $period_info['onsale_status'];
        $off_sell_type = $period_info['off_sell_type'];
        // 库存不足下架
        if ($params['inventory'] <= 0) {
            if ($sold_out_time > time()) {
                $onsale_status = 4;
            } else {
                $onsale_status = 3;
            }
            $data['off_sell_type'] = 3;
            $data['off_sell_time'] = time();
        } elseif ($params['inventory'] > 0) {
            // 售空下架才上架
//            if ($sold_out_time > time() && $off_sell_type == 3) {
            if ($off_sell_type == 3) {
                $onsale_status = 2;
            }
        }
        $data['onsale_status'] = $onsale_status;
        // 更新期数状态
        return $this->updateInfoById((int)$params['period'], $data);
    }

    /**
     * 方法描述：导入商品数据
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2022/12/12 11:02
     * @param $userId //操作人id
     * @param $userName //操作人名称
     * @param $flashData //闪购数据
     * @param $secondData //秒发数据
     * @param $crossData //跨境数据
     * @param $leftoverData //尾货数据
     * @return bool|string //成功返回true  失败返回失败信息字符串
     */
    public function importByExcel($userId, $userName, $flashData, $secondData, $crossData, $leftoverData)
    {
        $sev = new Package(99999);
        $products_data = $periods_data = $imports_data = [];
        //拿到所有期数
        $oerates= [];
        try {
            //如果慢 这里可以优化成一条sql 内连查询需要的字段 在根据类型取出
            if (count($flashData) > 0) {
                $flashIds = array_unique(array_column($flashData, 'T'));
                $flashById = PeriodsFlash::where('id', 'in', $flashIds)->where('onsale_status', 0)->column('*', 'id');
                foreach ($flashData as $v) {
                    if (empty($flashById[$v['T']])) {
                        return '导入的闪购期数不存在，期数：' . $v['T'];
                    }
                    if (!empty($flashById[$v['T']]['payee_merchant_id']) && $v['payee_merchant_id'] != $flashById[$v['T']]['payee_merchant_id']) {
                        return '收款商户与采购提交不一致，期数：' . $v['T'];
                    }
                    $periods_data[$v['T']] = $flashById[$v['T']];
                    $imports_data[$v['T']][] = $v;
                }
            }
            if (count($secondData) > 0) {
                $secondIds = array_unique(array_column($secondData, 'T'));
                $secondById = PeriodsSecond::where('id', 'in', $secondIds)->where('onsale_status', 0)->column('*', 'id');
                foreach ($secondData as $v) {
                    if (empty($secondById[$v['T']])) {
                        return '导入的秒发期数不存在，期数：' . $v['T'];
                    }
                    if (!empty($secondById[$v['T']]['payee_merchant_id']) && $v['payee_merchant_id'] != $secondById[$v['T']]['payee_merchant_id']) {
                        return '收款商户与采购提交不一致，期数：' . $v['T'];
                    }
                    $periods_data[$v['T']] = $secondById[$v['T']];
                    $imports_data[$v['T']][] = $v;
                }
            }
            if (count($crossData) > 0) {
                $crossIds = array_unique(array_column($crossData, 'T'));
                $crossById = PeriodsCross::where('id', 'in', $crossIds)->where('onsale_status', 0)->column('*', 'id');
                foreach ($crossData as $v) {
                    if (empty($crossById[$v['T']])) {
                        return '导入的跨境期数不存在，期数：' . $v['T'];
                    }
                    if (!empty($crossById[$v['T']]['payee_merchant_id']) && $v['payee_merchant_id'] != $crossById[$v['T']]['payee_merchant_id']) {
                        return '收款商户与采购提交不一致，期数：' . $v['T'];
                    }
                    $periods_data[$v['T']] = $crossById[$v['T']];
                    $imports_data[$v['T']][] = $v;
                }
            }
            if (count($leftoverData) > 0) {
                $leftoverIds = array_unique(array_column($leftoverData, 'T'));
                $leftoverById = PeriodsLeftover::where('id', 'in', $leftoverIds)->where('onsale_status', 0)->column('*', 'id');
                foreach ($leftoverData as $v) {
                    if (empty($leftoverById[$v['T']])) {
                        return '导入的尾货期数不存在，期数：' . $v['T'];
                    }
                    if (!empty($leftoverById[$v['T']]['payee_merchant_id']) && $v['payee_merchant_id'] != $leftoverById[$v['T']]['payee_merchant_id']) {
                        return '收款商户与采购提交不一致，期数：' . $v['T'];
                    }
                    $periods_data[$v['T']] = $leftoverById[$v['T']];
                    $imports_data[$v['T']][] = $v;
                }
            }
            // 参数
            $all = array_merge($flashData, $secondData, $crossData, $leftoverData);


            //产品库存信息
            $period_ids = array_unique(array_column($all, 'T'));
            $periods_product_inventory = Db::name('periods_product_inventory')
                ->whereIn('period', $period_ids)
                ->column('id,period,product_id,short_code,warehouse_id');
            $pro_ids = $pro_info = [];
            foreach ($periods_product_inventory as $v) {
                $pro_ids["{$v['period']}:{$v['product_id']}"] = $v;
                $pro_info[$v['period']][] = $v;
            }

            //拿到所有供应商
            $supplierNames = array_unique(array_column($all, 'G'));
            $suppliersUrl = env('ITEM.WINE_WIKI_URL') . '/wiki/v3/supplier/bulkquery?names=' . implode(',', $supplierNames);
            $suppliersData = $this->httpGet($suppliersUrl);
            if (count($suppliersData) < count($supplierNames)) {
                return '查询不到导入的供应商信息 请检查后再试!';
            }

            $suppliersByName = array_column($suppliersData, null, 'supplier_name');

            //拿到采购信息
            $buyerUrl = env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/specifyList?type=2';
            $buyerData = $this->httpGet($buyerUrl)['list'] ?? [];
            $buyerByName = array_column($buyerData, null, 'realname');
            $oerates = Db::name('buyer_related')->alias('t1')
                ->join('operate_related t2', 't1.operation_review_id=t2.operation_review_id')
                ->where('t1.delete_time', null)
                ->where('t2.delete_time', null)
                ->column('t2.operation_review_id,t2.operation_review_name,t1.buyer_id', 't1.buyer_id');


            //拿到所有产品信息
            $es = $this->getEsClient();
            $productShoreCodes = [];
            foreach (array_column($all, 'S') as $shoreCodes) {
                $productShoreCodes = array_merge($productShoreCodes, explode(',', $shoreCodes));
            }
            $productShoreCodes = array_unique($productShoreCodes);
            $search = $es->setIndex('vinehoo.panshi.products')
                ->setScrollSize(count($productShoreCodes));
            foreach ($productShoreCodes as $shoreCode) {
                $search = $search->setShouldTerm('short_code', $shoreCode);
            }
            $products = $search->field(['id', 'cn_product_name', 'en_product_name', 'bar_code', 'short_code', 'capacity', 'is_gift', 'prize', 'product_keywords', 'product_category_name', 'country_name', 'product_type_name', 'corp', 'import_type'])->setScrollSize(10000)->select();
            $productByShortCode = array_column($products['data'], null, 'short_code');
            foreach ($products['data'] as $v) {
                $products_data[$v['short_code']] = $v;
            }

            // 商品导入，收款公司验证
            foreach ($imports_data as $k => $v) {
                // 期数信息
                $periodinfo = $periods_data[$k];
                $period_pmid = $empty_num = 0;
                foreach ($v as $vv) {
                    $supplier_info = $suppliersByName[$vv['G']] ?? [];
                    if (empty($supplier_info)) {
                        return "查询不到导入的供应商信息【{$vv['G']}】";
                    }
                    // 供应商对应的收款主体是否和磐石中的收款主体一致
                    $payee_merchant_code = payeeMerchantIdCodeExchange($vv['payee_merchant_id']);
                    if (empty($supplier_info['corps'][$payee_merchant_code])) {
                        return "供应商对应的收款公司与磐石收款主体不一致，收款公司【{$vv['F']}】，供应商【{$vv['G']}】";
                    }

                    // 自进口导入，如果存货的收款公司与表格填写不一致
                    if (isset($periodinfo['import_type']) && intval($periodinfo['import_type']) === 0) {
                        if ($period_pmid > 0 && $period_pmid != $vv['payee_merchant_id']) {
                            return '自进口商品收款公司不一致，期数：' . $k;
                        }
                        $period_pmid = $vv['payee_merchant_id'];
                        $product = $products_data[$vv['S']] ?? [];
                        if (empty($product)) {
                            return '产品档案查询失败，简码：' . $vv['S'];
                        }
                        if (empty($product['corp'])) {// 公司为空记录未知
                            $empty_num++;

                        } else {
                            $corps = explode(',', $product['corp']);
                            foreach ($corps as $cv) {
                                if (!empty($cv)) {
                                    $pmid = payeeMerchantIdCodeExchange($cv, 1);
                                    if (!empty($pmid) && !empty($period_pmid) && $period_pmid != $pmid) {
                                        return '自进口商品存货的收款公司与表格填写不一致，期数：' . $k;
                                    }
                                } else {// 公司为空记录未知
                                    $empty_num++;
                                }
                            }
                        }
                        if ($empty_num >= 2) {
                            return '自进口商品存货的收款公司与表格填写不一致，有两个及以上存货收款公司未知，期数：' . $k;
                        }
                    }
                }
            }

            //拿到仓库信息
            $warehouses = VirtualWarehouse::select()->each(function (&$data) {
                $data['name'] = $data['physical_name'] . '-' . $data['virtual_name'];
            })->toArray();
            $warehousesByName = array_column($warehouses, null, 'name');

        } catch (\Throwable $e) {
            Log::error('importByExcel: ' . $e->getMessage() . '   :' . $e->getFile() . '  :' . $e->getLine());
            return '导入失败 请稍后再试!';
        }

        //待添加的库存信息
        $productInventoryData = [];
        $flashSetData = [];
        $secondSetData = [];
        $crossSetData = [];
        $leftoverSetData = [];
        $updateProductInventoryData = [];

        //待组装期数数据
        $flashUpdateData = [];
        $secondUpdateData = [];
        $crossUpdateData = [];
        $leftoverUpdateData = [];

        //待添加的备注信息
        $remarks = [];

        //待验证的产品库存数据
        $inIdAndProId = [];//key period_id value [productId1,productId2]

        //数据处理
        foreach ([['type' => 0, 'data' => $flashData], ['type' => 1, 'data' => $secondData], ['type' => 2, 'data' => $crossData], ['type' => 3, 'data' => $leftoverData]] as $genreData) {
            switch ($genreData['type']) {
                case 0:
                    $periods = &$flashById;
                    $set = &$flashSetData;
                    $update = &$flashUpdateData;
                    $setTable = 'periods_flash_set';
                    break;
                case 1:
                    $periods = &$secondById;
                    $set = &$secondSetData;
                    $update = &$secondUpdateData;
                    $setTable = 'periods_second_set';
                    break;
                case 2:
                    $periods = &$crossById;
                    $set = &$crossSetData;
                    $update = &$crossUpdateData;
                    $setTable = 'periods_cross_set';
                    break;
                case 3:
                    $periods = &$leftoverById;
                    $set = &$leftoverSetData;
                    $update = &$leftoverUpdateData;
                    $setTable = 'periods_leftover_set';
                    break;
                default:
                    return '导入类型不支持';
            }

            //待验证的套餐id和名称
            $setIdAndName = [];//key period_id value [name1,name2]

            foreach ($genreData['data'] as $v) {
                if (!isset($suppliersByName[$v['G']])) {
                    return '查询不到导入的供应商信息 请检查后再试';
                }
                if (!isset($buyerByName[$v['U']])) {
                    return '查询不到导入的采购人信息 请检查后再试';
                }
                $s_periods_data = $periods_data[$v['T']] ?? [];
                if (empty($s_periods_data)) {
                    return '期数信息获取失败 请检查后再试';
                }

                //关键词
                $product_keyword = explode(',', $s_periods_data['product_keyword']);
                //商品类别
                $product_category = explode(',', $s_periods_data['product_category']);
                //商品主类
                $product_main_category = explode(',', $s_periods_data['product_main_category']);
                //国家
                $country = explode(',', $s_periods_data['country']);
                //容量
                $capacity = explode(',', $s_periods_data['capacity']);
                //售价
                $price = '';
                //市场价
                $market_price = '';
                //产品id
                $product_id = explode(',', $s_periods_data['product_id']);
                //简码
                $short_code = explode(',', $s_periods_data['short_code']);

                //拿到库存
                $in = explode(',', $v['R']);
                //拿到成本
                $cos = explode(',', $v['M']);
                //拿到仓库
                $ware = $v['E'] ?? '江苏南通仓库-佰酿云酒（南通闪购仓）';
                $s_product_ids = [];
                $s_warehouse_ids = [];

                //组装产品
                foreach (explode(',', $v['S']) as $i => $shortCode) {
                    if (!isset($productByShortCode[$shortCode])) {
                        return '查询不到导入简码:' . $shortCode . ' 的产品数据 请检查无误后再试';
                    }
                    if (!isset($in[$i], $cos[$i], $warehousesByName[$ware])) {
                        return '查询不到导入简码:' . $shortCode . ' 的库存或成本或仓库 请检查无误后再试';
                    }
                    if(!in_array($v['payee_merchant_id'], explode(',', ($warehousesByName[$ware]['company_ids'] ?? [])))){
                        return '导入简码:' . $shortCode . ' 收款公司 与 发货仓库不匹配!';
                    }
                    $s_product_id = $productByShortCode[$shortCode]['id'];
                    $s_product_ids[] = $s_product_id;
                    //添加产品库存信息， 不存在则添加
                    // $pro_id = Db::name('periods_product_inventory')->where([
                    //     'period' => intval($v['T']), 'product_id' => $productByShortCode[$shortCode]['id']
                    // ])->value('id');
                    // if (!$pro_id) {
                    $s_pro_info = $pro_ids["{$v['T']}:{$s_product_id}"] ?? [];
                    if (empty($s_pro_info)) {
                        $productInventoryData[] = [
                            'period' => intval($v['T']),
                            'periods_type' => $genreData['type'],
                            'product_id' => $productByShortCode[$shortCode]['id'],
                            'title' => $periods[intval($v['T'])]['title'],
                            'product_name' => $productByShortCode[$shortCode]['cn_product_name'],
                            'en_product_name' => $productByShortCode[$shortCode]['en_product_name'],
                            'bar_code' => $productByShortCode[$shortCode]['bar_code'],
                            'short_code' => $productByShortCode[$shortCode]['short_code'],
                            'inventory' => $in[$i],
                            'costprice' => $cos[$i],
                            'inventory_accum' => $in[$i],
                            'warehouse' => $warehousesByName[$ware]['name'],
                            'warehouse_id' => $warehousesByName[$ware]['virtual_id'],
                            'erp_id' => $warehousesByName[$ware]['erp_id'],
                            'created_time' => time(),
                            'capacity' => $productByShortCode[$shortCode]['capacity'],
                        ];
                    } else {
                        $updateProductInventoryData[] = [
                            'id' => $s_pro_info['id'],
                            'inventory' => $in[$i],
                            'costprice' => $cos[$i],
                            'inventory_accum' => $in[$i],
                            'warehouse' => $warehousesByName[$ware]['name'],
                            'warehouse_id' => $warehousesByName[$ware]['virtual_id'],
                            'erp_id' => $warehousesByName[$ware]['erp_id'],
                        ];
                    }
                    $s_warehouse_ids[] = intval($warehousesByName[$ware]['virtual_id']);
                    $inIdAndProId[intval($v['T'])][] = $productByShortCode[$shortCode]['id'];

                    $product_keyword[] = $productByShortCode[$shortCode]['product_keywords'];
                    $product_category[] = $productByShortCode[$shortCode]['product_type_name'];
                    $product_main_category[] = $productByShortCode[$shortCode]['product_category_name'];
                    $country[] = $productByShortCode[$shortCode]['country_name'];
                    $capacity[] = $productByShortCode[$shortCode]['capacity'];
                    $product_id[] = $productByShortCode[$shortCode]['id'];
                    $short_code[] = $shortCode;

                    // $product_keyword .= $productByShortCode[$shortCode]['product_keywords'] . ',';
                    // $product_category .= $productByShortCode[$shortCode]['product_type_name'] . ',';
                    // $product_main_category .= $productByShortCode[$shortCode]['product_category_name'] . ',';
                    // $country .= $productByShortCode[$shortCode]['country_name'] . ',';
                    // $capacity .= $productByShortCode[$shortCode]['capacity'] . ',';
                    // $product_id .= $productByShortCode[$shortCode]['id'] . ',';
                }
                // 导入前所有产品
                $s_pro_info = $pro_info[intval($v['T'])] ?? [];
                foreach ($s_pro_info as $v1) {
                    if (!in_array($v1['product_id'], $s_product_ids)) {
                        $s_warehouse_ids[] = intval($v1['warehouse_id']);
                    }
                }
                $s_warehouse_ids = array_values(array_filter(array_unique($s_warehouse_ids)));
                if (count($s_warehouse_ids) != 1) {
                    return '所有绑定的简码仓库必须一致，期数：' . $v['T'];
                }

                //套餐名称
                /**
                 * $packageNames = explode('&', $v['L']);
                 * $packagePrices = explode('&', $v['N']);
                 * $packageMarketPrices = explode('&', $v['O']);
                 * //组装套餐
                 * foreach (explode('&', $v['M']) as $pi => $packages) {
                 * if (!isset($packageNames[$pi])) {
                 * return '套餐名称对应套餐数据';
                 * }
                 * if (!isset($packagePrices[$pi], $packageMarketPrices[$pi])) {
                 * return '套餐售价或套餐市场价 必须对应套餐数据';
                 * }
                 * //拼接套装产品信息
                 * $associated_products = [];
                 * foreach (explode('+', $packages) as $package) {
                 * $pro = explode('*', $package);
                 * if (count($pro) !== 2) {
                 * return '套餐:' . $packageNames[$pi] . ' 的 套餐简码数量未设置';
                 * }
                 * $associated_products[] = [
                 * 'product_id' => $productByShortCode[$pro[0]]['id'],
                 * 'nums' => $pro[1],
                 * 'isGift' => $productByShortCode[$pro[0]]['is_gift'],
                 * ];
                 * }
                 * $set[] = [
                 * 'id' => $sev->getGeneratorID(2),
                 * 'period_id' => intval($v['B']),
                 * 'package_name' => $packageNames[$pi],
                 * 'price' => $packagePrices[$pi],
                 * 'market_price' => $packageMarketPrices[$pi],
                 * 'associated_products' => json_encode($associated_products),
                 * 'created_time' => time(),
                 * 'inventory_accum' => 0
                 * ];
                 * $setIdAndName[intval($v['B'])][] = $packageNames[$pi];
                 * if($pi === 0){
                 * $price = $packagePrices[$pi];
                 * $market_price = $packageMarketPrices[$pi];
                 * }
                 * }
                 * */

                //组装期数
                $update[] = [
                    'id' => intval($v['T']),
                    'import_type' => intval($v['H']),
                    'supplier' => $suppliersByName[$v['G']]['supplier_name'] ?? '',
                    'supplier_id' => $suppliersByName[$v['G']]['id'] ?? 0,
                    'buyer_id' => $buyerByName[$v['U']]['id'],
                    'buyer_name' => $buyerByName[$v['U']]['realname'],
                    'operation_review_id' => $oerates[$buyerByName[$v['U']]['id']]['operation_review_id'] ?? 0,
                    'operation_review_name' => $oerates[$buyerByName[$v['U']]['id']]['operation_review_name'] ?? null,
                    'is_supplier_delivery' => $v['Y'],
                    'is_presell' => $v['Z'],
                    'limit_number' => $v['V'] ?? 6,
                    'invariant_number' => $v['V'] ?? 6,
                    'critical_value' => $v['W'] ?? 3,
                    'incremental' => $v['X'] ?? 3,
                    'onsale_time' => strtotime($v['A']),
                    'sell_time' => empty(strtotime($v['C'])) ? strtotime($v['A']) : strtotime($v['C']),
                    'sold_out_time' => empty(strtotime($v['B'])) ? strtotime("+7 day", strtotime($v['A'])) : strtotime($v['B']),
                    'predict_shipment_time' => empty(strtotime($v['D'])) ? strtotime("+7 day", strtotime($v['A'])) : strtotime($v['D']),
                    'payee_merchant_name' => $v['F'],
                    'payee_merchant_id' => $v['payee_merchant_id'],
                    'quota_rule' => '{"check_addr": 0, "check_level": 0, "check_time": 0, "quota_type": "1", "quota_number": "9999", "register_time": "", "min_level": "", "max_level": "", "addrs": []}',
                    'operation_id' => $userId,
                    'operation_name' => $userName,
                    'short_code' => implode(',', array_values(array_unique($short_code))),//implode(',', explode(',', $v['S'])),
                    'product_keyword' => implode(',', array_values(array_unique($product_keyword))),//substr($product_keyword, 0, -1),
                    'product_category' => implode(',', array_values(array_unique($product_category))),
                    'product_main_category' => implode(',', array_values(array_unique($product_main_category))),
                    'country' => implode(',', array_values(array_unique($country))),
                    'capacity' => implode(',', array_values(array_unique($capacity))),
                    'price' => $price,
                    'market_price' => $market_price,
                    'product_id' => implode(',', array_values(array_unique($product_id))),
                ];

                //组装备注信息
//                if (isset($v['AA']) && !empty($v['AA'])) {
                $remarks[] = [
                    'period' => intval($v['T']),
                    'periods_type' => $genreData['type'],
                    'remark'        => "由采购：-更换到采购：" . ($buyerByName[$v['U']]['realname'] ?? '') . "。",
                    'operator'      => $userId,
                    'operator_name' => $userName,
                    'created_time'  => time(),
                ];
                $remarks[] = [
                    'period' => intval($v['T']),
                    'periods_type' => $genreData['type'],
//                        'remark' => $v['AA'],
                    'remark' => $v['F'] . '，' . $v['G'] . '，' . $v['R'] . '，' . $v['AA'],
                    'operator' => $userId,
                    'operator_name' => $userName,
                    'created_time' => time(),
                ];
//                }
            }

            if (count($setIdAndName) > 0) {
                try {
                    //查询套餐是否重复
                    $count = Db::name($setTable)->where(function (Query $query) use ($setIdAndName) {
                        $or = [];
                        foreach ($setIdAndName as $pid => $packageNames) {
                            $or[] = [['period_id', '=', $pid], ['package_name', 'in', $packageNames]];
                        }
                        $query->whereOr($or);
                    })->count();
                    if ($count !== 0) {
                        return '套餐数据已存在 请检查无误后再试';
                    }
                } catch (\Throwable $e) {
                    Log::error('importByExcelThrowable_package: ' . $e->getMessage() . '   :' . $e->getFile() . '  :' . $e->getLine());
                    return $e->getMessage();
                }
            }
        }

        //验证产品是否存在
//        if (count($inIdAndProId) > 0) {
//            try {
//                $count = Db::name('periods_product_inventory')->where(function (Query $query) use ($inIdAndProId) {
//                    $or = [];
//                    foreach ($inIdAndProId as $pid => $productIds) {
//                        $or[] = [['period', '=', $pid], ['product_id', 'in', $productIds]];
//                    }
//                    $query->whereOr($or);
//                })->count();
//                if ($count !== 0) {
//                    return '已存在相同期数的产品数据 无法导入';
//                }
//            } catch (\Throwable $e) {
//                Log::error('importByExcelThrowable_in: ' . $e->getMessage() . '   :' . $e->getFile() . '  :' . $e->getLine());
//                return $e->getMessage();
//            }
//        }

//        if (count($productInventoryData) > 0) {
        try {
            Db::startTrans();
            //循环修改期数和套餐信息
            foreach ([['type' => 0, 'data' => $flashUpdateData], ['type' => 1, 'data' => $secondUpdateData], ['type' => 2, 'data' => $crossUpdateData], ['type' => 3, 'data' => $leftoverUpdateData]] as $genreData) {
                switch ($genreData['type']) {
                    case 0:
                        $updateTable = 'periods_flash';
                        $setTable = 'periods_flash_set';
                        $setData = &$flashSetData;
                        break;
                    case 1:
                        $updateTable = 'periods_second';
                        $setTable = 'periods_second_set';
                        $setData = &$secondSetData;
                        break;
                    case 2:
                        $updateTable = 'periods_cross';
                        $setTable = 'periods_cross_set';
                        $setData = &$crossSetData;
                        break;
                    case 3:
                        $updateTable = 'periods_leftover';
                        $setTable = 'periods_leftover_set';
                        $setData = &$leftoverSetData;
                        break;
                    default:
                        throw new \Exception('获取不到正确的修改类型');
                }
                //添加套餐信息
                if (count($setData) > 0) {
                    if (Db::name($setTable)->insertAll($setData) !== count($setData)) {
                        throw new \Exception('添加套餐数据失败');
                    }
                }

                //修改期数
                foreach ($genreData['data'] as $v) {
                    //确认id 避免全局修改
                    if (!isset($v['id']) || empty($v['id'])) {
                        throw new \Exception('要修改的期数id不存在');
                    }
                    Db::name($updateTable)->strict(false)->update($v);
                }
            }

            //添加库存
            if (count($productInventoryData) > 0) {
                if (Db::name('periods_product_inventory')->insertAll($productInventoryData) !== count($productInventoryData)) {
                    throw new \Exception('添加库存数据失败');
                }
            }
            // 更新库存
            if (!empty($updateProductInventoryData)) {
                foreach ($updateProductInventoryData as $v) {
                    Db::name('periods_product_inventory')->where(['id' => $v['id']])->update($v);
                }
            }

            //添加备注
            if (count($remarks) > 0) {
                if (Db::name('periods_remark')->insertAll($remarks) !== count($remarks)) {
                    throw new \Exception('添加备注数据失败');
                }
            }

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollback();
            Log::error('importByExcelThrowable: ' . $e->getMessage() . '   :' . $e->getFile() . '  :' . $e->getLine());
            return $e->getMessage();
        }
//        }

        return true;
    }

    public function importCrossByExcel($userId, $userName, $flashData, $secondData, $crossData, $leftoverData)
    {
        $sev = new Package(99999);
        //拿到所有期数
        try {
            //如果慢 这里可以优化成一条sql 内连查询需要的字段 在根据类型取出
            if (count($flashData) > 0) {
                $flashIds = array_unique(array_column($flashData, 'T'));
                $flashById = PeriodsFlash::where('id', 'in', $flashIds)->where('onsale_status', 0)->column('*', 'id');
                if (count($flashData) !== count($flashById)) {
                    return '导入的闪购期数不存在!';
                }
            }
            if (count($secondData) > 0) {
                $secondIds = array_unique(array_column($secondData, 'T'));
                $secondById = PeriodsSecond::where('id', 'in', $secondIds)->where('onsale_status', 0)->column('*', 'id');
                if (count($secondData) !== count($secondById)) {
                    return '导入的秒发期数不存在!';
                }
            }
            if (count($crossData) > 0) {
                $crossIds = array_unique(array_column($crossData, 'T'));
                $crossById = PeriodsCross::where('id', 'in', $crossIds)->where('onsale_status', 0)->column('*', 'id');
                if (count($crossData) !== count($crossById)) {
                    return '导入的跨境期数不存在!';
                }
            }
            if (count($leftoverData) > 0) {
                $leftoverIds = array_unique(array_column($leftoverData, 'T'));
                $leftoverById = PeriodsLeftover::where('id', 'in', $leftoverIds)->where('onsale_status', 0)->column('*', 'id');
                if (count($leftoverData) !== count($leftoverById)) {
                    return '导入的尾货期数不存在!';
                }
            }

            $all = array_merge($flashData, $secondData, $crossData, $leftoverData);

            //拿到所有供应商
            $supplierNames = array_unique(array_column($all, 'G'));
            $suppliersUrl = env('ITEM.WINE_WIKI_URL') . '/wiki/v3/supplier/bulkquery?names=' . implode(',', $supplierNames);
            $suppliersData = $this->httpGet($suppliersUrl);
            if (count($suppliersData) < count($supplierNames)) {
                return '查询不到导入的供应商信息 请检查后再试!';
            }
            
            $suppliersByName = array_column($suppliersData, null, 'supplier_name');

            //拿到采购信息
            $buyerUrl = env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/specifyList?type=2';
            $buyerData = $this->httpGet($buyerUrl)['list'] ?? [];
            $buyerByName = array_column($buyerData, null, 'realname');

            //拿到所有产品信息
            $es = $this->getEsClient();
            $productShoreCodes = [];
            foreach (array_column($all, 'S') as $shoreCodes) {
                $productShoreCodes = array_merge($productShoreCodes, explode(',', $shoreCodes));
            }
            $productShoreCodes = array_unique($productShoreCodes);
            $search = $es->setIndex('vinehoo.panshi.products')
                ->setScrollSize(count($productShoreCodes));
            foreach ($productShoreCodes as $shoreCode) {
                $search = $search->setShouldTerm('short_code', $shoreCode);
            }
            $products = $search->field(['id', 'cn_product_name', 'en_product_name', 'bar_code', 'short_code', 'capacity', 'is_gift', 'prize', 'product_keywords', 'product_category_name', 'country_name', 'product_type_name'])->setScrollSize(10000)->select();
            $productByShortCode = array_column($products['data'], null, 'short_code');

            //拿到仓库信息
            $warehouses = VirtualWarehouse::select()->each(function (&$data) {
                $data['name'] = $data['physical_name'] . '-' . $data['virtual_name'];
            })->toArray();
            $warehousesByName = array_column($warehouses, null, 'name');

        } catch (\Throwable $e) {
            Log::error('importByExcel: ' . $e->getMessage() . '   :' . $e->getFile() . '  :' . $e->getLine());
            return '导入失败 请稍后再试!';
        }

        //待添加的库存信息
        $productInventoryData = [];
        $flashSetData = [];
        $secondSetData = [];
        $crossSetData = [];
        $leftoverSetData = [];

        //待组装期数数据
        $flashUpdateData = [];
        $secondUpdateData = [];
        $crossUpdateData = [];
        $leftoverUpdateData = [];

        //待添加的备注信息
        $remarks = [];

        //待验证的产品库存数据
        $inIdAndProId = [];//key period_id value [productId1,productId2]

        $oerates = Db::name('buyer_related')->alias('t1')
            ->join('operate_related t2', 't1.operation_review_id=t2.operation_review_id')
            ->where('t1.delete_time', null)
            ->where('t2.delete_time', null)
            ->column('t2.operation_review_id,t2.operation_review_name,t1.buyer_id', 't1.buyer_id');

        //数据处理
        foreach ([['type' => 0, 'data' => $flashData], ['type' => 1, 'data' => $secondData], ['type' => 2, 'data' => $crossData], ['type' => 3, 'data' => $leftoverData]] as $genreData) {
            switch ($genreData['type']) {
                case 0:
                    $periods = &$flashById;
                    $set = &$flashSetData;
                    $update = &$flashUpdateData;
                    $setTable = 'periods_flash_set';
                    break;
                case 1:
                    $periods = &$secondById;
                    $set = &$secondSetData;
                    $update = &$secondUpdateData;
                    $setTable = 'periods_second_set';
                    break;
                case 2:
                    $periods = &$crossById;
                    $set = &$crossSetData;
                    $update = &$crossUpdateData;
                    $setTable = 'periods_cross_set';
                    break;
                case 3:
                    $periods = &$leftoverById;
                    $set = &$leftoverSetData;
                    $update = &$leftoverUpdateData;
                    $setTable = 'periods_leftover_set';
                    break;
                default:
                    return '导入类型不支持';
            }

            //待验证的套餐id和名称
            $setIdAndName = [];//key period_id value [name1,name2]

            foreach ($genreData['data'] as $v) {
                $supplier_info = $suppliersByName[$v['G']] ?? [];
                if (empty($supplier_info)) {
                    return "查询不到导入的供应商信息【{$v['G']}】";
                }
                if (!isset($buyerByName[$v['U']])) {
                    return '查询不到导入的采购人信息 请检查后再试';
                }
                
                // 供应商对应的收款主体是否和磐石中的收款主体一致
                $payee_merchant_code = payeeMerchantIdCodeExchange($v['payee_merchant_id']);
                if (empty($supplier_info['corps'][$payee_merchant_code])) {
                    return "供应商对应的收款公司与磐石收款主体不一致，收款公司【{$v['F']}】，供应商【{$v['G']}】";
                }

                //关键词
                $product_keyword = '';
                //商品类别
                $product_category = '';
                //商品主类
                $product_main_category = '';
                //国家
                $country = '';
                //容量
                $capacity = '';
                //售价
                $price = '';
                //市场价
                $market_price = '';
                //产品id
                $product_id = '';

                //拿到库存
                $in = explode(',', $v['R']);
                //拿到成本
                $cos = explode(',', $v['M']);
                //拿到仓库
                $ware = $v['E'] ?? '江苏南通仓库-佰酿云酒（南通闪购仓）';

                //组装产品
                foreach (explode(',', $v['S']) as $i => $shortCode) {
                    if (!isset($productByShortCode[$shortCode])) {
                        return '查询不到导入简码:' . $shortCode . ' 的产品数据 请检查无误后再试';
                    }
                    if (!isset($in[$i], $cos[$i], $warehousesByName[$ware])) {
                        return '查询不到导入简码:' . $shortCode . ' 的库存或成本或仓库 请检查无误后再试';
                    }
                    //添加产品库存信息， 不存在则添加
                    $pro_id = Db::name('periods_product_inventory')->where([
                        'period' => intval($v['T']), 'product_id' => $productByShortCode[$shortCode]['id']
                    ])->value('id');
                    if (!$pro_id) {
                        $productInventoryData[] = [
                            'period' => intval($v['T']),
                            'periods_type' => $genreData['type'],
                            'product_id' => $productByShortCode[$shortCode]['id'],
                            'title' => $periods[intval($v['T'])]['title'],
                            'product_name' => $productByShortCode[$shortCode]['cn_product_name'],
                            'en_product_name' => $productByShortCode[$shortCode]['en_product_name'],
                            'bar_code' => $productByShortCode[$shortCode]['bar_code'],
                            'short_code' => $productByShortCode[$shortCode]['short_code'],
                            'inventory' => $in[$i],
                            'costprice' => $cos[$i],
                            'inventory_accum' => $in[$i],
                            'warehouse' => $warehousesByName[$ware]['name'],
                            'warehouse_id' => $warehousesByName[$ware]['virtual_id'],
                            'erp_id' => $warehousesByName[$ware]['erp_id'],
                            'created_time' => time(),
                            'capacity' => $productByShortCode[$shortCode]['capacity'],
                        ];
                    }
                    $inIdAndProId[intval($v['T'])][] = $productByShortCode[$shortCode]['id'];
                    $product_keyword .= $productByShortCode[$shortCode]['product_keywords'] . ',';
                    $product_category .= $productByShortCode[$shortCode]['product_type_name'] . ',';
                    $product_main_category .= $productByShortCode[$shortCode]['product_category_name'] . ',';
                    $country .= $productByShortCode[$shortCode]['country_name'] . ',';
                    $capacity .= $productByShortCode[$shortCode]['capacity'] . ',';
                    $product_id .= $productByShortCode[$shortCode]['id'] . ',';
                }

                //套餐名称
                $associated_products[] = [
                    'product_id' => $productByShortCode[$v['S']]['id'],
                    'nums' => 1,
                    'isGift' => 0,
                ];
                //组装套餐
                $set[] = [
                    'id' => $sev->getGeneratorID(2),
                    'period_id' => intval($v['T']),
                    'package_name' => '单支',
                    'price' => $v['N'],
                    'market_price' => $v['N'],
                    'associated_products' => json_encode($associated_products),
                    'created_time' => time(),
                    'inventory_accum' => 0
                ];
                $es_ser = new ElasticSearch();
                $es_ser->updatePeriodsById((int)$v['T'], ['package_prices' => $v['N']]);
                $setIdAndName[intval($v['T'])][] = '单支';
//                foreach (explode('&', $v['M']) as $pi => $packages) {
//                    if (!isset($packageNames[$pi])) {
//                        return '套餐名称对应套餐数据';
//                    }
//                    if (!isset($packagePrices[$pi], $packageMarketPrices[$pi])) {
//                        return '套餐售价或套餐市场价 必须对应套餐数据';
//                    }
//                    //拼接套装产品信息
//                    $associated_products = [];
//                    foreach (explode('+', $packages) as $package) {
//                        $pro = explode('*', $package);
//                        if (count($pro) !== 2) {
//                            return '套餐:' . $packageNames[$pi] . ' 的 套餐简码数量未设置';
//                        }
//                        $associated_products[] = [
//                            'product_id' => $productByShortCode[$pro[0]]['id'],
//                            'nums' => $pro[1],
//                            'isGift' => $productByShortCode[$pro[0]]['is_gift'],
//                        ];
//                    }
//                    $set[] = [
//                        'id' => $sev->getGeneratorID(2),
//                        'period_id' => intval($v['B']),
//                        'package_name' => $packageNames[$pi],
//                        'price' => $packagePrices[$pi],
//                        'market_price' => $packageMarketPrices[$pi],
//                        'associated_products' => json_encode($associated_products),
//                        'created_time' => time(),
//                        'inventory_accum' => 0
//                    ];
//                    $setIdAndName[intval($v['B'])][] = $packageNames[$pi];
//                    if ($pi === 0) {
//                        $price = $packagePrices[$pi];
//                        $market_price = $packageMarketPrices[$pi];
//                    }
//                }


                //组装期数
                $update[] = [
                    'id' => intval($v['T']),
                    'import_type' => intval($v['H']),
                    'supplier' => $suppliersByName[$v['G']]['supplier_name'] ?? '',
                    'supplier_id' => $suppliersByName[$v['G']]['id'] ?? 0,
                    'buyer_id' => $buyerByName[$v['U']]['id'] ?? 48,
                    'buyer_name' => $buyerByName[$v['U']]['realname'] ?? '岑启盛',
                    'operation_review_id' => $oerates[$buyerByName[$v['U']]['id']]['operation_review_id'] ?? 0,
                    'operation_review_name' => $oerates[$buyerByName[$v['U']]['id']]['operation_review_name'] ?? null,
                    'is_supplier_delivery' => $v['Y'],
                    'is_presell' => $v['Z'],
                    'limit_number' => $v['V'] ?? 3,
                    'invariant_number' => $v['V'] ?? 3,
                    'critical_value' => $v['W'] ?? 1,
                    'incremental' => $v['X'] ?? 3,
                    'onsale_time' => strtotime($v['A']),
                    'sell_time' => empty(strtotime($v['C'])) ? strtotime($v['A']) : strtotime($v['C']),
                    'sold_out_time' => empty(strtotime($v['B'])) ? strtotime("+7 day", strtotime($v['A'])) : strtotime($v['B']),
                    'predict_shipment_time' => empty(strtotime($v['D'])) ? strtotime("+3 day", strtotime($v['A'])) : strtotime($v['D']),
                    'payee_merchant_name' => $v['F'],
                    'payee_merchant_id' => $v['payee_merchant_id'],
                    'quota_rule' => '{"check_addr": 0, "check_level": 0, "check_time": 0, "quota_type": "1", "quota_number": "9999", "register_time": "", "min_level": "", "max_level": "", "addrs": []}',
                    'operation_id' => $userId,
                    'operation_name' => $userName,
                    'short_code' => implode(',', explode(',', $v['S'])),
                    'product_keyword' => substr($product_keyword, 0, -1),
                    'product_category' => substr($product_category, 0, -1),
                    'product_main_category' => substr($product_main_category, 0, -1),
                    'country' => substr($country, 0, -1),
                    'capacity' => substr($capacity, 0, -1),
                    'price' => $v['N'],
                    'market_price' => $v['N'],
                    'product_id' => substr($product_id, 0, -1),
                    'onsale_review_status' => 1
                ];

                //组装备注信息
//                if (isset($v['AA']) && !empty($v['AA'])) {
                $remark_str = $v['AG'];
                if (empty($remark_str)) {
                    $remark_str = $v['AA'] . '，' . $v['R'] . '，' . $v['AH'];
                }
                $remarks[] = [
                    'period'        => intval($v['T']),
                    'periods_type'  => $genreData['type'],
                    'remark'        => "由采购：-更换到采购：" . ($buyerByName[$v['U']]['realname'] ?? ''). "。",
                    'operator'      => $userId,
                    'operator_name' => $userName,
                    'created_time'  => time(),
                ];
                $remarks[] = [
                    'period' => intval($v['T']),
                    'periods_type' => $genreData['type'],
//                        'remark' => $v['AA'],
                    'remark' =>  $remark_str,
                    'operator' => $userId,
                    'operator_name' => $userName,
                    'created_time' => time(),
                ];
//                }
            }

            if (count($setIdAndName) > 0) {
                try {
                    //查询套餐是否重复
                    $count = Db::name($setTable)->where(function (Query $query) use ($setIdAndName) {
                        $or = [];
                        foreach ($setIdAndName as $pid => $packageNames) {
                            $or[] = [['period_id', '=', $pid], ['package_name', 'in', $packageNames]];
                        }
                        $query->whereOr($or);
                    })->count();
                    if ($count !== 0) {
                        return '套餐数据已存在 请检查无误后再试';
                    }
                } catch (\Throwable $e) {
                    Log::error('importByExcelThrowable_package: ' . $e->getMessage() . '   :' . $e->getFile() . '  :' . $e->getLine());
                    return $e->getMessage();
                }
            }
        }

        //验证产品是否存在
//        if (count($inIdAndProId) > 0) {
//            try {
//                $count = Db::name('periods_product_inventory')->where(function (Query $query) use ($inIdAndProId) {
//                    $or = [];
//                    foreach ($inIdAndProId as $pid => $productIds) {
//                        $or[] = [['period', '=', $pid], ['product_id', 'in', $productIds]];
//                    }
//                    $query->whereOr($or);
//                })->count();
//                if ($count !== 0) {
//                    return '已存在相同期数的产品数据 无法导入';
//                }
//            } catch (\Throwable $e) {
//                Log::error('importByExcelThrowable_in: ' . $e->getMessage() . '   :' . $e->getFile() . '  :' . $e->getLine());
//                return $e->getMessage();
//            }
//        }

//        if (count($productInventoryData) > 0) {
        try {
            Db::startTrans();
            //循环修改期数和套餐信息
            foreach ([['type' => 0, 'data' => $flashUpdateData], ['type' => 1, 'data' => $secondUpdateData], ['type' => 2, 'data' => $crossUpdateData], ['type' => 3, 'data' => $leftoverUpdateData]] as $genreData) {
                switch ($genreData['type']) {
                    case 0:
                        $updateTable = 'periods_flash';
                        $setTable = 'periods_flash_set';
                        $setData = &$flashSetData;
                        break;
                    case 1:
                        $updateTable = 'periods_second';
                        $setTable = 'periods_second_set';
                        $setData = &$secondSetData;
                        break;
                    case 2:
                        $updateTable = 'periods_cross';
                        $setTable = 'periods_cross_set';
                        $setData = &$crossSetData;
                        break;
                    case 3:
                        $updateTable = 'periods_leftover';
                        $setTable = 'periods_leftover_set';
                        $setData = &$leftoverSetData;
                        break;
                    default:
                        throw new \Exception('获取不到正确的修改类型');
                }
                //添加套餐信息
                if (count($setData) > 0) {
                    if (Db::name($setTable)->insertAll($setData) !== count($setData)) {
                        throw new \Exception('添加套餐数据失败');
                    }
                }

                //修改期数
                foreach ($genreData['data'] as $v) {
                    //确认id 避免全局修改
                    if (!isset($v['id']) || empty($v['id'])) {
                        throw new \Exception('要修改的期数id不存在');
                    }
                    Db::name($updateTable)->strict(false)->update($v);
                }
            }

            //添加库存
            if (count($productInventoryData) > 0) {
                if (Db::name('periods_product_inventory')->insertAll($productInventoryData) !== count($productInventoryData)) {
                    throw new \Exception('添加库存数据失败');
                }
            }

            //添加备注
            if (count($remarks) > 0) {
                if (Db::name('periods_remark')->insertAll($remarks) !== count($remarks)) {
                    throw new \Exception('添加备注数据失败');
                }
            }

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollback();
            Log::error('importByExcelThrowable: ' . $e->getMessage() . '   :' . $e->getFile() . '  :' . $e->getLine());
            return $e->getMessage();
        }
//        }

        return true;
    }


    public function httpGet($url)
    {
        $rst = get_url($url);
        if (!empty($rst)) {
            $rstData = json_decode($rst, true);
            if (!empty($rstData['data']) && is_array($rstData['data'])) {
                return $rstData['data'];
            }
        }
        return [];
    }

    public function getEsClient(): \Ligenhui\PhpElasticsearch\Elasticsearch
    {
        $esConfig = new \Ligenhui\PhpElasticsearch\Config();
        $esConfig->setHost(env('ES.HOST', '127.0.0.1'))
            ->setPort(env('ES.PORT', 9200))
            ->setUser(env('ES.USER', 'root'))
            ->setPassword(env('ES.PASS', 'vinehoo666'));
        return new \Ligenhui\PhpElasticsearch\Elasticsearch([$esConfig]);
    }

    /**
     * 更新标签
     * @param array $params
     * @return PeriodsAuction|PeriodsCross|PeriodsFlash|PeriodsLeftover|PeriodsRabbit|PeriodsRabbitCoupon|PeriodsSecond|PeriodsSecondMerchants
     */
    public function updateLabel(array $params) {
        return $this->model::where('id', $params['id'])->update(['label' => $params['label']]);
    }

    public function getModel()
    {
        return $this->model;
    }
    
    /**
     * 发送订金信息推送
     * @param array $period_info 期数数据
     * @param array $list 套餐列表
     */
    public function SendDepositSms($period_info, $list) {
        $time = time();
        $period_title = getsre($period_info['title'], 26);

        $coupon_id = array_column($list,'deposit_coupon_id');
        // 查询未支付尾款用户
        $uid = Db::table('vh_marketing.vh_coupon_issue')
            ->where([
                ['coupon_id', 'in', $coupon_id],
                ['review_status', '=', 2],
            ])->group('uid')->column('uid');

        if (!empty($uid)) {
            // 查询推送成功用户
            $push_user = Db::name('deposit_sms')
                ->where([
                    ['period','=',$period_info['id']],
                    ['uid','in',$uid]
                ])->column('id,uid,status', 'uid');
            $push_uid = [];
            foreach ($push_user as $v) {
                if ($v['status'] == 1) {
                    $push_uid[] = $v['uid'];
                }
            }
            // 过滤已推送用户
            $uids = [];
            foreach ($uid as $v) {
                if (in_array($v, $push_uid)) {
                    continue;
                }
                $uids[] = $v;
            }
            $uids = array_values(array_unique($uids));
            // 查询用户信息
            $user_res = getUserInfoByUids(implode(',', $uids),'uid,telephone_encrypt,plaintext_telephone');
            //获取手机号
            $telephone = [];
            foreach ($uids as $v) {
                if (!empty($user_res[$v]['plaintext_telephone'])) {
                    $telephone[] = $user_res[$v]['plaintext_telephone'];
                }
            }

            // 推送短信
            if (!empty($telephone)) {
                $sms_data = json_encode([
                    'type' => 2,
                    'telephone' => implode(',', $telephone),
                    'content' => '【酒云网】您预订的“'.$period_title.'”可以抢付尾款了，数量有限，点击 y.m.cn/0qGO抢付。拒收请回复R'
                ]);
                $sms_url = env('ITEM.SMS_URL').'/sms/v3/group/sendSms';
                $sms_res = curlRequest($sms_url, $sms_data);
            }

            $insert_log = [];
            foreach ($uids as $v) {
                $is_push = 2;

                // 单推
                $content = '【酒云网】兔友您好 ，您预付的【' . $period_title . '】已经上线啦~快去抢付尾款吧！';
                $push_data = json_encode([
                    'is_push' => 1,
                    'uid' => $v,
                    'title' => '订单通知',
                    'content' => $content,
                    'data_type' => 18,
                    'custom_param' => ['id' => $period_info['id']],
                    'data' => [
                        'title' => '订单通知',
                        'content' => $content,
                    ],
                    'label' => 'DepositOrderList',
                ]);
                $url = env('ITEM.APPPUSH_URL') . '/apppush/v3/push/single';
                $res = curlRequest($url, $push_data);
                if (isset($res['error_code']) && intval($res['error_code']) === 0) {
                    $is_push = 1;
                }

                // 推送短信
                if (isset($sms_res['error_code']) && intval($sms_res['error_code']) === 0) {
                    if (!empty($user_res[$v]['plaintext_telephone'])) {
                        $is_push = 1;
                    }
                }

                if (!empty($push_user[$v])) {
                    Db::name('deposit_sms')
                        ->where('id', $push_user[$v]['id'])
                        ->update([
                            'status'      => $is_push,
                            'update_time' => $time,
                        ]);
                } else {
                    $insert_log[] = [
                        'period'       => $period_info['id'],
                        'uid'          => $v,
                        'status'       => $is_push,
                        'created_time' => $time,
                        'update_time'  => $time,
                    ];
                }
            }
            // 保存日志
            if (!empty($insert_log)) {
                Db::name('deposit_sms')->insertAll($insert_log);
            }
        }

        return true;
    }

    /**
     * 非渠道销售不能存在同产品同时售卖，判断依据是【商品售卖区间】开售时间-下架时间。不能存在相同存货
     * @param int $period 期数
     * @param int $sell_time 开售时间
     * @param int $sold_out_time 下架时间
     * @param array $product_ids 指定最终产品id
     * @param int $source 来源：0异步，1同步，2上架，3查询同时在售期数
     * @return bool|string|array 
     */
    public function VerifySimultaneousSales($period, $sell_time = 0, $sold_out_time = 0, $product_ids = [], $source = 1, $notify_content = '') {
        
        // 测试环境不验证
        if (empty(env('COMMODITIES.ONSALE_GDWJ_TOKEN'))) {
            return true;
        }

        if ($this->type > 3) {
            return true;
        }

        // 查询商品信息
        if (empty($sell_time) && empty($sold_out_time)) {
            $period_info = $this->model::where('id', $period)
                ->where('id', $period)
                ->field('id,sell_time,sold_out_time,is_channel')
                ->find();
            if (empty($period_info)) {
                return '商品信息获取失败';
            }
            if (!empty($period_info['is_channel']) && $period_info['is_channel'] == 1 && $source != 3) {
                return true;
            }
            $sell_time = strtotime($period_info['sell_time']);
            $sold_out_time = strtotime($period_info['sold_out_time']);
        }

        // 时间为空不验证
        if (empty($sell_time) && empty($sold_out_time)) {
            return true;
        }

        $periods_types = [0, 1, 2, 3];
        $table = [
            'periods_flash',
            'periods_second',
            'periods_cross',
            'periods_leftover',
        ];
        $table_set = [
            'periods_flash_set',
            'periods_second_set',
            'periods_cross_set',
            'periods_leftover_set',
        ];

        if (empty($product_ids)) {
            // 过滤赠品
            $package_product = Db::name($table_set[$this->type])
                ->where([
                    ['period_id', '=', $period],
                    ['is_hidden', '=', 0]
                ])
                ->column('associated_products');
            $product_ids = [];
            foreach ($package_product as $v) {
                $product_info = json_decode($v ?? '', true) ?? [];
                if (!empty($product_info)) {
                    foreach ($product_info as $vv) {
                        if (empty($vv['product_id'])) {
                            continue;
                        }
                        // 非赠品
                        if (isset($vv['isGift']) && intval($vv['isGift']) === 0) {
                            if (is_array($vv['product_id'])) {
                                $product_ids = array_merge($product_ids, $vv['product_id']);

                            } else {
                                $product_ids[] = $vv['product_id'];
                            }
                        }
                    }
                }
            }
        }

        foreach ($periods_types as $v) {
            $where = [
                ['pi.product_id', 'in', $product_ids],
                ['pi.is_use_comment', '=', 0],
                ['p.onsale_status', 'in', [1,2]],
                ['p.sell_time', '<=', $sold_out_time]
            ];
            // 秒发不验证下架时间
            if ($v != 1) {
                $where[] = ['p.sold_out_time', '>=', $sell_time];
            }
            if ($source != 3) {
                $where[] = ['p.is_channel', '=', 0];
                $where[] = ['pi.period', '<>', $period];
            }
            // 查询相同区间期数信息
            $period_map = Db::name('periods_product_inventory')
                ->alias('pi')
                ->LeftJoin("{$table[$v]} p",'p.id=pi.period')
                ->where($where)
                ->group('p.id')
                ->column("p.id,p.title,p.is_channel,{$v} as periods_type", 'p.id');
            $period_id = array_keys($period_map);
            if (!empty($period_id)) {
                $products = Db::name($table_set[$v])
                    ->where([
                        ['period_id', 'in', $period_id],
                        ['is_hidden', '=', 0]
                    ])
                    ->column('period_id,associated_products');
                $exist_period_id = [];
                foreach ($products as $v) {
                    $product_info = json_decode($v['associated_products'] ?? '', true) ?? [];
                    if (!empty($product_info)) {
                        foreach ($product_info as $vv) {
                            if (empty($vv['product_id'])) {
                                continue;
                            }
                            if (isset($vv['isGift']) && intval($vv['isGift']) === 0 && in_array($vv['product_id'], $product_ids)) {
                                if (!in_array($v['period_id'], $exist_period_id)) {
                                    $exist_period_id[] = $v['period_id'];
                                }
                            }
                        }
                    }
                }

                if (!empty($exist_period_id)) {
                    $sale_period = implode(',', $exist_period_id);
                    $error_msg = '在售期数：' . $sale_period;
                    if ($source == 0) {
                        $content = $notify_content == '' ? '【' . $period . '】商品上架失败，商品售卖区间存在相同存货。' : $notify_content;
                        SendWeChatRobot($content . $error_msg);
                    }
                    if ($source == 3) {
                        $exist_period_info = [];
                        foreach ($exist_period_id as $v) {
                            $s_period = $period_map[$v] ?? [];
                            if (!empty($s_period)) {
                                $s_period['type_name'] = $this->type_name[$s_period['periods_type']] ?? '';
                                $exist_period_info[] = $s_period;
                            }
                        }
                        return $exist_period_info;
                    }
                    return '商品售卖区间存在相同存货，'.$error_msg;
                }
            }
        }
        return true;
    }

    /**
     * 期数上架验证关单卫检
     *
     * @param int $period 期数
     * @param int $source 来源：0-自动任务，1-手动上架
     * @param array $package_list 套餐列表
     * @return bool|string
     */
    public function vCustomsOrderHealthInspect($period, $source = 0, $package_list = [])
    {
        // 跨境、兔头不验证
        if (!in_array($this->type, [0, 1, 3])) {
            return true;
        }

        // 自进口不验证
        $period_info = $this->getOne($period, 'id,import_type,supplier');
        if (isset($period_info['import_type']) && intval($period_info['import_type']) === 0) {
            return true;
        }
        // 上架时供应商名称如果包含【闪购】，则不验证关单卫检
        if (isset($period_info['supplier']) && strpos(strval($period_info['supplier']), '闪购') !== false) {
            return true;
        }

        if (empty($package_list)) {
            $package_ser = new Package($this->type);
            $package_list = $package_ser->packageList(['period' => $period]);
        }

        if (!empty($package_list)) {
            $product_ids = [];
            foreach ($package_list as $v) {
                // 过滤隐藏套餐
                if (!empty($v['is_hidden']) && $v['is_hidden'] == 1) {
                    continue;
                }
                // 解析套餐产品
                $product_info = json_decode($v['associated_products'] ?? '', true) ?? [];

                if (!empty($product_info) && is_array($product_info)) {
                    foreach ($product_info as $vv) {
                        if (empty($vv['product_id'])) {
                            continue;
                        }
                        if (is_array($vv['product_id'])) {
                            $product_ids = array_merge($product_ids, $vv['product_id']);

                        } else {
                            $product_ids[] = $vv['product_id'];
                        }
                    }
                }
            }

            if (!empty($product_ids)) {
                // 查询磐石关单卫检未通过审核产品(剔除白酒)
                $short_codes = Db::table('vh_wiki.vh_products')
                    ->where([
                        ['id', 'in', $product_ids],
                        ['product_type', '<>', 21],
                        ['product_attachment_status', '<>', 2],
                    ])
                    ->column('short_code');
                if (!empty($short_codes)) {
                    $short_code_str = implode(',', $short_codes);
                    $error_msg =  '简码 ' . $short_code_str . ' 关单卫检未通过审批或未上传。';
                    // 自动上架通过机器人提示
                    if ($source == 0) {
                        $content = '【' . $period . '】商品上架失败，' . $error_msg;
                        SendWeChatRobot($content);
                    }
                    return $error_msg;
                }
            }
        }

        return true;
    }

    /**
     * 查询期数关单卫检状态和毛利率
     *
     * @param int $period 期数
     * @return bool|string
     */
    public function getCustomsOrderHealthInspect($period)
    {
        //状态：0-未审批 1-审批中 2-审批通过 3-审批驳回
        $result = ['gdwj_status' => [], 'gross_margin' => []];
        // 兔头不验证
        if (!in_array($this->type, [0, 1, 2, 3])) {
            return $result;
        }

        $period_info = $this->getOne($period, 'id,import_type');

        if (empty($package_list)) {
            $package_ser = new Package($this->type);
            $package_list = $package_ser->packageList(['period' => $period]);
        }

        if (!empty($package_list)) {
            $product_ids = [];
            foreach ($package_list as &$pkgv) {
                // 解析套餐产品
                $product_info = json_decode($pkgv['associated_products'] ?? '', true) ?? [];
                // 产品id
                if (!empty($product_info) && is_array($product_info)) {
                    $pkgv['products'] = $product_info;
                    foreach ($product_info as $vv) {
                        if (empty($vv['product_id'])) {
                            continue;
                        }
                        if (is_array($vv['product_id'])) {
                            $product_ids = array_merge($product_ids, $vv['product_id']);

                        } else {
                            $product_ids[] = $vv['product_id'];
                        }
                    }
                }
            }

            if (!empty($product_ids)) {
                // 查询成本价
                $costprice = Db::name('periods_product_inventory')
                    ->where([
                        ['period','=',$period],
                        ['product_id','in',$product_ids],
                    ])
                    ->column('product_id,costprice', 'product_id');
                    
                foreach ($package_list as $v) {
                    // 毛利率
                    $gross_margin = 0;
                    // 套餐成本
                    $package_cost = '0';
                    if (!empty($v['products'])) {
                        foreach ($v['products'] as $vv) {
                            // 产品成本
                            $cost = 0;
                            // 盲盒取成本最高的产品
                            if (is_array($vv['product_id'])) {
                                foreach ($vv['product_id'] as $pv) {
                                    $sub_cost = $costprice[$pv]['costprice'] ?? 0;
                                    if ($sub_cost > $cost) {
                                        $cost = $sub_cost;
                                    }
                                }
                            } else {
                                $cost = $costprice[$vv['product_id']]['costprice'] ?? 0;
                            }
                            // 数量
                            $vv['nums'] = $vv['nums'] ?? 1;
                            $cost = bcmul(strval($cost), strval($vv['nums']), 2);
                            $package_cost = bcadd($package_cost, $cost, 2);
                        }
                        if ($v['price'] > 0 && $package_cost > 0) {
                            // 毛利率=（套餐价格-套餐成本）/套餐价格
                            $gross_profit = bcsub(strval($v['price']), $package_cost, 2);
                            if (floatval($gross_profit) > 0) {
                                $gross_margin = floatval(bcdiv($gross_profit, strval($v['price']), 4));
                            }
                        }
                        
                    }
                    
                    $result['gross_margin'][] = [
                        'id' => $v['id'],
                        'package_name' => $v['package_name'],
                        // 'price' => floatval($v['price']),
                        // 'package_cost' => floatval($package_cost),
                        'gross_margin' => $gross_margin,
                    ];
                }

                // 跨境、自进口不验证
                if ($this->type != 2 && isset($period_info['import_type']) && $period_info['import_type'] != 0) {
                    // 查询磐石关单卫检审核产品(剔除白酒)
                    $products = Db::table('vh_wiki.vh_products')
                        ->alias('p')
                        ->leftJoin('vh_wiki.vh_product_type pt','pt.id=p.product_type')
                        ->where([
                            ['p.id', 'in', $product_ids],
                            ['pt.id', '<>', 21],
                            ['pt.fid', '<>', 21],
                        ])
                        ->column('p.short_code,p.product_attachment_status as status');
                    if (!empty($products)) {
                        $result['gdwj_status'] = $products;
                    }
                }
            }
        }

        // 根据规则查询可自动添加的营销版块
        $url = env('ITEM.COMMODITIES_SERVICES') . '/commodities_server/v3/marketing/GetMarketingSection';
        $data = json_encode([
            'period' => intval($period),
            'period_type' => intval($this->type),
        ]);
        $res = curlRequest($url, $data);
        $result['card'] = $res['data']['card'] ?? [];
        $result['column'] = $res['data']['column'] ?? [];

        return $result;
    }

    /**
     * 验证期数是否烈酒
     * @param int $period 期数
     * @param array $package_list 套餐数据
     * @return bool
     */
    public function VerificationSpirits ($period, $package_list = [])
    {
        $result = false;
        if (empty($package_list)) {
            $package_ser = new Package($this->type);
            $package_list = $package_ser->packageList(['period' => $period]);
        }

        if (!empty($package_list)) {
            $product_ids = [];
            foreach ($package_list as $v) {
                // 过滤隐藏套餐
                if (!empty($v['is_hidden']) && $v['is_hidden'] == 1) {
                    continue;
                }
                // 解析套餐产品
                $product_info = json_decode($v['associated_products'] ?? '', true) ?? [];

                if (!empty($product_info) && is_array($product_info)) {
                    foreach ($product_info as $vv) {
                        if (empty($vv['product_id'])) {
                            continue;
                        }
                        if (is_array($vv['product_id'])) {
                            $product_ids = array_merge($product_ids, $vv['product_id']);

                        } else {
                            $product_ids[] = $vv['product_id'];
                        }
                    }
                }
            }
            if (!empty($product_ids)) {
                // 烈酒类型
                $lj_type= $this->getProductType(22);
                // 查询磐石烈酒类型
                $products = Db::table('vh_wiki.vh_products')
                    ->alias('p')
                    ->leftJoin('vh_wiki.vh_product_type t','p.product_type=t.id')
                    ->where([
                        ['t.id', 'in', array_column($lj_type, 'id')],
                        ['p.id', 'in', $product_ids],
                    ])
                    ->value('p.id');
                if (!empty($products)) {
                    $result = true;
                }
            }
        }
        return $result;
    }

    /**
     * 查询产品类型（包含下级所有类型）
     * @param int $period 期数
     * @param int $id 类型ID
     * @return array
     */
    public function getProductType ($id,$product_type = [],$fid = 0)
    {
        if (empty($product_type)) {
            $product_type = Db::table('vh_wiki.vh_product_type')->select()->toArray();
        }
        
        $list = [];
        foreach ($product_type as $v) {
            if ($fid == 0 && $v['id'] == $id) {
                $s_list = $this->getProductType($id, $product_type, $id);
                $list[] = $v;
                $list = array_merge($list, $s_list);

            } else if ($fid > 0 && $v['fid'] == $fid) {
                $s_list = $this->getProductType($id, $product_type, $v['id']);
                $list[] = $v;
                $list = array_merge($list, $s_list);
            }
        }

        return $list;
    }

    /**
     * 批量查询期数毛利率
     *
     * @param array $period 期数
     * @return array
     */
    public function BatchQueryPeriodGrossProfitMargin ($period, $period_product = [])
    {
        $package_list = Es::name('periods_set')
            ->where([
                ['period_id', 'in', $period],
            ])
            ->select()->toArray();
        $result = [];
        if (!empty($package_list)) {
            $periodProduct = [];
            if (empty($period_product)) {
                // 查询成本价
                $period_product = Db::name('periods_product_inventory')
                    ->whereIn('period', $period)
                    ->column('period,product_id,costprice,short_code');
            }
            foreach ($period_product as $v) {
                $periodProduct[$v['period'] . $v['product_id']] = $v;
            }
                
            foreach ($package_list as $v) {
                // 套餐价格=套餐价格-优惠减免金额
                $package_price = bcsub(strval($v['price']), strval($v['preferential_reduction']), 2);
                // 毛利率
                $gross_margin = '0';
                // 套餐成本
                $package_cost = '0';
                // 解析套餐产品
                $v['products'] = json_decode($v['associated_products'] ?? '', true) ?? [];
                // 简码
                $short_code = [];
                if (!empty($v['products'])) {
                    foreach ($v['products'] as $vv) {
                        // 产品成本
                        $cost = 0;
                        // 盲盒取成本最高的产品
                        if (is_array($vv['product_id'])) {
                            foreach ($vv['product_id'] as $pv) {
                                $pp = $periodProduct[$v['period_id'] . $pv] ?? [];
                                $sub_cost = 0;
                                if (!empty($pp)) {
                                    $sub_cost = $pp['costprice'];
                                    $short_code[] = $pp['short_code'];
                                }

                                if ($sub_cost > $cost) {
                                    $cost = $sub_cost;
                                }
                            }
                        } else {
                            $pp = $periodProduct[$v['period_id'] . $vv['product_id']] ?? [];
                            if (!empty($pp)) {
                                $cost = $pp['costprice'];
                                $short_code[] = $pp['short_code'];
                            }
                        }
                        // 数量
                        $vv['nums'] = $vv['nums'] ?? 1;
                        $cost = bcmul(strval($cost), strval($vv['nums']), 2);
                        $package_cost = bcadd($package_cost, $cost, 2);
                    }
                    if (floatval($package_price) > 0 && floatval($package_cost) > 0) {
                        // 毛利率=（套餐价格-套餐成本）/套餐价格
                        $gross_profit = bcsub($package_price, $package_cost, 2);
                        if (floatval($gross_profit) != 0) {
                            $gross_margin = bcdiv($gross_profit, $package_price, 4);
                        }
                    }
                    
                }
                
                $result[$v['period_id']][] = [
                    'id' => $v['id'],
                    'period_id' => $v['period_id'],
                    'package_name' => $v['package_name'],
                    'price' => floatval($v['price']),
                    'package_cost' => floatval($package_cost),
                    'short_code' => $short_code,
                    'gross_margin' => floatval(bcmul($gross_margin, '100', 2)),
                ];
            }
        }

        return $result;
    }

    /**
     * 更新预计采购时间
     *
     * @param int $period_id 期数
     * @param array $period_info 期数信息
     * @param array $es_info 期数es信息
     * @param array $package_list 套餐信息
     */
    public function updatePurchaseTime ($period_id, $period_info = [], $es_info = [], $package_list = [])
    {
        // $time = strtotime(date('Y-m-d'));
        if (empty($period_info)) {
            // 查询期数
            $period_info = $this->getOne($period_id, 'id,is_supplier_delivery,sold_out_time');
        }
        // if (empty($es_info)) {
        //     // 预计采购时间
        //     $es_info = Es::name('periods')
        //         ->where([['_id', '=', $period_id]])
        //         ->field('estimate_purchase')
        //         ->find();
        // }
        // 当前预计采购时间
        // $estimate_purchase = !empty($es_info['estimate_purchase']) ? strtotime($es_info['estimate_purchase']) : 0;
        // if ($estimate_purchase < $time) {
            // 代发
            if ($period_info['is_supplier_delivery'] == 1) {
                #采购时间为【下架时间】的后一天
                $estimate_purchase = date('Y-m-d H:i:s', strtotime($period_info['sold_out_time']) + 86400);
                // $estimate_purchase = $period_info['sold_out_time'];

            } else {// 非代发
                $stime = time();
                // if ($estimate_purchase == 0) {
                //     $stime = strtotime($period_info['sell_time']);
                // }
                #采购时间改为2天一次
                $estimate_purchase = date('Y-m-d H:i:s', $stime + (48 * 3600));
                // 烈酒
                // if ($this->VerificationSpirits($period_id, $package_list)) {
                //     #采购时间为【开售时间】+24*4小时
                //     $estimate_purchase = date('Y-m-d H:i:s', $stime + (3600 * 24 * 4));

                // } else {// 非烈酒
                //     #采购时间为【开售时间】+48小时
                //     $estimate_purchase = date('Y-m-d H:i:s', $stime + (48 * 3600));
                // }
            }
            Es::name('periods')->update([
                'id' => intval($period_id),
                'estimate_purchase' => $estimate_purchase,
            ]);
        // }

    }

    /**
     * 保存操作记录
     * @param array $params 请求参数
     * @return bool|string
     */
    public function SaveOperationRecord($params)
    {
        $save = [
            'period' => 0,
            'periods_type' => 0,
            'change_item' => '',
            'change_content' => '',
            'data' => $params['request_body'] ?? '',
            'operator' => $params['uid'] ?? '',
            'operator_name' => $params['vos_name'] ?? '',
            'created_time' => time(),
        ];
        $request_body = json_decode($params['request_body'] ?? '', true);
        switch ($params['route_url']) {
            case '/commodities/v3/review/offSale'://下架/上架
                //{"period":141352,"periods_type":0,"onsale_status":3,"operation_id":"1","operation_name":"操作人"}
                $save['period'] = $request_body['period'];
                $save['periods_type'] = $request_body['periods_type'];
                //上架
                if ($request_body['onsale_status'] == 2) {
                    $save['change_item'] = '上架';
                    $save['change_content'] = '点击上架';
                } else {
                    $save['change_item'] = '下架';
                    $save['change_content'] = '点击下架';
                }
                break;

            case '/commodities/v3/inventory/update'://修改库存
                //{\"period_id\":137951,\"product_id\":58824,\"nums\":6,\"action\":\"inc\"}
                // 查询期数产品信息
                $info = Db::name('periods_product_inventory')
                    ->where([
                        ['period', '=', $request_body['period_id']],
                        ['product_id', '=', $request_body['product_id']],
                    ])
                    ->field('id,period,periods_type,short_code')
                    ->find();
                $save['period'] = $request_body['period_id'];
                $save['periods_type'] = $info['periods_type'] ?? 0;
                $short_code = $info['short_code'] ?? '';
                if ($request_body['action'] == 'inc') {
                    $save['change_item'] = '增加库存';
                    $save['change_content'] = "提交增加库存【简码：{$short_code}，数量：{$request_body['nums']}】";
                } else {
                    $save['change_item'] = '减少库存';
                    $save['change_content'] = "提交减少库存【简码：{$short_code}，数量：{$request_body['nums']}】";
                }
                break;
                
            case '/commodities/v3/periods/updateUncUsed'://售完不下架/未售完自动延期
                //{\"period\":141340,\"periods_type\":0,\"sort\":\"685\"}
                $save['period'] = $request_body['period'];
                $save['periods_type'] = $request_body['periods_type'];
                if (isset($request_body['sellout_sold_out'])) {
                    $save['change_item'] = '售完不下架';
                    $save['change_content'] = $request_body['sellout_sold_out'] == 0 ? '关闭售完不下架' : '开启售完不下架';
                } else if (isset($request_body['is_postpone'])) {
                    $save['change_item'] = '未售完自动延期';
                    $save['change_content'] = $request_body['is_postpone'] == 0 ? '关闭未售完自动延期' : '开启未售完自动延期';
                } else {
                    return true;
                }
                break;

            case '/commodities/v3/periods/update'://修改商品信息
                $save['period'] = $request_body['period'];
                $save['periods_type'] = $request_body['periods_type'];
                $save['change_item'] = '编辑商品信息';
                $save['change_content'] = '编辑商品信息';
                break;

            case '/commodities/v3/package/create'://编辑套餐
                $is_add = false;
                foreach ($request_body as $v) {
                    $save['period'] = $v['period_id'];
                    $save['periods_type'] = $v['periods_type'];
                    if (empty($v['id'])) {
                        $is_add = true;
                    }
                }
                if ($is_add) {
                    $save['change_item'] = '新增套餐';
                    $save['change_content'] = '新增/编辑套餐';
                } else {
                    $save['change_item'] = '编辑套餐';
                    $save['change_content'] = '编辑套餐';
                }
                break;
            
            case '/commodities/v3/periods/updateTimes'://修改开售时间
                $save['period'] = $request_body['period'];
                $save['periods_type'] = $request_body['periods_type'];
                $save['change_item'] = '编辑商品时间';
                $save['change_content'] = "上架：{$request_body['onsale_time']}，开售：{$request_body['sell_time']}，下架：{$request_body['sold_out_time']}，发货：{$request_body['predict_shipment_time']}";
                break;

            case 'sort'://修改排序值
                $save['period'] = $params['period'];
                $save['periods_type'] = $params['periods_type'];
                $save['change_item'] = '修改排序值';
                $save['change_content'] = "{$params['old_sort']} 修改为 {$params['sort']}";
                break;

            default:
                return true;
        }

        #启动事务
        Db::startTrans();
        try {
            Db::name('periods_operation')->insert($save);
            #提交事务
            Db::commit();
            return true;
        } catch (\Exception $e) {
            #回滚事务
            Db::rollback();
            Log::error('保存操作记录失败，期数：' . $save['period'] . ',：' . $e->getMessage());
            return $e->getMessage();
        }
    }

    /**
     * 操作记录列表
     * @param array $params 请求参数
     * @return array 列表
     */
    public function OperationRecordList($params)
    {
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;
        $where = [
            ['period', '=', $params['period']]
        ];
        $where_str = '';

        if (!empty($params['change_item'])) {
            $where_str = "(change_item like '%{$params['change_item']}%' or change_content like '%{$params['change_item']}%')";
        }
        // if (!empty($params['change_content'])) {
        //     $where[] = ['change_content', 'like', "%{$params['change_content']}%"];
        // }

        $list = Db::name('periods_operation')
            ->where($where)
            ->where($where_str)
            ->order('id desc')
            ->limit(($page - 1) * $limit, $limit)
            ->column('id,period,periods_type,change_item,change_content,operator_name,created_time');
        $total = Db::name('periods_operation')->where($where)->count();

        $list = array_map(function ($v) {
            $v['created_time'] = date('Y-m-d H:i:s', $v['created_time']);
            return $v;
        }, $list);
        return [
            'list' => $list,
            'total' => $total
        ];
    }

    /**
     * 方法描述：预约
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2024/8/8 15:45
     * @param array $params
     * @return string
     */
    public function reservation(array $params):string
    {
        unset($params['id']);
        //加了唯一索引，如果重复会失败
        try {
            PeriodsReservation::insert($params);
        }catch (\Throwable $e){
            //不用处理
        }
        return '';
    }

    /**
     * 方法描述：返回用户是否预约过
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2024/8/8 16:09
     * @param array $params
     * @return bool
     */
    public function isReservation(array $params):bool
    {
        //加了唯一索引，如果重复会失败
        try {
            $id = PeriodsReservation::where(['period'=>$params['period'],'uid'=>$params['uid']])->value('id');
            if($id > 0){
                return true;
            }
        }catch (\Throwable $e){
            //不用处理
        }
        return false;
    }

    /**
     * 方法描述：批量取消预约
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2024/9/24 14:26
     * @param int $uid
     * @param array $periods
     * @return bool
     */
    public function cancelReservation(int $uid,array $periods):bool
    {
        //加了唯一索引，如果重复会失败
        try {
            PeriodsReservation::where(['uid'=>$uid,'period'=>$periods])->delete();
        }catch (\Throwable $e){
            //不用处理
        }
        return false;
    }

    /**
     * 查询未推送萌牙订单
     *
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getunpushorder(array $params)
    {
        $period_product = Db::name('periods_product_inventory')->where([
            ['period', '=', $params['period']],
            ['is_use_comment', '=', 0]
        ])->column('product_id,short_code','product_id');
        $short_code = array_column($period_product, 'short_code');
        $product_ids = array_column($period_product, 'product_id');
        $result = [];

        // 查询产品其他期数
        $other_period_product = Db::name('periods_product_inventory')->where([
            ['period', '<>', $params['period']],
            ['short_code', 'in', $short_code],
            ['is_use_comment', '=', 0]
        ])->column('period,periods_type,product_id,short_code');
        $period_ids = array_column($other_period_product, 'period');

        // 查询盲盒订单
        $mystery_box = Db::table('vh_orders.vh_order_mystery_box_log')
            ->alias('b')
            ->leftJoin('vh_orders.vh_order_main m','m.main_order_no=b.main_order_no')
            ->where([
                ['b.period', 'in', $period_ids]
            ])
            ->column('b.period,b.product_info,m.id as main_order_id');
        if (!empty($mystery_box)) {
            $main_order_ids = $mystery_box_info = [];
            foreach ($mystery_box as $v) {
                $mystery_box_info[$v['main_order_id']] = $v;
                $product_info = json_decode($v['product_info'], true);
                if (!empty($product_info)) {
                    foreach ($product_info as $value) {
                        if (!empty($value['product_id']) && in_array($value['product_id'], $product_ids)) {
                            $main_order_ids[] = $v['main_order_id'];
                        }
                    }
                }
            }
            if (!empty($main_order_ids)) {
                $orders = Es::name('orders')->where([
                    ['main_order_id', 'in', $main_order_ids],
                    ['sub_order_status', '=', 1],
                    ['push_wms_status', 'in', [0,2]]
                ])->field('sub_order_no,main_order_id')->select()->toArray();
                if (!empty($orders)) {
                    foreach ($orders as $v) {
                        $s_mystery_box = $mystery_box_info[$v['main_order_id']];
                        $product_info = json_decode($s_mystery_box['product_info'], true);
                        if (!empty($product_info)) {
                            foreach ($product_info as $v1) {
                                if (!empty($v1['product_id']) && in_array($v1['product_id'], $product_ids)) {
                                    $s_period_product = $period_product[$v1['product_id']];
                                    $result[$s_period_product['short_code']][] = $v['sub_order_no'];
                                }
                            }
                        }
                    }
                }
            }
        }

        // 查询套餐
        $package = Es::name('periods_set')->where([
            ['period_id', 'in', $period_ids]
        ])->field('id,period_id,associated_products')->select()->toArray();
        $package_ids = $package_info = [];
        foreach ($package as $v) {
            $package_info[$v['id']] = $v;
            $associated_products = json_decode($v['associated_products'], true);
            if (!empty($associated_products)) {
                foreach ($associated_products as $value) {
                    if (!empty($value['product_id']) && !is_array($value['product_id']) && in_array($value['product_id'], $product_ids)) {
                        $package_ids[] = $v['id'];
                    }
                }
            }
        }
        // 查询订单信息
        $orders = Es::name('orders')->where([
            ['package_id', 'in', $package_ids],
            ['sub_order_status', '=', 1],
            ['push_wms_status', 'in', [0,2]]
        ])->field('sub_order_no,package_id')->select()->toArray();
        if (!empty($orders)) {
            foreach ($orders as $v) {
                $s_package = $package_info[$v['package_id']];
                $associated_products = json_decode($s_package['associated_products'], true);
                if (!empty($associated_products)) {
                    foreach ($associated_products as $v1) {
                        if (!empty($v1['product_id']) && !is_array($v1['product_id']) && in_array($v1['product_id'], $product_ids)) {
                            $s_period_product = $period_product[$v1['product_id']];
                            $result[$s_period_product['short_code']][] = $v['sub_order_no'];
                        }
                    }
                }
            }
        }

        $re = [];
        if (!empty($result)) {
            foreach ($result as $k => $v) {
                $re[] = [
                    'short_code' => $k,
                    'order_no' => $v
                ];
            }
        }
        return ['list' => $re];
    }

    /**
     * 方法描述：获取参数配置默认值
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2024/9/24 14:26
     * @param int $uid
     * @param array $periods
     * @return bool
     */
    public function get_params_default_value($params)
    {
        $periods_type = $this->type;
        $parameters = Db::name('periods_parameters')
            ->where('periods_type', $periods_type)
            ->column('field_value,is_default,default_value');
        foreach ($parameters as $v) {
            $val = 0;
            if ($v['is_default'] == 1) {
                $val = $v['default_value'];
            }
            $params[$v['field_value']] = $val;
        }
        return $params;
    }

    /**
     * 方法描述：更新代发期数发货时间
     * User：gangh
     * DateTime:2025/1/22
     * @param int $period 期数
     * @return bool|string
     */
    public function updateDfDeliveryTime($period = 0, $period_info = [])
    {
        $time = time();
        
        if (empty($period_info)) {
            $where = [
                ['is_supplier_delivery', '=', 1],
                ['onsale_status', '=', 2],
            ];
            if ($period > 0) {
                $where[] = ['id', '=', $period];
            }
            $period_info = $this->model::where($where)
                ->field('id,is_supplier_delivery,supplier_delivery_time,supplier_delivery_weekend,predict_shipment_time')
                ->select()->toArray();
            if (empty($period_info)) {
                return true;
            }
        }

        try {
            foreach ($period_info as $v) {
                $predict_shipment_time = $v['predict_shipment_time'];
                if (strpos($v['predict_shipment_time'], ':')) {
                    $predict_shipment_time = strtotime($v['predict_shipment_time']);
                }

                // 代发（发货时效）：0-24小时内，1-48小时内，2-72小时内
                $supplier_delivery_time = $v['supplier_delivery_time'];
                // 代发（周末是否发货）：0-是，1-否
                $supplier_delivery_weekend = $v['supplier_delivery_weekend'];
                $day = 0;
                switch ($supplier_delivery_time) {
                    case 0://24小时内
                        $delivery_time = strtotime('+1 day');
                        $day = 1;
                        break;
                    case 1://48小时内
                        $delivery_time = strtotime('+2 day');
                        $day = 2;
                        break;
                    case 2://72小时内
                        $delivery_time = strtotime('+3 day');
                        $day = 3;
                        break;
                }
                if (empty($delivery_time)) {
                    continue;
                }

                // 周末不发货
                if ($supplier_delivery_weekend == 1) {
                    $use_day = 0;
                    $delivery_time = $time;
                    while ($use_day < $day) {
                        $delivery_time = intval($delivery_time + 86400);
                        $week = date('w', $delivery_time);
                        if (in_array($week, [1, 2, 3, 4, 5])) {
                            $use_day++;
                        }
                        if ($use_day >= $day) {
                            break;
                        }
                    }
                }

                if ($predict_shipment_time > $delivery_time) {
                    continue;
                }
                
                $this->model::where('id', $v['id'])->update([
                    'predict_shipment_time' => $delivery_time,
                    'update_time' => $time,
                ]);

                // 记录备注
                $this->createRemark([
                    'period' => $v['id'],
                    'periods_type' => $this->type,
                    'remark' => '代发期数自动更新发货时间，旧发货时间为：' . date('Y-m-d', $predict_shipment_time) . '，新发货时间为：' . date('Y-m-d', $delivery_time),
                    'operator' => 0,
                    'operator_name' => '系统',
                    'created_time' => $time,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('更新代发期数发货时间失败：' . $e->getMessage());
            return $e->getMessage();
        }
        
    }

    /**
     * 方法描述：包含此简码的在售的闪购非渠道期数修改发货时间
     * User：gangh
     * DateTime:2025/1/22
     * @param array $params 参数
     * @return bool|string
     */
    public function stockUpdateDeliveryTime($params)
    {
        $time = time();

        $periods_product = Db::name('periods_product_inventory')
            ->where([
                ['short_code', 'in', $params['short_code']],
                ['periods_type', '=', 0],
                ['warehouse_id', '=', $params['fictitious_id']],
                ['is_use_comment', '=', 0],
            ])->column('period,periods_type,short_code');
        if (empty($periods_product)) {
            return true;
        }

        Db::startTrans();
        try {
            /*
            如果【当前发货时间】小等于【当前时间+4天】，则：
            把发货时间设置为当前时间+5，超卖发货时间设置为当前时间+7
            如果当前时间是周五，则把发货时间设置为当前时间+6，超卖发货时间设置为当前时间+8
            */
            $period_ids = $short_code_map = [];
            foreach ($periods_product as $v) {
                $period_ids[] = $v['period'];
                $short_code_map[$v['period']][] = $v['short_code'];
            }
            $shipment_time = time() + (4 * 86400);
            $week = date('w');

            $inventory_order = $periods_remark = [];

            $period_data = $this->model->where([
                ['id', 'in', $period_ids],
                ['onsale_status', '=', 2],
                ['is_channel', '=', 0],
                ['predict_shipment_time', '<', $shipment_time],
            ])->column('id,predict_shipment_time');
            if (empty($period_data)) {
                Db::commit();
                return true;
            }

            foreach ($period_data as $v) {
                $predict_shipment_time = $v['predict_shipment_time'];
                $oversold_shipment_time = $v['predict_shipment_time'];
                if ($week == 5) {//如果当前时间是周五，则把发货时间设置为当前时间+6，超卖发货时间设置为当前时间+8
                    $predict_shipment_time = $time + (6 * 86400);
                    $oversold_shipment_time = $time + (8 * 86400);

                } else {// 把发货时间设置为当前时间+5，超卖发货时间设置为当前时间+7
                    $predict_shipment_time = $time + (5 * 86400);
                    $oversold_shipment_time = $time + (7 * 86400);
                }

                $old_shipment_date = date('Y.m.d', $v['predict_shipment_time']);
                $new_shipment_date = date('Y.m.d', $predict_shipment_time);

                $short_code_arr = $short_code_map[$v['id']] ?? [];
                foreach ($short_code_arr as $s) {
                    // 库存订货操作记录表
                    $inventory_order[] = [
                        'period' => $v['id'],
                        'short_code' => $s,
                        'type' => 1,
                        'order' => 0,
                        'operator' => '系统',
                        'operator_id' => 0,
                        'predict_shipment_time' => $oversold_shipment_time,
                        'created_time' => $time,
                    ];
                }
                
                // 备注
                $periods_remark[] = [
                    'period' => $v['id'],
                    'periods_type' => $this->type,
                    'remark' => "萌牙库存不足，发货时间调整：{$old_shipment_date} -> {$new_shipment_date}",
                    'operator' => 0,
                    'operator_name' => '系统',
                    'created_time' => $time,
                ];
                // 修改发货时间
                $this->model->where('id', $v['id'])->update([
                    'predict_shipment_time' => $predict_shipment_time,
                    'update_time' => time(),
                ]);
            }
        
            if (!empty($periods_remark)) {
                // 添加商品备注
                Db::name('periods_remark')->insertAll($periods_remark);
            }
            if (!empty($inventory_order)) {
                // 添加订货记录
                Db::name('periods_product_inventory_order')->insertAll($inventory_order);
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('包含此简码的在售的闪购非渠道期数修改发货时间失败：' . $e->getMessage());
            return $e->getMessage();
        }
        return true;
    }

    /**
     * 方法描述：取消在售期数渠道标识
     * User：gangh
     * DateTime:2025/7/22
     * @param array $params 参数
     * @return bool|string
     */
    public function cancelChannel($period)
    {
        try {
            $this->model->where('id', $period)->update(['is_channel' => 0]);
            
            // 删除redis缓存
            $redis_config = config('cache.stores.redis');
            $redis_config['select'] = 0;
            $conn = new \think\cache\driver\Redis($redis_config);
            $conn->srem('vinehoo.details.channel', intval($period));

        } catch (\Exception $e) {
            Log::error('取消在售期数渠道标识失败：' . $e->getMessage());
            return $e->getMessage();
        }
        return true;
    }

}
