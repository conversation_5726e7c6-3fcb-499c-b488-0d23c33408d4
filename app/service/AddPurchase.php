<?php

namespace app\service;

use app\model\PeriodsAddPurchase;
use app\service\es\Es;
use think\Exception;
use think\facade\Db;
use think\facade\Log;
use think\facade\Validate;
use app\service\Package as PeriodsPackage;

class AddPurchase
{
    public $period_table = [
        "vh_periods_flash" => 0, 
        "vh_periods_second" => 1, 
        "vh_periods_cross" => 2, 
        "vh_periods_leftover" => 3
    ];

    /**
     * 添加期数
     * @param array $params
     * @return PeriodsAddPurchase|\think\Model|int
     */
    public function addPurchase(array $params)
    {
        if (isset($params['ap_vp_st']) && $params['ap_vp_st'] != '') {
            $params['ap_vp_st'] = strtotime($params['ap_vp_st']);
        }
        if (isset($params['ap_vp_et']) && $params['ap_vp_et'] != '') {
            $params['ap_vp_et'] = strtotime($params['ap_vp_et']);
        }
        if (isset($params['predict_shipment_time']) && $params['predict_shipment_time'] != '') {
            $params['predict_shipment_time'] = strtotime($params['predict_shipment_time']);
        }
        $getAP = PeriodsAddPurchase::where(['period' => $params['period'], 'package_id' => $params['package_id']])
            ->find();
        if (!empty($getAP)) {
            return -1;
        }
        if ($params['periods_type'] == 1 || $params['periods_type'] == 0) {

            if (!empty($params['is_force_merge']) && !empty($params['period']) && $params['is_force_merge'] == 1) {
                $redis_config = config('cache.stores.redis');
                $redis_config['select'] = 0;
                $conn         = new \think\cache\driver\Redis($redis_config);
                if(!$conn->sIsMember('vinehoo.details.channel', $params['period'])){
                    throw new Exception('期数不是【渠道且安全验证】, 不能勾选 【强制合并发货】');
                }
            }
            return PeriodsAddPurchase::create($params);
        }
        return 0;
    }

    /**
     * 查询加购
     * @param array $params
     * @return PeriodsAddPurchase[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getAddPurchase(array $params)
    {
        $whereOr = [];
        if (!empty($params['use_flash'])) {
            $whereOr[] = ['use_flash', '=', $params['use_flash']];
        }
        if (!empty($params['use_second'])) {
            $whereOr[] = ['use_second', '=', $params['use_second']];
        }

        $where = [];
        //    if (isset($params['use_leftover']) && $params['use_leftover'] != '') {
        //        $where[] = ['use_leftover', '=', $params['use_leftover']];
        //    }
        if (isset($params['period']) && $params['period'] != '') {
            $where[] = ['period', '=', $params['period']];
        }
        if (isset($params['ap_title']) && $params['ap_title'] != '') {
            $where[] = ['ap_title', 'like', '%' . $params['ap_title'] . '%'];
        }
        if (isset($params['is_ap']) && $params['is_ap'] != '') {
            $where[] = ['is_ap', '=', $params['is_ap']];
        }
        if (isset($params['is_vp']) && $params['is_vp'] == 1) {
            $where[] = ['ap_vp_st', '<=', time()];
            $where[] = ['ap_vp_et', '>=', time()];
        }


        $per = PeriodsAddPurchase::where($where)
            ->where(function ($query) use ($whereOr) {
                $query->whereOr($whereOr);
            })
            ->order('sort', "DESC")
            ->order('id', "DESC")
            ->select()
            ->map(function ($item) {
                $item->predict_shipment_time = date('Y-m-d', $item->predict_shipment_time);
                $item->ap_product_img        = env('ALIURL') . $item->ap_product_img;
                return $item;
            });
        !empty($per) && $per = $per->toArray();

        // 返回套餐相同仓库顺手购
        if (!empty($params['package_id']) && !empty($per)) {
            //下单确认页的商品只有代发订单时，不返回顺手购给前端:https://devops.aliyun.com/projex/project/8ab3146db7bf4a79876e3a5333/task#viewIdentifier=56699b99ca87de064b75037ff1&openWorkitemIdentifier=d2786f5aea3fdf20c039a1dfcc
            $params['package_id'] = array_values(array_filter(explode(',', strval($params['package_id']))));
            $package = Es::name('periods_set')->where([
                ['id', 'in', $params['package_id']]
            ])->field('id,period_id')->select()->toArray();
            if (!empty($package)) {
                $period_ids = array_column($package, 'period_id');
                $periods = Es::name('periods')->where([
                    ['id', 'in', $period_ids]
                ])->field('id,is_supplier_delivery')->select()->toArray();
                if (!empty($periods)) {
                    $is_supplier_delivery = true;
                    foreach ($periods as $v) {
                        if ($v['is_supplier_delivery'] != 1) {
                            $is_supplier_delivery = false;
                            break;
                        }
                    }
                    if ($is_supplier_delivery) {
                        $per = [];
                    }
                }
            }
            

        //     $package_id = $params['package_id'];
        //     foreach ($per as $v) {
        //         $package_id[] = $v['package_id'];
        //     }
        //     $package_id = array_values(array_unique($package_id));

        //     $package = new PeriodsPackage(0);
        //     // 获取套餐仓库
        //     $warehouse = $package->getPackageShippingWarehouse($package_id);
        //     $warehouse_id = [];
        //     foreach ($params['package_id'] as $v) {
        //         $w_info = $warehouse[$v] ?? [];
        //         $w_ids = array_values(array_unique(array_values($w_info)));
        //         if (empty($w_ids) || count($w_ids) > 1) {
        //             $warehouse_id = [];
        //             break;
        //         }

        //         $warehouse_id[] = $w_ids[0];
        //     }
        //     foreach ($per as $k => $v) {
        //         $w_info = $warehouse[$v['package_id']] ?? [];
        //         $w_ids = array_values(array_unique(array_values($w_info)));
        //         if (empty($w_ids) || count($w_ids) > 1) {
        //             unset($per[$k]);
        //             continue;
        //         }
        //         $w_id = $w_ids[0];
        //         if (!in_array($w_id, $warehouse_id)) {
        //             unset($per[$k]);
        //         }
        //     }
        }

        return array_values($per);
    }

    /**
     * 更新配置
     * @param array $params
     * @return PeriodsAddPurchase
     */
    public function updateAP(array $params)
    {
        if (isset($params['ap_vp_st']) && $params['ap_vp_st'] != '') {
            $params['ap_vp_st'] = strtotime($params['ap_vp_st']);
        }
        if (isset($params['ap_vp_et']) && $params['ap_vp_et'] != '') {
            $params['ap_vp_et'] = strtotime($params['ap_vp_et']);
        }
        if (isset($params['predict_shipment_time']) && $params['predict_shipment_time'] != '') {
            $params['predict_shipment_time'] = strtotime($params['predict_shipment_time']);
        }

        if (!empty($params['is_force_merge']) && !empty($params['period']) && $params['is_force_merge'] == 1) {
            $redis_config = config('cache.stores.redis');
            $redis_config['select'] = 0;
            $conn         = new \think\cache\driver\Redis($redis_config);
            if(!$conn->sIsMember('vinehoo.details.channel', $params['period'])){
                throw new Exception('期数不是【渠道且安全验证】, 不能勾选 【强制合并发货】');
            }
        }
        return PeriodsAddPurchase::where('id', $params['id'])->update($params);
    }

    /**
     * 更新加购数据
     * @param array $where
     * @param array $data
     * @return PeriodsAddPurchase
     */
    public function apDisabled(array $where, array $data)
    {
        return PeriodsAddPurchase::where($where)->update($data);
    }

    /**
     * 待售商品处理
     * @param array $params 请求参数
     * @return bool
     */
    public function goodsForSaleHandle($params)
    {
        $this->autoAddThemeActivity($params['period']); //产品包含香槟 自动添加到 香槟专题活动

        if (empty($params['table'])) {
            return true;
        }
        $periods_type = 0;
        switch ($params['table']) {
            case "vh_periods_flash":
                $periods_type = 0;
                break;

            default:
                return true;
        }
        $package_ser = new Package($periods_type);
        $periods_ser = new Periods($periods_type);

        //根据期数查询期数订金套餐列表
        $where = [
            ['period_id', '=', $params['period']],
            ['is_hidden', '=', 0],
            ['is_deposit', '=', 1],
            // ['deposit_coupon_id', '=', 0]
        ];
        $exist = $package_ser->ConditionalQueryPackageExists($where);
        if (!empty($exist)) {
            // 更新 json 文件
            $periods_ser->create_period_json(intval($params['period']), $periods_type,0,-1);
            // 刷新CDN
            $periods_ser::CDNrefreshObject(intval($params['period']));
        }

        return true;
    }

    /**
     * 在售商品处理
     * @param array $params 请求参数
     * @return bool
     */
    public function goodsOnSaleHandle($params)
    {
        $this->autoAddThemeActivity($params['period']); //产品包含香槟 自动添加到 香槟专题活动

        if (empty($params['table'])) {
            return true;
        }
        $periods_type = $this->period_table[$params['table']] ?? 0;
        $package_ser = new Package($periods_type);
        $periods_ser = new Periods($periods_type);

        // 查询期数
        $period_info = $periods_ser->getOne(intval($params['period']));
        //秒杀商品处理
        if($periods_type == 0 && !empty($period_info['is_seckill']) && $period_info['is_seckill'] == 1){
            $resp = post_url('http://go-seckill/seckill/v3/vest/salesStart', json_encode(['period'=>$period_info['id']]),['Content-Type: application/json;charset=utf-8']);
            Log::info("miaosa" . $resp);
        }

        try {
            $is_update_purchase_time = false;
            // 商品上架修改预计采购时间
            if (isset($params['old']['onsale_status']) && $params['old']['onsale_status'] != 2) {
                $is_update_purchase_time = true;
                
            } else {
                // 预计采购时间
                $es_info = Es::name('periods')
                    ->where([['_id', '=', intval($params['period'])]])
                    ->field('estimate_purchase')
                    ->find();
                if (empty($es_info['estimate_purchase'])) {
                    $is_update_purchase_time = true;
                }
            }
            
            if ($is_update_purchase_time) {
                //更新期数预计采购时间
                $periods_ser->updatePurchaseTime(intval($params['period']), $period_info);
            }

            // 更新代发期数发货时间
            if ($is_update_purchase_time && !empty($period_info['is_supplier_delivery']) && $period_info['is_supplier_delivery'] == 1) {
                $periods_ser->updateDfDeliveryTime(intval($params['period']), [$period_info]);
            }
            
        } catch (\Exception $e) {
            Log::error("更新期数预计采购时间 ERROR：{$e->getMessage()}");
        }
        if ($params['table'] != 'vh_periods_flash') {
            return true;
        }
        //根据期数查询期数订金套餐列表
        $list = $package_ser->depositPackageList($params['period'], 'id,deposit_coupon_id', [
            ['deposit_coupon_id', '>', 0]
        ]);
        if (!empty($list)) {
            
            if (!empty($period_info)) {
                $currentHour = date('H', strtotime($period_info['sell_time']));// 提取开售时间小时
                // 定金商品开售时间在【9点-21点】之间立即发送对定金用户发送短信，如果开售时间在【21点-次日9点】的，次日早上十点如果定金商品还有库存，需对未支付尾款的定金用户发送短信通知，只发一次。
                if (intval($currentHour) >= 9 && intval($currentHour) < 21) {
                    // 发送订金信息推送
                    $periods_ser->SendDepositSms($period_info, $list);
                }
            }
            // 更新 json 文件
            $periods_ser->create_period_json(intval($params['period']), $periods_type,0,-1);
            // 刷新CDN
            $periods_ser::CDNrefreshObject(intval($params['period']));
        }

        return true;
    }

    /**
     * 下架商品处理
     * @param array $params 请求参数
     * @return bool
     */
    public function goodsOffShelfHandle($params)
    {
        $periods_type = 0;
        switch ($params['table']) {
            case "vh_periods_flash":
                $periods_type = 0;
                break;

            default:
                return true;
        }

        /**获取距离下一个17点相隔分钟 */
        $currentDateTime = new \DateTime();  // 获取当前时间
        $currentHour     = $currentDateTime->format('H');  // 提取当前小时
        if (intval($currentHour) >= 17) {
            // 如果当前时间晚于等于17点，目标时间为明天的17点
            $targetTime = new \DateTime($currentDateTime->format('Y-m-d') . ' 17:00:00');
            $targetTime->modify('+1 day');
        } else {
            // 否则，目标时间为今天的17点
            $targetTime = new \DateTime($currentDateTime->format('Y-m-d') . ' 17:00:00');
        }
        $interval     = $currentDateTime->diff($targetTime);  // 计算时间间隔
        $totalMinutes = ($interval->days * 24 * 60) + ($interval->h * 60) + $interval->i + 1;  // 将时间间隔转换为分钟

        #期数下架未付尾款的订金订单批量退款(添加超时任务)
        $url  = env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add';
        $body = json_encode([
            'namespace' => 'tp6-commodities',
            'key'       => 'depositRefund.' . $params['period'],
            'data'      => base64_encode(json_encode([
                'period'      => $params['period'],
                'period_type' => $periods_type,
            ])),
            'callback'  => env('ITEM.COMMODITIES_URL') . '/commodities/v3/ap/depositRefund',
            'timeout'   => $totalMinutes . 'm',
        ]);
        curlRequest($url, $body);

        #期数下架未付尾款的订金订单批量退款
        // $url = env('ITEM.ORDERS_URL') . '/orders/v3/additional/depositRefund';
        // $body = json_encode([
        //     'period'      => $params['period'],
        //     'period_type' => $periods_type,
        // ]);
        // curlRequest($url, $body);

        return true;
    }

    /**
     * 更新秒发筛选标签商品状态
     * @param array $params 请求参数
     * @return bool
     */
    public function updateSecondFiltersGoods($params)
    {
        $period        = $params['period'] ?? 0;
        $onsale_status = $params['onsale_status'] ?? 0;
        $table         = $params['table'] ?? '';
        if ($table != "vh_periods_second") {
            return true;
        }

        // 查询绑定的期数
        $goods = Db::name('periods_second_filters_goods')
            ->where('periods', $period)
            ->column('id,filters_id,status');
        if (!empty($goods)) {
            $goodsid = array_column($goods, 'id');
            // 修改商品状态
            Db::name('periods_second_filters_goods')
                ->whereIn('id', $goodsid)
                ->update(['status' => 0, 'onsale_status' => $onsale_status]);

            $filtersid = [];
            foreach ($goods as $v) {
                if ($v['status'] == 1) {
                    $filtersid[] = $v['filters_id'];
                }
            }
            if (!empty($filtersid)) {
                // 显示商品数
                $filters_goods = Db::name('periods_second_filters_goods')
                    ->where('status', 1)
                    ->whereIn('filters_id', $filtersid)
                    ->group('filters_id')
                    ->column('filters_id,count(1) as c', 'filters_id');
                $filters_id    = [];
                foreach ($filtersid as $v) {
                    if (empty($filters_goods[$v])) {
                        $filters_id[] = $v;
                    } else {
                        if (empty($filters_goods[$v]['c'])) {
                            $filters_id[] = $v;
                        }
                    }
                }

                //修改筛选失效
                if (!empty($filters_id)) {
                    Db::name('periods_second_filters')
                        ->whereIn('id', $filters_id)
                        ->update(['status' => 0]);
                }
            }
        }

        return true;
    }

    /**
     * @方法描述: 产品包含香槟 自动添加到 香槟专题活动
     * <AUTHOR>
     * @Date 2023/11/27 18:10
     * @param $period_id
     * @return bool
     */
    public function autoAddThemeActivity($period_id)
    {
        try {
            $types    = ['香槟', '烈酒'];
            $type_arr = $find_type = [];

            $product_types = Db::connect('wiki')->name('product_type')->order('fid', 'desc')->column('id,name,fid');
            $product_types = buildTree($product_types, 'children', 'fid');
            $product_types = array_column($product_types, null, 'name');

            foreach ($types as $type) {
                $type_arr[$type] = tree_to_array([$product_types[$type]]);
                $find_type       = array_merge($find_type, array_column($type_arr[$type], 'name'));
            }


            $period = Es::name(Es::PERIODS)->where([
                ['periods_type', 'in', [0, 1, 2, 3]],
                ['is_channel', '=', 0],
                ['id', '=', $period_id],
                ['product_category', 'in', $find_type]
            ])->find();


            if (!empty($period)) {
                if (array_intersect($period['product_category'], array_column($type_arr['香槟'] ?? [], 'name'))) {
                    //香槟
                    //【白酒】及以下子分类都放在【白酒】
                    $activity_id = 50;//专题活动ID  50 香槟
                    if ($period['price'] >= 1000) {
                        //1000+（包含1000）：旗舰香槟
                        $label_id = Db::connect('marketing')->name('special_activity_label')->where('activity_id', $activity_id)
                            ->where('label_name', '旗舰香槟')
                            ->order('created_at', 'DESC')
                            ->value('id') ?? '194';
                    } elseif ($period['price'] >= 500) {
                        //500-1000（包含500）：高端香槟
                        $label_id = Db::connect('marketing')->name('special_activity_label')->where('activity_id', $activity_id)
                            ->where('label_name', '高端香槟')
                            ->order('created_at', 'DESC')
                            ->value('id') ?? '193';
                    } else {
                        //500-1000（包含500）：高端香槟
                        $label_id = Db::connect('marketing')->name('special_activity_label')->where('activity_id', $activity_id)
                            ->where('label_name', '主力香槟')
                            ->order('created_at', 'DESC')
                            ->value('id') ?? '171';
                    }

                } elseif (array_intersect($period['product_category'], array_column($type_arr['白酒'] ?? [], 'name'))) {
                    //白酒 【白酒】及以下子分类都放在【白酒】
                    $activity_id = 48;//专题活动ID  48 烈酒
                    $label_id    = Db::connect('marketing')->name('special_activity_label')->where('activity_id', $activity_id)->where('label_name', '白酒')->order('created_at', 'DESC')->value('id') ?? 161;

                } elseif (array_intersect($period['product_category'], array_column($type_arr['烈酒'] ?? [], 'name'))) {
                    //烈酒 【烈酒】下的【威士忌、白兰地、朗姆】放在对应板块，其他的类型都放在【其他】
                    $activity_id = 48;//专题活动ID  48 烈酒
                    $label_id = 162;//162 其他
                    $p_types     = array_intersect($period['product_category'], array_column($type_arr['烈酒'], 'name'));
                    $label_names = [
                        '157' => ['威士忌','调和威士忌','单一麦芽威士忌','单一谷物威士忌','其他威士忌',],
                        '158' => ['朗姆酒'],
                        '160' => ['白兰地'],
//                        '161' => ['龙舌兰'],
                    ];

                    foreach ($label_names as $lid => $lname) {
                        if (!empty(array_intersect($lname, $p_types))) {
                            $label_id = Db::connect('marketing')->name('special_activity_label')->where('activity_id', $activity_id)
                                ->where('label_name', $lname[0])
                                ->order('created_at', 'DESC')
                                ->value('id') ?? $lid;
                            break;
                        }
                    }
                }

                $res = \Curl::sepcActivityGoodsAdd([
                    'periods'              => $period['id'],
                    'goods_short_name'     => '',
                    'product_introduction' => '',
                    'activity_id'          => $activity_id,
                    'label_id'             => $label_id,
                    'status'               => 2,
                    'sort'                 => 2,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('autoAddThemeActivity 添加指定商品到相应专题活动: ' . $e->getMessage() . '  Period: ' . strval($period_id));
            return false;
        }

        return true;
    }

    /**
     * 订金期数下架自动退款
     * <AUTHOR>
     * @Date 2023/12/15
     * @param array $params
     * @return bool|string
     */
    public function depositRefund($params)
    {
        #数据验证
        $validate = Validate::rule([
            'period_type|频道' => 'require|number|>=:0',
            'period|期数' => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            return $validate->getError();
        }
        $periods_ser = new Periods(intval($params['period_type']));
        $period_info = $periods_ser->getOne(intval($params['period']), 'id,onsale_status');

        if (!empty($period_info['onsale_status']) && $period_info['onsale_status'] == 2) {
            Log::info('订金期数在售中，退款任务执行失败，期数：' . $params['period']);
            return true;
        }
        
        #期数下架未付尾款的订金订单批量退款
        $url = env('ITEM.ORDERS_URL') . '/orders/v3/additional/depositRefund';
        $body = json_encode([
            'period'      => intval($params['period']),
            'period_type' => intval($params['period_type']),
        ]);
        curlRequest($url, $body);
        
        return true;
    }
}