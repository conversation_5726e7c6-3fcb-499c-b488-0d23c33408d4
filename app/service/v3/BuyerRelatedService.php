<?php

namespace app\service\v3;

use app\BaseService2;
use app\ErrorCode;
use app\model\BuyerRelated;
use app\model\BuyerRelatedLog;
use app\model\OperateRelated;
use app\validate\BuyerRelatedValidate;
use think\facade\Db;
use think\facade\Log;

/**
 * 采购人员关联
 * Class BuyerRelatedService
 * @package app\service\v3
 */
class BuyerRelatedService extends BaseService2
{
    public function __construct()
    {
        $this->model       = new BuyerRelated;
        $this->validate    = BuyerRelatedValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2025/04/14 15:42
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id 主键id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //主键id
            }
            #endregion

            #region EQ operation_review_id 商品运营审核者id
            if (isset($param['operation_review_id']) && strlen($param['operation_review_id']) > 0) {
                $query->where('operation_review_id', "=", $param['operation_review_id']); //商品运营审核者id
            }
            #endregion

            #region EQ buyer_id 采购人id
            if (isset($param['buyer_id']) && strlen($param['buyer_id']) > 0) {
                $query->where('buyer_id', "=", $param['buyer_id']); //采购人id
            }
            #endregion

            #region LIKE buyer_name 采购人名称
            if (isset($param['buyer_name']) && strlen($param['buyer_name']) > 0) {
                $query->where('buyer_name', "LIKE", "{$param['buyer_name']}%"); //采购人名称
            }
            #endregion

            #region >= start_created_time 开始创建时间
            if (isset($param['start_created_time']) && strlen($param['start_created_time']) > 0) {
                $query->whereTime('created_time', '>=', $param['start_created_time']); //开始创建时间
            }
            #endregion

            #region < end_created_time 结束创建时间
            if (isset($param['end_created_time']) && strlen($param['end_created_time']) > 0) {
                $query->whereTime('created_time', '<', $param['end_created_time']); //结束创建时间
            }
            #endregion

            #region EQ vh_uid 操作用户id
            if (isset($param['vh_uid']) && strlen($param['vh_uid']) > 0) {
                $query->where('vh_uid', "=", $param['vh_uid']); //操作用户id
            }
            #endregion

            #region LIKE vh_vos_name VOS用户姓名
            if (isset($param['vh_vos_name']) && strlen($param['vh_vos_name']) > 0) {
                $query->where('vh_vos_name', "LIKE", "{$param['vh_vos_name']}%"); //VOS用户姓名
            }
            #endregion

        };
    }


    public function add($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        $buyer_ids = [];
        foreach ($param['buyer'] as $p_buyer) {
            $buyer_ids[] = $p_buyer['buyer_id'];
            $validate    = $this->check(__FUNCTION__ . '_buyer', $p_buyer);
            if ($validate !== true) {
                return $validate;
            }
        }
        if (!OperateRelated::where('operation_review_id', $param['operation_review_id'])->count()) {
            $this->throwError("关联运营人员未找到");
        }
        if ((BuyerRelated::where('buyer_id', 'in', $buyer_ids)->count() > 0) || (count(array_unique($buyer_ids)) != count($buyer_ids))) {
            $this->throwError("不能重复添加采购人员");
        }
        #endregion

        //region 插入数据
        Db::startTrans();
        try {
            $data = $log = [];
            foreach ($param['buyer'] as $p_buyer) {
                $idata                         = $ilog = [
                    'operation_review_id' => $param['operation_review_id'],
                    'buyer_id'            => $p_buyer['buyer_id'],
                    'buyer_name'          => $p_buyer['buyer_name'],
                    'created_time'        => time(),
                    'vh_uid'              => $param['vh_uid'],
                    'vh_vos_name'         => $param['vh_vos_name'],
                ];
                $ilog['operation_review_name'] = $param['operation_review_name'];
                $ilog['type']                  = 1;
                $data[]                        = $idata;
                $log[]                         = $ilog;
            }

            (new BuyerRelated)->saveAll($data);
            (new BuyerRelatedLog())->saveAll($log);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('插入数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('插入数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success();
    }

    public function del($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        //region 删除
        Db::startTrans();
        try {
            $res = 0;

            $model = $this->model->where('id', $param['id'])->find();
            if ($model) {
                $operation = OperateRelated::where('operation_review_id', $model['operation_review_id'])->find() ?? [];
                $log       = [
                    'operation_review_id'   => $model['operation_review_id'],
                    'buyer_id'              => $model['buyer_id'],
                    'buyer_name'            => $model['buyer_name'],
                    'created_time'          => time(),
                    'vh_uid'                => $param['vh_uid'],
                    'vh_vos_name'           => $param['vh_vos_name'],
                    'operation_review_name' => $operation['operation_review_name'] ?? '',
                    'type'                  => 2,
                ];
                (new BuyerRelatedLog())->save($log);
                $res = $model->delete() ? 1 : 0;
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('更新数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('更新数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($res);
    }

}
