<?php

namespace app\service\v3;

use app\BaseService2;
use app\model\BuyerRelatedLog;
use app\validate\BuyerRelatedLogValidate;

/**
 * 采购人员关联日志
 * Class BuyerRelatedLogService
 * @package app\service\v3
 */
class BuyerRelatedLogService extends BaseService2
{
    public function __construct()
    {
        $this->model       = new BuyerRelatedLog;
        $this->validate    = BuyerRelatedLogValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2025/04/14 15:42
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {
            
            #region EQ id 主键id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //主键id
            }
            #endregion
            
            #region EQ operation_review_id 商品运营审核者id
            if (isset($param['operation_review_id']) && strlen($param['operation_review_id']) > 0) {
                $query->where('operation_review_id', "=", $param['operation_review_id']); //商品运营审核者id
            }
            #endregion
            
            #region LIKE operation_review_name 商品运营审核者名称
            if (isset($param['operation_review_name']) && strlen($param['operation_review_name']) > 0) {
                $query->where('operation_review_name', "LIKE", "{$param['operation_review_name']}%"); //商品运营审核者名称
            }
            #endregion
            
            #region EQ buyer_id 采购人id
            if (isset($param['buyer_id']) && strlen($param['buyer_id']) > 0) {
                $query->where('buyer_id', "=", $param['buyer_id']); //采购人id
            }
            #endregion
            
            #region IN type 操作类型
            if (isset($param['type']) && strlen($param['type']) > 0) {
                $query->where('type', "IN", $param['type']); //操作类型:1=添加关联,2=解除关联
            }
            #endregion
            
            #region LIKE buyer_name 采购人名称
            if (isset($param['buyer_name']) && strlen($param['buyer_name']) > 0) {
                $query->where('buyer_name', "LIKE", "{$param['buyer_name']}%"); //采购人名称
            }
            #endregion
            
            #region >= start_created_time 开始创建时间
            if (isset($param['start_created_time']) && strlen($param['start_created_time']) > 0) {
                $query->whereTime('created_time', '>=' , $param['start_created_time']); //开始创建时间
            }
            #endregion

            #region < end_created_time 结束创建时间
            if (isset($param['end_created_time']) && strlen($param['end_created_time']) > 0) {
                $query->whereTime('created_time', '<' , $param['end_created_time']); //结束创建时间
            }
            #endregion
            
            #region EQ vh_uid 操作用户id
            if (isset($param['vh_uid']) && strlen($param['vh_uid']) > 0) {
                $query->where('vh_uid', "=", $param['vh_uid']); //操作用户id
            }
            #endregion
            
            #region LIKE vh_vos_name VOS用户姓名
            if (isset($param['vh_vos_name']) && strlen($param['vh_vos_name']) > 0) {
                $query->where('vh_vos_name', "LIKE", "{$param['vh_vos_name']}%"); //VOS用户姓名
            }
            #endregion
            
        };
    }

}



