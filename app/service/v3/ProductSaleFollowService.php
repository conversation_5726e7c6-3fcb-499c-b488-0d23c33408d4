<?php

namespace app\service\v3;

use app\BaseService2;
use app\ErrorCode;
use app\model\ProductSaleFollow;
use app\model\ProductSaleFollowRemark;
use app\model\ProductSaleFollowPeriod;
use app\service\es\Es;
use app\validate\ProductSaleFollowValidate;
use think\Exception;
use think\facade\Db;
use think\facade\Log;
use think\facade\Validate;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

/**
 * 产品销售跟进关注看板
 * Class ProductSaleFollowService
 * @package app\service\v3
 */
class ProductSaleFollowService extends BaseService2
{

    protected $model, $validate, $select_with;

    public function __construct()
    {
        $this->model       = new ProductSaleFollow();
        $this->validate    = ProductSaleFollowValidate::class;
        $this->select_with = ['period' => function ($query){
            $query->field('id,product_id,period,periods_type');
            
        }, 'remark'];
    }

    /**
     * @方法描述: 分页获取列表数据
     * <AUTHOR>
     * @Date 2024/03/26
     * @param $param
     * @return array|bool|mixed
     */
    public function list($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        #region where查询条件,分页,查询数据的处理
        $where = $this->builderWhere($param);
        $limit     = $param['limit'] ?? 10;
        $page      = $param['page'] ?? 1;
        $pagestart = ($page - 1) * $limit;
        $field     = $param['fields'] ?? "*";
        //endregion

        //region 分页获取数据
        Db::startTrans();
        try {
            // 跨境看板只查询跨境类型
            if (!empty($param['kanban_type']) && $param['kanban_type'] == 1) {
                $this->select_with = ['period' => function ($query){
                    $query->where('periods_type', 2);
                    $query->field('id,product_id,period,periods_type');
                }, 'remark'];
            }
            $model = $this->model->follow()
                ->field($field)
                ->with($this->select_with)
                ->where($where);
            // ->limit($pagestart, $limit);
            if (!empty($this->select_order)) {
                foreach ($this->select_order as $sort_field => $sort) {
                    $model->order($sort_field, $sort);
                }
            }
            if (!empty($this->hidden_field)) {
                $model->hidden($this->hidden_field);
            }

            $list = $model->select()->toArray();

            // 总条数
            // $total = $this->model->where($where)->count();

            // 获取实时数据更新
            $list = $this->GetRealTimeData($list);

            // 条件过滤
            $list = $this->listFilterate($param, $list);
            $total = count($list);

            // 排序分页
            $list = $this->listSortPaging($param, $list);


            $data  = [
                'list'  => $list,
                'total' => $total
            ];

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('查询数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('查询数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($this->listDataProcessing($data));
    }

    /**
     * @方法描述: 列表通过筛选条件过滤
     * <AUTHOR>
     * @Date 2024/03/25
     * @param $param
     * @return array
     */
    protected function listSortPaging($param, $list, $ty = 0)
    {
        $time_field = ['planned_completion_time', 'storage_time'];

        // 排序
        if (!empty($param['sort_field']) && !empty($param['sort_order'])) {
            $value = array_column($list, $param['sort_field']);
            if (in_array($param['sort_field'], $time_field)) {
                $value = array_map(function ($v) {
                    return !empty($v) ? strtotime($v) : 0;
                }, $value);
            }
            $order = $param['sort_order'] == 'desc' ? SORT_DESC : SORT_ASC;
            array_multisort($value, $order, $list);
        }

        // 分页处理
        if ($ty == 0) {
            $limit = $param['limit'] ?? 10;
            $page  = $param['page'] ?? 1;
            $start = ($page - 1) * $limit;
            $list  = array_slice($list, $start, $limit);
        }

        return $list;
    }

    /**
     * @方法描述: 列表通过筛选条件过滤
     * <AUTHOR>
     * @Date 2024/03/25
     * @param $param
     * @return array
     */
    protected function listFilterate($param, $list)
    {
        $buyer_id = [];
        #region EQ operator 采购人id
        if (isset($param['buyer_id']) && strlen($param['buyer_id']) > 0) {
            $buyer_id = explode(',', $param['buyer_id']);
        }
        #endregion

        $result = [];
        foreach ($list as &$v) {
            if (isset($param['is_slippage']) && strlen($param['is_slippage']) > 0) {
                $status = $param['is_slippage'] == 0 ? [0, 1] : [2, 3];
                if (!in_array($v['status'], $status)) {
                    continue;
                }
            }

            #region EQ operator 采购人id
            if (!empty($buyer_id) && !in_array($v['buyer_id'], $buyer_id)) {
                continue;
            }
            #endregion
            #region LIKE supplier 供应商
            if (isset($param['supplier_id']) && strlen($param['supplier_id']) > 0) {
                if ($v['supplier_id'] != $param['supplier_id']) {
                    continue;
                }
            }
            #endregion

            $result[] = $v;
        }

        return $result;
    }

    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2024/03/25
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {
            $query->where('is_delete', "=", 0);
            $query->where('kanban_type', "=", $param['kanban_type'] ?? 0);

            #region EQ id id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //id
            }
            #endregion

            // #region EQ operator 采购人id
            // if (isset($param['buyer_id']) && strlen($param['buyer_id']) > 0) {
            //     $query->where('buyer_id', "=", $param['buyer_id']); //采购人id
            // }
            // #endregion
            // #region LIKE supplier 供应商
            // if (isset($param['supplier_id']) && strlen($param['supplier_id']) > 0) {
            //     $query->where('supplier_id', "=", $param['supplier_id']);
            // }
            // #endregion

            #region EQ short_code 简码
            if (isset($param['short_code']) && strlen($param['short_code']) > 0) {
                $query->where('short_code', "like", "%{$param['short_code']}%");
            }
            #endregion


            #region = operate_id 运营ID
            if (isset($param['operation_id']) && strlen($param['operation_id']) > 0) {
                $operation_id = explode(',', $param['operation_id']);
                $query->where('operation_id', "in", $operation_id);
            }
            #endregion


            #region period 期数
            if (isset($param['period']) && strlen($param['period']) > 0) {
                $query->where('product_id', 'IN', function ($query) use ($param) {
                    $query->name('periods_product_inventory')
                        ->where('period', $param['period'])
                        ->field('product_id');
                });
            }
            #endregion

            #region chateau_id 酒庄ID
            if (isset($param['chateau_id']) && strlen($param['chateau_id']) > 0) {
                $query->where('product_id', 'IN', function ($query) use ($param) {
                    $query->table('vh_wiki.vh_products')
                        ->where('chateau_id', $param['chateau_id'])
                        ->field('id');
                });
            }
            #endregion

            #region is_sale 是否在售：0=否,1=是
            if (isset($param['is_sale']) && is_numeric($param['is_sale'])) {
                $periods_id = Es::name('periods')->where([
                        ['onsale_status', '=', 2]
                    ])->field('id')->select()->toArray();
                $periods_ids = array_column($periods_id, 'id');
                if ($param['is_sale'] == 0) {
                    $query->where('product_id', 'NOT IN', function ($query) use ($periods_ids) {
                        $query->name('periods_product_inventory')
                            ->where('period', 'in', $periods_ids)
                            ->field('product_id');
                    });
                } else {
                    $query->where('product_id', 'IN', function ($query) use ($periods_ids) {
                        $query->name('periods_product_inventory')
                            ->where('period', 'in', $periods_ids)
                            ->field('product_id');
                    });
                }
            }
            #endregion

            #region EQ is_slippage 是否逾期：0=未逾期,1=已逾期
            // if (isset($param['is_slippage']) && strlen($param['is_slippage']) > 0) {
            //     $status = $param['is_slippage'] == 0 ? [0, 1] : [2, 3];
            //     $query->where('status', "in", $status);
            // }
            #endregion

            #region >= start_completion_time 开始计划完成时间
            if (isset($param['start_completion_time']) && strlen($param['start_completion_time']) > 0) {
                $query->whereTime('planned_completion_time', '>=', strtotime($param['start_completion_time']));
            }
            #endregion

            #region >= start_completion_time 开始计划完成时间
            if (isset($param['end_completion_time']) && strlen($param['end_completion_time']) > 0) {
                if (strlen($param['end_completion_time']) == 10) {
                    $param['end_completion_time'] = $param['end_completion_time'] . ' 23:59:59';
                }
                $query->whereTime('planned_completion_time', '<=', strtotime($param['end_completion_time']));
            }
            #endregion

            #region >= start_completion_time 开始创建时间
            if (isset($param['start_created_time']) && strlen($param['start_created_time']) > 0) {
                $query->whereTime('created_time', '>=', $param['start_created_time']);
            }
            #endregion

            #region >= start_completion_time 结束创建时间
            if (isset($param['end_created_time']) && strlen($param['end_created_time']) > 0) {
                if (strlen($param['end_created_time']) == 10) {
                    $param['end_created_time'] = $param['end_created_time'] . ' 23:59:59';
                }
                $query->whereTime('created_time', '<=', $param['end_created_time']);
            }
            #endregion
        };
    }

    /**
     * @方法描述: 获取实时数据更新
     * <AUTHOR>
     * @Date 2024/3/25
     * @param array $list
     * @return \Closure
     */
    public function GetRealTimeData($list)
    {
        $time = time();
        if (empty($list)) {
            return $list;
        }
        $short_code = $period_id = $main_period = [];
        foreach ($list as $k => $v) {
            $period = 0;
            foreach ($v['period'] as $item) {
                $period_id[] = $item['period'];
                if ($period < $item['period']) {
                    $period = $item['period'];
                }
            }
            $list[$k]['main_period'] = $period;
            $main_period[] = $period;
            $short_code[] = $v['short_code'];
        }
        // 获取聚合数据
        $aggregated = $this->getAggregatedData($main_period, $short_code, $period_id);
        
        // 产品信息
        $vh_products = $aggregated['products'];
        // 套餐信息
        $es_packages = $aggregated['es_packages'];
        // 期数信息
        $es_periods = $aggregated['es_periods'];
        // 已售瓶数
        $sale_num = $aggregated['sale_num'];
        // 近3天已售瓶数
        $threeday_sale_num = $aggregated['threeday_sale_num'];
        // 萌牙库存信息
        $my_inventory_data = $aggregated['my_inventory_data'];
        // 期数产品信息
        $periods_product = $aggregated['periods_product'];
        // 萌牙上架信息
        $storage_goods = $aggregated['storage_goods'];

        $update_data = [];
        foreach ($list as $k => &$v) {
            
            $is_update = false;
            // 最后一期期数信息
            $main_period_info = $es_periods[$v['main_period']] ?? [];
            $v['import_type'] = $main_period_info['import_type'] ?? 0;
            $v['supplier'] = $main_period_info['supplier'] ?? '';
            $v['supplier_id'] = $main_period_info['supplier_id'] ?? 0;
            $v['buyer_name'] = $main_period_info['buyer_name'] ?? '';
            $v['buyer_id'] = $main_period_info['buyer_id'] ?? 0;
            // 萌牙库存
            $v['my_inventory'] = $my_inventory_data[$v['short_code']] ?? 0;

            // 最近入库时间
            $v['storage_time'] = '';
            if (!empty($storage_goods[$v['short_code']]['update_time'])) {
                $v['storage_time'] = date('Y-m-d H:i:s', $storage_goods[$v['short_code']]['update_time']);
            }

            // 成本
            $v['costprice'] = $periods_product["{$v['main_period']}_{$v['short_code']}"]['costprice'] ?? 0;
            $v['costprice'] = round($v['costprice'], 2);
            // 库存金额(库存金额 = 库存*成本)
            $v['inventory_amount'] = round($v['my_inventory'] * $v['costprice'], 2);


            // 计划进度：进度% =（采购数-库存）/采购数 * 100 
            $v['planned_progress'] = round((intval($v['purchase_nums'] - $v['my_inventory']) / $v['purchase_nums']) * 100, 2);

            if ($v['planned_progress'] == 100 && $v['completion_time'] == 0) { // 完成计划
                $v['completion_time'] = $time;
                $is_update = true;
            } else if ($v['planned_progress'] != 100 && $v['completion_time'] != 0) { // 计划未完成
                $v['completion_time'] = 0;
                $is_update = true;
            }

            $end_time = $v['completion_time'] > 0 ? $v['completion_time'] : $time;
            $v['days'] = getTimeDiffDays($v['planned_completion_time'], $end_time);
            if ($v['completion_time'] == 0) {
                $v['status'] = 0; // 待完成
                if ($v['planned_completion_time'] < $time) { // 已逾期
                    $v['status'] = 2;
                }
            } else {
                $v['status'] = 3; // 逾期完成
                if ($v['planned_completion_time'] >= $v['completion_time']) { // 已完成
                    $v['status'] = 1;
                }
            }

            if ($is_update) {
                $update_data[] = [
                    'id' => $v['id'],
                    'status' => $v['status'],
                    'completion_time' => $v['completion_time'],
                ];
            }

            // 计划完成时间
            $v['planned_completion_time'] = date('Y-m-d', $v['planned_completion_time']);
            // 完成时间
            $v['completion_time'] = $v['completion_time'] > 0 ? date('Y-m-d', $v['completion_time']) : '';

            // 产品信息
            $product = $vh_products[$v['short_code']] ?? [];
            $v['country_name_cn'] = $product['country_name_cn'] ?? '';
            $v['winery_name_cn'] = $product['winery_name_cn'] ?? '';
            $v['product_type_name'] = $product['product_type_name'] ?? '';
            $v['cn_product_name'] = $product['cn_product_name'] ?? '';
            $v['en_product_name'] = $product['en_product_name'] ?? '';

            $real_sale_num = $three_day_sale_num = 0;
            $period = [];
            foreach ($v['period'] as $kk => $item) {
                $real_sale_num += $sale_num[$item['period']][$v['short_code']] ?? 0;
                $three_day_sale_num += $threeday_sale_num[$item['period']][$v['short_code']] ?? 0;

                $period_info = $es_periods[$item['period']] ?? [];
                $item['onsale_status'] = $period_info['onsale_status'] ?? 0;
                $item['is_channel']    = $period_info['is_channel'] ?? 0;
                $list[$k]['period'][$kk] = $item;

                $period[$kk] = $item['period'];
            }

            // 排序
            array_multisort($period, SORT_DESC, $v['period']);
            $sale_period = [];
            foreach ($v['period'] as $kk => $item) {
                if ($item['onsale_status'] == 2) {
                    $sale_period[] = $item;
                    unset($v['period'][$kk]);
                }
            }
            $v['period'] = array_merge($sale_period, $v['period']);

            //已售瓶数
            $v['real_sale_num'] = $real_sale_num;
            //近3天已售瓶数
            $v['three_day_sale_num'] = $three_day_sale_num;

            $v['package'] = [];
            $es_package = $es_packages[$v['main_period']] ?? [];
            foreach ($es_package as $p) {
                $is_use = false;
                $associated_products = json_decode($p['associated_products'], true);
                foreach ($associated_products as $a) {
                    if (empty($a['product_id'])) {
                        continue;
                    }
                    $product_id = $a['product_id'];
                    if (!is_array($a['product_id'])) {
                        $product_id = [$a['product_id']];
                    }
                    if (in_array($v['product_id'], $product_id)) {
                        $is_use = true;
                    }
                }
                if ($is_use) {
                    $v['package'][] = $p;
                }
            }
        }

        if (!empty($update_data)) {
            $this->model->saveAll($update_data);
        }

        return $list;
    }

    /**
     * @方法描述: 
     * <AUTHOR>
     * @Date 2024/03/25
     * @param array $main_period 最后一期期数
     * @param array $short_code 简码
     * @param array $period_id 期数
     * @param array $ty 查询类型：0-所有，1-产品
     * @return array
     */
    public function getAggregatedData($main_period, $short_code, $period_id, $ty = 0)
    {
        $url = env('ITEM.COMMODITIES_SERVICES') . '/commodities_server/v3/data_analysis/ProductSaleFollowDatas';
        $data = json_encode([
            'main_period' => $main_period,
            'short_code' => $short_code,
            'period_id' => $period_id,
            'ty' => $ty,
        ]);

        $res = curlRequest($url, $data);

        return [
            'products' => $res['data']['products'] ?? [],
            'es_packages' => $res['data']['packages'] ?? [],
            'es_periods' => $res['data']['periods'] ?? [],
            'sale_num' => $res['data']['sale_num'] ?? [],
            'my_inventory_data' => $res['data']['my_inventory_data'] ?? [],
            'periods_product' => $res['data']['periods_product'] ?? [],
            'storage_goods' => $res['data']['storage_goods'] ?? [],
            'threeday_sale_num' => $res['data']['threeday_sale_num'] ?? [],
        ];
    }

    /**
     * @方法描述: 根据简码搜索
     * <AUTHOR>
     * @Date 2024/03/25
     * @param $param
     * @return mixed
     */
    public function shortCodeSearch($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion
        $where = [
            ['short_code', '=', $param['short_code']]
        ];
        // 跨境看板只查询跨境类型
        if (!empty($param['kanban_type']) && $param['kanban_type'] == 1) {
            $where[] = ['periods_type', '=', 2];
        }

        $periods_product = Db::name('periods_product_inventory')
            ->where($where)
            ->group('short_code,period')
            ->order('period desc')
            ->column('short_code,period,periods_type');
        if (empty($periods_product)) {
            return $this->failed('简码未绑定期数', ErrorCode::EXEC_ERROR);
        }
        $main_period = 0;
        $period_id = [];
        foreach ($periods_product as $v) {
            if ($main_period < $v['period']) {
                $main_period = $v['period'];
            }
            $period_id[] = $v['period'];
        }

        // 排序
        array_multisort($period_id, SORT_DESC, $periods_product);

        // 获取聚合数据
        $aggregated = $this->getAggregatedData([$main_period], [$param['short_code']], $period_id);
        // 产品信息
        $vh_products = $aggregated['products'][$param['short_code']] ?? [];
        // 套餐信息
        $es_package = $aggregated['es_packages'][$main_period] ?? [];
        // 期数信息
        $es_periods = $aggregated['es_periods'];
        // 已售信息
        $sale_num = $aggregated['sale_num'];
        // 萌牙库存信息
        $my_inventory_data = $aggregated['my_inventory_data'];
        // 产品ID
        $vh_products['product_id'] = $product_id = $vh_products['id'] ?? 0;


        // 期数数据处理
        $es_period = [];
        $main_period = 0;
        foreach ($es_periods as $k => $v) {
            $period_id = $v['id'];
            if ($main_period < $period_id) {
                $main_period = $period_id;
            }
            $v['period'] = $period_id;
            $es_period[] = $v;
        }
        // 套餐数据处理
        $es_packages = [];
        foreach ($es_package as $p) {
            $is_use = false;
            $associated_products = json_decode($p['associated_products'], true);
            foreach ($associated_products as $a) {
                if (empty($a['product_id'])) {
                    continue;
                }
                $s_product_id = $a['product_id'];
                if (!is_array($a['product_id'])) {
                    $s_product_id = [$a['product_id']];
                }
                if (in_array($product_id, $s_product_id)) {
                    $is_use = true;
                }
            }
            if ($is_use) {
                $es_packages[] = $p;
            }
        }

        $real_sale_num = 0;
        foreach ($es_period as $v) {
            $real_sale_num += $sale_num[$v['id']][$param['short_code']] ?? 0;
        }

        foreach ($es_period as &$subarray) {
            unset($subarray['id']);
        }

        $result = $vh_products;
        $result = array_merge($result, $es_periods[$main_period] ?? []);
        unset($result['id']);
        $result['main_period'] = $main_period;
        $result['my_inventory'] = $my_inventory_data[$param['short_code']] ?? 0;
        $result['real_sale_num'] = $real_sale_num;
        $result['period'] = $es_period;
        $result['packages'] = $es_packages;

        return $this->success($this->listDataProcessing($result));
    }


    /**
     * @方法描述: 插入一条数据
     * <AUTHOR>
     * @Date 2024/3/26
     * @param $param
     * @return bool|mixed
     */
    public function add($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }

        // $period_id = [];
        // foreach ($param['period'] as $check_item) {
        //     $validate = $this->check('add_items', $check_item);
        //     if ($validate !== true) {
        //         return $validate;
        //     }
        // }
        #endregion

        //region 插入数据
        Db::startTrans();
        try {
            $param['creator_id']  = $param['vh_uid'];
            $param['creator'] = $param['vh_vos_name'];
            $param['planned_completion_time'] = strtotime($param['planned_completion_time'] . ' 23:59:59');
            $param['kanban_type'] = $param['kanban_type'] ?? 0;

            $model         = $this->model;
            $check_where = [
                ['short_code', '=', $param['short_code']],
                ['is_delete', '=', 0],
                ['kanban_type', '=', $param['kanban_type']],
            ];
            if (!empty($param['id'])) {
                $check_where[] = ['id', '<>', $param['id']];
                $model = $model->where('id', $param['id'])->find();
                if (empty($this->model)) {
                    throw new Exception('记录不存在');
                }
            }
            if ($this->model->where($check_where)->count()) {
                throw new Exception('简码重复');
            }


            $model->save($param);
            // $model->period()->delete();
            // $model->period()->saveAll($param['period']);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('add失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        return $this->success(['id' => $model->id]);
    }

    /**
     * @方法描述: 移除
     * <AUTHOR>
     * @Date 2024/3/26
     * @param $param
     * @return bool|mixed
     */
    public function remove($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion
        $error_msg = '';
        //region 插入数据
        Db::startTrans();
        try {
            $info = [
                'id' => $param['id'],
                'creator_id' => $param['vh_uid'],
                'creator' => $param['vh_vos_name'],
                'is_delete' => 1,
            ];

            $model = $this->model->where('id', $info['id'])->find();
            if (empty($this->model)) {
                throw new Exception('记录不存在');
            }

            $model->save($info);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('更新数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('更新数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        if (!empty($error_msg)) {
            return $this->failed($e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        return $this->success(['id' => $model->id]);
    }

    /**
     * @方法描述: 添加备注
     * <AUTHOR>
     * @Date 2024/3/26
     * @param $param
     * @return bool|mixed
     */
    public function addRemark($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        //region 插入数据
        Db::startTrans();
        try {
            $model = $this->model->where('id', $param['id'])->find();
            if (empty($this->model)) {
                throw new Exception('记录不存在');
            }

            Db::name('product_sale_follow_remark')->insert([
                'fid' => $param['id'],
                'creator_id' => $param['vh_uid'],
                'creator' => $param['vh_vos_name'],
                'created_time' => time(),
                'remark' => $param['remark'],
            ]);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('插入备注失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('插入备注失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        return $this->success(['id' => $model->id]);
    }

    /**
     * @方法描述: 分页获取备注列表数据
     * <AUTHOR>
     * @Date 2024/03/26
     * @param $param
     * @return array|bool|mixed
     */
    public function remarkList($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion
        $limit     = $param['limit'] ?? 10;
        $page      = $param['page'] ?? 1;
        $pagestart = ($page - 1) * $limit;
        $field     = $param['fields'] ?? "*";
        //endregion

        //region 分页获取数据
        Db::startTrans();
        try {
            $model = new ProductSaleFollowRemark();
            $where = ['fid' => $param['id']];
            $model = $model->field($field)->where($where)->limit($pagestart, $limit);
            if (!empty($this->select_order)) {
                foreach ($this->select_order as $sort_field => $sort) {
                    $model->order($sort_field, $sort);
                }
            }
            if (!empty($this->hidden_field)) {
                $model->hidden($this->hidden_field);
            }

            $list = $model->select()->toArray();

            // 总条数
            $total = $model->where($where)->count();
            $data  = [
                'list'  => $list,
                'total' => $total
            ];

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('查询备注数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('查询备注数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($this->listDataProcessing($data));
    }


    /**
     * @方法描述: 导入
     * <AUTHOR>
     * @Date 2024/03/25
     * @param $param
     * @return mixed
     */
    public function import($params)
    {
        $time = time();
        #region验证
        $validate = $this->check(__FUNCTION__, $params);
        if ($validate !== true) {
            return $validate;
        }
        // 下载文件
        $tempPath = saveLocal($params['file']);
        $creator_id  = $params['vh_uid'];
        $creator = $params['vh_vos_name'];

        // $file = request()->file('file');
        // $tempPath = $file->getRealPath();
        $spreadsheet = IOFactory::load($tempPath);
        $excelData = $spreadsheet->getActiveSheet()->toArray(null, true, true, true);
        if (empty($excelData) || count($excelData) == 1) {
            return $this->failed("空表格", ErrorCode::EXEC_ERROR);
        }
        @unlink($tempPath);
        unset($excelData[1]);
        $param = $short_code_all = $operation_name_all = [];
        // 格式化参数
        foreach ($excelData as $k => $v) {
            $no = ($k - 1);
            if (!empty($v['A'])) {
                $validate = $this->check('remove_itme', $v);
                if ($validate !== true) {
                    return $validate;
                }

                $v['D'] = trim(strval($v['D']));
                $planned_completion_time = strtotime($v['D'] . ' 23:59:59');
                if (empty($planned_completion_time)) {
                    $this->failed("第 {$no} 行，计划完成时间格式错误【{$v['D']}】", ErrorCode::EXEC_ERROR);
                }
                $purchase_nums = empty($v['C']) ? 0 : trim(strval($v['C']));
                if (!is_numeric($purchase_nums)) {
                    $this->failed("第 {$no} 行，采购数量必须是数字【{$v['C']}】", ErrorCode::EXEC_ERROR);
                }
                $short_code = trim(strval($v['A']));
                $operation_name = trim(strval($v['B']));
                $param[] = [
                    'short_code' => $short_code,
                    'planned_completion_time' => $planned_completion_time,
                    'purchase_nums' => $purchase_nums,
                    'operation_name' => $operation_name,
                    'remark' => trim(strval($v['E'])),
                ];
                $short_code_all[] = $short_code;
                $operation_name_all[] = $operation_name;
            }
        }

        if (empty($param)) {
            return $this->failed("空表格", ErrorCode::EXEC_ERROR);
        }

        // 查询运营
        $admins = Db::table('vh_authority.vh_admins')
            ->whereIn('realname', $operation_name_all)
            ->column('id', 'realname');
        $not_operation = [];
        foreach ($operation_name_all as $v) {
            if (empty($admins[$v])) {
                $not_operation[] = $v;
            }
        }
        if (!empty($not_operation)) {
            return $this->failed("未查询运营信息：" . implode(',', $not_operation), ErrorCode::EXEC_ERROR);
        }

        // 查询简码是否已添加
        $exist_short_code = Db::name('product_sale_follow')
            ->whereIn('short_code', $short_code_all)
            ->where('is_delete', 0)
            ->where('kanban_type', $params['kanban_type'] ?? 0)
            ->column('short_code');
        if (!empty($exist_short_code)) {
            return $this->failed("简码已存在：" . implode(',', $exist_short_code), ErrorCode::EXEC_ERROR);
        }

        $where = [
            ['short_code', 'in', $short_code_all]
        ];
        // 跨境看板只查询跨境类型
        if (!empty($params['kanban_type']) && $params['kanban_type'] == 1) {
            $where[] = ['periods_type', '=', 2];
        }
        // 查询关联期数
        $periods_product = Db::name('periods_product_inventory')
            ->where($where)
            ->group('short_code,period')
            ->order('period desc')
            ->column('short_code,period,periods_type');
        $periods_product_row = $period_id = [];
        foreach ($periods_product as $v) {
            $periods_product_row[$v['short_code']][] = $v;
            $period_id[] = $v['period'];
        }

        $not_short_code = $main_period = [];
        foreach ($param as $k => $v) {
            if (empty($periods_product_row[$v['short_code']])) {
                $not_short_code[] = $v['short_code'];
                continue;
            }
            $main_period_id = 0;
            foreach ($periods_product_row[$v['short_code']] as $r) {
                if ($main_period_id < $r['period']) {
                    $main_period_id = $r['period'];
                }
            }
            $param[$k]['main_period'] = $main_period_id;
            $main_period[] = $main_period_id;
        }
        if (!empty($not_short_code)) {
            return $this->failed('未绑定期数简码：' . implode(',', $not_short_code), ErrorCode::EXEC_ERROR);
        }

        // 获取聚合数据
        $aggregated = $this->getAggregatedData($main_period, $short_code_all, $period_id, 1);
        // 产品信息
        $vh_products = $aggregated['products'] ?? [];
        // $es_periods = $aggregated['es_periods'] ?? [];

        $not_short_code = [];
        foreach ($param as $v) {
            if (empty($vh_products[$v['short_code']])) {
                $not_short_code[] = $v['short_code'];
                continue;
            }
        }

        if (!empty($not_short_code)) {
            return $this->failed('磐石商品档案查询失败，简码：' . implode(',', $not_short_code), ErrorCode::EXEC_ERROR);
        }

        Db::startTrans();
        try {
            $max_id = Db::name('product_sale_follow')->lock(true)->max('id');

            $info = $period_info = $remark_info = [];
            foreach ($param as $v) {
                $max_id++;
                $products_info = $vh_products[$v['short_code']];
                $info[] = [
                    'id' => $max_id,
                    'product_id' => $products_info['id'],
                    'bar_code' => $products_info['bar_code'],
                    'short_code' => $products_info['short_code'],
                    'planned_completion_time' => $v['planned_completion_time'],
                    'purchase_nums' => $v['purchase_nums'],
                    'main_period' => $v['main_period'],
                    'operation_name' => $v['operation_name'],
                    'operation_id' => $admins[$v['operation_name']],
                    'kanban_type' => $params['kanban_type'] ?? 0,
                    'creator' => $creator,
                    'creator_id' => $creator_id,
                    'created_time' => $time,
                    'update_time' => $time,
                ];
                $s_period = [];
                foreach ($periods_product_row[$v['short_code']] as $r) {
                    $s_period[] = $r['period'];
                    // $period_info[] = [
                    //     'fid' => $max_id,
                    //     'period' => $r['period'],
                    //     'periods_type' => $r['periods_type'],
                    // ];
                }
                if (!empty($v['remark'])) {
                    $remark_info[] = [
                        'fid' => $max_id,
                        'remark' => $v['remark'],
                        'creator' => $creator,
                        'creator_id' => $creator_id,
                        'created_time' => $time,
                    ];
                }
            }
            Db::name('product_sale_follow')->insertAll($info);
            // Db::name('product_sale_follow_period')->insertAll($period_info);
            if (!empty($remark_info)) {
                Db::name('product_sale_follow_remark')->insertAll($remark_info);
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('导入数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('导入数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        return $this->success();
    }

    /**
     * @方法描述: 导出
     * <AUTHOR>
     * @Date 2024/04/01
     * @param $param
     * @return array|bool|mixed
     */
    public function export($param)
    {
        #region where查询条件,分页,查询数据的处理
        $where = $this->builderWhere($param);
        $field     = $param['fields'] ?? "*";
        //endregion

        //region 获取数据
        Db::startTrans();
        try {
            // 跨境看板值查询跨境类型
            if (!empty($param['kanban_type']) && $param['kanban_type'] == 1) {
                $this->select_with = ['period' => function ($query){
                    $query->where('periods_type', 2);
                    $query->field('id,product_id,period,periods_type');
                }, 'remark'];
            }

            $model = $this->model
                ->field($field)
                ->with($this->select_with)
                ->where($where);
            // ->limit($pagestart, $limit);
            if (!empty($this->select_order)) {
                foreach ($this->select_order as $sort_field => $sort) {
                    $model->order($sort_field, $sort);
                }
            }
            if (!empty($this->hidden_field)) {
                $model->hidden($this->hidden_field);
            }

            $list = $model->select()->toArray();

            // 总条数
            // $total = $this->model->where($where)->count();

            // 获取实时数据更新
            $list = $this->GetRealTimeData($list);
            // 条件过滤
            $list = $this->listFilterate($param, $list);
            // 排序
            $list = $this->listSortPaging($param, $list, 1);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('查询数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('查询数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion
        // 创建新的 Excel 对象
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // 表头
        $headers = ['简码', '期数', '供应商', '酒庄', '品名', '售价', '成本', '计划完成期限', '进度', '入库时间', '采购量', '已售瓶数', '近3天销售瓶数', '萌牙库存', '库存金额', '备注'];

        // 批量填充表头
        $columnIndex = 1; // 初始列索引
        foreach ($headers as $header) {
            $cell = $sheet->getCellByColumnAndRow($columnIndex, 1);
            $cell->setValue($header);
            $columnIndex++;
        }

        // 填充数据
        $row = 2;
        foreach ($list as $v) {
            // 进口类型:0-自进口，1-地采，2-跨境
            $import_type_name_all = ['自进口', '地采', '跨境'];
            // 频道：0-闪购，1-秒发，2-跨境，3-尾货
            $channel_name = ['闪购', '秒发', '跨境', '尾货'];
            //上架状态（0：待上架，1：待售中，2：在售中，3：已下架，4：已售罄）
            $onsale_status_name_all = ['待上架', '待售中', '在售中', '已下架', '已售罄'];

            // 简码
            $import_type_name = $import_type_name_all[$v['import_type']] ?? '';
            $short_code = $v['short_code'] . PHP_EOL . $import_type_name;

            // 期数
            $period_str = '';
            foreach ($v['period'] as $vv) {
                $periods_type_name = $channel_name[$vv['periods_type']] ?? '';
                $onsale_status_name = $onsale_status_name_all[$vv['onsale_status']] ?? '';
                $str = "{$vv['period']}  {$periods_type_name} {$onsale_status_name}";
                if (empty($period_str)) {
                    $period_str = $str;
                } else {
                    $period_str .= PHP_EOL . $str;
                }
            }

            // 供应商
            $supplier_name = $v['supplier'] . PHP_EOL . "采购：{$v['buyer_name']}" . PHP_EOL . "运营：{$v['operation_name']}";

            // 酒庄
            $v['winery_name_cn'] = empty($v['winery_name_cn']) ? '-' : $v['winery_name_cn'];
            $v['country_name_cn'] = empty($v['country_name_cn']) ? '-' : $v['country_name_cn'];
            $winery_name = $v['winery_name_cn'] . PHP_EOL . $v['country_name_cn'] . PHP_EOL . $v['product_type_name'];

            // 品名
            $product_name = $v['cn_product_name'] . PHP_EOL . $v['en_product_name'];

            // 售价
            $sale_price = '';
            foreach ($v['package'] as $vv) {
                if (empty($sale_price)) {
                    $sale_price = "{$vv['package_name']}：{$vv['price']}";
                } else {
                    $sale_price .= PHP_EOL . "{$vv['package_name']}：{$vv['price']}";
                }
            }

            // 计划完成期限
            $planned_completion_time = $v['planned_completion_time'];
            if (in_array($v['status'], [0, 1])) {
                $planned_completion_time .= PHP_EOL . "剩余{$v['days']}天";
            } else {
                $planned_completion_time .= PHP_EOL . "逾期{$v['days']}天";
            }

            // 备注
            $remark = '';
            foreach ($v['remark'] as $kk => $vv) {
                $no = ($kk + 1) . '：';
                if (empty($remark)) {
                    $remark = $no . $vv['remark'];
                } else {
                    $remark .= PHP_EOL . $no . $vv['remark'];
                }
            }

            $sheet->setCellValue('A' . $row, $short_code);
            $sheet->setCellValue('B' . $row, $period_str);
            $sheet->setCellValue('C' . $row, $supplier_name);
            $sheet->setCellValue('D' . $row, $winery_name);
            $sheet->setCellValue('E' . $row, $product_name);
            $sheet->setCellValue('F' . $row, $sale_price);
            $sheet->setCellValue('G' . $row, $v['costprice']);
            $sheet->setCellValue('H' . $row, $planned_completion_time);
            $sheet->setCellValue('I' . $row, $v['planned_progress'] . '%');
            $sheet->setCellValue('J' . $row, $v['storage_time']);
            $sheet->setCellValue('K' . $row, $v['purchase_nums']);
            $sheet->setCellValue('L' . $row, $v['real_sale_num']);
            $sheet->setCellValue('M' . $row, $v['three_day_sale_num']);
            $sheet->setCellValue('N' . $row, $v['my_inventory']);
            $sheet->setCellValue('O' . $row, $v['inventory_amount']);
            $sheet->setCellValue('P' . $row, $remark);
            $row++;
        }

        // 导出 Excel 文件
        $filename = 'ProductSaleFollowExport.xlsx';
        $filePath = app()->getRuntimePath() . $filename;
        $writer = new Xlsx($spreadsheet);
        $writer->save($filePath);

        excelDownload($filePath, $filename);
    }
}
