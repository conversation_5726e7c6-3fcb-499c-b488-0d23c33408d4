<?php

namespace app\service\v3;

use app\BaseService2;
use app\ErrorCode;
use app\model\BuyerRelated;
use app\model\BuyerRelatedLog;
use app\model\OperateRelated;
use app\validate\OperateRelatedValidate;
use think\Exception;
use think\facade\Db;
use think\facade\Log;

/**
 * 运营人员关联
 * Class OperateRelatedService
 * @package app\service\v3
 */
class OperateRelatedService extends BaseService2
{
    public function __construct()
    {
        $this->model       = new OperateRelated;
        $this->validate    = OperateRelatedValidate::class;
        $this->select_with = ['buyer' => function ($query) {
            $query->order('created_time', 'DESC');
        }, 'log'                      => function ($query) {
            $query->order('created_time', 'DESC');
        }];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2025/04/14 15:38
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id 主键id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //主键id
            }
            #endregion

            #region EQ operation_review_id 商品运营审核者id
            if (isset($param['operation_review_id']) && strlen($param['operation_review_id']) > 0) {
                $query->where('operation_review_id', "=", $param['operation_review_id']); //商品运营审核者id
            }
            #endregion

            #region LIKE operation_review_name 商品运营审核者名称
            if (isset($param['operation_review_name']) && strlen($param['operation_review_name']) > 0) {
                $query->where('operation_review_name', "LIKE", "{$param['operation_review_name']}%"); //商品运营审核者名称
            }
            #endregion

            #region >= start_created_time 开始创建时间
            if (isset($param['start_created_time']) && strlen($param['start_created_time']) > 0) {
                $query->whereTime('created_time', '>=', $param['start_created_time']); //开始创建时间
            }
            #endregion

            #region < end_created_time 结束创建时间
            if (isset($param['end_created_time']) && strlen($param['end_created_time']) > 0) {
                $query->whereTime('created_time', '<', $param['end_created_time']); //结束创建时间
            }
            #endregion

            #region EQ vh_uid 操作用户id
            if (isset($param['vh_uid']) && strlen($param['vh_uid']) > 0) {
                $query->where('vh_uid', "=", $param['vh_uid']); //操作用户id
            }
            #endregion

            #region LIKE vh_vos_name VOS用户姓名
            if (isset($param['vh_vos_name']) && strlen($param['vh_vos_name']) > 0) {
                $query->where('vh_vos_name', "LIKE", "{$param['vh_vos_name']}%"); //VOS用户姓名
            }
            #endregion

        };
    }

    public function del($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        //region 删除
        Db::startTrans();
        try {
            $res   = 0;
            $model = $this->model->where('id', $param['id'])->find();
            if ($model) {
               $res =  (new BuyerRelated())->where('operation_review_id', $model['operation_review_id'])->update(['delete_time'=> time()]);
                (new BuyerRelatedLog())->where('operation_review_id', $model['operation_review_id'])->update(['delete_time'=> time()]);
                $res = $model->delete() ? 1 : 0;
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('更新数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('更新数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($res);
    }

    public function add($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }

        if($this->model->where('operation_review_id',$param['operation_review_id'])->count()){
            throw new Exception("重复添加运营人员");
        }
        #endregion

        //region 插入数据
        Db::startTrans();
        try {
            $this->model->save($param);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('插入数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('插入数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success();
    }
}



