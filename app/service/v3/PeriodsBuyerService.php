<?php

namespace app\service\v3;

use app\BaseService2;
use app\ErrorCode;
use app\model\PeriodsBuyerModel;
use app\service\es\Es;
use think\Exception;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;
use think\facade\Validate;
use app\validate\PeriodsBuyerValidate;
use app\service\Periods as PeriodsService;

/**
 * 商品采购分账配置
 * Class PeriodsBuyerService
 * @package app\service\v3
 */
class PeriodsBuyerService extends BaseService2
{

    protected $model, $validate, $select_with;

    public function __construct()
    {
        $this->model = new PeriodsBuyerModel();
        $this->validate    = PeriodsBuyerValidate::class;
    }

    /**
     * @方法描述: 获取列表数据
     * <AUTHOR>
     * @Date 2024/12/25
     * @param $param
     * @return array|bool|mixed
     */
    public function list($param)
    {
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }

        //region 获取数据
        Db::startTrans();
        try {
            $list = $this->model->where('period', $param['period'])
                ->column('id,periods_type,period,buyer_id,buyer_name,distribution_ratio');
            // 获取主采购人信息
            $PeriodsService = new PeriodsService($param['periods_type']);
            $info = $PeriodsService->getOne($param['period'], 'id,buyer_id,buyer_name');
            if (empty($info['buyer_id'])) {
                throw new \Exception('获取主采购人信息失败');
            }
            // 无数据默认采购100%
            if (empty($list)) {
                $list[] = [
                    'id'            => 0,
                    'periods_type'  => $param['periods_type'],
                    'period'        => $param['period'],
                    'buyer_id'      => $info['buyer_id'],
                    'buyer_name'    => $info['buyer_name'],
                    'distribution_ratio' => '100',
                ];
            }
            // 主采购人放第一个
            $main_buyer = [];
            foreach ($list as $k => $v) {
                $list[$k]['distribution_ratio'] = $v['distribution_ratio'] = floatval($v['distribution_ratio']);
                if ($v['buyer_id'] == $info['buyer_id']) {
                    $main_buyer = $v;
                    unset($list[$k]);
                    break;
                }
            }
            array_unshift($list, $main_buyer);

            $data  = [
                'list'  => $list
            ];

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('查询数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('查询数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($this->listDataProcessing($data));
    }
    /**
     * @方法描述: 保存数据
     * <AUTHOR>
     * @Date 2024/12/25
     * @param $param
     * @return array|bool|mixed
     */
    public function save($data)
    {
        $validate = $this->check(__FUNCTION__, $data);
        if ($validate !== true) {
            return $validate;
        }

        $param = $data['param'];
        $time = time();

        // 获取主采购人信息
        $PeriodsService = new PeriodsService($data['periods_type']);
        $info = $PeriodsService->getOne($data['period'], 'id,buyer_id,buyer_name');
        if (empty($info['buyer_id'])) {
            return $this->failed('获取主采购人信息失败', ErrorCode::PARAM_ERROR);
        }
        $main_buyer_id = $info['buyer_id'];

        
        #region验证
        $distribution_ratio = '0';
        $buyer_id = [];
        foreach ($param as $k => $v) {
            $validate = $this->check('save_param', $v);
            if ($validate !== true) {
                return $validate;
            }
            $distribution_ratio = bcadd($distribution_ratio, strval($v['distribution_ratio']), 2);
            if (floatval($distribution_ratio) > 100) {
                return $this->failed('分账比例不能大于100%', ErrorCode::PARAM_ERROR);
            }
            if (!empty($buyer_id[$v['buyer_id']])) {
                return $this->failed('采购人重复', ErrorCode::PARAM_ERROR);
            }
            $buyer_id[$v['buyer_id']] = 1;
        }
        //endregion
        if (floatval($distribution_ratio) < 100) {
            return $this->failed('分账比例必须等于100%', ErrorCode::PARAM_ERROR);
        }
        if (empty($buyer_id[$main_buyer_id])) {
            return $this->failed('获取主采购人必须保留', ErrorCode::PARAM_ERROR);
        }

        //region 保存数据
        Db::startTrans();
        try {
            $this->model->where('period', $data['period'])->delete();

            $insert = [];
            foreach ($param as $k => $v) {
                $insert[] = [
                    'periods_type'       => $data['periods_type'],
                    'period'             => $data['period'],
                    'buyer_id'           => $v['buyer_id'],
                    'buyer_name'         => $v['buyer_name'],
                    'distribution_ratio' => $v['distribution_ratio'],
                    'operator'           => $data['vh_uid'],
                    'created_time'       => $time,
                ];
            }
            $this->model->insertAll($insert);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('保存数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('保存数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success();
    }
}
