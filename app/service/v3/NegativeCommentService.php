<?php

namespace app\service\v3;

use app\BaseService2;
use app\ErrorCode;
use app\model\NegativeComment;
use app\service\es\Es;
use app\validate\NegativeCommentValidate;
use think\Exception;
use think\facade\Db;
use think\facade\Log;

/**
 * 差评记录
 * Class NegativeCommentService
 * @package app\service\v3
 */
class NegativeCommentService extends BaseService2
{
    public function __construct()
    {
        $this->model       = new NegativeComment;
        $this->validate    = NegativeCommentValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2024/12/16 13:49
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {
            
            #region EQ id 自增id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //自增id
            }
            #endregion
            
            #region EQ period 期数
            if (isset($param['period']) && strlen($param['period']) > 0) {
                $query->where('period', "=", $param['period']); //期数
            }
            #endregion
            
            #region LIKE period_name 期数名称
            if (isset($param['period_name']) && strlen($param['period_name']) > 0) {
                $query->where('period_name', "LIKE", "{$param['period_name']}%"); //期数名称
            }
            #endregion
            
            #region EQ buyer_id 采购人ID
            if (isset($param['buyer_id']) && strlen($param['buyer_id']) > 0) {
                $query->where('buyer_id', "=", $param['buyer_id']); //采购人ID
            }
            #endregion
            
            #region LIKE buyer_name 采购人名称
            if (isset($param['buyer_name']) && strlen($param['buyer_name']) > 0) {
                $query->where('buyer_name', "LIKE", "{$param['buyer_name']}%"); //采购人名称
            }
            #endregion
            
            #region >= start_created_time 开始创建时间
            if (isset($param['start_created_time']) && strlen($param['start_created_time']) > 0) {
                $query->whereTime('created_time', '>=' , $param['start_created_time']); //开始创建时间
            }
            #endregion

            #region < end_created_time 结束创建时间
            if (isset($param['end_created_time']) && strlen($param['end_created_time']) > 0) {
                $query->whereTime('created_time', '<' , $param['end_created_time']); //结束创建时间
            }
            #endregion
            
            #region >= start_update_time 开始更新时间
            if (isset($param['start_update_time']) && strlen($param['start_update_time']) > 0) {
                $query->whereTime('update_time', '>=' , $param['start_update_time']); //开始更新时间
            }
            #endregion

            #region < end_update_time 结束更新时间
            if (isset($param['end_update_time']) && strlen($param['end_update_time']) > 0) {
                $query->whereTime('update_time', '<' , $param['end_update_time']); //结束更新时间
            }
            #endregion
            
            #region EQ vh_uid 操作人ID
            if (isset($param['vh_uid']) && strlen($param['vh_uid']) > 0) {
                $query->where('vh_uid', "=", $param['vh_uid']); //操作人ID
            }
            #endregion
            
            #region LIKE vh_vos_name 操作人姓名
            if (isset($param['vh_vos_name']) && strlen($param['vh_vos_name']) > 0) {
                $query->where('vh_vos_name', "LIKE", "{$param['vh_vos_name']}%"); //操作人姓名
            }
            #endregion
            
            #region LIKE comment_id 评论ID
            if (isset($param['comment_id']) && strlen($param['comment_id']) > 0) {
                $query->where('comment_id', "LIKE", "{$param['comment_id']}%"); //评论ID
            }
            #endregion
            
        };
    }

    public function add($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        if(NegativeComment::where('comment_id', $param['comment_id'])->count()) throw new Exception("该条评论已添加!");
        #endregion

        //region 插入数据
        Db::startTrans();
        try {
            $period = Es::name(Es::PERIODS)->where([['_id','==',$param['period']]])->find();
            if(!empty($period) && empty($param['period_name'])){
                $param['period_name'] = $period['title'] ?? '';
            }
            if(!empty($period) && empty($param['buyer_id'])){
                $param['buyer_id'] = $period['buyer_id'] ?? '';
            }
            if(!empty($period) && empty($param['buyer_name'])){
                $param['buyer_name'] = $period['buyer_name'] ?? '';
            }
            $this->model->save($param);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('插入数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('插入数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success();
    }

    public function listDataProcessing($data)
    {
        if ($data['total']) {
            $list = $data['list']->toArray();
            $comment_ids = array_column($list, 'comment_id');
            $comments = Db::name('periods_comment')->where('id','in',$comment_ids)->column('content', 'id');

            $wine_evaluations = Db::table('vh_community.vh_wine_evaluation')->where('id','in',$comment_ids)->column('wine_evaluation', 'id');

            foreach ($list as &$item) {
                if($item['source'] == 6){
                    $item['comment_content'] = $wine_evaluations[$item['comment_id']] ?? '';
                }else{
                    $item['comment_content'] = $comments[$item['comment_id']] ?? '';
                }
            }

            $data['list'] = $list;
        }

        return $data;
    }

}



