<?php

namespace app\service\v3;

use app\BaseService2;
use app\ErrorCode;
use app\model\PeriodsProductInventory;
use app\model\UserQueryLog;
use app\service\es\Es;
use app\validate\UserQueryLogValidate;
use think\facade\Db;
use think\facade\Log;

/**
 * 搜索反馈记录
 * Class UserQueryLogService
 * @package app\service\v3
 */
class UserQueryLogService extends BaseService2
{
    public function __construct()
    {
        $this->model       = new UserQueryLog;
        $this->validate    = UserQueryLogValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/06/19 10:18
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id 主键id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //主键id
            }
            #endregion

            #region LIKE keywords 搜索关键词
            if (isset($param['keywords']) && strlen($param['keywords']) > 0) {
                $query->where('keywords', "LIKE", "{$param['keywords']}%"); //搜索关键词
            }
            #endregion

            #region IN satisfaction 满意度
            if (isset($param['satisfaction']) && strlen($param['satisfaction']) > 0) {
                $query->where('satisfaction', "IN", $param['satisfaction']); //满意度:0=未评分,1=满意,2=一般,3=不满意
            }
            #endregion

            #region EQ uid 用户ID
            if (isset($param['uid']) && strlen($param['uid']) > 0) {
                $query->where('uid', "=", $param['uid']); //用户ID
            }

            if (isset($param['vh_uid']) && strlen($param['vh_uid']) > 0) {
                $query->where('uid', "=", $param['vh_uid']); //用户ID
            }
            #endregion

            #region EQ user_phone 用户手机号
            if (isset($param['user_phone']) && strlen($param['user_phone']) > 0) {
                $query->where('user_phone', "=", $param['user_phone']); //用户手机号
            }
            #endregion

            #region LIKE nickname 昵称
            if (isset($param['nickname']) && strlen($param['nickname']) > 0) {
                $query->where('nickname', "LIKE", "{$param['nickname']}%"); //昵称
            }
            #endregion

            #region >= start_created_time 开始创建时间
            if (isset($param['start_created_time']) && strlen($param['start_created_time']) > 0) {
                $query->whereTime('created_time', '>=', $param['start_created_time']); //开始创建时间
            }
            #endregion

            #region < end_created_time 结束创建时间
            if (isset($param['end_created_time']) && strlen($param['end_created_time']) > 0) {
                $query->whereTime('created_time', '<', $param['end_created_time']); //结束创建时间
            }
            #endregion

        };
    }

    /**
     * @方法描述: 插入一条数据
     * <AUTHOR>
     * @Date 2022/8/10 14:37
     * @param $param
     * @return bool|mixed
     */
    public function add($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        //region 插入数据
        Db::startTrans();
        try {
            if (!empty($param['vh_uid'])) {
                $user                = \Curl::getUserInfo($param['vh_uid'], 'uid,telephone_encrypt,nickname');
                $param['uid']        = $user['uid'];
                $param['nickname']   = $user['nickname'];
                $param['user_phone'] = $user['telephone_encrypt'];
            }

            $this->model->save($param);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('插入数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('插入数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success();
    }

    /**
     * @方法描述: 分页获取列表数据
     * <AUTHOR>
     * @Date 2022/8/10 14:52
     * @param $param
     * @return array|bool|mixed
     */
    public function userList($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        #region where查询条件,分页,查询数据的处理
        $where = function ($query) use ($param) {
            $query->whereOr('user_unique_code', "=", $param['user_unique_code']); //用户ID

            if (isset($param['vh_uid']) && strlen($param['vh_uid']) > 0) {
                $query->whereOr('uid', "=", $param['vh_uid']); //用户ID
            }
        };

        $limit     = $param['limit'] ?? 10;
        $page      = $param['page'] ?? 1;
        $pagestart = ($page - 1) * $limit;
        $field     = $param['fields'] ?? "*";
        //endregion

        //region 分页获取数据
        Db::startTrans();
        try {
            $model = $this->model->field($field)->with($this->select_with)->where($where)->limit($pagestart, $limit);
            if (!empty($this->select_order)) {
                foreach ($this->select_order as $sort_field => $sort) {
                    $model->order($sort_field, $sort);
                }
            }
            if (!empty($this->hidden_field)) {
                $model->hidden($this->hidden_field);
            }

//            $list = $model->select();

            $total       = $this->model->where($where)->count();
            $today_total = $this->model->where($where)->whereTime('created_time', '>', date('Y-m-d H:i:s', strtotime('-3 month')))->count();
            $data        = [
//                'list'  => $list,
                'total'       => $total,
                'today_total' => $today_total,
                'is_pop_up'   => !$today_total,
            ];

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('查询数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('查询数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($this->listDataProcessing($data));
    }


    /**
     * @方法描述: 根据简码批量查询最后售卖时间和期数
     * <AUTHOR>
     * @Date 2023/7/17 14:08
     * @param $param
     * @param $param ['short_codes'] string 简码 必传
     * @return mixed
     */
    public function batchLastSellTime($param)
    {
        $data = [];
        if (!empty($param['short_codes'])) {
            $short_codes = array_unique(explode(',', trim($param['short_codes'])));

            $periods = PeriodsProductInventory::where(function ($query) use ($short_codes) {
                $query->where('short_code', 'in', $short_codes); //产品简码
                $query->where('periods_type', 'in', [0, 1, 2, 3]); //商品频道（0：闪购，1：秒发，2：跨境，3：尾货，4：兔头）
            })->column('id,short_code,period');

            $periods_group = [];
            $all_periods   = [];
            foreach ($periods as $period) {
                $all_periods[]                          = $period['period'];
                $periods_group[$period['short_code']][] = $period['period'];
            }

            $period_infos = Es::name(Es::PERIODS)->where([
                ['id', 'in', $all_periods],
                ['sell_time', '>', '1970-01-01 08:00:00'],
                ['sell_time', '<', date('Y-m-d H:i:s')],
            ])->order(['sell_time' => 'desc'])->field('id,short_code,sell_time')->select()->toArray();

            foreach ($short_codes as $short_code) {
                $data[$short_code]  = null;
                $short_code_periods = $periods_group[$short_code] ?? [];

                if (!empty($short_code_periods)) {
                    foreach ($period_infos as $period_info) {
                        if (in_array($period_info['id'], $short_code_periods)) {
                            $data[$short_code] = $period_info['sell_time'];
                            continue(2);
                        }
                    }
                }
            }
        }

        return $this->success(['list' => $data]);
    }

}



