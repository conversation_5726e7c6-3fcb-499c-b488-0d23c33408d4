<?php

namespace app\service\v3;

use app\BaseService2;
use app\model\PeriodsPurchasedSync;
use app\validate\PeriodsPurchasedSyncValidate;

/**
 * 商家秒发期数销量同步日志
 * Class PeriodsPurchasedSyncService
 * @package app\service\v3
 */
class PeriodsPurchasedSyncService extends BaseService2
{
    public function __construct()
    {
        $this->model       = new PeriodsPurchasedSync;
        $this->validate    = PeriodsPurchasedSyncValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/06/16 14:26
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {
            
            #region EQ id 主键id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //主键id
            }
            #endregion
            
            #region LIKE sub_order_no 子单号
            if (isset($param['sub_order_no']) && strlen($param['sub_order_no']) > 0) {
                $query->where('sub_order_no', "LIKE", "{$param['sub_order_no']}%"); //子单号
            }
            #endregion
            
            #region EQ vmall_period 商家期数
            if (isset($param['vmall_period']) && strlen($param['vmall_period']) > 0) {
                $query->where('vmall_period', "=", $param['vmall_period']); //商家期数
            }
            #endregion
            
            #region EQ second_period 秒发期数
            if (isset($param['second_period']) && strlen($param['second_period']) > 0) {
                $query->where('second_period', "=", $param['second_period']); //秒发期数
            }
            #endregion
            
            #region EQ purchased 销量
            if (isset($param['purchased']) && strlen($param['purchased']) > 0) {
                $query->where('purchased', "=", $param['purchased']); //销量
            }
            #endregion
            
            #region >= start_created_time 开始创建时间
            if (isset($param['start_created_time']) && strlen($param['start_created_time']) > 0) {
                $query->whereTime('created_time', '>=' , $param['start_created_time']); //开始创建时间
            }
            #endregion

            #region < end_created_time 结束创建时间
            if (isset($param['end_created_time']) && strlen($param['end_created_time']) > 0) {
                $query->whereTime('created_time', '<' , $param['end_created_time']); //结束创建时间
            }
            #endregion
            
        };
    }

}



