<?php

namespace app\service\v3;

use app\BaseService2;
use app\ErrorCode;
use app\model\PurchaseOrderno2;
use app\service\es\Es;
use app\validate\PurchaseOrdernoValidate;
use think\Exception;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;
use think\facade\Validate;
use app\service\WorkWeixinService;
use app\service\Periods as PeriodsService;

/**
 * 采购单表
 * Class PurchaseOrdernoService
 * @package app\service\v3
 */
class PurchaseOrdernoService extends BaseService2
{
    protected $company_info = [
        '029' => [
            'corp_code'     => '029',
            'business_type' => 'CG01', //CG01-地采采购
            'exchange_rate' => '1',
            'currency_code' => 'CNY', //CNY-人民币
        ],
        '032' => [
            'corp_code'     => '032',
            'business_type' => 'CG01', //CG01-地采采购
            'exchange_rate' => '1',
            'currency_code' => 'CNY', //CNY-人民币
        ],
    ];

    protected $model, $validate, $select_with;

    public function __construct()
    {
        $this->model       = new PurchaseOrderno2();
        $this->validate    = PurchaseOrdernoValidate::class;
        $this->select_with = ['items' => function ($query) {
            $query->order('period desc');
        }, 'approve', 'waybill' => function ($query) {
            $query->field('id,purchase_orderno_id,waybill_no,express_type');
        }];
    }

    /**
     * @方法描述: 分页获取列表数据
     * <AUTHOR>
     * @Date 2024/01/18
     * @param $param
     * @return array|bool|mixed
     */
    public function list($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        #region where查询条件,分页,查询数据的处理
        $where = $this->builderWhere($param);
        $type_where = $this->builderTypeWhere($param);
        $limit     = $param['limit'] ?? 10;
        $page      = $param['page'] ?? 1;
        $pagestart = ($page - 1) * $limit;
        $field     = $param['fields'] ?? "*";
        //endregion

        //region 分页获取数据
        Db::startTrans();
        try {
            $model = $this->model->field($field)->with($this->select_with)->where($where)->where($type_where)->limit($pagestart, $limit);
            if (!empty($this->select_order)) {
                foreach ($this->select_order as $sort_field => $sort) {
                    $model->order($sort_field, $sort);
                }
            }
            if (!empty($this->hidden_field)) {
                $model->hidden($this->hidden_field);
            }

            $list = $model->select();

            $list = $list->append(['status_text','push_erp_status_text'])->toArray();
            // 总条数
            $total = $this->model->where($where)->where($type_where)->count();
            $data  = [
                'list'  => $list,
                'total' => $total
            ];


            #今日采购订单：0-待确认，1-已确认，2-历史采购订单，3-财务审核，4-已驳回，5-采购审核，6-预下单采购单，7-自动审核采购单，8-财务主管审核
            if (isset($param['query_type']) && in_array($param['query_type'], [0, 1, 2, 3, 4, 5, 6, 7, 8])) {
                $stime = strtotime(date('Y-m-d'));
                $etime = strtotime(date('Y-m-d 23:59:59'));
                if (in_array($param['query_type'], [1, 4, 6])) { // 待确认数量
                    $s_where = [
                        ['source', "=", 2],
                        ['created_time', "BETWEEN", [$stime, $etime]],
                        ['operate_status', "=", 0],
                        ['push_erp_status', "=", 0],
                    ];
                    $unconfirmed_count = $this->model->where($where)->where($s_where)->count();
                }
                if (in_array($param['query_type'], [0, 4, 6])) { // 已确认数量
                    $s_where = [
                        ['source', 'in', [1, 2]],
                        ['created_time', "BETWEEN", [$stime, $etime]],
                        ['operate_status', "in", [1, 7]],
                    ];
                    $confirm_count = $this->model->where($where)->where($s_where)->count();
                }
                if (in_array($param['query_type'], [0, 1, 6])) { // 已驳回数量
                    $s_where = [
                        ['source', 'in', [1, 2]],
                        ['operate_status', "=", 0],
                        ['push_erp_status', "=", 3],
                    ];
                    $refuse_count = $this->model->where($where)->where($s_where)->count();
                }
                if (in_array($param['query_type'], [0, 1, 4])) { // 预下单数量
                    $s_where = [
                        ['source', 'in', [1, 2]],
                        ['created_time', "BETWEEN", [$stime, $etime]],
                        ['operate_status', "=", 0],
                        ['push_erp_status', "=", 6],
                    ];
                    $preorder_count = $this->model->where($where)->where($s_where)->count();
                }

                // 默认库存实时数据处理
                $ty = 1;
                switch ($param['query_type']) {
                    case 0: // 待确认
                        $ty = 0;
                        $unconfirmed_count = $total;
                        break;
                    case 1: // 已确认
                        $confirm_count = $total;
                        break;
                    case 2: // 历史采购订单
                        $ty = 3;
                        break;
                    case 3: // 财务审核
                        $ty = 2;
                        break;
                    case 4: // 已驳回
                        $refuse_count = $total;
                        break;
                    case 5: // 采购审核
                        $ty = 2;
                        break;
                    case 6: // 预下单
                        $preorder_count = $total;
                    case 7: // 自动审核
                        $ty = 2;
                        break;
                    case 8: // 财务主管审核
                        $ty = 2;
                        break;
                }
                // 实时数据处理
                $data['list'] = $this->RealTimeDataProcessing($list, $ty);

                if (in_array($param['query_type'], [0, 1, 4, 6])) {
                    // 未确认数量
                    $data['unconfirmed_nums']  = $unconfirmed_count;
                    // 已确认数量
                    $data['confirm_nums']  = $confirm_count;
                    // 已驳回数量
                    $data['refuse_nums']  = $refuse_count;
                    // 已驳回数量
                    $data['preorder_count']  = $preorder_count;
                }
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('查询数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('查询数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($this->listDataProcessing($data));
    }

    /**
     * @方法描述: 实时数据处理
     * <AUTHOR>
     * @Date 2024/2/29
     * @param array $list
     * @param int $ty 类型：0-待确认列表，1-预下单列表，2-审核列表，3-提交ERP
     * @return \Closure
     */
    public function RealTimeDataProcessing($list, $ty = 0)
    {
        if (empty($list)) {
            return $list;
        }
        $short_code = $period_id = $supplier_name = [];
        foreach ($list as $v) {
            $supplier_name[] = $v['supplier'];
            foreach ($v['items'] as $item) {
                $period_id = array_merge($period_id, array_filter(array_unique(explode(',', strval($item['period'])))));
                $short_code[] = $item['short_code'];
            }
        }
        
        //查询信息
        $products = Db::table('vh_wiki.vh_products')
            ->whereIn('short_code', $short_code)
            ->column('grape_picking_years,short_code', 'short_code');
            
        // 查询实时数据
        $url = env('ITEM.COMMODITIES_SERVICES') . '/commodities_server/v3/data_analysis/GetPurchaseOrdersRealTimeData';
        $body = json_encode([
            'purchase_order_no' => array_column($list, 'orderno'),
            'period_id' => array_values(array_unique($period_id)),
            'short_code' => array_values(array_unique($short_code)),
            'supplier_name' => array_values(array_unique($supplier_name)),
            'query_type' => $ty,
        ]);
        $res = curlRequest($url, $body);
        $resq = $res['data'] ?? [];
        // 期数信息
        $es_periods = $resq['periods'] ?? [];
        // 产品信息
        $vh_products = $resq['products'] ?? [];
        // 供应商信息
        $supplier = $resq['supplier'] ?? [];
        // 期数产品信息
        $period_product = $resq['period_product'] ?? [];
        // 毛利率
        $gross_margin = $resq['gross_margin'] ?? [];
        // 已售数量
        $sale_num = $resq['sale_num'] ?? [];
        // 未发数量
        $unshipped_num = $resq['unshipped_num'] ?? [];
        // 萌牙库存
        $my_inventory_data = $resq['my_inventory'] ?? [];
        // 萌牙入库单信息
        $my_storage = $resq['my_storage'] ?? [];
        // 萌牙库存列表
        $my_inventory_list = $resq['my_inventory_list'] ?? [];

        foreach ($list as $k => &$v) {
            $v['my_entry_status'] = '未推送萌牙';
            $s_my_storage = $my_storage[$v['orderno']] ?? [];
            if (!empty($s_my_storage)) {
                //入库单状态（1未开始，2清点上架中，3已完成，4已终止（不能再次启用））
                switch ($s_my_storage['status']) {
                    case 1:
                        $v['my_entry_status'] = '未开始';
                        break;
                    case 2:
                        $v['my_entry_status'] = '清点上架中';
                        break;
                    case 3:
                        $v['my_entry_status'] = '已完成';
                        break;
                    case 4:
                        $v['my_entry_status'] = '已终止';
                        break;
                }
            }
            // 虚拟仓编码
            $fictitious_id = intval($v['warehouse_code']);
            if ($ty == 2) { // 采购、财务审核列表
                // 合同到期日期
                $v['contract_end'] = $supplier[$v['supplier']]['contract_end'] ?? '';
            }

            foreach ($v['items'] as $key => $item) {
                // 真实已售数量
                $real_sale_num = 0;
                // 已采数量
                $purchased_nums = 0;
                // 萌牙库存数量
                $item['my_inventory'] = 0;
                // 萌牙在途数量
                $item['transit_count'] = 0;
                // 萌牙所有仓总库存
                $item['total_my_inventory'] = 0;
                // 萌牙所有仓总在途数量
                $item['total_transit_count'] = 0;
                // 未推送萌牙数量
                $unshipped_number = 0;
                // 萌牙库存列表
                $item['my_inventory_list'] = [];

                if (in_array($ty, [0, 1, 2])) {
                    // 期数信息
                    $period = array_filter(array_unique(explode(',', strval($item['period']))));
                    $item['period_info'] = [];
                    foreach ($period as $vv) {
                        $es_periods_info = $es_periods[$vv] ?? [];
                        $es_periods_info['is_yesterday'] = false;
                        if (!empty($es_periods_info)) {
                            // 是否昨天
                            $es_periods_info['is_yesterday'] = strtotime(date('Y-m-d', strtotime($es_periods_info['onsale_time'] ?? ''))) == strtotime(date('Y-m-d', strtotime('-1 day')));
                            $item['period_info'][] = $es_periods_info;
                        }
                    }
                    // 萌牙库存计算
                    $inventory_data = $my_inventory_data["{$item['short_code']}_{$fictitious_id}"] ?? [];
                    if (!empty($inventory_data)) {
                        $item['my_inventory'] = $inventory_data['goods_count'] ?? 0;
                        $item['transit_count'] = $inventory_data['transit_count'] ?? 0;
                    }
                    $inventory_all = $my_inventory_data[$item['short_code']] ?? [];
                    if (!empty($inventory_all)) {
                        $item['total_my_inventory'] = $inventory_all['goods_count'] ?? 0;
                        $item['total_transit_count'] = $inventory_all['transit_count'] ?? 0;
                    }
                    // 萌牙库存列表
                    $inventory_list = $my_inventory_list[$item['short_code']] ?? [];
                    if (!empty($inventory_list)) {
                        foreach ($inventory_list as $vv) {
                            $s_list = $item['my_inventory_list'][$vv['fictitious_id']] ?? [];
                            if (empty($s_list)) {
                                $s_list = [
                                    "fictitious_id" => $vv['fictitious_id'],
                                    "fictitious_name" => $vv['fictitious_name'],
                                    "goods_count" => $vv['goods_count'],
                                    "transit_count" => $vv['transit_count'],
                                ];
                            } else {
                                $s_list['goods_count'] += $vv['goods_count'];
                                $s_list['transit_count'] += $vv['transit_count'];
                            }
                            $item['my_inventory_list'][$vv['fictitious_id']] = $s_list;
                        }
                        $item['my_inventory_list'] = array_values($item['my_inventory_list']);
                    }
                }

                if (in_array($ty, [0, 1, 2, 3])) {
                    // 产品信息
                    $wiki_products = $vh_products[$item['short_code']] ?? [];
                    if (!empty($wiki_products)) {
                        $item['tax_rate'] = $wiki_products['tax_rate'];
                        $item['billing_name'] = $wiki_products['cn_product_name'];
                        $item['en_product_name'] = $wiki_products['en_product_name'];
                        $item['capacity'] = $wiki_products['capacity'];
                        $item['unit'] = $wiki_products['unit_name'];
                    }
                    // 如果是小规模供应商，采购税率需要读取配置的税率进行下单
                    if (!empty($supplier[$v['supplier']]['supplier_tax'])) {
                        $item['tax_rate'] = $supplier[$v['supplier']]['supplier_tax'];
                    }
                }

                switch ($ty) {
                    case 0: //待确认列表
                        foreach ($period as $p_id) {
                            $unshipped_number += $unshipped_num[$p_id][$item['short_code']] ?? 0;
                            $real_sale_num += $sale_num[$p_id][$item['short_code']] ?? 0;
                            $purchased_nums += $period_product["{$item['short_code']}_{$p_id}"]['order'] ?? 0;
                        }

                        // 代发 下单数量 = 已售-已采
                        if ($item['is_supplier_delivery'] == 1) {
                            // 下单数量 = 已售-已采
                            $number = $real_sale_num - $purchased_nums;
                        } else { // 非代发
                            // 无未发数量，不下单
                            if ($unshipped_number <= 0) {
                                $number = 0;
                            } else {
                                //已采（+备货）＞= 已售，不下单
                                if ($purchased_nums >= $real_sale_num) {
                                    $number = 0;
                                } else { //已售＞已采（备货）
                                    //已售<（未发+已采），下单=已售-已采
                                    if ($real_sale_num < ($unshipped_number + $purchased_nums)) {
                                        $number = $real_sale_num - $purchased_nums;
                                    } else { //已售>=（未发+已采），下单=未发-库存
                                        $number = $unshipped_number - $item['my_inventory'];
                                    }
                                }
                            }
                        }

                        $number = $number < 0 ? 0 : intval($number);
                        // 首次采购瓶数=实际瓶数*1.2倍且需要是6的倍数
                        if (!empty($item['is_first']) && $item['is_first'] == 1) {
                            $number = FirstQuantityCalculation($number);
                        }
                        // 价税合计=下单数量*含税单价
                        $total_amount = bcmul(strval($item['price']), strval($number), 2);

                        $item['total'] = $total_amount;
                        $item['number'] = $number;
                        // $item['remark'] = '已售 ' . $real_sale_num;
                        $item['sale_nums'] = $real_sale_num;
                        $item['unshipped_num'] = $unshipped_number;
                        $item['purchased_nums'] = $purchased_nums;
                        break;
                    case 2: // 采购、财务审核列表
                        // 毛利率
                        $sun_gross_margin = [];
                        foreach ($period as $p_id) {
                            if (!empty($gross_margin[$p_id])) {
                                foreach ($gross_margin[$p_id] as $gm) {
                                    if (in_array($item['short_code'], $gm['short_code'])) {
                                        $sun_gross_margin[] = $gm;
                                    }
                                }
                            }
                        }
                        $item['gross_margin'] = $sun_gross_margin;
                        break;
                }

                $item['years'] = $products[$item['short_code']]['grape_picking_years'] ?? '';
                $list[$k]['items'][$key] = $item;
            }
        }

        return $list;
    }

    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/11/14 17:08
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {
            $query->where('source', "in", [1, 2]);

            #region EQ id id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //id
            }
            #endregion

            #region LIKE orderno 采购单号
            if (isset($param['orderno']) && strlen($param['orderno']) > 0) {
                $query->where('orderno', "LIKE", "%{$param['orderno']}%"); //采购单号
            }
            #endregion

            #region EQ operator 采购人id
            if (isset($param['operator']) && strlen($param['operator']) > 0) {
                $query->where('operator', "=", $param['operator']); //采购人id
            }
            #endregion

            #region LIKE operator_name 采购人名称
            if (isset($param['operator_name']) && strlen($param['operator_name']) > 0) {
                $query->where('operator_name', "LIKE", "%{$param['operator_name']}%"); //采购人名称
            }
            #endregion

            #region >= start_created_time 开始添加时间
            if (isset($param['start_created_time']) && strlen($param['start_created_time']) > 0) {
                $query->whereTime('created_time', '>=', $param['start_created_time']); //开始添加时间
            }
            #endregion

            #region < end_created_time 结束添加时间
            if (isset($param['end_created_time']) && strlen($param['end_created_time']) > 0) {
                $query->whereTime('created_time', '<=', $param['end_created_time']); //结束添加时间
            }
            #endregion

            #region >= start_created_time 开始单据时间
            if (isset($param['start_bill_date']) && strlen($param['start_bill_date']) > 0) {
                $query->whereTime('bill_date', '>=', $param['start_bill_date']);
            }
            #endregion

            #region < end_created_time 结束单据时间
            if (isset($param['end_bill_date']) && strlen($param['end_bill_date']) > 0) {
                $query->whereTime('bill_date', '<=', $param['end_bill_date']);
            }
            #endregion

            #region period 期数
            if (isset($param['period']) && strlen($param['period']) > 0) {
                $query->where('id', 'IN', function ($query) use ($param) {
                    $query->name('purchase_orderno_period')
                        ->where('period', $param['period'])
                        ->field('purchase_orderno_id');
                });
            }
            #endregion

            #region 运单号
            if (isset($param['waybill_no']) && strlen($param['waybill_no']) > 0) {
                $query->where('id', 'IN', function ($query) use ($param) {
                    $query->name('purchase_orderno_waybill')
                        ->where('waybill_no', $param['waybill_no'])
                        ->field('purchase_orderno_id');
                });
            }
            #endregion

            #region EQ source 来源：0=手动,1=自动,2-机器人自动下单
            if (isset($param['source']) && strlen($param['source']) > 0) {
                $query->where('source', "=", $param['source']); //来源：0=手动，1=自动
            }
            #endregion

            #region >= start_update_time 开始更新时间
            if (isset($param['start_update_time']) && strlen($param['start_update_time']) > 0) {
                $query->whereTime('update_time', '>=', $param['start_update_time']); //开始更新时间
            }
            #endregion

            #region < end_update_time 结束更新时间
            if (isset($param['end_update_time']) && strlen($param['end_update_time']) > 0) {
                $query->whereTime('update_time', '<', $param['end_update_time']); //结束更新时间
            }
            #endregion

            #region LIKE warehouse 入库仓库
            if (isset($param['warehouse']) && strlen($param['warehouse']) > 0) {
                $query->where('warehouse', "LIKE", "{$param['warehouse']}%"); //入库仓库
            }
            #endregion

            #region LIKE warehouse_code 入库仓erp编码
            if (isset($param['warehouse_code']) && strlen($param['warehouse_code']) > 0) {
                $query->where('warehouse_code', "LIKE", "{$param['warehouse_code']}%"); //入库仓erp编码
            }
            #endregion

            #region LIKE supplier 供应商
            if (isset($param['supplier']) && strlen($param['supplier']) > 0) {
                $query->where('supplier', "LIKE", "%{$param['supplier']}%"); //供应商
            }
            #endregion

            #region LIKE supplier_code 供应商编码
            if (isset($param['supplier_code']) && strlen($param['supplier_code']) > 0) {
                $query->where('supplier_code', "=", $param['supplier_code']); //供应商编码
            }
            #endregion

            #region LIKE operator_code 采购员工编码
            if (isset($param['operator_code']) && strlen($param['operator_code']) > 0) {
                $query->where('operator_code', "LIKE", "{$param['operator_code']}%"); //采购员工编码
            }
            #endregion

            #region LIKE department 采购部门
            if (isset($param['department']) && strlen($param['department']) > 0) {
                $query->where('department', "LIKE", "{$param['department']}%"); //采购部门
            }
            #endregion

            #region LIKE department_code 采购部门编码
            if (isset($param['department_code']) && strlen($param['department_code']) > 0) {
                $query->where('department_code', "LIKE", "{$param['department_code']}%"); //采购部门编码
            }
            #endregion

            #region EQ created_uid 操作人
            if (isset($param['created_uid']) && strlen($param['created_uid']) > 0) {
                $query->where('created_uid', "=", $param['created_uid']); //操作人
            }
            #endregion

            #region LIKE created_name 制单人名称
            if (isset($param['created_name']) && strlen($param['created_name']) > 0) {
                $query->where('created_name', "=", $param['created_name']); //操作人名称
            }
            #endregion

            #region IN status 采购单状态
            if (isset($param['status']) && strlen($param['status']) > 0) {
                $query->where('status', "IN", $param['status']); //采购单状态:0=自由(缺省),1=未用,2=正在审批,3=审批通过,4=审批未通过,5=输出,6=冻结,7=执行完毕
            }
            #endregion

            #region LIKE setttlement 付款方式
            if (isset($param['setttlement']) && strlen($param['setttlement']) > 0) {
                $query->where('setttlement', "LIKE", "{$param['setttlement']}%"); //付款方式
            }
            #endregion

            #region LIKE setttlement_code 付款方式编码
            if (isset($param['setttlement_code']) && strlen($param['setttlement_code']) > 0) {
                $query->where('setttlement_code', "LIKE", "{$param['setttlement_code']}%"); //付款方式编码
            }
            #endregion

            #region LIKE remark 备注
            if (isset($param['remark']) && strlen($param['remark']) > 0) {
                $query->where('remark', "LIKE", "{$param['remark']}%"); //备注
            }
            #endregion

            #region LIKE corp_code 公司编码
            if (isset($param['corp_code']) && strlen($param['corp_code']) > 0) {
                $query->where('corp_code', "LIKE", "{$param['corp_code']}%"); //公司编码
            }
            #endregion

            #region LIKE corp_code 收款公司ID
            if (!empty($param['payee_merchant_id'])) {
                $query->where('payee_merchant_id', "=", $param['payee_merchant_id']); //公司编码
            }
            #endregion

            #region IN status 简码
            if (!empty($param['short_code'])) {
                $query->where('id', 'IN', function ($query) use ($param) {
                    $query->name('purchase_orderno_items')
                        ->where([['short_code', 'like', "%{$param['short_code']}%"]])
                        ->field('purchase_orderno_id');
                });
            }

            #region 推送状态
            if (isset($param['push_erp_status']) && strlen($param['push_erp_status']) > 0) {
                $query->where('push_erp_status', "=", $param['push_erp_status']);
            }
            #endregion

            #region 是否备货
            if (isset($param['is_stockup']) && strlen($param['is_stockup']) > 0) {
                $query->where('is_stockup', "=", $param['is_stockup']);
            }
            #endregion
        };
    }

    /**
     * @方法描述: 类型筛选条件
     * <AUTHOR>
     * @Date 2023/11/14 17:08
     * @param $param
     * @return \Closure
     */
    protected function builderTypeWhere($param)
    {
        return function ($query) use ($param) {
            #今日采购订单：0-待确认，1-已确认，2-历史采购订单，3-财务审核，4-已驳回，5-采购审核，6-预下单采购单，7-自动审核，8-财务主管审核
            if (isset($param['query_type']) && in_array($param['query_type'], [0, 1, 3, 4, 5, 6, 7, 8])) {
                $stime = strtotime(date('Y-m-d'));
                $etime = strtotime(date('Y-m-d 23:59:59'));
                switch ($param['query_type']) {
                    case 0:
                        $query->where([
                            ['source', "=", 2],
                            ['created_time', "BETWEEN", [$stime, $etime]],
                            ['push_erp_status', "=", 0],
                            ['operate_status', "=", 0],
                        ]);
                        break;
                    case 1:
                        $query->where([
                            ['created_time', "BETWEEN", [$stime, $etime]],
                            ['push_erp_status', "in", [1, 7]],
                        ]);
                        break;
                    case 3:
                        $query->where([
                            ['push_erp_status', "=", 5],
                            ['operate_status', "=", 0],
                        ]);
                        break;
                    case 4:
                        $query->where([
                            ['push_erp_status', "=", 3],
                            ['operate_status', "=", 0],
                        ]);
                        break;
                    case 5:
                        $s_where = [
                            ['push_erp_status', "=", 4],
                            ['operate_status', "=", 0],
                        ];
                        if (!in_array($param['vh_uid'], [463, 57, 1, 102])) {
                            $s_where[] = ['purchase_reviewer_id', 'in', [$param['vh_uid'], 0]];
                        }
                        $query->where($s_where);

                        break;
                    case 6:
                        $query->where([
                            ['created_time', "BETWEEN", [$stime, $etime]],
                            ['push_erp_status', "=", 6],
                            ['operate_status', "=", 0],
                        ]);
                        break;
                    case 7:
                        $query->where([
                            ['push_erp_status', "=", 7],
                            ['operate_status', "=", 1],
                        ]);
                        break;
                    case 8:
                        $query->where([
                            ['push_erp_status', "=", 10],
                            ['operate_status', "=", 0],
                        ]);
                        break;
                }
            }
        };
    }


    /**
     * @方法描述: 生成PO单号
     * <AUTHOR>
     * @Date 2023/11/15 9:33
     * @param $param
     * @return mixed
     */
    public function getPoNo($param)
    {
        $today      = date('Ymd');
        $number_key = "po_number_auto_increment_{$today}";
        $number     = Cache::get($number_key, 1000);
        $number++;
        $ttl = 24 * 60 * 60;
        Cache::set($number_key, $number, $ttl);

        $no = "PO{$today}{$number}K";

        return $this->success(compact('no'));
    }

    /**
     * @方法描述: 添加审批流
     * <AUTHOR>
     * @Date 2024/4/3
     * @param int $id 采购单ID
     * @param int $approve_type 审批类型：0-预下单，1-确认下单，2-采购审核，3-财务审核，4-延期下单，5-财务复审，6-作废，7-财务主管审核
     * @param int $status 审批状态：0-待审批，1-通过，2-驳回
     * @param string $comments 审批意见
     * @return bool|mixed
     */
    public function addApprove($id, $approve_type, $status, $comments = '')
    {
        $time = time();

        $vh_uid             = request()->header('vinehoo-uid', null);
        $base64_vh_vos_name = request()->header('vinehoo-vos-name', null);
        $vh_vos_name = $base64_vh_vos_name !== null ? base64_decode($base64_vh_vos_name) : '';

        if (in_array($approve_type, [0, 1, 4, 5, 6]) || $status == 0) {
            $info = [
                'purchase_orderno_id' => $id,
                'approve_type' => $approve_type,
                'status' => $status,
                'comments' => $comments,
                'sender' => $vh_vos_name,
                'sender_id' => $vh_uid,
                'send_time' => $time,
            ];
            $data = [];
            if (in_array($approve_type, [0, 1, 4, 5, 6])) {
                $info['reviewer'] = $vh_vos_name;
                $info['reviewer_id'] = $vh_uid;
                $info['reviewe_time'] = $time;
                // 发送采购审批
                if ($approve_type == 1) {
                    $new_info = $info;
                    $new_info['status'] = 0;
                    $new_info['approve_type'] = 2;
                    $new_info['reviewer'] = '';
                    $new_info['reviewer_id'] = 0;
                    $new_info['reviewe_time'] = 0;

                    $data = [$info, $new_info];
                }
            }

            if (empty($data)) {
                $data = [$info];
            }
            Db::name('purchase_orderno_approve')->insertAll($data);
        } else {
            $approve_id = Db::name('purchase_orderno_approve')
                ->where([
                    ['approve_type', '=', $approve_type],
                    ['purchase_orderno_id', '=', $id],
                    ['status', '=', 0],
                ])->value('id');

            if (empty($approve_id)) {
                throw new Exception('审批流查询失败');
            }
            $info = [
                'status' => $status,
                'comments' => $comments,
                'reviewer' => $vh_vos_name,
                'reviewer_id' => $vh_uid,
                'reviewe_time' => $time,
            ];
            Db::name('purchase_orderno_approve')->where('id', $approve_id)->update($info);
        }

        return true;
    }

    /**
     * @方法描述: 插入一条数据
     * <AUTHOR>
     * @Date 2022/8/10 14:37
     * @param $param
     * @return bool|mixed
     */
    public function add($param)
    {
        if (empty($param['orderno'])) {
            $param['orderno'] = $this->generateBillNo();
        }
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }

        $period_id = [];
        foreach ($param['items'] as $check_item) {
            $validate = $this->check('add_items', $check_item);
            if ($validate !== true) {
                return $validate;
            }
            $period = explode(',', strval($check_item['period']));
            $period_id = array_values(array_filter(array_unique(array_merge($period_id, $period))));
            $item_price = $check_item['price'] ?? 0;
            $item_number = $check_item['number'] ?? 0;
            $item_total = $check_item['total'] ?? 0;
            if (bcmul(strval($item_price), strval($item_number), 2) != strval($item_total)) {
                return $this->failed('含税单价乘数量不等于价税合计', ErrorCode::EXEC_ERROR);
            }

        }
        #endregion

        $error_msg = '';
        //region 插入数据
        Db::startTrans();
        try {
            if (!empty($param['department']) && $param['department'] == '市场部') {
                throw new Exception('暂未支持此部门审批流程');
            }
            $param['source']       = 1; //来源：0=手动，1=自动
            $param['created_uid']  = $param['vh_uid'];
            $param['created_name'] = $param['vh_vos_name'];
            $param['created_time'] = time();
            $param['status']       = 0;
            $param['orderno'] = trim($param['orderno']);

            $model         = $this->model;
            $check_where[] = ['orderno', '=', $param['orderno']];
            if (!empty($param['id'])) {
                $check_where[] = ['id', '<>', $param['id']];
                $model         = $model->where('id', $param['id'])->find();
                if ($model->operate_status == 1) {
                    throw new Exception('已经推送U8C, 不可更改数据');
                }
                if ($model->source == 2) {
                    throw new Exception('请在今日采购订单列表操作。');
                }
                if (!in_array($model->push_erp_status, [0, 3, 6])) {
                    throw new Exception('采购单已提交请勿重复操作。');
                }
            }
            if ($this->model->where($check_where)->count()) {
                throw new Exception('PO单重复');
            }
            // 判断是否已生成新采购单
            $this->judgmentNewPurchaseOrder($param);

            // 查询期数信息
            $period_es = Es::name('periods')->where([['_id', 'in', $period_id]])
                ->field('id,periods_type,payee_merchant_id,payee_merchant_name')
                ->select()->toArray();
            $period_info = [];
            $payee_merchant_id = 0;
            $payee_merchant_name = '';
            foreach ($period_es as $v) {
                if ($payee_merchant_id > 0 && $payee_merchant_id != $v['payee_merchant_id']) {
                    throw new Exception('期数收款商户不一致！');
                }
                $payee_merchant_id = $v['payee_merchant_id'];
                $payee_merchant_name = $v['payee_merchant_name'];
                $period_info[$v['id']] = $v;
            }
            $param['payee_merchant_id'] = $payee_merchant_id;
            $param['payee_merchant_name'] = $payee_merchant_name;

            if (empty($param['corp_code'])) {
                $param['corp_code'] = payeeMerchantIdCodeExchange($payee_merchant_id, 2);
            }
            if (empty($param['payee_merchant_id'])) {
                $param['payee_merchant_id'] = payeeMerchantIdCodeExchange($param['corp_code'], 1);
                $param['payee_merchant_name'] = getPayeeMerchantNameID($param['payee_merchant_id']);
            }
            $company_info       = $this->company_info[$param['corp_code']] ?? [];
            $param              = array_merge($param, $company_info);

            // 期数对应下单数量
            $period_datas = [];
            foreach ($param['items'] as $item) {
                if ($item['period'] == 1) {
                    $param['is_stockup'] = 1;
                }
                $period = array_values(array_filter(array_unique(explode(',', strval($item['period'])))));
                // 剩余数量
                $surplus = $item['number'];
                $total_num = count($period);
                foreach ($period as $k => $v) {
                    // 是否最后一条
                    $is_end = ($k + 1) == $total_num;
                    $pinfo = $period_info[$v] ?? [];
                    // if (empty($pinfo)) {
                    //     throw new Exception('【' . $v . '】期数信息查询失败。');
                    // }
                    $number = intval($item['number'] / $total_num);
                    if ($is_end) {
                        $number = $surplus;
                    } else {
                        $surplus -= $number;
                    }
                    $period_datas[] = [
                        'period' => $v,
                        'periods_type' => $pinfo['periods_type'] ?? 0,
                        'short_code' => $item['short_code'],
                        'number' => $number,
                        'created_time' => time(),
                    ];
                }
            }

            // 采购审核
            $approve_type = 0;
            if (isset($param['operate_status'])) {
                if ($param['operate_status'] == 1) {
                    //验证供应商合同日期
                    $this->contract_end($param['supplier']);

                    $approve_type = 1;
                    $param['operate_status']  = 0;
                    $param['push_erp_status'] = 4;
                    $param['push_erp_fail_reason'] = '';

                    // 查询审核人
                    $revieweror = Db::name('purchase_orderno_revieweror')
                        ->whereIn('purchaseor', [$param['operator_name'], '其他'])
                        ->column('purchaseor_id,revieweror,revieweror_id', 'purchaseor');
                    $reviewerinfo = $revieweror[$param['operator_name']] ?? $revieweror['其他'];
                    $param['purchase_reviewer'] = $reviewerinfo['revieweror'];
                    $param['purchase_reviewer_id'] = $reviewerinfo['revieweror_id'];
                    // if (in_array($param['operator_name'], ['刘歆韵', '彭媛媛'])) {
                    //     // 推送ERP
                    //     $this->pushErp($param, 1, $period_datas);
                    //     $param['operate_status']  = 1;
                    //     $param['push_erp_status'] = 1;
                    // }

                } else if ($param['operate_status'] == 0) {
                    $param['operate_status']  = 0;
                    $param['push_erp_status'] = 6;
                    //修改超卖发货时间
                    $this->addOversellDeliveryTime($period_datas);
                }
            }

            $model->save($param);
            $model->items()->delete();
            $model->items()->saveAll($param['items']);
            $model->period()->delete();
            if (!empty($period_datas)) {
                $model->period()->saveAll($period_datas);
            }

            // 添加审批流
            $this->addApprove($model->id, $approve_type, 1);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('插入数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('插入数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        if (!empty($error_msg)) {
            return $this->failed($error_msg, ErrorCode::EXEC_ERROR);
        }

        return $this->success(['id' => $model->id]);
    }

    /**
     * @方法描述: 更新数据
     * <AUTHOR>
     * @Date 2024/1/18
     * @param $param
     * @return bool|mixed
     */
    public function edit($param)
    {
        $time = time();
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }

        $period_ids = $short_code_all = [];
        foreach ($param['items'] as &$check_item) {
            $validate = $this->check('add_items', $check_item);
            if ($validate !== true) {
                return $validate;
            }
            if (in_array($check_item['short_code'], $short_code_all)) {
                return $this->failed('产品简码不能重复！', ErrorCode::EXEC_ERROR);
            }
            $item_price = $check_item['price'] ?? 0;
            $item_number = $check_item['number'] ?? 0;
            $item_total = $check_item['total'] ?? 0;
            if (bcmul(strval($item_price), strval($item_number), 2) != strval($item_total)) {
                return $this->failed('含税单价乘数量不等于价税合计', ErrorCode::EXEC_ERROR);
            }
            $short_code_all[] = $check_item['short_code'];
            // if (empty($check_item['id'])) {
            //     return $this->failed('产品ID不能为空！', ErrorCode::EXEC_ERROR);
            // }
            $periodSlice = array_filter(explode(',', $check_item['period']), 'intval');
            if (empty($check_item['id']) && count($periodSlice) > 1) {
                return $this->failed('新增简码不能对应多期数', ErrorCode::EXEC_ERROR);
            }

            foreach ($periodSlice as $v) {
                $period_ids[] = $v;
            }
        }

        $es_period_infos = [];
        if (!empty($period_ids)) {
            $period_info = Es::name(Es::PERIODS)->where([
                ['_id', 'in', $period_ids],
            ])->field('id,periods_type')->select()->toArray();
            foreach ($period_info as $v) {
                $es_period_infos[$v['id']] = $v;
            }

            foreach ($period_ids as $v) {
                if (empty($es_period_infos[$v]) && $v != 1) {
                    return $this->failed("期数【{$v}】信息查询失败！", ErrorCode::EXEC_ERROR);
                }
            }
        }

        $source = 0;
        // 财务手动确认
        if (isset($param['source']) && intval($param['source']) === 1) {
            $source = 1;
        }
        unset($param['source']);

        #endregion
        $error_msg = '';
        //region 插入数据
        Db::startTrans();
        try {
            $param['orderno'] = trim($param['orderno']);
            $model         = $this->model;
            if (empty($param['id'])) {
                throw new Exception('采购单ID不能为空！');
            }
            $exist = $model->where([
                ['orderno', '=', $param['orderno']],
                ['id', '<>', $param['id']],
            ])->value('id');
            if (!empty($exist)) {
                throw new Exception('PO单重复。');
            }

            $model = $model->where('id', $param['id'])->find();
            if (empty($model)) {
                throw new Exception('PO单不存在');
            }
            if ($model->operate_status == 1) {
                throw new Exception('已经推送U8C, 不可更改数据');
            }
            if (!in_array($model->push_erp_status, [0, 3, 6])) {
                throw new Exception('采购单已提交请勿重复操作。');
            }
            // 判断是否已生成新采购单
            $this->judgmentNewPurchaseOrder($param);
            $param['payee_merchant_id'] = $model->payee_merchant_id;
            $param['payee_merchant_name'] = $model->payee_merchant_name;

            if ($source == 0) {
                $param['created_uid']  = $param['vh_uid'];
                $param['created_name'] = $param['vh_vos_name'];
            } else {
                $param['created_uid']  = $model->created_uid;
                $param['created_name'] = $model->created_name;
            }


            $param['corp_code'] = $model->corp_code;
            $param['update_time'] = $time;
            $param['created_time'] = $time;

            // 查询产品数据
            $item_datas = $model->items()
                ->where('purchase_orderno_id', $model->id)
                ->select()->toArray();

            // 查询采购期数数据
            $period_datas = $model->period()
                ->where('purchase_orderno_id', $model->id)
                ->select()->toArray();
            $exist_periods = [];
            foreach ($period_datas as $k => $v) {
                $exist_periods[$v['period'] . $v['short_code']] = $v;
            }

            // 获取存在的期数和产品
            $exist_item = [];
            foreach ($param['items'] as $item) {
                if ($item['period'] == 1) {
                    $param['is_stockup'] = 1;
                }
                $periodSlice = array_filter(explode(',', $item['period']), 'intval');
                $periodTotal =  count($periodSlice);
                $number = intval($item['number'] / $periodTotal);
                foreach ($periodSlice as $k => $v) {
                    $is_end = ($k + 1) == $periodTotal;
                    if ($is_end && $periodTotal > 1) {
                        $number = $item['number'] - $number * ($k);
                    }
                    if (!empty($v)) {
                        $exist_item[$v . $item['short_code']] = $v;
                    }
                    if (empty($item['id']) || empty($exist_periods[$v . $item['short_code']])) {
                        $period_datas[] = [
                            'period' => $v,
                            'periods_type' => $es_period_infos[$v]['periods_type'],
                            'short_code' => $item['short_code'],
                            'number' => $number,
                            'created_time' => $time,
                        ];
                    }
                }
            }

            // 删除期数
            $del_ids = [];
            foreach ($period_datas as $k => $v) {
                if (empty($exist_item[$v['period'] . $v['short_code']])) {
                    unset($period_datas[$k]);
                    $del_ids[] = $v['id'];
                }
            }
            if (!empty($del_ids)) {
                Db::name('purchase_orderno_period')->whereIn('id', $del_ids)->delete();
            }

            // 删除产品
            $del_ids = [];
            $ids = array_column($param['items'], 'id');
            foreach ($item_datas as $k => $v) {
                if (!in_array($v['id'], $ids)) {
                    $del_ids[] = $v['id'];
                }
            }
            if (!empty($del_ids)) {
                Db::name('purchase_orderno_items')->whereIn('id', $del_ids)->delete();
            }

            // 修改期数下单数量
            $period_id = [];
            foreach ($param['items'] as $item) {
                $periodSlice = array_filter(explode(',', $item['period']), 'intval');

                $period_id = array_merge($period_id, $periodSlice);
                foreach ($period_datas as $k => $v) {
                    if (count($periodSlice) == 1) {
                        if (in_array($v['period'], $periodSlice) && $v['short_code'] == $item['short_code']) {
                            $period_datas[$k]['number'] = $item['number'];
                        }
                    } else {
                        // 获取最大期数
                        $maxperiod = max($periodSlice);
                        if ($maxperiod == $v['period'] && $v['short_code'] == $item['short_code']) {
                            $number = $item['number'];
                            foreach ($period_datas as $kk => $vv) {
                                if (in_array($vv['period'], $periodSlice) && $vv['period'] != $maxperiod && $vv['short_code'] == $item['short_code']) {
                                    if ($number > $vv['number']) {
                                        $number = $number - $vv['number'];
                                    } else {
                                        $period_datas[$kk]['number'] = $number;
                                        $number = 0;
                                    }
                                }
                            }
                            $period_datas[$k]['number'] = $number;
                        }
                    }
                }
            }
            $param['period'] = implode(',', $period_id);
            $approve_type = 0;
            if (isset($param['operate_status'])) {
                if ($param['operate_status'] == 1) {
                    //验证供应商合同日期
                    $this->contract_end($param['supplier']);

                    $approve_type = 1;
                    $param['operate_status']  = 0;
                    $param['push_erp_status'] = 4;
                    $param['push_erp_fail_reason'] = '';
                    // 查询审核人
                    $revieweror = Db::name('purchase_orderno_revieweror')
                        ->whereIn('purchaseor', [$param['operator_name'], '其他'])
                        ->column('purchaseor,revieweror,revieweror_id', 'purchaseor');
                    $reviewerinfo = $revieweror[$param['operator_name']] ?? $revieweror['其他'];
                    $param['purchase_reviewer'] = $reviewerinfo['revieweror'];
                    $param['purchase_reviewer_id'] = $reviewerinfo['revieweror_id'];

                    // if (in_array($param['operator_name'], ['刘歆韵', '彭媛媛'])) {
                    //     // 推送ERP
                    //     $this->pushErp($param, 1, $period_datas);
                    //     $param['operate_status']  = 1;
                    //     $param['push_erp_status'] = 1;
                    // }

                } else if ($param['operate_status'] == 0) {
                    $param['operate_status']  = 0;
                    $param['push_erp_status'] = 6;
                    //修改超卖发货时间
                    $this->addOversellDeliveryTime($period_datas);
                }
            }

            $model->save($param);
            $model->items()->saveAll($param['items']);
            $model->period()->saveAll($period_datas);

            // 添加审批流
            $this->addApprove($model->id, $approve_type, 1);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('更新数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('更新数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        if (!empty($error_msg)) {
            return $this->failed($error_msg, ErrorCode::EXEC_ERROR);
        }

        return $this->success(['id' => $model->id]);
    }

    /**
     * @方法描述: 修改超卖发货时间
     * <AUTHOR>
     * @Date 2024/04/18
     * @param array $param 请求参数
     * @param int $predict_shipment_time 发货时间
     * @return bool
     */
    public function addOversellDeliveryTime($period_datas)
    {
        $period_ids = array_column($period_datas, 'period');
        // 查询当前最新超卖发货时间
        $order_time = Db::name('periods_product_inventory_order')
            ->whereIn('period', $period_ids)
            ->group('period')
            ->column('max(predict_shipment_time) as predict_shipment_time', 'period');
        // 查询当前最新发货时间
        $periods_data = Es::name('periods')
            ->where([
                ['id', 'in', $period_ids],
            ])
            ->field('id,predict_shipment_time')
            ->select()->toArray();
        $periods_info = [];
        foreach ($periods_data as $v) {
            $periods_info[$v['id']] = $v;
        }

        $time = time();
        //把发货时间设置为当前时间+6，超卖发货时间设置为当前时间+8
        //如果当前时间是周五，则把发货时间设置为当前时间+7，超卖发货时间设置为当前时间+9
        //https://devops.aliyun.com/projex/project/8ab3146db7bf4a79876e3a5333/task#viewIdentifier=1945bcda00c0cf935ebdd8a5&openWorkitemIdentifier=027a29d8d059639313a7feb915
        $predict_shipment_time = $time + (86400 * 6);
        $surpass_shipment_time = $time + (86400 * 8);

        $week = date('w');
        //如果当前时间是周五，则把发货时间设置为当前时间+7，超卖发货时间设置为当前时间+9
        if ($week == 5) {
            $predict_shipment_time = $time + (86400 * 7);
            $surpass_shipment_time = $time + (86400 * 9);
        }
        
        foreach ($period_datas as $v) {
            //当前最新超卖发货时间大于需设置超卖发货时间
            $s_order_time = $order_time[$v['period']] ?? 0;
            $s_surpass_shipment_time = $s_order_time >= $surpass_shipment_time ? $s_order_time : $surpass_shipment_time;

            //当前最新发货时间大于需设置发货时间
            $s_predict_shipment_time = !empty($periods_info[$v['period']]['predict_shipment_time']) ? strtotime($periods_info[$v['period']]['predict_shipment_time']): 0;
            $s_predict_shipment_time = $s_predict_shipment_time >= $predict_shipment_time ? $s_predict_shipment_time : $predict_shipment_time;

            // 更新期数发货时间
            (new PeriodsService($v['periods_type']))->updateInfo($v['period'], [
                'predict_shipment_time' => $s_predict_shipment_time,
                'update_time' => $time,
            ]);
            
            $inventory_order[] = [
                'period' => $v['period'],
                'short_code' => $v['short_code'],
                'type' => 1,
                'order' => 0,
                'operator' => '采购单预下单',
                'operator_id' => 0,
                'predict_shipment_time' => $s_surpass_shipment_time,
                'created_time' => $time,
            ];
        }
        // 添加订货记录
        Db::name('periods_product_inventory_order')->insertAll($inventory_order);
    }


    /**
     * @方法描述: 判断新采购单是否生成
     * <AUTHOR>
     * @Date 2024/04/11
     * @param array $param 请求参数
     * @return bool
     */
    public function judgmentNewPurchaseOrder($param)
    {
        $where_str = '';
        foreach ($param['items'] as $item) {
            if ($item['period'] == 1) {
                continue;
            }

            $periodSlice = array_filter(explode(',', $item['period']), 'intval');
            foreach ($periodSlice as $p) {
                $s = "(op.short_code = '{$item['short_code']}' and op.period = {$p})";
                $where_str = empty($where_str) ? $s : "$where_str or $s";
            }
        }
        if (empty($where_str)) {
            return true;
        }

        $stime = strtotime(date('Y-m-d'));
        $etime = strtotime(date('Y-m-d 23:59:59'));
        $where = [
            ['o.created_time', 'BETWEEN', [$stime, $etime]],
            ['o.push_erp_status', '<>', 8],
        ];
        if (!empty($param['id'])) {
            $where[] = ['o.id', '<>', $param['id']];
        }

        $exist = Db::name('purchase_orderno_period')
            ->alias('op')
            ->leftJoin('purchase_orderno o', 'o.id = op.purchase_orderno_id')
            ->where($where)
            ->where($where_str)
            ->column('op.period,o.orderno');
        if (!empty($exist)) {
            $period_id = implode(',', array_values(array_unique(array_column($exist, 'period'))));
            $orderno = implode(',', array_values(array_unique(array_column($exist, 'orderno'))));
            throw new Exception('新单据已生成，期数：' . $period_id . '，单号：' . $orderno);
        }

        return true;
    }

    /**
     * @方法描述: 验证供应商合同日期
     * <AUTHOR>
     * @Date 2024/04/08
     * @param string $supplier_name 供应商名称
     * @return bool
     */
    public function contract_end($supplier_name)
    {
        // 供应商信息
        $contract_end = Db::table('vh_wiki.vh_supplier')
            ->whereIn('supplier_name', $supplier_name)
            ->where('delete_time', 0)
            ->value('contract_end');
        if (empty($contract_end)) {
            throw new Exception('供应商合同已到期，请先续约。');
        }

        if (strtotime($contract_end . ' 23:59:59') < time()) {
            throw new Exception('供应商合同已到期，请先续约。');
        }

        return true;
    }

    /**
     * @方法描述: 推送ERP
     * <AUTHOR>
     * @Date 2023/11/14 17:08
     * @param object $info 采购单数据
     * @param int $source 来源：0-验证，1-不验证
     * @return \Closure
     */
    public function pushErp($info, $source = 0, $period_datas = [])
    {
        $time = time();
        // 获取实时数据并更新
        $datas = $this->RealTimeDataProcessing([$info->toArray()], 3);
        $param = $datas[0];
        $param['update_time'] = $time;
        unset($param['created_time']);
        $info->save($param);
        $info->items()->saveAll($param['items']);

        //税率确认1-已确认 0-未确认(默认)
        $check_tax = 1;
        //1-校验数据(默认), 2-不校验
        $check = 2;
        if ($source == 0) {
            $check_tax = 0;
            $check = 1;
        }
        // 修改期数下单数量
        $period_id = $short_code = [];
        foreach ($param['items'] as $item) {
            $short_code[] = $item['short_code'];
            $periodSlice = array_filter(explode(',', $item['period']), 'intval');
            $period_id = array_merge($period_id, $periodSlice);
        }

        // 查询期数信息
        $period_es = Es::name('periods')->where([['_id', 'in', $period_id]])
            ->field('id,estimate_purchase,operation_review_id')
            ->select()->toArray();
        $period_info = $operation_review_id = [];
        foreach ($period_es as $v) {
            if (empty($v['estimate_purchase'])) {
                $v['estimate_purchase'] = date('Y-m-d H:i:s');
            }
            $period_info[$v['id']] = $v;
            $operation_review_id[] = $v['operation_review_id'];
        }

        //推送U8C
        $push_list = [];
        foreach ($param['items'] as $item) {
            $push_list[] = [
                'short_code' => $item['short_code'],
                'number'     => $item['number'],
                'price'      => $item['price'],
                'tax_rate'   => $item['tax_rate'],
                'is_gift'    => $item['is_gift'],
                'period'     => $item['period'] == 1 ? '闪购备货' : $item['period'],
                'remark'     => $item['remark'],
                'check_tax'  => $check_tax,
            ];
        }

        // 附件
        $files = [];
        if (!empty($param['annex'])) {
            foreach ($param['annex'] as $v) {
                if (empty($v)) {
                    continue;
                }
                $file_name = basename($v);
                $v = str_replace(env('ALIURL'), '', $v);
                $v = str_replace($file_name, urlencode($file_name), $v);
                try {
                    $a_url = env('ALIURL') . $v;
                    $a_data = file_get_contents($a_url);
                } catch (\Exception $e) {
                    throw new Exception('附件获取失败', 10001);
                }
                if (empty($a_data)) {
                    throw new Exception('附件数据获取失败', 10001);
                }
                $files[] = [
                    'file_name' => $file_name,
                    'file_length' => strlen($a_data),
                    'content' => base64_encode($a_data),
                ];
            }
        }

        $push_data = [
            'operator'        => $param['created_uid'],
            'check'           => $check, //1-校验数据(默认), 2-不校验
            'bill_code'       => $param['orderno'],
            'corp_code'       => $param['corp_code'],
            'bill_date'       => $param['bill_date'],
            'business_type'   => $param['business_type'] ?? 'CG01',
            'warehouse_code'  => $param['warehouse_code'],
            'vendor_code'     => $param['supplier_code'],
            'dept_code'       => $param['department_code'],
            'psn_code'        => $param['operator_code'],
            'settlement_code' => $param['setttlement_code'],
            'invoice_vendor'  => $param['supplier_code'],
            'exchange_rate'   => $param['exchange_rate'] ?? '1',
            'currency_code'   => $param['currency_code'] ?? 'CNY',
            'remark'          => $param['remark'],
            'list'            => $push_list,
            'files'           => $files,
            'is_stockup'      => $param['is_stockup'] ?? 0,
        ];
        if (empty(env('APP_DEBUG')) && !in_array($param['corp_code'], ['032'])) {
            // 文档：https://showdoc.wineyun.com/web/#/94/4686
            \Curl::createByPeriodList($push_data, ["vinehoo-uid:{$param['created_uid']}"]);
            
        } else if (in_array($param['corp_code'], ['032'])) {
            $is_supplier_delivery = Db::name('virtual_warehouse')
            ->where('virtual_id', intval($param['warehouse_code']))
            ->value('is_supplier_delivery');
            // 非代发仓推送WMS
            if (empty($is_supplier_delivery) || $is_supplier_delivery == 0) {
                try {
                    $this->pushWms($info);
                } catch (\Exception $e) { // 推送失败
                    $error_msg = $e->getMessage();
                    if (strpos($error_msg, 'po或调拨单号已存在') === false) {
                        throw new Exception($error_msg);

                    } else {
                        $wx = new WorkWeixinService();
                        $wx->uid = empty(env('APP_DEBUG')) ? 'ZhangFeng' : 'XuanYiChang';
                        $wx->sendNotify("{$param['orderno']} 一花一世界采购订单萌牙已已存在，请核实！");
                    }
                }
            }
        }
        

        if (empty($period_datas)) {
            if (empty($param['id'])) {
                return $period_info;
            }

            // 查询采购期数数据
            $period_datas = $this->model->period()
                ->where('purchase_orderno_id', $param['id'])
                ->select()->toArray();
        }
        //把发货时间设置为当前时间+6，超卖发货时间设置为当前时间+8
        //如果当前时间是周五，则把发货时间设置为当前时间+7，超卖发货时间设置为当前时间+9
        $surpass_shipment_time = $time + (86400 * 8);
        $week = date('w');
        if ($week == 5) {
            $surpass_shipment_time = $time + (86400 * 9);
        }
        $period_ids = array_column($period_datas, 'period');
        // 查询当前最新超卖发货时间
        $order_time = Db::name('periods_product_inventory_order')
            ->whereIn('period', $period_ids)
            ->group('period')
            ->column('max(predict_shipment_time) as predict_shipment_time', 'period');
        // 添加商品备注
        $periods_remark = $inventory_order = $inventory_adjust = [];
        foreach ($period_datas as $v) {
            // 备货和数量为负不操作
            if ($v['period'] == 1 || $v['number'] < 0) {
                continue;
            }
            $inventory_adjust[$v['short_code']][] = $v;

            //当前最新超卖发货时间大于需设置超卖发货时间
            $s_order_time = $order_time[$v['period']] ?? 0;
            $s_surpass_shipment_time = $s_order_time >= $surpass_shipment_time ? $s_order_time : $surpass_shipment_time;

            $p_info = $period_info[$v['period']] ?? [];
            $estimate_purchase = $p_info['estimate_purchase'] ?? date('Y-m-d H:i:s');

            $periods_remark[] = [
                'period' => $v['period'],
                'periods_type' => $v['periods_type'],
                'remark' => "【{$v['short_code']}】已订货：{$v['number']}",
                'operator' => $param['created_uid'],
                'operator_name' => $param['created_name'],
                'created_time' => $time,
            ];
            $inventory_order[] = [
                'period' => $v['period'],
                'short_code' => $v['short_code'],
                'type' => 1,
                'order' => $v['number'],
                'operator' => $param['created_name'],
                'operator_id' => $param['created_uid'],
                'predict_shipment_time' => $s_surpass_shipment_time,
                'created_time' => $time,
            ];
            // 更新订货量
            Db::name('periods_product_inventory')
                ->where([
                    'period' => $v['period'],
                    'short_code' => $v['short_code'],
                ])
                ->inc('order', $v['number'])->update();
        }

        if (!empty($inventory_order)) {
            // 添加订货记录
            Db::name('periods_product_inventory_order')->insertAll($inventory_order);
        }
        

        //采购下单 - 采购订单审核通过后，自动调整运营库存:https://devops.aliyun.com/projex/project/8ab3146db7bf4a79876e3a5333/task#viewIdentifier=1945bcda00c0cf935ebdd8a5&openWorkitemIdentifier=1c98b42e52b5f54c39622509fd
        $product_inventory = Db::name('periods_product_inventory')->where([
            ['period', 'in', $period_ids],
            ['short_code', 'in', $short_code],
        ])->column('period,short_code,inventory,product_id,inventory_accum,order');
        $product_inventory_map = [];
        foreach ($product_inventory as $v) {
            $product_inventory_map["{$v['period']}:{$v['short_code']}"] = $v;
        }
        foreach ($inventory_adjust as $s_short_code => $v) {
            //如果简码订货量对应的期数为单个，则判断期数的简码总订货量大于期数设置的运营库存，则修改运营库存为总订货量值。
            if (count($v) == 1) {
                $info = $v[0];
                $s_inventory = $product_inventory_map["{$info['period']}:{$info['short_code']}"] ?? [];
                $s_inventory_number = $s_inventory['inventory_accum'] ?? 0;
                $number = $s_inventory['order'] ?? 0;
                
                if ($number <= $s_inventory_number) {
                    continue;
                }
                $url = env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/sys/update';
                $nums = intval($number - $s_inventory_number);
                $body = json_encode([
                    'period_id' => intval($s_inventory['period']),
                    'product_id' => intval($s_inventory['product_id']),
                    'nums' => $nums,
                    'action' => 'inc',
                ]);
                $header = [
                    'vinehoo-uid:0',
                    'vinehoo-vos-name:' . base64_encode('系统'),
                ];
                $res = curlRequest($url, $body, $header);
                $remark = "成功，【{$param['orderno']}】{$s_short_code}总订货{$number}，数量：+{$nums}";
                if (!isset($res['error_code']) || intval($res['error_code']) !== 0) {
                    $msg = $res['error_msg'] ?? '修改请求失败';
                    if (strpos($msg, '等待库存操作超时') === false) {
                        $remark = "【{$param['orderno']}】采购下单期数的简码总订货量大于期数设置的运营库存，失败：{$msg}";
                    }
                }
                $periods_remark[] = [
                    'period' => $info['period'],
                    'periods_type' => $info['periods_type'],
                    'remark' => $remark,
                    'operator' => 0,
                    'operator_name' => '系统',
                    'created_time' => $time,
                ];

            } else {//如果简码订货量对应的期数为多个，则判断期数的简码总订货量大于期数设置的运营库存，则通知运营修改运营库存。
                $notice_period = [];
                foreach ($v as $vv) {
                    $s_inventory = $product_inventory_map["{$vv['period']}:{$vv['short_code']}"] ?? [];
                    $s_inventory_number = $s_inventory['inventory_accum'] ?? 0;
                    $number = $s_inventory['order'] ?? 0;
                    
                    if ($number <= $s_inventory_number) {
                        continue;
                    }
                    $notice_period[] = $vv['period'];
                }

                if (!empty($notice_period)) {
                    $access_token = '86a016fd-c8b5-46f7-b1be-f917aa418abd';
                    if (!empty(env('APP_DEBUG'))){
                        $access_token = '3ba990b1-9a7b-425e-b1ae-c6f363c024ed';
                    }
                    $content = "{$s_short_code}总订货{$number}，有【" . implode('、', $notice_period) . "】需要调整库存，请手动处理！";
                    SendWeChatRobot($content, $access_token, '@all');
                }
                
            }
        }
        // 添加商品备注
        if (!empty($periods_remark)) {
            Db::name('periods_remark')->insertAll($periods_remark);
        }

        return $period_info;
    }

    /**
     * @方法描述: 取消订单
     * <AUTHOR>
     * @Date 2024/1/29
     * @param $param
     * @return bool|mixed
     */
    public function cancel($param)
    {
        Db::startTrans();
        try {
            #数据验证
            $validate = Validate::rule([
                'vh_uid|操作人ID' => 'require|number|>=:1',
                'vh_vos_name|操作人' => 'require|max:32',
                'id|采购单ID' => 'require|number|>=:1',
                'estimate_purchase|下次采购时间' => 'dateFormat:Y-m-d H:i:s',
            ]);
            if (!$validate->check($param)) {
                throw new Exception($validate->getError());
            }
            $model = $this->model->where('id', $param['id'])->find();
            if (empty($model->id)) {
                throw new Exception('采购单信息查询失败。');
            }
            if ($model->operate_status == 1) {
                throw new Exception('采购单已确认下单。');
            }
            if (!in_array($model->push_erp_status, [0, 3, 6])) {
                throw new Exception('采购单状态不可操作。');
            }


            if (!empty($param['estimate_purchase'])) {
                // 查询期数
                $periods = $model->period()
                    ->where('purchase_orderno_id', $model->id)
                    ->column('period');
                // 修改预计采购时间
                Es::name('periods')
                    ->where([
                        ['_id', 'in', $periods]
                    ])
                    ->save(['estimate_purchase' => $param['estimate_purchase']]);
                // 添加审批流
                $comments = "下次下单时间：" . $param['estimate_purchase'];
                $push_erp_status = 8;
                $approve_type = 4;
            } else {
                $comments = '作废采购单';
                $push_erp_status = 9;
                $approve_type = 6;
                if ($model->push_erp_status != 3) {
                    throw new Exception('只有已驳回的采购单才能被作废。');
                }
            }

            $model->save([
                'created_uid' => $param['vh_uid'],
                'created_name' => $param['vh_vos_name'],
                'operate_status' => 2,
                'push_erp_status' => $push_erp_status,
                'update_time' => time(),
            ]);

            $this->addApprove($param['id'], $approve_type, 1, $comments);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('取消采购单失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('操作失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        return $this->success();
    }

    /**
     * @方法描述: 查询商品库存
     * <AUTHOR>
     * @Date 2023/11/15 14:02
     * @param $param
     * @return mixed
     * @throws Exception
     */
    public function goodsGetFictitiousCount($param)
    {
        $period      = $param['period'];
        $period_info = Es::name(Es::PERIODS)->where([['id', '=', $period]])->find();
        $data        = \Curl::goodsGetFictitiousCount(['short_code' => $period_info['short_code']]);
        return $this->success($data);
    }

    /**
     * @方法描述: 同步更新采购单
     * <AUTHOR>
     * @Date 2024/2/28
     * @param $param
     * @return bool|mixed
     */
    public function sync($param)
    {
        $time = time();
        Db::startTrans();
        try {
            #数据验证
            $validate = Validate::rule([
                'bill_code|采购单号' => 'require|max:50',
                'items|产品数据' => 'require|array',
            ]);
            if (!$validate->check($param)) {
                throw new Exception($validate->getError());
            }
            $items = [];
            foreach ($param['items'] as $v) {
                #数据验证
                $validate = Validate::rule([
                    'short_code|简码' => 'require|max:50',
                    'number|产品数量' => 'require|number|>=:0',
                ]);
                if (!$validate->check($v)) {
                    throw new Exception($validate->getError());
                }
                if (empty($items[$v['short_code']])) {
                    $items[$v['short_code']] = $v;
                } else {
                    $items[$v['short_code']]['number'] += $v['number'];
                }
            }

            $model = $this->model
                ->where('orderno', $param['bill_code'])
                ->where('operate_status', 1)
                ->find();
            if (empty($model->id)) {
                throw new Exception('采购单信息查询失败。');
            }
            if ($model->operate_status != 1) {
                throw new Exception('采购单未成功下单。');
            }
            // 查询采购期数数据
            $period_datas = $model->period()
                ->where('purchase_orderno_id', $model->id)
                ->select()->toArray();
            $periodItem = [];
            foreach ($period_datas as $v) {
                $periodItem[$v['period'] . $v['short_code']] = $v;
            }

            // 查询产品数据
            $item_datas = $model->items()
                ->where('purchase_orderno_id', $model->id)
                ->select()->toArray();
            $itemData = $items_update = $period_id = [];
            foreach ($item_datas as $v) {
                // 期数下单数量
                $period_id = array_merge($period_id, array_filter(array_unique(explode(',', strval($v['period'])))));
                $v['new_number'] = 0;
                $check_item = $items[$v['short_code']] ?? [];
                if (!empty($items[$v['short_code']])) {
                    $v['new_number'] = $check_item['number'];
                }
                $itemData[$v['short_code']] = $v;
            }

            // 新增产品
            foreach ($items as $v) {
                if (empty($itemData[$v['short_code']])) {
                    $items_update[] = [
                        'purchase_orderno_id' => $model->id,
                        'short_code' => $v['short_code'],
                        'number' => $v['number'],
                        'is_gift' => 1,
                    ];
                }
            }

            // 查询期数信息
            $es_period = Es::name('periods')
                ->where([['_id', 'in', $period_id]])
                ->field('id,estimate_purchase,periods_type')
                ->select()->toArray();
            $period_info = [];
            foreach ($es_period as $v) {
                $period_info[$v['id']] = $v;
            }

            $period_update = $periods_remark = $inventory_order = [];
            foreach ($itemData as $v) {
                if ($v['number'] != $v['new_number']) {
                    // 期数下单数量
                    $period = array_values(array_filter(array_unique(explode(',', strval($v['period'])))));
                    $new_number = $v['new_number'];
                    foreach ($period as $kk => $pod) {
                        // 期数信息
                        $p_info = $period_info[$pod] ?? [];

                        // 是否最后一条数据
                        $is_end = intval($kk + 1) == count($period);
                        $pitem = $periodItem[$pod . $v['short_code']] ?? [];
                        if (!empty($periodItem[$pod . $v['short_code']])) {
                            if ($new_number <= $pitem['number'] || $is_end) {
                                $remark = '';
                                $num = $oper_type = 0;
                                if ($new_number < $pitem['number']) {
                                    $num = intval($pitem['number'] - $new_number);
                                    $remark = "【{$v['short_code']}】减少订货：{$num}";
                                } else if ($new_number > $pitem['number']) {
                                    $oper_type = 1;
                                    $num = intval($new_number - $pitem['number']);
                                    $remark = "【{$v['short_code']}】已订货：{$num}";
                                }
                                if ($num > 0) {
                                    // 创建人
                                    $created_name = 'ERP同步';
                                    // 期数备注
                                    $periods_remark[] = [
                                        'period' => $pod,
                                        'periods_type' => $pitem['periods_type'],
                                        'remark' => $remark,
                                        'operator' => 0,
                                        'operator_name' => $created_name,
                                        'created_time' => $time,
                                    ];
                                    // 订货量备注
                                    $inventory_order[] = [
                                        'period' => $pod,
                                        'short_code' => $v['short_code'],
                                        'type' => $oper_type,
                                        'order' => $num,
                                        'operator' => $created_name,
                                        'operator_id' => 0,
                                        'predict_shipment_time' => !empty($p_info['estimate_purchase']) ? strtotime($p_info['estimate_purchase']) : '',
                                        'created_time' => $time,
                                    ];

                                    // 更新订货量
                                    $execute = Db::name('periods_product_inventory')
                                        ->where([
                                            'period' => $pod,
                                            'short_code' => $v['short_code'],
                                        ]);
                                    if ($oper_type == 0) { //减少
                                        $execute->dec('order', $num)->update();
                                    } else { // 增加
                                        $execute->inc('order', $num)->update();
                                    }
                                }
                                // 期数采购数量
                                $pitem['number'] = $new_number;
                                $new_number = 0;
                            } else {
                                $new_number = intval($new_number - $pitem['number']);
                            }
                        }
                        $period_update[] = $pitem;
                    }

                    $v['number'] = $v['new_number'];
                    $v['total'] = bcmul(strval($v['number']), strval($v['price']), 2);
                    unset($v['new_number']);
                    $items_update[] = $v;
                }
            }

            if (!empty($items_update)) {
                // 更新采购数据
                $model->items()->saveAll($items_update);
            }
            if (!empty($period_update)) {
                // 更新期数采购数据
                $model->period()->saveAll($period_update);
            }
            if (!empty($periods_remark)) {
                // 添加商品备注
                Db::name('periods_remark')->insertAll($periods_remark);
            }
            if (!empty($inventory_order)) {
                // 添加订货记录
                Db::name('periods_product_inventory_order')->insertAll($inventory_order);
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('同步更新采购单失败: ' . $e->getMessage());
            return $this->failed('同步更新采购单失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        return $this->success();
    }

    /**
     * @方法描述: 采购单审核通过
     * <AUTHOR>
     * @Date 2024/2/28
     * @param $param
     * @return mixed
     * @throws Exception
     */
    public function approved($param)
    {
        Db::startTrans();
        try {
            #数据验证
            $validate = Validate::rule([
                'vh_uid|操作人ID' => 'require|number|>=:1',
                'vh_vos_name|操作人' => 'require|max:32',
                'id|采购单ID' => 'require|number|>=:1',
                'source|来源' => 'require|number|in:0,1,2',
            ]);
            if (!$validate->check($param)) {
                throw new Exception($validate->getError());
            }

            $info = $this->model->with(['items'])->where('id', $param['id'])->find();
            if (empty($info)) {
                throw new Exception('采购单信息获取失败。');
            }
            if ($info->operate_status == 2) {
                throw new Exception('采购单已取消。');
            }
            if ($info->operate_status == 1) {
                throw new Exception('采购单已推送成功。');
            }

            $error_msg = '';
            if ($param['source'] == 0) { // 采购审核
                if ($info->push_erp_status != 4) {
                    throw new Exception('采购单无需采购审核。');
                }

                if ($info['corp_code'] == '032') {
                    $error_msg = '中台：一花一世界';
                }
                
                if (empty($error_msg)) {
                    foreach ($info['items'] as $item) {
                        if ($item['period'] == 1) {
                            $error_msg = '中台：备货';
                            break;
                        }
                    }

                    /**以销订采判断标准：
                        1. 南通闪购仓
                        2. 南通食品仓
                        3. 代发仓 */
                    $warehouse_id = intval($info->warehouse_code);
                    $is_supplier_delivery = Db::name('virtual_warehouse')
                        ->where([
                            ['virtual_id', '=', $warehouse_id],
                            ['is_supplier_delivery', '=', 1],
                        ])->value('id');
                    if (empty($is_supplier_delivery) && !in_array($warehouse_id, [4, 314])) {
                        $error_msg = '中台：非以销订采';
                    }
                }
            } else { // 财务审核
                if (!in_array($info->push_erp_status, [5, 10])) {
                    throw new Exception('采购单无需财务审核。');
                }
                if ($param['source'] == 1 && $info->push_erp_status != 5) {
                    throw new Exception('采购单无需财务审核。');
                }
                if ($param['source'] == 2 && $info->push_erp_status != 10) {
                    throw new Exception('采购单无需财务主管审核。');
                }
                if (empty($param['comments'])) {
                    throw new Exception('批语不能为空');
                }

                if ($info->push_erp_status == 10) {
                    // 查询财务主管ID
                    $admin_ids = Db::table('vh_authority.vh_admins_roles')
                        ->where('role_id', 116)
                        ->column('admin_id');
                    $is_finance = in_array($param['vh_uid'], $admin_ids);

                    if (!$is_finance) {
                        throw new Exception('只有财务主管才能操作。');
                    }
                } else {
                    // 一花一世界主体的采购订单财务审核后需要财务主管在中台审核，审核完成后不推送ERP
                    if ($info['corp_code'] == '032') {
                        $error_msg = '一花一世界';
                    }
                    // 备货订单经过从财务主管审核
                    // if ($info['is_stockup'] == 1) {
                    //     $error_msg = '备货单';
                    // }
                }
            }

            if (empty($error_msg)) {
                try {
                    // 推送ERP
                    $this->pushErp($info, $param['source']);
                } catch (\Exception $e) { // 推送失败
                    $error_msg = $e->getMessage();
                    if ($e->getCode() == 10001 || in_array($param['source'], [1, 2])) {
                        throw new Exception($error_msg);
                    }
                }
            }

            $approve_type = 2;
            if ($param['source'] == 0) { // 采购审核
                $update = [
                    'push_erp_status' => 7,
                    'operate_status' => 1,
                    'purchase_reviewer' => $param['vh_vos_name'],
                    'purchase_reviewer_id' => $param['vh_uid'],
                    'finance_comments' => '',
                ];
                if (!empty($error_msg)) { // 验证失败
                    $update['push_erp_status'] = 5;
                    $update['operate_status'] = 0;
                    $update['push_erp_fail_reason'] = $error_msg;
                    // 发送财务审核
                    $this->addApprove($param['id'], 3, 0);
                } else {
                    $update['finance_comments'] = '以销订采自动审批';
                }

            } else if ($param['source'] == 1) {// 财务审核
                $approve_type = 3;
                $update = [
                    'push_erp_status' => 1,
                    'operate_status' => 1,
                    'finance_comments' => $param['comments'],
                    'finance_reviewer' => $param['vh_vos_name'],
                    'finance_reviewer_id' => $param['vh_uid'],
                ];

                if (
                    !empty($error_msg) && $info['corp_code'] == '032' 
                ) {
                    $update['push_erp_status'] = 10;
                    $update['operate_status'] = 0;
                    $update['push_erp_fail_reason'] = '财务：' . $param['comments'];
                    // 发送财务主管审核
                    $this->addApprove($param['id'], 7, 0);
                }

            } else if ($param['source'] == 2) {// 财务主管审核
                $approve_type = 7;
                $update = [
                    'push_erp_status' => 1,
                    'operate_status' => 1,
                    'finance_comments' => $param['comments'],
                    'finance_reviewer' => $param['vh_vos_name'],
                    'finance_reviewer_id' => $param['vh_uid'],
                ];
            }

            // 修改审批流完成
            $this->addApprove($param['id'], $approve_type, 1, $update['finance_comments']);

            // 推送成功
            $info->save($update);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error($e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed($e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        return $this->success();
    }

    /**
     * @方法描述: 驳回采购单
     * <AUTHOR>
     * @Date 2024/2/28
     * @param $param
     * @return mixed
     * @throws Exception
     */
    public function reject($param)
    {
        Db::startTrans();
        try {
            #数据验证
            $validate = Validate::rule([
                'id|采购单ID' => 'require|number|>:0',
                'reason|驳回原因' => 'require|max:500',
                'source|来源' => 'require|number|in:0,1,2',
                'vh_uid|操作人ID' => 'require|number|>=:1',
                'vh_vos_name|操作人' => 'require|max:32',
            ]);
            if (!$validate->check($param)) {
                throw new Exception($validate->getError());
            }

            $model = $this->model->where('id', $param['id'])->find();
            if (empty($model->id)) {
                throw new Exception('采购单信息查询失败。');
            }
            if ($model->operate_status == 2) {
                throw new Exception('采购单已取消。');
            }
            if ($model->operate_status == 1) {
                throw new Exception('采购单已推送成功。');
            }

            $update = [
                'push_erp_status' => 3,
                'push_erp_refuse_reason' => $param['reason'],
                'created_time' => time(),
            ];
            $approve_type = 2;
            if ($param['source'] == 0) { // 采购审核
                if ($model->push_erp_status != 4) {
                    throw new Exception('采购单无需采购审核。');
                }
                $update['purchase_reviewer'] = $param['vh_vos_name'];
                $update['purchase_reviewer_id'] = $param['vh_uid'];

            } else if ($param['source'] == 1) { // 财务审核
                $approve_type = 3;
                if ($model->push_erp_status != 5) {
                    throw new Exception('采购单无需财务审核。');
                }
                $update['finance_reviewer'] = $param['vh_vos_name'];
                $update['finance_reviewer_id'] = $param['vh_uid'];

            } else if ($param['source'] == 2) { // 财务主管审核
                $approve_type = 7;
                if ($model->push_erp_status != 10) {
                    throw new Exception('采购单无需财务主管审核。');
                }
                $update['finance_reviewer'] = $param['vh_vos_name'];
                $update['finance_reviewer_id'] = $param['vh_uid'];
            }

            // 更新数据
            $model->save($update);

            // 添加审批流
            $this->addApprove($param['id'], $approve_type, 2, $param['reason']);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('驳回采购单失败: ' . $e->getMessage());
            return $this->failed('驳回采购单失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        return $this->success();
    }


    /**
     * @方法描述: 复审
     * <AUTHOR>
     * @Date 2024/3/25
     * @param $param
     * @return mixed
     * @throws Exception
     */
    public function retrial($param)
    {
        Db::startTrans();
        try {
            #数据验证
            $validate = Validate::rule([
                'id|采购单ID' => 'require|number|>:0',
                'remarks|备注' => 'require|max:500',
                'vh_uid|操作人ID' => 'require|number|>=:1',
                'vh_vos_name|操作人' => 'require|max:32',
            ]);
            if (!$validate->check($param)) {
                throw new Exception($validate->getError());
            }

            $model = $this->model->where('id', $param['id'])->find();
            if (empty($model->id)) {
                throw new Exception('采购单信息查询失败。');
            }
            if ($model->push_erp_status != 7) {
                throw new Exception('采购单无需复审。');
            }

            $update = [
                'push_erp_status' => 1,
                'retrial_remarks' => $param['remarks'],
                'retrialor' => $param['vh_vos_name'],
                'retrialor_id' => $param['vh_uid'],
                'update_time' => time(),
            ];

            // 更新数据
            $model->save($update);

            // 添加审批流
            $this->addApprove($param['id'], 5, 1, $param['remarks']);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('复审采购单失败: ' . $e->getMessage());
            return $this->failed('复审采购单失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        return $this->success();
    }

    /**
     * @方法描述: 更新附件
     * <AUTHOR>
     * @Date 2024/4/10
     * @param $param
     * @return mixed
     * @throws Exception
     */
    public function updateAnnex($param)
    {
        Db::startTrans();
        try {
            #数据验证
            $validate = Validate::rule([
                'id|采购单ID' => 'require|number|>:0',
            ]);
            if (!$validate->check($param)) {
                throw new Exception($validate->getError());
            }

            $model = $this->model->where('id', $param['id'])->find();
            if (empty($model->id)) {
                throw new Exception('采购单信息查询失败。');
            }
            $annex = $model->annex;

            $update = [
                'annex' => $param['annex'] ?? [],
                'update_time' => time(),
            ];

            // 更新数据
            $model->save($update);

            // 已推送ERP，直接上传附件
            if ($model->push_erp_status == 1 && $model->corp_code == '029') {
                $files = [];
                // 获取远程附件信息
                foreach ($param['annex'] as $v) {
                    if (empty($v)) {
                        continue;
                    }
                    $v = str_replace(env('ALIURL'), '', $v);
                    // 判断附件是否已存在
                    if (!empty($annex)) {
                        $is_exits = false;
                        foreach ($annex as $a) {
                            $a = str_replace(env('ALIURL'), '', $a);
                            if ($v == $a) {
                                $is_exits = true;
                            }
                        }
                        if ($is_exits === true) {
                            continue;
                        }
                    }
                    
                    $file_name = basename($v);
                    $v = str_replace($file_name, urlencode($file_name), $v);
                    try {
                        $a_url = env('ALIURL') . $v;
                        $a_data = file_get_contents($a_url);
                    } catch (\Exception $e) {
                        throw new Exception('附件获取失败', 10001);
                    }
                    if (empty($a_data)) {
                        throw new Exception('附件获取失败');
                    }
                    $files[] = [
                        'file_name' => $file_name,
                        'file_length' => strlen($a_data),
                        'content' => base64_encode($a_data),
                    ];
                }
                if (!empty($files)) {
                    $body = json_encode([
                        'corp' => strval($model->corp_code),
                        'code' => $model->orderno,
                        'files' => $files,
                    ]);
                    $res = curlRequest(env('ITEM.ERP_URL') . '/erp/v3/purchaseOrder/uploadBase64', $body);
                    if (!isset($res['error_code']) ||  intval($res['error_code']) !== 0) {
                        $msg = $res['error_msg'] ?? 'ERP API无响应';
                        throw new Exception('附件上传ERP失败: ' . $msg);
                    }
                }
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('更新附件失败: ' . $e->getMessage());
            return $this->failed('更新附件失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        return $this->success();
    }

    /**
     * @方法描述: 查询采购单产品信息
     * <AUTHOR>
     * @Date 2024/4/19
     * @param $param
     * @return mixed
     * @throws Exception
     */
    public function getItems($param)
    {
        #数据验证
        $validate = Validate::rule([
            'short_code|简码' => 'require',
            'period|期数' => 'number|>:0',
        ]);
        if (!$validate->check($param)) {
            return $this->failed($validate->getError(), ErrorCode::EXEC_ERROR);
        }
        if (empty($param['period']) && empty($param['supplier'])) {
            return $this->failed('期数和供应商不能都为空', ErrorCode::EXEC_ERROR);
        }
        $supplier_name = '';
        if (!empty($param['period']) && $param['period'] != 1) {
            // 查询期数信息
            $es_period = Es::name('periods')
                ->where([['_id', '=', $param['period']]])
                ->field('id,estimate_purchase,periods_type,onsale_status,onsale_time,supplier,product_id')
                ->find();
            if (empty($es_period)) {
                return $this->failed('期数信息查询失败', ErrorCode::EXEC_ERROR);
            }
            $supplier_name = $es_period['supplier'];
        } else {
            $supplier_name = trim($param['supplier']);
        }

        // 产品信息
        $vh_products = Db::table('vh_wiki.vh_products')
            ->alias('p')
            ->leftJoin('vh_wiki.vh_product_unit_open pu', 'pu.id=p.product_unit')
            ->where('p.short_code', $param['short_code'])
            ->field('p.id,p.short_code,p.tax_rate,p.cn_product_name,p.en_product_name,p.capacity,pu.name as unit,p.grape_picking_years as years')
            ->find();
        if (empty($vh_products)) {
            return $this->failed('产品档案不存在', ErrorCode::EXEC_ERROR);
        }

        // 磐石供应商信息
        $supplier = Db::table('vh_wiki.vh_supplier')
            ->where('supplier_name', $supplier_name)
            ->field('supplier_name,contract_end,supplier_tax')
            ->find();

        if (!empty($es_period)) {
            // 查询期数产品
            $period_product = Db::name('periods_product_inventory')
                ->where('period', $param['period'])
                ->where('product_id', $vh_products['id'])
                ->field('period,product_id,costprice,short_code,order,warehouse_id')
                ->find();
            if (empty($period_product) && empty($es_period['product_id'])) {
                return $this->failed('期数与简码不匹配', ErrorCode::EXEC_ERROR);
            }
            if (empty($period_product)) {
                $product_ids = explode(',', $es_period['product_id']);
                if (!in_array($vh_products['id'], $product_ids)) {
                    return $this->failed('期数与简码不匹配', ErrorCode::EXEC_ERROR);
                }
            }

            $query_param = [[
                'period' => $es_period['id'],
                'period_type' => $es_period['periods_type'],
            ]];
            // 查询简码真实已售
            $sale_num = GetSaleBottleNums($query_param);
            // 查询简码已售未发货数量
            $unshipped_num = GetUnshippedBottleNums($query_param);
            // 查询萌牙库存
            $url = env('ITEM.DISTRIBUTE_URL') . '/query/goodsGetFictitiousCount';
            $body = json_encode([
                'short_code' => [$param['short_code']],
                'store_code' => 'xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa',
            ]);
            $res = curlRequest($url, $body);
            $wms_data = $res['data'] ?? [];
            $my_inventory_data = $my_inventory_map = [];
            if (!empty($wms_data) && is_array($wms_data)) {
                foreach ($wms_data as $k => $v) {
                    if (!empty($v) && is_array($v)) {
                        foreach ($v as $vv) {
                            $my_inventory_data[$k . $vv['fictitious_id']] = $vv;

                            if (empty($my_inventory_map[$k])) {
                                $my_inventory_map[$k] = $vv;
                            } else {
                                $my_inventory_map[$k]['goods_count'] += $vv['goods_count'] ?? 0;
                                $my_inventory_map[$k]['transit_count'] += $vv['transit_count'] ?? 0;
                            }
                            
                        }
                    }
                }
            }
            $fictitious_id = $period_product['warehouse_id'] ?? 0;
            if (!empty($fictitious_id)) {
                $inventory_data = $my_inventory_data[$param['short_code'] . $fictitious_id] ?? [];

            } else {
                $inventory_data = $my_inventory_map[$param['short_code']] ?? [];
            }
            
        }
        
        

        $result = $vh_products;
        $result += [
            'period' => $param['period'] ?? 1,
            'purchased_nums' => $period_product['order'] ?? 0,
            'price' => $period_product['costprice'] ?? 0,
            'sale_nums' => $sale_num[$param['period'] ?? 0][$param['short_code']] ?? 0,
            'unshipped_num' => $unshipped_num[$param['period'] ?? 0][$param['short_code']] ?? 0,
            'my_inventory' => $inventory_data['goods_count'] ?? 0,
            'transit_count' => $inventory_data['transit_count'] ?? 0,
            // 是否昨天
            'is_yesterday' => strtotime(date('Y-m-d', strtotime($es_period['onsale_time'] ?? ''))) == strtotime(date('Y-m-d', strtotime('-1 day'))),
        ];
        $result['remark'] = '已售 ' . $result['sale_nums'];

        if (!empty($supplier['supplier_tax'])) {
            $result['tax_rate'] = $supplier['supplier_tax'];
        }

        return $this->success($result);
    }

    /**
     * @方法描述: 收回采购单
     * <AUTHOR>
     * @Date 2024/4/28
     * @param $param
     * @return mixed
     * @throws Exception
     */
    public function retract($param)
    {
        Db::startTrans();
        try {
            #数据验证
            $validate = Validate::rule([
                'id|采购单ID' => 'require|number|>:0',
            ]);
            if (!$validate->check($param)) {
                throw new Exception($validate->getError());
            }

            $model = $this->model->where('id', $param['id'])->find();
            if (empty($model->id)) {
                throw new Exception('采购单信息查询失败。');
            }
            if ($model->operate_status == 1) {
                throw new Exception('采购单已确认。');
            }
            if ($model->operate_status == 2) {
                throw new Exception('采购单已取消。');
            }
            if (!in_array($model->push_erp_status, [4, 5])) {
                throw new Exception('采购单状态不支持收回。');
            }

            switch ($model->push_erp_status) {
                case 4: //采购审核
                    $approve_type = 2;
                    break;
                case 5: //财务审核
                    $approve_type = 3;
                    break;
            }

            $update = [
                'push_erp_status' => 6,
                'update_time' => time(),
                'created_time' => time(),
            ];
            // 更新数据
            $model->save($update);
            // 添加审批流
            $this->addApprove($param['id'], $approve_type, 2, '收回采购单');

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('收回采购单失败: ' . $e->getMessage());
            return $this->failed('收回采购单失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        return $this->success();
    }

    /**
     * @方法描述: 上传运单号
     * <AUTHOR>
     * @Date 2024/4/28
     * @param $param
     * @return mixed
     * @throws Exception
     */
    public function uploadWaybill($param)
    {
        $time = time();
        Db::startTrans();
        try {
            #数据验证
            $validate = Validate::rule([
                'id|采购单ID' => 'require|number|>:0',
                'waybill_no|运单号' => 'require',
                'express_type|快递类型' => 'require|number|>:0',
            ]);
            if (!$validate->check($param)) {
                throw new Exception($validate->getError());
            }

            $model = $this->model->where('id', $param['id'])->find();
            if (empty($model->id)) {
                throw new Exception('采购单信息查询失败。');
            }
            if ($model->operate_status != 1) {
                throw new Exception('采购单未确认。');
            }
            $waybill_no = explode(',', $param['waybill_no']);
            $waybill_data = [];
            foreach ($waybill_no as $v) {
                if (!empty($v)) {
                    $waybill_data[] = [
                        'waybill_no' => $v,
                        'express_type' => $param['express_type'],
                        'created_time' => $time,
                        'createor' => $param['vh_vos_name'],
                        'createor_id' => intval($param['vh_uid']),
                    ];
                }
            }
            // 更新运单号数据
            $model->waybill()->delete();
            $model->waybill()->saveAll($waybill_data);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('上传运单号失败: ' . $e->getMessage());
            return $this->failed('上传运单号失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        return $this->success();
    }

    /**
     * @方法描述: 查询历史采购价格
     * <AUTHOR>
     * @Date 2025/4/14
     * @param $param
     * @return mixed
     * @throws Exception
     */
    public function getHistoryPrice($param)
    {
        $result = [];
        try {
            #数据验证
            $validate = Validate::rule([
                'short_code|简码' => 'require',
            ]);
            if (!$validate->check($param)) {
                throw new Exception($validate->getError());
            }

            $item = Db::name('purchase_orderno_period')
                ->alias('a')
                ->leftJoin('purchase_orderno_items i', 'i.purchase_orderno_id=a.purchase_orderno_id')
                ->leftJoin('purchase_orderno p', 'p.id=a.purchase_orderno_id')
                ->where([
                    'i.short_code' => $param['short_code'],
                ])
                ->field('p.orderno,i.short_code,i.price,p.supplier')
                ->order('p.id', 'desc')
                ->select();
            if (!empty($item)) {
                $history_price = [];
                foreach ($item as $v) {
                    $price = strval($v['price']);
                    $key = $v['supplier'] . ':' . $price;
                    
                    if (in_array($key, $history_price)) {
                        continue;
                    }

                    $history_price[] = $key;
                    $result[] = [
                        'orderno' => $v['orderno'],
                        'price' => $price,
                        'supplier' => $v['supplier'],
                    ];
                }
            }

            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('查询历史采购价格失败: ' . $e->getMessage());
            return $this->failed($e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        return $this->success(['list' => $result]);
    }

    /**
     * 生成单据编号
     * @return string
     */
    private function generateBillNo()
    {
        try {
            // 前缀
            $prefix = 'PO';
            
            // 获取年月日
            $year = date('Y'); // 获取两位年份
            $month = date('m');
            $day = date('d');
            
            // 获取当天的最大序列号
            $datePrefix = $prefix . '-' . $year . '-' . $month . '-' . $day;
            $maxBillNo = Db::name('purchase_orderno')
                ->where('orderno', 'like', $datePrefix . '%')
                ->order('orderno', 'desc')
                ->lock(true)
                ->value('orderno');
            
            // 生成新的序列号
            if ($maxBillNo) {
                $bill_no = explode('-', $maxBillNo);
                $sequence = intval($bill_no[4] ?? 0) + 1;
            } else {
                $sequence = 1;
            }
            
            // 格式化为6位序列号
            $sequenceStr = str_pad($sequence, 6, '0', STR_PAD_LEFT);
            
            // 组合单据号
            $billNo = $datePrefix . '-' . $sequenceStr;
            
            return $billNo;
        } catch (Exception $e) {
            // 如果出现异常，使用时间戳作为备用方案
            $timestamp = date('His');
            return $prefix . $year . $month . $day . $timestamp;
        }
    }

    /**
     * 获取采购付款单信息
     * @param array $param 请求参数
     * @return array
     */
    public function getPaymentInfo($param)
    {
        try {
            #数据验证
            $validate = Validate::rule([
                'id|采购单ID' => 'require',
            ]);
            if (!$validate->check($param)) {
                throw new Exception($validate->getError());
            }

            $billInfo = $this->queryPaymentInfo($param);

            return $this->success([
                'bill_info' => $billInfo
            ]);
        } catch (\Exception $e) {
            Log::error('获取采购付款单信息失败: ' . $e->getMessage());
            return $this->failed('获取采购付款单信息失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
    }

    /**
     * 查询采购付款单信息
     * @param array $param 请求参数
     * @return array
     */
    public function queryPaymentInfo($param)
    {
        // 获取采购单信息
        $purchaseOrder = Db::name('purchase_orderno')->where('id', $param['id'])->find();
        if (!$purchaseOrder) {
            throw new Exception('采购单不存在');
        }

        // 获取采购单明细
        $items = Db::name('purchase_orderno_items')->where('purchase_orderno_id', $param['id'])->select()->toArray();
        if (empty($items)) {
            throw new Exception('采购单明细不存在');
        }

        // 获取供应商信息
        $supplierInfo = Db::table('vh_supplychain.vh_partner_entity')
            ->alias('p')
            ->leftJoin('vh_supplychain.vh_partner_entity_bank b', 'b.peid=p.id')
            ->where('p.code', $purchaseOrder['supplier_code'])
            ->field('p.id,b.bank_name,b.bank_no')
            ->order('b.default desc,b.id desc')
            ->find();

        // 获取产品信息
        $shortCodes = array_column($items, 'short_code');
        $products = Db::table('vh_wiki.vh_products')
            ->whereIn('short_code', $shortCodes)
            ->column('cn_product_name', 'short_code');

        $corp_name = Db::table('vh_push_t_plus.vh_corp_map')
            ->where('erp_code', $purchaseOrder['corp_code'])
            ->value('corp_name');

        // 构建单据信息
        $billInfo = [
            'id' => $purchaseOrder['id'],
            'pre_payment_flag' => '否',
            'company' => $corp_name,
            'bill_type' => '采购付款单',
            'bill_no' => str_replace(['PO', '-'], ['FK', ''], $purchaseOrder['orderno']),
            'bill_date' => date('Y-m-d', $purchaseOrder['bill_date']),
            'supplier' => $purchaseOrder['supplier'],
            'department' => $purchaseOrder['department'],
            'salesman' => $purchaseOrder['operator_name'],
            'payment_bank_account' => '2201021509200628201',
            'payment_bank_name' => '中国工商银行股份有限公司海南省分行营业部',
            'receipt_bank_account' => $supplierInfo['bank_no'] ?? '',
            'receipt_bank_name' => $supplierInfo['bank_name'] ?? '',
            'settlement_method' => $purchaseOrder['setttlement'],
            'currency' => '人民币',
            'original_amount' => array_sum(array_column($items, 'total')),
            'remark' => $purchaseOrder['remark'],
            'sp_no' => $purchaseOrder['sp_no'],
            'items' => [],
        ];

        // 构建单据明细
        foreach ($items as $item) {
            $billInfo['items'][] = [
                'id' => $item['id'],
                'order_no' => $purchaseOrder['orderno'],
                'short_code' => $item['short_code'],
                'cn_product_name' => $products[$item['short_code']] ?? '',
                'period' => $item['period'],
                'original_amount' => $item['total'],
                'remark' => $item['remark'],
                'number' => intval($item['number']),
            ];
        }

        return $billInfo;
    }

    /**
     * 发起【采购付款单】企微审批
     * @param array $param 请求参数
     * @return array
     */
    public function sendFkApproval($param)
    {
        try {
            Db::startTrans();
            #数据验证
            $validate = Validate::rule([
                'id|采购单ID' => 'require',
                'receipt_bank_name|收款银行名称' => 'require',
                'receipt_bank_account|收款银行账号' => 'require',
                'items|付款单明细' => 'require|array',
            ]);
            if (!$validate->check($param)) {
                throw new Exception($validate->getError());
            }
            $items_ids = [];
            foreach ($param['items'] as $item) {
                #数据验证
                $validate = Validate::rule([
                    'id|明细ID' => 'require',
                    'order_no|订单号' => 'require',
                    'short_code|简码' => 'require',
                    'cn_product_name|中文品名' => 'require',
                    'period|期数' => 'require',
                    'original_amount|原币金额' => 'require|float',
                    'number|数量' => 'require|number|>:0',
                ]);
                if (!$validate->check($item)) {
                    throw new Exception($validate->getError());
                }
                $items_ids[] = $item['id'];
            }
            $purchase_orderno_items = Db::name('purchase_orderno_items')->whereIn('id', $items_ids)->column("id,number,total", 'id');
            if (empty($purchase_orderno_items)) {
                throw new Exception('采购单明细不存在');
            }

            // 获取采购单信息
            $purchaseOrder = Db::name('purchase_orderno')->where('id', $param['id'])->find();
            if (empty($purchaseOrder)) {
                throw new Exception('采购单不存在');
            }

            // 查询审批状态
            if (!empty($purchaseOrder['sp_no'])) {
                $res = curlRequest(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/approval/detail', ['sp_no' => $purchaseOrder['sp_no']], [], 'GET');
                $sp_result = $res['process_instance']['result'] ?? '';
                if ($sp_result == 'processing') {
                    throw new Exception('审批已提交请勿重复发起。');
                }
            }
            $wx = new WorkWeixinService();

            $res = $wx->SendDingtalkApproval($param, 2);

            // 审批发送失败
            if (empty($res) || !is_array($res)) {
                throw new \Exception(empty($res) ? '审批发送失败。' : $res);
            }
            if (!isset($res['error_code']) || intval($res['error_code']) !== 0) {
                throw new \Exception($res['error_msg'] ?? '审批发送失败。');
            }

            // 添加审批单号
            Db::name('purchase_orderno')->where('id', $param['id'])->update([
                'sp_no' => $res['sp_no'],
                'update_time' => time(),
            ]);
            Db::commit();
            return $this->success();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('发起审批失败: ' . $e->getMessage());
            return $this->failed('发起审批失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
    }

    /**
     * 采购付款单企微审批回调
     * @param array $param 请求参数
     * @return array
     */
    public function fkApprovalCallback($param)
    {
        Log::info('采购付款单企微审批回调: ' . json_encode($param));
        $time = time();
        $process_instance = $param['process_instance'] ?? [];
        $wx = new WorkWeixinService();
        $wx->uid = $process_instance['originator_userid'] ?? '';

        Db::startTrans();
        try {
            
            #数据验证
            $validate = Validate::rule([
                'status|审批状态' => 'require',
                'result|审批结果' => 'require',
                'spNodeApprovers|节点审批人' => 'require|array',
                'form_component_values|表单数据' => 'require|array',
            ]);
            if (!$validate->check($process_instance)) {
                throw new Exception($validate->getError());
            }
            
            $form_component_values = $process_instance['form_component_values'];
            $order_no = '';
            $fk_order_no = '';
            $remark = '';
            $bill_date = '';
            $amount = '0';
            $salesman = '';
            $supplier_account = $supplier_bank = $payment_bank = $payment_account = $pre_payment_flag = '';
            $items = [];
            foreach ($form_component_values as $v) {
                switch ($v['name']) {
                    case '付款单号':
                        $fk_order_no = $v['value'];
                        break;
                        
                    case '明细':
                        $rowitem = [];
                        foreach ($v['value'] as $m) {
                            foreach ($m['rowValue'] as $r) {
                                if ($r['label'] == '订单号') {
                                    $order_no = $r['value'];
                                }
                                $rowitem[$r['label']] = $r['value'];
                            }
                            $items[] = $rowitem;
                        }
                        break;
                        
                    case '备注':
                        $remark = $v['value'];
                        break;
                        
                    case '单据日期':
                        $bill_date = $v['value'];
                        break;

                    case '是否预付款':
                        $pre_payment_flag = $v['value'];
                        break;
                        
                    case '金额':
                        $amount = $v['value'];
                        break;
                        
                    case '业务员':
                        $salesman = $v['value'];
                        break;
                        
                    case '收款银行':
                        $supplier_bank = $v['value'];
                        break;
                        
                    case '收款账号':
                        $supplier_account = $v['value'];
                        break;
                        
                    case '付款银行':
                        $payment_bank = $v['value'];
                        break;
                        
                    case '付款账号':
                        $payment_account = $v['value'];
                        break;
                }
            }

            if ($process_instance['result'] != 'processing') {
                Db::commit();
                return $this->success();
            }

            // 获取采购单信息
            $info = $this->model->with(['items'])->where('orderno', $order_no)->find();

            // 指定审批人余海莉
            $approvers =  'haley.l';
            $access_token = '230c8f95-a714-4b2f-9540-b978050aa29d';
            if (!empty(env('APP_DEBUG'))) {
                $approvers =  'XuanYiChang';
                $access_token = '3ba990b1-9a7b-425e-b1ae-c6f363c024ed';
            }
            $current_result = $process_instance['current_result'] - 1;
            
            // 节点审批人
            $NodeApprovers = $process_instance['spNodeApprovers'][$current_result] ?? '';
            if (strpos($NodeApprovers, $approvers) === false) {
                Db::commit();
                return $this->success();
            }

            // 上级审批人
            $p_approvers = str_replace('/', '', $process_instance['spNodeApprovers'][$current_result - 1] ?? '');
            $p_approvers_info = Db::table('vh_authority.vh_admins')
                ->where('userid', $p_approvers)
                ->find();

            $content = '付款订单：'.$fk_order_no.' 已到余海莉，请尽快将付款打印提交至财务！';
            SendWeChatRobot($content, $access_token);

            if (empty($order_no)) {
                throw new Exception($fk_order_no . '采购付款单已到余海莉，未获取到采购单号，同步中台失败。');
            }

            // 获取采购单信息
            // $purchaseOrder = Db::name('purchase_orderno')->where('orderno', $order_no)->find();
            if (empty($info)) {
                throw new Exception($fk_order_no . '采购付款单已到余海莉，采购单不存在，同步中台失败。');
            }
            // 采购单审批流程
            $purchaseOrderFlow = Db::name('purchase_orderno_approve')
                ->where('purchase_orderno_id', $info['id'])
                ->order('id', 'asc')
                ->select()->toArray();

            /*创建采购付款单*/
            $exist = Db::table('vh_push_t_plus.vh_payment_order')
                ->where('code', $fk_order_no)
                ->value('id');
            if (!empty($exist)) {
                throw new Exception($fk_order_no . '采购付款单已到余海莉，采购付款单已存在，同步中台失败。');
            }

            // 保存主表数据
            $mainData = [
                'code' => $fk_order_no,
                'bill_date' => strtotime($bill_date),
                'corp' => '032',
                'status' => 2,
                'amount' => $amount,
                'local_amount' => $amount,
                'remark' => $remark,
                'creator' => $salesman,
                'auditor' => $p_approvers_info['realname'] ?? '',
                'is_prepay' => $pre_payment_flag == '预付款' ? 1 : 0,
                'created_at' => $time,
                'updated_at' => $time
            ];
            // 插入主表
            $orderId = Db::table('vh_push_t_plus.vh_payment_order')->insertGetId($mainData);

            $amount = '0';
            foreach ($items as $item) {
                $amount = bcadd($amount, strval($item['金额']), 2);
            }

            // 保存明细表数据
            $detailData = [
                'order_id' => $orderId,
                'po_code' => $info['orderno'],
                'applicant' => $info['operator_name'],
                'apply_date' => strtotime($info['bill_date']),
                'currency' => '人民币',
                'nprepaymny' => '0',
                'amount' => $amount,
                'local_amount' => $amount,
                'supplier_code' => $info['supplier_code'],
                'supplier_name' => $info['supplier'],
                'supplier_account' => $supplier_account,
                'supplier_bank' => $supplier_bank,
                'supplier_contact' => '',
                'payment_bank' => $payment_bank,
                'payment_account' => $payment_account,
                'summary' => $info['remark'],
                'verify_bill_date' => 0,
                'verify_bill_code' => '',
                'verify_amount' => 0,
                'verify_local_amount' => 0,
                'business_type' => '地采采购',
                'department' => $info['department'],
                'created_at' => $time,
                'updated_at' => $time
            ];
            // 插入明细表
            $detail_id = Db::table('vh_push_t_plus.vh_payment_order_detail')->insertGetId($detailData);

            $items_data = [];
            foreach ($items as $item) {
                $items_data[] = [
                    'detail_id' => $detail_id,
                    'order_no' => $item['订单号'] ?? '',
                    'short_code' => $item['简码'] ?? '',
                    'cn_product_name' => $item['中文名'] ?? '',
                    'period' => $item['期数'] ?? 0,
                    'original_amount' => $item['金额'] ?? 0,
                    'remark' => $item['备注'] ?? '',
                    'number' => $item['数量'] ?? 0,
                ];
            }
            Db::table('vh_push_t_plus.vh_payment_order_items')->insertAll($items_data);
            
            $approvalData = [];
            foreach ($purchaseOrderFlow as $note) {
                switch ($note['status']) {
                    case 1:
                        $approve_result = 'Y';
                        break;
                    case 2:
                        $approve_result = 'R';
                        break;
                    default:
                        $approve_result = '';
                        break;
                }
                $approvalData[] = [
                    'order_id' => $orderId,
                    'submitter' => $note['sender'],
                    'submit_date' => $note['send_time'],
                    'approver' => $note['reviewer'],
                    'approve_result' => $approve_result,
                    'approve_date' => $note['reviewe_time'],
                    'approve_note' => $note['comments'],
                    'business_type' => '收付通用流程',
                    'created_at' => $time,
                    'updated_at' => $time
                ];
            }
            // 插入审批流程表
            Db::table('vh_push_t_plus.vh_payment_order_approval')->insertAll($approvalData);

            Db::commit();
            return $this->success();
        } catch (\Exception $e) {
            Db::rollback();
            $msg = '采购付款单企微审批回调失败: ' . $e->getMessage();
            $wx->sendNotify($msg);
            Log::error($msg);
            return $this->failed($msg, ErrorCode::EXEC_ERROR);
        }
    }

    /**
     * @方法描述: 推送采购订单到wms
     * <AUTHOR>
     * @Date 2025/5/14
     * @param array $info 采购单信息
     * @return bool
     */
    public function pushWms($info)
    {
        $fictitious_id = intval($info['warehouse_code']);

        $is_supplier_delivery = Db::name('virtual_warehouse')
            ->where('virtual_id', $fictitious_id)
            ->value('is_supplier_delivery');
        if (!empty($is_supplier_delivery) && $is_supplier_delivery == 1) {
            throw new Exception('代发仓不推送WMS。');
        }

        $short_codes = [];
        foreach ($info['items'] as $item) {
            $short_codes[] = $item['short_code'];
        }

        $products = Db::table('vh_wiki.vh_products')
            ->whereIn('short_code', $short_codes)
            ->column('short_code,cn_product_name,en_product_name,bar_code,grape_picking_years,capacity', 'short_code');

        $goodsArr = [];
        foreach ($info['items'] as $item) {
            //一花一世界采购订单 - 支持负数，负数部分不进萌牙:https://devops.aliyun.com/projex/project/8ab3146db7bf4a79876e3a5333/task#viewIdentifier=7f5f68b23b9da320b7b70a1f3b&openWorkitemIdentifier=f5a0ec41d4773dbd0415b6d7ac
            $number = intval($item['number']);
            if ($number <= 0) {
                continue;
            }
            $goods = $products[$item['short_code']] ?? [];
            $goodsArr[] = [
                'goods_name' => $goods['cn_product_name'],
                'en_goods_name' => $goods['en_product_name'],
                'bar_code' => $goods['bar_code'],
                'short_code' => $goods['short_code'],
                'goods_years' => $goods['grape_picking_years'],
                'capacity' => $goods['capacity'],
                'price' => $item['price'],
                'tax_price' => $item['total'],
                'number' => $number,
            ];
        }
        
        if (empty($goodsArr)) {
            return false;
        }

        // 查询供应商采购执行人
        $purchase_exec = Db::table('vh_supplychain.vh_partner_entity')
            ->alias('pe')
            ->leftJoin('vh_supplychain.vh_staff s', 's.id = pe.purchase_exec_id')
            ->where('pe.code', $info['supplier_code'])
            ->value('s.realname');

        $body = [
            'rd_code' => $info['orderno'],
            'store_code' => 'xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa',
            'fictitious_id' => intval($info['warehouse_code']),
            'corp' => payeeMerchantIdCodeExchange($info['corp_code'], 1),
            'applicant' => $info['operator_name'],
            'purchase_exec' => !empty($purchase_exec) ? $purchase_exec : '',
            'order_type' => 1,
            'apply_time' => date('Y-m-d', strtotime($info['created_time'])),
            'remake' => $info['remark'],
            'supplier' => $info['supplier'],
            'goods_arr' => $goodsArr
        ];
        \Curl::pushWms($body);
    }

    /**
     * @方法描述: 手动推送采购订单到wms
     * <AUTHOR>
     * @Date 2025/5/14
     * @param array $param 请求参数
     * @return bool
     */
    public function pushPurchaseOrderToWMS($param)
    {
        try {
            #数据验证
            $validate = Validate::rule([
                'id|采购单ID' => 'require',
            ]);
            if (!$validate->check($param)) {
                throw new Exception($validate->getError());
            }
            $info = $this->model->with(['items'])->where('id', $param['id'])->find();
            if (empty($info)) {
                throw new Exception('采购单信息获取失败。');
            }
            if ($info['operate_status'] != 1) {
                throw new Exception('采购单未审核完成不能推送到WMS。');
            }
            $this->pushWms($info);

        } catch (\Exception $e) {
            return $this->failed($e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        return $this->success();
    }
}
