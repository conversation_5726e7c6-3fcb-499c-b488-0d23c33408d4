<?php

namespace app\service\v3;

use app\BaseService2;
use app\controller\Other;
use app\controller\Periods;
use app\ErrorCode;
use app\model\InterfaceCallLog;
use app\model\PeriodsPool;
use app\model\PeriodsProductEvaluate;
use app\model\PeriodsProductInventory;
use app\service\ElasticSearch;
use app\service\es\Es;
use app\service\Package;
use app\validate\PeriodsPoolValidate;
use Cassandra\Uuid;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use think\Exception;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Env;
use think\facade\Log;

/**
 * 产品池主表
 * Class PeriodsPoolService
 * @package app\service\v3
 */
class PeriodsPoolService extends BaseService2
{

    const DELAYED             = 259200; //过期时间(秒)  259200 todo .. 60
    const STATUS_NOTSTARTED   = 'NotStarted'; //未开始
    const STATUS_PROCESSING   = 'Processing'; //处理中
    const STATUS_FINISHED     = 'Finished'; //办理完成
    const STATUS_APPROVED     = 'Approved'; //审核通过
    const STATUS_DENIED       = 'Denied'; //审核驳回
    const FLOW_UI_PARTICIPANT = 'flow_ui_participant'; //流程自动推荐分配UI参与人 redis_key
    const ROLE_ID             = 67;  // todo role_id 需要龙飞配置后给出 测试 40 正式 67

    protected $status_text   = ['3' => '通过', '4' => '驳回', '5' => '延期'];
    protected $status_values = ['3' => self::STATUS_APPROVED, '4' => self::STATUS_DENIED];
    protected $role_id = null;
    protected $field_ctl_role_id = null;

    public function __construct()
    {
        $this->model       = Db::connect('mongodb')->name('periods_pool');
        $this->validate    = PeriodsPoolValidate::class;
        $this->select_with = [];
        $this->role_id = env('FLOW_SERVICE.ROLE_ID',  self::ROLE_ID);
        $this->field_ctl_role_id = explode(',', env('FLOW_SERVICE.FIELD_CTL_ROLE_ID',  '2,78,48,14'));
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/07/19 09:25
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id 期数
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //期数
            }
            #endregion

            #region EQ periods_type 频道
            if (isset($param['periods_type']) && strlen($param['periods_type']) > 0) {
                $query->where('periods_type', "=", $param['periods_type']); //频道
            }
            #endregion

            #region IN import_type 进口类型
            if (isset($param['import_type']) && strlen($param['import_type']) > 0) {
                $query->where('import_type', "IN", $param['import_type']); //进口类型:0=自采,1=地采,2=跨境
            }
            #endregion

            #region EQ payee_merchant_id 收款商户id
            if (isset($param['payee_merchant_id']) && strlen($param['payee_merchant_id']) > 0) {
                $query->where('payee_merchant_id', "=", $param['payee_merchant_id']); //收款商户id
            }
            #endregion

            #region LIKE payee_merchant_name 收款商户名称
            if (isset($param['payee_merchant_name']) && strlen($param['payee_merchant_name']) > 0) {
                $query->where('payee_merchant_name', "LIKE", "{$param['payee_merchant_name']}%"); //收款商户名称
            }
            #endregion

            #region IN is_gift_box 礼盒发货
            if (isset($param['is_gift_box']) && strlen($param['is_gift_box']) > 0) {
                $query->where('is_gift_box', "IN", $param['is_gift_box']); //礼盒发货:0=不带,1=代
            }
            #endregion

            #region IN is_supplier_delivery 是否代发
            if (isset($param['is_supplier_delivery']) && strlen($param['is_supplier_delivery']) > 0) {
                $query->where('is_supplier_delivery', "IN", $param['is_supplier_delivery']); //是否代发:0=否,1=是
            }
            #endregion

            #region IN is_presell 是否预售
            if (isset($param['is_presell']) && strlen($param['is_presell']) > 0) {
                $query->where('is_presell', "IN", $param['is_presell']); //是否预售:0=否,1=是
            }
            #endregion

            #region IN shipping_conditions 发货条件
            if (isset($param['shipping_conditions']) && strlen($param['shipping_conditions']) > 0) {
                $query->where('shipping_conditions', "IN", $param['shipping_conditions']); //发货条件:0=常温,1=冷链
            }
            #endregion

            #region IN storage_conditions 存储条件
            if (isset($param['storage_conditions']) && strlen($param['storage_conditions']) > 0) {
                $query->where('storage_conditions', "IN", $param['storage_conditions']); //存储条件:0=常温,1=冰冻
            }
            #endregion

            #region IN delivery_time_limit 发货时效
            if (isset($param['delivery_time_limit']) && strlen($param['delivery_time_limit']) > 0) {
                $query->where('delivery_time_limit', "IN", $param['delivery_time_limit']); //发货时效:0=24小时,1=72小时,2=72小时以上
            }
            #endregion

            #region LIKE purchase_remake 采购备注
            if (isset($param['purchase_remake']) && strlen($param['purchase_remake']) > 0) {
                $query->where('purchase_remake', "LIKE", "{$param['purchase_remake']}%"); //采购备注
            }
            #endregion

            #region LIKE selling_point 卖点介绍
            if (isset($param['selling_point']) && strlen($param['selling_point']) > 0) {
                $query->where('selling_point', "LIKE", "{$param['selling_point']}%"); //卖点介绍
            }
            #endregion

            #region IN demand_grade 写作难易程度
            if (isset($param['demand_grade']) && strlen($param['demand_grade']) > 0) {
                $query->where('demand_grade', "IN", $param['demand_grade']); //写作难易程度:1=低,2=中,3=高
            }
            #endregion

            #region LIKE status 当前状态
            if (isset($param['status']) && strlen($param['status']) > 0) {
                $query->where('status', "LIKE", "{$param['status']}%"); //当前状态
            }
            #endregion

            #region >= start_delay_time 开始运营审核截止时间
            if (isset($param['start_delay_time']) && strlen($param['start_delay_time']) > 0) {
                $query->whereTime('delay_time', '>=', $param['start_delay_time']); //开始运营审核截止时间
            }
            #endregion

            #region < end_delay_time 结束运营审核截止时间
            if (isset($param['end_delay_time']) && strlen($param['end_delay_time']) > 0) {
                $query->whereTime('delay_time', '<', $param['end_delay_time']); //结束运营审核截止时间
            }
            #endregion

            #region EQ copy_periods_id 复制的期数id:0=未复制
            if (isset($param['copy_periods_id']) && strlen($param['copy_periods_id']) > 0) {
                $query->where('copy_periods_id', "=", $param['copy_periods_id']); //复制的期数id:0=未复制
            }
            #endregion

            #region IN is_design 是否需要设计
            if (isset($param['is_design']) && strlen($param['is_design']) > 0) {
                $query->where('is_design', "IN", $param['is_design']); //是否需要设计:0=否,1=是
            }
            #endregion

            #region LIKE demand 文案需求
            if (isset($param['demand']) && strlen($param['demand']) > 0) {
                $query->where('demand', "LIKE", "{$param['demand']}%"); //文案需求
            }
            #endregion

            #region >= start_expect_time 开始期望完成时间
            if (isset($param['start_expect_time']) && strlen($param['start_expect_time']) > 0) {
                $query->whereTime('expect_time', '>=', $param['start_expect_time']); //开始期望完成时间
            }
            #endregion

            #region < end_expect_time 结束期望完成时间
            if (isset($param['end_expect_time']) && strlen($param['end_expect_time']) > 0) {
                $query->whereTime('expect_time', '<', $param['end_expect_time']); //结束期望完成时间
            }
            #endregion

            #region LIKE title 期数标题（名称）
            if (isset($param['title']) && strlen($param['title']) > 0) {
                $query->where('title', "LIKE", "{$param['title']}%"); //期数标题（名称）
            }
            #endregion

            #region LIKE brief 一句话介绍（副标题）
            if (isset($param['brief']) && strlen($param['brief']) > 0) {
                $query->where('brief', "LIKE", "{$param['brief']}%"); //一句话介绍（副标题）
            }
            #endregion

            #region LIKE banner_img 题图
            if (isset($param['banner_img']) && strlen($param['banner_img']) > 0) {
                $query->where('banner_img', "LIKE", "{$param['banner_img']}%"); //题图
            }
            #endregion

            #region LIKE video 视频
            if (isset($param['video']) && strlen($param['video']) > 0) {
                $query->where('video', "LIKE", "{$param['video']}%"); //视频
            }
            #endregion

            #region LIKE video_cover 视频封面
            if (isset($param['video_cover']) && strlen($param['video_cover']) > 0) {
                $query->where('video_cover', "LIKE", "{$param['video_cover']}%"); //视频封面
            }
            #endregion

            #region EQ purchase_uid 采购人uid
            if (isset($param['purchase_uid']) && strlen($param['purchase_uid']) > 0) {
                $query->where('purchase_uid', "=", $param['purchase_uid']); //采购人uid
            }
            #endregion

            #region LIKE purchase_name 采购人
            if (isset($param['purchase_name']) && strlen($param['purchase_name']) > 0) {
                $query->where('purchase_name', "LIKE", "{$param['purchase_name']}%"); //采购人
            }
            #endregion

            #region EQ creator_uid 文案uid
            if (isset($param['creator_uid']) && strlen($param['creator_uid']) > 0) {
                $query->where('creator_uid', "=", $param['creator_uid']); //文案uid
            }
            #endregion

            #region LIKE creator_name 文案
            if (isset($param['creator_name']) && strlen($param['creator_name']) > 0) {
                $query->where('creator_name', "LIKE", "{$param['creator_name']}%"); //文案
            }
            #endregion

            #region LIKE design_title 设计酒款名
            if (isset($param['design_title']) && strlen($param['design_title']) > 0) {
                $query->where('design_title', "LIKE", "{$param['design_title']}%"); //设计酒款名
            }
            #endregion

            #region LIKE design_cn_highlights 中文亮点
            if (isset($param['design_cn_highlights']) && strlen($param['design_cn_highlights']) > 0) {
                $query->where('design_cn_highlights', "LIKE", "{$param['design_cn_highlights']}%"); //中文亮点
            }
            #endregion

            #region LIKE design_score 评分
            if (isset($param['design_score']) && strlen($param['design_score']) > 0) {
                $query->where('design_score', "LIKE", "{$param['design_score']}%"); //评分
            }
            #endregion

            #region LIKE design_style 设计风格
            if (isset($param['design_style']) && strlen($param['design_style']) > 0) {
                $query->where('design_style', "LIKE", "{$param['design_style']}%"); //设计风格
            }
            #endregion

            #region LIKE design_website 官网
            if (isset($param['design_website']) && strlen($param['design_website']) > 0) {
                $query->where('design_website', "LIKE", "{$param['design_website']}%"); //官网
            }
            #endregion

            #region IN is_postpone 是否延期
            if (isset($param['is_postpone']) && strlen($param['is_postpone']) > 0) {
                $query->where('is_postpone', "IN", $param['is_postpone']); //是否延期:0=否,1=是
            }
            #endregion

        };
    }

    /**
     * @方法描述: 根据简码查询期数
     * <AUTHOR>
     * @Date 2023/7/21 13:24
     * @param $param
     * @return mixed|true
     */
    public function shortCodesList($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        #region where查询条件,分页,查询数据的处理
        $short_code = array_values(array_unique(explode(',', $param['short_code'])));
        $where      = [['short_code', 'in', $short_code]];

        $limit     = $param['limit'] ?? 10;
        $page      = $param['page'] ?? 1;
        $pagestart = ($page - 1) * $limit;
        $field     = $param['fields'] ?? "*";
        //endregion

        //region 分页获取数据
        Db::startTrans();
        try {
            $list  = Es::name(Es::PERIODS)->where($where)->field($field)->limit($pagestart, $limit)->select()->toArray();
            $total = Es::name(Es::PERIODS)->where($where)->count();
            $data  = [
                'list'  => $list,
                'total' => $total
            ];

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('查询数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('查询数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($this->listDataProcessing($data));
    }

    /**
     * @方法描述: 开售
     * <AUTHOR>
     * @Date 2023/7/19 15:25
     * @param $param
     * @return mixed|true
     */
    public function onSale($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        foreach ($param['products'] as $productValidate) {
            $validate = $this->check(__FUNCTION__ . 'Products', $productValidate);
            if ($validate !== true) {
                return $validate;
            }
        }
        #endregion

        $vh_user = \Curl::adminInfo([
            'admin_id' => $param['vh_uid'],
            'field'    => 'id,realname,userid,status,roles'
        ])[$param['vh_uid']] ?? null;
        if (!$vh_user) throw new Exception("未找到管理员");
        $periods_types       = [
            '0' => '闪购',
            '1' => '秒发',
            '2' => '跨境',
            '3' => '尾货',
        ];
        $vh_user['role_ids'] = implode(',', array_column($vh_user['roles'], 'id'));
        $now                 = time();

        //region 修改
        try {

            //查询简码
            $product_field = "*";
            $product_ids   = array_values(array_unique(array_column($param['products'], 'product_id')));
            $products      = Es::name(Es::PRODUCTS)->where([['id', 'in', $product_ids]])->field($product_field)->select()->toArray();
            $products      = array_column($products, null, 'id');
            //产品列表数据组装
            $items = [];
            foreach ($param['products'] as $product) {
                $product_info                 = $products[$product['product_id']];
                $product['warehouse']         = $product_info['warehouse'] ?? null;//仓库
                $product['warehouse_id']      = $product_info['warehouse_id'] ?? null;//仓库ID
                $product['product_name']      = $product['cn_product_name'] = $product_info['cn_product_name'] ?? null;//中文名
                $product['en_product_name']   = $product_info['en_product_name'] ?? null;//英文名
                $product['short_code']        = $product_info['short_code'] ?? null;//短码
                $product['product_category']  = $product_info['product_category'] ?? null;//大类ID
                $product['product_type_name'] = $product_info['product_type_name'] ?? null;//小类中文
                $product['country']           = $product_info['country_name_cn'] ?? null;//国家
                $product['capacity']          = $product_info['capacity'] ?? null;//规格
                $product['costprice']         = null;//成本价
                $product['price']             = null;//售价
                $product['num']               = null;//售卖数量
                $product['actual_num']        = null;//实际库存
                $items[]                      = $product;
            }

            //期数ID
            $id = getGeneratorID(1);
            if (!$id) throw new Exception('生成期数ID失败');

            //日志
            $logs = [
                [
                    'uid'         => $param['vh_uid'], //操作人ID
                    'name'        => $param['vh_vos_name'], //操作人姓名
                    'create_time' => $now, //记录时间
                    'operation'   => '开售', //事项
                    'remarks'     => '', //事项
                ]
            ];

            //采购状态
            $purchase = [
                'status'        => '0', //办理状态: 0=办理中,1=已完成
                'purchase_uid'  => $vh_user['id'], //采购人UID
                'purchase_name' => $vh_user['realname'], //采购人姓名
            ];

            $res = \Curl::productPoolCreateApproval([
                'code'            => strval($id),
                'creator_process' => [
                    'genre'     => 1,//写死1
                    "role_id"   => strval($vh_user['roles'][0]['id'] ?? ''),
                    'name'      => strval($vh_user['realname']),
                    'we_com_id' => strval($vh_user['userid']),
                    'vos_uid'   => intval($vh_user['id']),
                ],
            ]);

            $flow_instance_status = \Curl::getFlowInstanceStatus(['code' => strval($id),]) ?? null;
            $data                 = compact('id', 'logs', 'items', 'purchase');
//            $cur                              = array_values($flow_instance_status)[0]; //todo 临时恢复
//            $data['current_node_id']          = $cur['id'];
//            $data['current_node_name']        = $cur['name'];
//            $data['current_node_customer_id'] = $cur['customer_id'];
//            $data['current_node_info']        = $cur;
            $data['current_node_info'] = array_values($flow_instance_status);

            $this->model->save($data);
        } catch (\Exception $e) {
            Log::error('更新数据失败: ' . $e->getMessage());
            return $this->failed('更新数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success();
    }

    /**
     * @方法描述: 列表
     * <AUTHOR>
     * @Date 2023/7/19 16:05
     * @param $param
     * @return array|bool|mixed
     */
    public function list($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        #region where查询条件,分页,查询数据的处理
        $limit     = $param['limit'] ?? 10;
        $page      = $param['page'] ?? 1;
        $pagestart = ($page - 1) * $limit;
        $field     = $param['fields'] ?? "*";
        $vh_user     = \Curl::adminInfo([
            'admin_id' => $param['vh_uid'],
            'field'    => 'id,realname,userid,status,roles'
        ])[$param['vh_uid']] ?? null;
        if (!$vh_user) throw new Exception("未找到管理员");
        $vh_user['role_ids_arr'] = array_column($vh_user['roles'], 'id');
        $vh_user['role_ids']     = implode(',', $vh_user['role_ids_arr']);
        //endregion

        //region 分页获取数据
        Db::startTrans();
        try {
            $model = $this->model;

            #region where 条件筛选

            #region EQ id 期数
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $model->where('id', intval(trim($param['id']))); //期数
            }
            #endregion

            #region EQ sku_id
            if (isset($param['sku_id']) && strlen($param['sku_id']) > 0) {
                $model->where('items.sku_id', intval(trim($param['sku_id'])));
            }
            #endregion

            #region EQ short_code
            if (isset($param['short_code']) && strlen($param['short_code']) > 0) {
                $model->where('items.short_code', strval(trim($param['short_code'])));
            }
            #endregion

            #region LIKE product_name
            if (isset($param['product_name']) && strlen($param['product_name']) > 0) {
                $model->where('items.product_name|items.en_product_name', "LIKE", trim($param['product_name']));
            }
            #endregion

            #region LIKE periods_types
            if (isset($param['periods_types']) && strlen($param['periods_types']) > 0) {
                $p_periods_type = explode(',', trim($param['periods_types']));
                foreach ($p_periods_type as &$p_i) {
                    $p_i = intval($p_i);
                }
                $model->where('purchase.periods_type', 'in', $p_periods_type);
            }
            #endregion

            #region LIKE purchase_uid
            if (isset($param['purchase_uid']) && strlen($param['purchase_uid']) > 0) {
                $model->where('purchase.purchase_uid', '=', intval(trim($param['purchase_uid'])));
            }
            #endregion

            #region LIKE creator_uid
            if (isset($param['creator_uid']) && strlen($param['creator_uid']) > 0) {
                $model->where('document_distribution.creator_uid', '=', intval(trim($param['creator_uid'])));
            }
            #endregion

            #region Other transaction_state

            //默认查询全部自己的
            //搜索全部查询全部
            //搜索其他的查询其他的

            $transaction_states = explode(',', trim($param['transaction_state'] ?? ''));
            $is_all             = in_array('全部', $transaction_states);

            if (!$is_all) {
                $no_keywords = !implode(',', $transaction_states);
                $vh_user['is_operate']   = (in_array(14, $vh_user['role_ids_arr']) || in_array(48, $vh_user['role_ids_arr'])); //14=运营, 48=秒发运营

                $model->where(function ($query) use ($vh_user, $transaction_states, $no_keywords) {
                    if ($no_keywords) {
                        //查询自己待办的
                        $operate_periods    = \Curl::flowProcessing([
                            "process"      => [//操作人信息
                                'genre'        => 1,//写死1
                                "role_id"      => strval($vh_user['role_ids']),
                                'name'         => strval($vh_user['realname']),
                                'we_com_id'    => strval($vh_user['userid']),
                                'vos_uid'      => intval($vh_user['id']),
                                'process_time' => time(),
                            ],
                            "customer_ids" => []
                        ])['list'] ?? []; //流程状态待办
                        $operate_period_ids = [];
                        foreach ($operate_periods as $operate_period) {
                            $operate_period_ids[] = intval($operate_period['master_instance_id']);
                        }
                        $query->whereOr('id', 'in', $operate_period_ids);

                        if ($vh_user['is_operate']) {
                            $query->whereOr('operation.status', 'in', ['运营确认上架中']);
                        }

                        $query->whereOr(function ($query) use ($vh_user) {
                            $query->where('operation.status', 'in', ['运营未排期', '运营延期']);
                            $query->where('purchase.purchase_uid', '=', $vh_user['id']);
                        });
                    } else {
                        $query->where('current_node_info.customer_id|operation.status', 'in', $transaction_states);
                    }
                });
            }
            #endregion


            #endregion

            $model2      = clone $model;
            $origin_list = $model->limit($pagestart, $limit)->order(['id' => -1])->hidden(['logs'])->select();
            $total       = $model2->count();

            $list            = [];
            $all_product_ids = [];
            foreach ($origin_list as $var) {
                $all_product_ids = array_merge($all_product_ids, array_column($var['items'], 'product_id'));
            }
            $wiki_products = Es::name(Es::PRODUCTS)->where([
                ['id', 'in', array_values(array_unique($all_product_ids))]
            ])->field('id,bar_code,produce_date,shelf_life,product_attachment,producing_area_name')->limit(0, 10)->select()->toArray();
            $wiki_products = array_column($wiki_products, null, 'id');

            foreach ($origin_list as $item) {
                unset($item['logs']);
                $products = $item['items'];
                foreach ($products as &$product) {
                    $wiki_product                   = $wiki_products[$product['product_id']] ?? [];
                    $product['produce_date']        = $wiki_product['produce_date'] ?? ''; //生产日期
                    $product['shelf_life']          = $wiki_product['shelf_life'] ?? ''; //保质期
                    $product['has_attachment']      = empty($wiki_product['product_attachment']) ? 0 : 1; //附件(关单 卫检): 0=未上传,1=已上传
                    $product['bar_code']            = $wiki_product['bar_code'] ?? ''; //条码
                    $product['producing_area_name'] = $wiki_product['producing_area_name'] ?? ''; //产区
                }
                $item['items']                                                 = $products;
                $item['purchase']['expect_time_text']                          = empty($item['purchase']['expect_time']) ? '' : Date('Y-m-d H:i:s', $item['purchase']['expect_time']);
                $item['document_distribution']['planned_completion_time_text'] = empty($item['document_distribution']['planned_completion_time']) ? '' : Date('Y-m-d H:i:s', $item['document_distribution']['planned_completion_time']);
                $item['audit']['purchase']['expect_time_text']                 = empty($item['audit']['purchase']['expect_time']) ? '' : Date('Y-m-d H:i:s', $item['audit']['purchase']['expect_time']);
                $item['audit']['purchase_last']['expect_time_text']            = empty($item['audit']['purchase_last']['expect_time']) ? '' : Date('Y-m-d H:i:s', $item['audit']['purchase_last']['expect_time']);
                $item['audit']['operation']['delay_time_text']                 = empty($item['audit']['operation']['delay_time']) ? '' : Date('Y-m-d H:i:s', $item['audit']['operation']['delay_time']);

                if (!empty($item['qualification'])) {
                    $item_qualifications = $item['qualification'];
                    foreach ($item_qualifications as &$item_qualification) {
                        $item_qualification['frontal_label_img']  = image_full_path($item_qualification['frontal_label_img'] ?? '');
                        $item_qualification['cn_back_label_img']  = image_full_path($item_qualification['cn_back_label_img'] ?? '');
                        $item_qualification['en_back_label_img']  = image_full_path($item_qualification['en_back_label_img'] ?? '');
                        $item_qualification['package_img']        = image_full_path($item_qualification['package_img'] ?? '');
                        $item_qualification['customs_pass']       = image_full_path($item_qualification['customs_pass'] ?? '');
                        $item_qualification['health']             = image_full_path($item_qualification['health'] ?? '');
                        $item_qualification['product_attachment'] = multiple_image_full_path($item_qualification['product_attachment'] ?? '');
                    }
                    $item['qualification'] = $item_qualifications;
                }

                if (!empty($item['editor'])) {
                    $item['editor']['banner_img']     = image_full_path($item['editor']['banner_img'] ?? '');
                    $item['editor']['product_img']    = implode(',', multiple_image_full_path(
                        empty($item['editor']['product_img']) ? '' : implode(',', $item['editor']['product_img'])
                    ));
                    $item['editor']['video_cover']    = image_full_path($item['editor']['video_cover'] ?? '');
                    $item['editor']['horizontal_img'] = image_full_path($item['editor']['horizontal_img'] ?? '');
                }

                if (!empty($item['picture_distribution'])) {
//                    $item['picture_distribution']['attachment'] = image_full_path($item['picture_distribution']['attachment'] ?? '');
                    $item['picture_distribution']['material']     = multiple_image_full_path($item['picture_distribution']['material'] ?? '');
                    $item['picture_distribution']['banner_img']     = image_full_path($item['picture_distribution']['banner_img'] ?? '');
                    $item['picture_distribution']['horizontal_img'] = image_full_path($item['picture_distribution']['horizontal_img'] ?? '');
                    $item['picture_distribution']['product_img']    = implode(',', multiple_image_full_path(
                        empty($item['picture_distribution']['product_img']) ? '' : implode(',', $item['picture_distribution']['product_img'])
                    ));
                }

                $item = $this->showField($vh_user,$item);

                $list[] = $item;
            }

            $data = [
                'list'  => $list,
                'total' => $total
            ];

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('查询数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('查询数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }

//endregion

        return $this->success($this->listDataProcessing($data));
    }

    public function showField($vh_user,$period)
    {
        $hidden_field = true;
        foreach ($this->field_ctl_role_id as $show_role_id){
            if(in_array($show_role_id, $vh_user['role_ids_arr'])){
                $hidden_field = false; ////采购\采购主管\运营 全部返回
            }
        }

        if ($hidden_field) {
            //3 67 77 不返回：进口商/收款公司/成本/小计/利润率/实际库存

            $period['purchase']['payee_merchant_id']   = ''; //收款公司ID
            $period['purchase']['payee_merchant_name'] = ''; //收款公司名称

            $items = $period['items'];
            foreach ($items as &$item) {
                $item['actual_num']  = '';
                $item['costprice']   = '';
                $item['supplier']    = '';
                $item['supplier_id'] = '';
            }
            $period['items'] = $items;

        }

        return $period;
    }
    

    /**
     * @方法描述: 操作日志
     * <AUTHOR>
     * @Date 2023/7/28 15:54
     * @param $param
     * @return mixed|true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public
    function logList($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        $period = $this->model->where('id', intval($param['id']))->field('logs')->find();
        $list   = $period['logs'] ?? [];
        foreach ($list as &$item) {
            $item['create_time_text'] = Date('Y-m-d H:i:s', $item['create_time']);
        }

        return $this->success(compact('list'));
    }

    /**
     * @方法描述: 查询期数
     * <AUTHOR>
     * @Date 2023/7/28 16:36
     * @param $param
     * @return mixed|true|void
     * @throws Exception
     */
    public
    function findPeriods($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        $period = Es::name(Es::PERIODS)->where([
            ['onsale_verify_status', '=', 1],
            ['id', '==', $param['id']],
        ])->field('id,title')->find();
        if (empty($period)) return $this->success([]);
        $packages = Es::name(Es::PERIODS_PACKAGE)->where([
            ['is_hidden', '==', 0],
            ['period_id', '==', $period['id']]
        ])->field('id,period_id,package_name,price,associated_products')->select()->toArray();

        $product_ids = [];
        foreach ($packages as $k => $package) {
            $package['associated_products'] = json_decode($package['associated_products'], true);
            foreach ($package['associated_products'] as $product) {
                if (is_array($product['product_id'])) {
                    foreach ($product['product_id'] as $i_p_pid) {
                        $product_ids[] = $i_p_pid;
                    }
                } else {
                    $product_ids[] = $product['product_id'];
                }
            }
            $packages[$k] = $package;
        }

        $wiki_products = Es::name(Es::PRODUCTS)->where([
            ['id', 'in', $product_ids]
        ])->field('id,short_code,costprice')->select()->toArray();
        $wiki_products = array_column($wiki_products, null, 'id');

        foreach ($packages as $k => $package) {
            $short_codes = [];
            $costprice   = '0';

            foreach ($package['associated_products'] as $product) {

                if (!is_array($product['product_id'])) {
                    $product['product_id'] = [$product['product_id']];
                }

                foreach ($product['product_id'] as $i_p_pid) {
                    $var_prroduct  = $wiki_products[$i_p_pid];
                    $short_codes[] = $var_prroduct['short_code'];
                    $costprice     = bcadd($costprice, bcmul($product['nums'], $var_prroduct['costprice'], 2), 2);
                }
            }

            $period['packages'][] = [
                'name'       => $package['package_name'],
                'short_code' => $short_codes,
                'price'      => $package['price'],
                'costprice'  => $costprice,
            ];
        }
        return $this->success(compact('period'));

    }


    /**
     * @方法描述: 获取期数
     * <AUTHOR>
     * @Date 2023/7/19 16:25
     * @param $param
     */
    public function getPeriod($param)
    {
        $period = $this->model->where('id', $param['id'])->find();
        if (empty($period)) throw new Exception('未找到期数  ' . $param['id']);
        return $period;
    }


    /**
     * @方法描述: 更新内容
     * <AUTHOR>
     * @Date 2023/7/27 10:52
     * @param $param
     * @return mixed|true
     * @throws Exception
     * @throws \think\db\exception\DbException
     */
    public
    function update($param)
    {
        $method = $param['method'];
        #region验证
        $validate = $this->check($method, $param);
        if ($validate !== true) {
            return $validate;
        }
        if (in_array($method, ['purchaseSubmit'])) {
            foreach ($param['items'] as $item) {
                $validate = $this->check($method . 'Items', $item);
                if ($validate !== true) {
                    return $validate;
                }
            }
        }
        #endregion

        #region 初始化变量
        $current_node_name        = $param['current_node_name'];
        $current_node_id          = $param['current_node_id'];
        $current_node_customer_id = $param['current_node_customer_id'] ?? '';
        $now                      = time();
        $period                   = $origin_period = $this->getPeriod($param);
        $vh_user                  = \Curl::adminInfo([
            'admin_id' => $param['vh_uid'],
            'field'    => 'id,realname,userid,status,roles'
        ])[$param['vh_uid']] ?? null;
        if (!$vh_user) throw new Exception("未找到管理员");
        $periods_types       = [
            '0' => '闪购',
            '1' => '秒发',
            '2' => '跨境',
            '3' => '尾货',
        ];
        $vh_user['role_ids'] = implode(',', array_column($vh_user['roles'], 'id'));
//        $flow_instance_status_list = \Curl::getFlowInstanceStatus(['code' => strval($period['id']),])['list'] ?? null;
//        if (!in_array($current_node_id, array_column($flow_instance_status_list, 'id'))) {
//        if (!in_array($current_node_id, array_keys($period['current_node_info']))) {
//            throw new Exception("不在可操作节点");
//        }
        #endregion 初始化变量


        //region 修改
        try {
            list($period, $extend) = $this->$method($period, $param, []);

            #region 记录日志
            if(empty($extend['not_remark'])){
            $period['logs'][] =
                [
                    'uid'         => $vh_user['id'], //操作人ID
                    'name'        => $vh_user['realname'], //操作人姓名
                    'create_time' => $now, //记录时间
                    'operation'   => $extend['current_node_name'] ?? $current_node_name, //事项
                    'remarks'     => $extend['remark'], //备注
                ];
            }
            #endregion 记录日志

            #region 是否更新节点
            if ($extend['node_is_done']) { //是否更新节点状态
                $res = \Curl::flowInstanceOperation([
                    "code"          => empty($param['instance_id']) ? strval($period['id']) : strval($param['instance_id']),//实例id
                    "node_id"       => strval($param['current_node_id']),//节点id
                    "process_mode"  => $extend['process_mode'] ?? 'process',//操作模式 process操作人,participant参与人
                    "process"       => [//操作人信息
                        "genre"        => 1, //用户类型(写死1)
                        "role_id"      => strval($vh_user['role_ids']),
                        'name'         => strval($vh_user['realname']),
                        'we_com_id'    => strval($vh_user['userid']),
                        'vos_uid'      => intval($vh_user['id']),
                        'process_time' => $now,
                        "status"       => strval($extend['status'])
                    ],
                    "custom_fields" => [
                        "是否复制" => empty($period['purchase']['copy_periods_id']) ? '否' : '是',
                        "频道"     => $periods_types[$period['purchase']['periods_type']],
                    ]
                ]);
            }
            #endregion 是否更新节点

            #region 前面操作可能更新了状态, 查询根辉最新的节点并存储
            $new_flow_instance_status_list = \Curl::getFlowInstanceStatus(['code' => strval($period['id']),]) ?? null;
            if ($new_flow_instance_status_list !== false) {
//                $cur                                = array_values($new_flow_instance_status_list)[0]; //todo 临时恢复
//                $period['current_node_id']          = $cur['id'] ?? null;
//                $period['current_node_name']        = $cur['name'] ?? null;
//                $period['current_node_customer_id'] = $cur['customer_id'] ?? null;
//                $period['current_node_info'] = $cur;
                $period['current_node_info'] = array_values($new_flow_instance_status_list) ?? [];

                if (empty($new_flow_instance_status_list)) { //没有课操作节点, 认为全部节点完成了
                    $task_id   = uuid();
                    $exec_time = empty($period['purchase']['expect_time']) ? strtotime('+3days') : $period['purchase']['expect_time'] + self::DELAYED; //延期三天

                    if ($exec_time <= $now) {
                        $exec_time = $now + self::DELAYED;
                    }

                    //超时运营未设置则过期
                    \Curl::secondLevelSchedulerAdd([
                        'task_id'           => $task_id,
                        'task_trigger_time' => $exec_time,
                        'task_url'          => env('ITEM.COMMODITIES_URL') . '/commodities/v3/periodsPool/operatOvertime',
                        'task_data'         => json_encode([
                            'id'     => $period['id'],
                            'status' => '运营未排期',
                        ])
                    ]);

                    $period['operation'] = [
                        'status'    => '运营确认上架中', //运营状态: 运营确认上架中,运营通过,运营未排期,运营延期
                        'task_id'   => $task_id,
                        'task_exec' => 0, //计划任务是否执行

                    ];
                    //状态置为运营确认上架中
                    //开启秒级任务,  三天后未处理过期
                }
            }

            $this->model->where('_id', $period['_id'])->update($period);
            #endregion
        } catch (\Exception $e) {
            $this->model->where('_id', $origin_period['_id'])->update($origin_period);
            Log::error('更新数据失败: ' . $e->getMessage());
            return $this->failed('更新数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($period);
    }

    /**
     * @方法描述: 提交采购信息 (新增, 复制文案)
     * <AUTHOR>
     * @Date 2023/7/27 10:52
     * @param $period
     * @param $param
     * @param $extend
     * @return array
     */
    public
    function purchaseSubmit($period, $param, $extend)
    {
        $node_is_done = 1; //节点是否完成 1 = 完成,
        $status       = self::STATUS_FINISHED; //完成状态
        $remark       = ''; //备注
        $current_node_name = '采购提交';

        #region 产品数据处理
        $new_items   = $qualification = [];
        $param_items = $param['items'];

        //查询简码
        $product_field = "id,capacity,cn_product_name,en_product_name,cn_product_name,country_name_cn,product_type_name,product_category,short_code,en_product_name,warehouse,warehouse_id,product_name,frontal_label_img,cn_back_label_img,en_back_label_img,package_img,product_attachment";
        $products      = Es::name(Es::PRODUCTS)->where([['id', 'in', array_column($param_items, 'product_id')]])->field($product_field)->select()->toArray();
        $products      = array_column($products, null, 'id');

        foreach ($param_items as $p_product) {
            $product_var  = [];
            $product_info = $products[$p_product['product_id']];

            $product_var['product_name']      = $product['cn_product_name'] = $product_info['cn_product_name'] ?? null;//中文名
            $product_var['en_product_name']   = $product_info['en_product_name'] ?? null;//英文名
            $product_var['short_code']        = $product_info['short_code'] ?? null;//短码
            $product_var['product_category']  = $product_info['product_category'] ?? null;//大类ID
            $product_var['product_type_name'] = $product_info['product_type_name'] ?? null;//小类中文
            $product_var['country']           = $product_info['country_name_cn'] ?? ($product_info['country'] ?? null);//国家
            $product_var['capacity']          = $product_info['capacity'] ?? null;//规格
            $product_var['product_id']        = $p_product['product_id']; //
            $product_var['sku_id']            = $p_product['sku_id']; //
            $product_var['supplier']          = $p_product['supplier']; //
            $product_var['supplier_id']       = $p_product['supplier_id']; //
            $product_var['costprice']         = $p_product['costprice']; //成本价
            $product_var['price']             = $p_product['price']; //售价
            $product_var['num']               = $p_product['num']; //销售数量
            $product_var['actual_num']        = $p_product['actual_num']; //实际库存
            $product_var['warehouse']         = $param['warehouse']; //仓库名称
            $product_var['warehouse_id']      = strval($param['warehouse_id']); //仓库ID
            $product_var['erp_id']            = strval($param['erp_id']); //


            $new_items[] = $product_var;


            //                $product_attachment = explode(',', $products[$product_item['product_id']]['product_attachment'] ?? '');
            $qualification[] = [
                'product_id'         => $p_product['product_id'],
                'frontal_label_img'  => $products[$p_product['product_id']]['frontal_label_img'] ?? '',
                'cn_back_label_img'  => $products[$p_product['product_id']]['cn_back_label_img'] ?? '',
                'en_back_label_img'  => $products[$p_product['product_id']]['en_back_label_img'] ?? '',
                'package_img'        => $products[$p_product['product_id']]['package_img'] ?? '',
                'customs_pass'       => $product_attachment[0] ?? '',
                'health'             => $product_attachment[1] ?? '',
                'product_attachment' => $products[$p_product['product_id']]['product_attachment'],
                'status'             => 0, //办理状态: 0=办理中,1=已完成,2=审核中,3=通过,4=驳回
            ];
        }

        $period['items']         = $new_items;
        $period['qualification'] = $qualification;
        #endregion 产品数据处理

        #region 期数数据处理
        $last_purchase                         = $period['purchase'];
        $last_purchase['periods_type']         = $param['periods_type'];//periods_type
        $last_purchase['import_type']          = $param['import_type'];//进口类型
        $last_purchase['payee_merchant_id']    = $param['payee_merchant_id'];//收款公司ID
        $last_purchase['payee_merchant_name']  = $param['payee_merchant_name'];//收款公司名称
        $last_purchase['is_gift_box']          = $param['is_gift_box'];//礼盒发货
        $last_purchase['is_supplier_delivery'] = $param['is_supplier_delivery'];//是否代发
        $last_purchase['supplier_delivery_address'] = $param['supplier_delivery_address'];//代发地址
        $last_purchase['is_presell']           = $param['is_presell'];//is_presell
        $last_purchase['shipping_conditions']  = $param['shipping_conditions'];//发货条件:0=常温,1=冷链
        $last_purchase['storage_conditions']   = $param['storage_conditions'];//存储条件:0=常温,1=冰冻  [保留]
        $last_purchase['delivery_time_limit']  = $param['delivery_time_limit'];//发货时效:0=24小时,1=72小时,2=72小时以上 [保留]
        $last_purchase['copy_periods_id']      = $param['copy_periods_id'] ?? 0;//复制的期数ID;value=0则是新增文案
        $last_purchase['is_design']            = $param['is_design'] ?? null;//是否需要设计:0=否,1=是
        $last_purchase['demand_grade']         = $param['demand_grade'] ?? null;//写作难易程度
        $last_purchase['supplier']             = $param['supplier'] ?? null;//供应商
        $last_purchase['supplier_id']          = $param['supplier_id'] ?? null;//供应商id
        $last_purchase['demand']               = $param['demand'] ?? null;//文案需求
        $last_purchase['purchase_remake']      = $param['purchase_remake'] ?? null;
        $last_purchase['selling_point']        = $param['selling_point'] ?? null;
        $last_purchase['expect_time']          = !empty($param['expect_time']) ? strtotime($param['expect_time']) : null;//期望完成时间
        $last_purchase['warehouse']            = $param['warehouse']; //仓库名称
        $last_purchase['warehouse_id']         = strval($param['warehouse_id']); //仓库ID
        $last_purchase['erp_id']               = strval($param['erp_id']); //
        $last_purchase['status']               = 1; //办理状态: 0=办理中,1=已完成
        $period['purchase']                    = $last_purchase;
        #endregion

        #region 复制期数数据处理
        if ($param['copy_periods_id'] ?? 0) {
            //复制的期数
            $copy_period = Es::name(Es::PERIODS)->where([['id', '==', $param['copy_periods_id']]])->find();

            $period['editor']                                = [
                "title"          => $copy_period['title'],
                "brief"          => $copy_period['brief'],
                "banner_img"     => $copy_period['banner_img'],
                "product_img"    => explode(',', $copy_period['product_img']),
                "video_cover"    => $copy_period['video_cover'],
                "video"          => $copy_period['video'],
                "horizontal_img" => $copy_period['horizontal_img'] ?? '',
                "detail"         => (new \app\service\Periods($copy_period['periods_type']))->getModel()->where('id', $copy_period['id'])->value('detail'),
                "status"         => 1
            ];
            $period['document_distribution']['creator_uid']  = $copy_period['creator_id'] ?? 0;
            $period['document_distribution']['creator_name'] = $copy_period['creator_name'] ?? '';
            $this->create_period_json($period);
        }
        #endregion

        return [$period, compact('node_is_done', 'remark', 'status', 'current_node_name')];
    }

    /**
     * @方法描述: 删除实例
     * <AUTHOR>
     * @Date 2023/7/27 10:52
     * @param $param
     * @return bool|mixed
     */
    public
    function del($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        //region 删除
//        $this->model->startTrans();
        try {
            $res = $this->model->where('id', $param['id'])->delete();
//            $this->model->commit();
        } catch (\Exception $e) {
//            $this->model->rollback();
            Log::error('更新数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('更新数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($res);
    }

    /**
     * @方法描述: 上传资质
     * <AUTHOR>
     * @Date 2023/7/20 11:50
     * @param $param
     * @return mixed|true
     * @throws Exception
     */
    public
    function qualification($period, $param, $extend)
    {
        $node_is_done = 0; //节点是否完成 1 = 完成,
        $status       = self::STATUS_PROCESSING; //完成状态
        $remark       = '保存 产品ID: ' . $param['product_id'] . ' 资质'; //备注

        #region 资质内容处理
        $qualifications                             = array_column($period['qualification'], null, 'product_id');
        $product_qualification                      = $qualifications[$param['product_id']] ?? [];
        $product_qualification['product_id']        = $param['product_id'];//产品ID
        $product_qualification['frontal_label_img'] = $param['frontal_label_img'];//正标图
        $product_qualification['cn_back_label_img'] = $param['cn_back_label_img'];//中文背标图
        $product_qualification['en_back_label_img'] = $param['en_back_label_img'];//英文背标图
        $product_qualification['package_img']       = $param['package_img'];//包装图
        $product_qualification['customs_pass']      = $param['customs_pass'];//关单
        $product_qualification['health']            = $param['health'];//卫检
        $qualifications[$param['product_id']]       = $product_qualification;
        $period['qualification']                    = array_values($qualifications);
        #endregion 资质内容处理

        return [$period, compact('node_is_done', 'remark', 'status')];
    }

    /**
     * @方法描述: 上传资质 提交
     * <AUTHOR>
     * @Date 2023/7/20 11:50
     * @param $param
     * @return mixed|true
     * @throws Exception
     */
    public
    function qualificationSubmit($period, $param, $extend)
    {
        $node_is_done = 1; //节点是否完成 1 = 完成,
        $status       = self::STATUS_FINISHED; //完成状态
        $remark       = '上传资质-提交';

        #region 资质内容处理
        if (!empty($param['product_id'])) {
            $qualifications        = array_column($period['qualification'], null, 'product_id');
            $product_qualification = $qualifications[$param['product_id']] ?? [];
            isset($param['product_id']) && $product_qualification['product_id'] = $param['product_id'];//产品ID
            isset($param['frontal_label_img']) && $product_qualification['frontal_label_img'] = $param['frontal_label_img'];//正标图
            isset($param['cn_back_label_img']) && $product_qualification['cn_back_label_img'] = $param['cn_back_label_img'];//中文背标图
            isset($param['en_back_label_img']) && $product_qualification['en_back_label_img'] = $param['en_back_label_img'];//英文背标图
            isset($param['package_img']) && $product_qualification['package_img'] = $param['package_img'];//包装图
            isset($param['customs_pass']) && $product_qualification['customs_pass'] = $param['customs_pass'];//关单
            isset($param['health']) && $product_qualification['health'] = $param['health'];//卫检
            $qualifications[$param['product_id']] = $product_qualification;
            $period['qualification']              = array_values($qualifications);
        }
        #endregion 资质内容处理

        $period['audit']['purchase_exec'] = 2;

        return [$period, compact('node_is_done', 'remark', 'status')];
    }

    /**
     * @方法描述: 文案编辑
     * <AUTHOR>
     * @Date 2023/7/20 14:20
     * @param $param
     * @return mixed|true
     * @throws Exception
     * @throws \think\db\exception\DbException
     */
    public
    function editor($period, $param, $extend)
    {
        $node_is_done = 0; //节点是否完成 1 = 完成,
        $status       = self::STATUS_PROCESSING; //完成状态
        $remark       = '修改了商品详情';

        #region 文案编辑内容
        $editor                   = $period['editor'] ?? [];
        $editor['title']          = $param['title'] ?? null; //商品标题
        $editor['brief']          = $param['brief'] ?? null; //一句话介绍
        $editor['banner_img']     = $param['banner_img'] ?? null; //题图
        $editor['horizontal_img'] = $param['horizontal_img'] ?? null; //题图
        $editor['product_img']    = empty($param['product_img']) ? [] : explode(',', $param['product_img']); //产品图
        $editor['video_cover']    = $param['video_cover'] ?? null; //视频封面
        $editor['video']          = $param['video'] ?? null; //视频
        $editor['detail']         = $param['detail'] ?? null; //商品详情
//        $editor['purchase_uid']  = $param['purchase_uid']; //采购人UID
//        $editor['purchase_name'] = $param['purchase_name']; //采购人姓名
        $editor['status'] = 0; //办理状态: 0=办理中,1=已完成
        $period['editor'] = $editor;

        isset($param['horizontal_img']) && $period['picture_distribution']['horizontal_img'] = $param['horizontal_img']; //竖图
        isset($param['banner_img']) && $period['picture_distribution']['banner_img'] = $param['banner_img']; //竖图
        isset($param['product_img']) && $period['picture_distribution']['product_img'] = explode(',', $param['product_img']); //产品图

        #endregion

        #region 生成JSON文件
        // 生成json文件
        $this->create_period_json($period);
        // 刷新CDN
//        self::CDNrefreshObject((int)$params['period']);

        #endregion 生成JSON文件

        return [$period, compact('node_is_done', 'remark', 'status')];
    }

    /**
     * @方法描述: 文案提交
     * <AUTHOR>
     * @Date 2023/7/28 13:23
     * @param $period
     * @param $param
     * @param $extend
     * @return array
     */
    public
    function editorSubmit($period, $param, $extend)
    {
        $node_is_done = 1; //节点是否完成 1 = 完成,
        $status       = self::STATUS_FINISHED; //完成状态
        $remark       = '文案提交';

        #region 文案编辑内容
        $editor                   = $period['editor'] ?? [];
        $editor['title']          = $param['title'] ?? null; //商品标题
        $editor['brief']          = $param['brief'] ?? null; //一句话介绍
        $editor['banner_img']     = $param['banner_img'] ?? null; //题图
        $editor['horizontal_img'] = $param['horizontal_img'] ?? null; //题图
        $editor['product_img']    = empty($param['product_img']) ? [] : explode(',', $param['product_img']); //产品图
        $editor['video_cover']    = $param['video_cover'] ?? null; //视频封面
        $editor['video']          = $param['video'] ?? null; //视频
        $editor['detail']         = $param['detail'] ?? null; //商品详情
//        $editor['purchase_uid']  = $param['purchase_uid']; //采购人UID
//        $editor['purchase_name'] = $param['purchase_name']; //采购人姓名
        $editor['status'] = 1; //办理状态: 0=办理中,1=已完成
        $period['editor'] = $editor;

        isset($param['horizontal_img']) && $period['picture_distribution']['horizontal_img'] = $param['horizontal_img']; //竖图
        isset($param['banner_img']) && $period['picture_distribution']['banner_img'] = $param['banner_img']; //竖图
        isset($param['product_img']) && $period['picture_distribution']['product_img'] = explode(',', $param['product_img']); //产品图
        #endregion

        #region 生成JSON文件
        // 生成json文件
        $this->create_period_json($period);
        // 刷新CDN

        #endregion 生成JSON文件

        return [$period, compact('node_is_done', 'remark', 'status')];
    }

    /**
     * @方法描述: 图片提交
     * <AUTHOR>
     * @Date 2023/7/28 13:23
     * @param $period
     * @param $param
     * @param $extend
     * @return array
     */
    public
    function pictureSubmit($period, $param, $extend)
    {
        $node_is_done = 1; //节点是否完成 1 = 完成,
        $status       = self::STATUS_FINISHED; //完成状态
        $remark       = '图片提交';

        if ($period['purchase']['periods_type'] == 1) {
            $process_mode = 'process'; //操作模式 process操作人,participant参与人
        } else {
            $process_mode = 'participant'; //操作模式 process操作人,participant参与人
        }

        #region 图片分发
        $picture_distribution = $period['picture_distribution'] ?? [];
        isset($param['design_title']) && $picture_distribution['design_title'] = $param['design_title']; //酒款名
        isset($param['design_cn_highlights']) && $picture_distribution['design_cn_highlights'] = $param['design_cn_highlights']; //中文亮点
        isset($param['design_score']) && $picture_distribution['design_score'] = $param['design_score']; //评分内容
        isset($param['design_style']) && $picture_distribution['design_style'] = $param['design_style']; //风格
        isset($param['design_website']) && $picture_distribution['design_website'] = $param['design_website']; //官网地址
        isset($param['design_uid']) && $picture_distribution['design_uid'] = $param['design_uid']; //设计师UID
        isset($param['creator_name']) && $picture_distribution['creator_name'] = $param['creator_name']; //设计师姓名
        isset($param['material']) && $picture_distribution['material'] = $param['material']; //文案附件
        isset($param['horizontal_img']) && $picture_distribution['horizontal_img'] = $param['horizontal_img']; //竖图
        isset($param['banner_img']) && $picture_distribution['banner_img'] = $param['banner_img']; //竖图
        isset($param['product_img']) && $picture_distribution['product_img'] = explode(',', $param['product_img']); //产品图
        isset($param['creator_name']) && $picture_distribution['status'] = 0; //办理状态: 0=办理中,1=已完成

        $period['picture_distribution'] = $picture_distribution;

        isset($param['horizontal_img']) && $period['editor']['horizontal_img'] = $param['horizontal_img']; //竖图
        isset($param['banner_img']) && $period['editor']['banner_img'] = $param['banner_img']; //竖图
        isset($param['product_img']) && $period['editor']['product_img'] = explode(',', $param['product_img']); //产品图
        isset($param['detail']) && $period['editor']['detail'] = $param['detail'];
        $this->create_period_json($period);
        #endregion

        return [$period, compact('node_is_done', 'remark', 'status', 'process_mode')];
    }

    /**
     * @方法描述: 图片分发
     * <AUTHOR>
     * @Date 2023/7/20 14:20
     * @param $param
     * @return mixed|true
     * @throws Exception
     * @throws \think\db\exception\DbException
     */
    public
    function pictureDistribution($period, $param, $extend)
    {
        $node_is_done = 0; //节点是否完成 1 = 完成,
        $status       = self::STATUS_PROCESSING; //完成状态
        $remark       = '修改了商品详情';

        #region 图片分发
        $picture_distribution = $period['picture_distribution'] ?? [];
        isset($param['design_title']) && $picture_distribution['design_title'] = $param['design_title']; //酒款名
        isset($param['design_cn_highlights']) && $picture_distribution['design_cn_highlights'] = $param['design_cn_highlights']; //中文亮点
        isset($param['design_score']) && $picture_distribution['design_score'] = $param['design_score']; //评分内容
        isset($param['design_style']) && $picture_distribution['design_style'] = $param['design_style']; //风格
        isset($param['design_website']) && $picture_distribution['design_website'] = $param['design_website']; //官网地址
        isset($param['design_uid']) && $picture_distribution['design_uid'] = $param['design_uid']; //设计师UID
        isset($param['creator_name']) && $picture_distribution['creator_name'] = $param['creator_name']; //设计师姓名
        isset($param['material']) && $picture_distribution['material'] = $param['material']; //文案附件
        isset($param['horizontal_img']) && $picture_distribution['horizontal_img'] = $param['horizontal_img']; //竖图
        isset($param['banner_img']) && $picture_distribution['banner_img'] = $param['banner_img']; //竖图
        isset($param['product_img']) && $picture_distribution['product_img'] = explode(',', $param['product_img']); //产品图
        isset($param['creator_name']) && $picture_distribution['status'] = 0; //办理状态: 0=办理中,1=已完成

        $period['picture_distribution'] = $picture_distribution;

        isset($param['horizontal_img']) && $period['editor']['horizontal_img'] = $param['horizontal_img']; //竖图
        isset($param['banner_img']) && $period['editor']['banner_img'] = $param['banner_img']; //竖图
        isset($param['product_img']) && $period['editor']['product_img'] = explode(',', $param['product_img']); //产品图
        isset($param['detail']) && $period['editor']['detail'] = $param['detail'];
        $this->create_period_json($period);
        #endregion

        return [$period, compact('node_is_done', 'remark', 'status')];
    }


    public
    function pictureDistributionSubmit($period, $param, $extend)
    {
        $node_is_done                             = 1; //节点是否完成 1 = 完成,
        $status                                   = self::STATUS_FINISHED; //完成状态
        $remark                                   = '图片提交';
        $period['picture_distribution']['status'] = 1;

        #region 图片分发
        $picture_distribution = $period['picture_distribution'] ?? [];
        isset($param['design_title']) && $picture_distribution['design_title'] = $param['design_title']; //酒款名
        isset($param['design_cn_highlights']) && $picture_distribution['design_cn_highlights'] = $param['design_cn_highlights']; //中文亮点
        isset($param['design_score']) && $picture_distribution['design_score'] = $param['design_score']; //评分内容
        isset($param['design_style']) && $picture_distribution['design_style'] = $param['design_style']; //风格
        isset($param['design_website']) && $picture_distribution['design_website'] = $param['design_website']; //官网地址
        isset($param['design_uid']) && $picture_distribution['design_uid'] = $param['design_uid']; //设计师UID
        isset($param['creator_name']) && $picture_distribution['creator_name'] = $param['creator_name']; //设计师姓名
        isset($param['material']) && $picture_distribution['material'] = $param['material']; //文案附件
        isset($param['horizontal_img']) && $picture_distribution['horizontal_img'] = $param['horizontal_img']; //竖图
        isset($param['banner_img']) && $picture_distribution['banner_img'] = $param['banner_img']; //竖图
        isset($param['product_img']) && $picture_distribution['product_img'] = explode(',', $param['product_img']); //产品图
        isset($param['creator_name']) && $picture_distribution['status'] = 0; //办理状态: 0=办理中,1=已完成

        $period['picture_distribution'] = $picture_distribution;

        isset($param['horizontal_img']) && $period['editor']['horizontal_img'] = $param['horizontal_img']; //竖图
        isset($param['banner_img']) && $period['editor']['banner_img'] = $param['banner_img']; //竖图
        isset($param['product_img']) && $period['editor']['product_img'] = explode(',', $param['product_img']); //产品图
        isset($param['detail']) && $period['editor']['detail'] = $param['detail'];
        $this->create_period_json($period);
        #endregion

        return [$period, compact('node_is_done', 'remark', 'status')];
    }

    /**
     * @方法描述: 文案分发
     * <AUTHOR>
     * @Date 2023/7/20 14:20
     * @param $param
     * @return mixed|true
     * @throws Exception
     * @throws \think\db\exception\DbException
     */
    public
    function documentDistribution($period, $param, $extend)
    {
        $node_is_done = $param['node_is_done'] ?? 1; //节点是否完成 1 = 完成,
        $status       = self::STATUS_FINISHED; //完成状态
        $remark       = '设置文案负责人: '.$param['creator_name'];

        #region 文案分发
        $period['purchase']['demand_grade']               = $param['demand_grade']; //写作难易程度
        $document_distribution                            = $period['document_distribution'] ?? [];
        $document_distribution['creator_name']            = $param['creator_name']; //文案姓名
        $document_distribution['creator_uid']             = $param['creator_uid']; //文案UID
        $document_distribution['planned_completion_time'] = !empty($param['planned_completion_time']) ? strtotime($param['planned_completion_time']) : null; //文案计划完成时间
        $document_distribution['status']                  = 1; //办理状态: 0=办理中,1=已完成
        $period['document_distribution']                  = $document_distribution;
        #endregion

        return [$period, compact('node_is_done', 'remark', 'status')];
    }

    /**
     * @方法描述: 修改文案负责人
     * <AUTHOR>
     * @Date 2023/8/1 9:52
     * @param $period
     * @param $param
     * @param $extend
     * @return array
     * @throws Exception
     */
    public
    function documentPersonnel($period, $param, $extend)
    {
        $node_is_done = 0; //节点是否完成 1 = 完成,
        $status       = self::STATUS_PROCESSING; //完成状态
        $remark       = '修改文案负责人';
        $not_remark       = true;

        $vh_user = \Curl::adminInfo([
            'admin_id' => $param['creator_uid'],
            'field'    => 'id,realname,userid,status,roles'
        ])[$param['creator_uid']] ?? null;
        if (!$vh_user) throw new Exception("未找到管理员");
        $vh_user['role_ids'] = implode(',', array_column($vh_user['roles'], 'id'));

        #region 更新数据组装
        $p_current_node_id  = $param['current_node_id'];
        $current_node_infos = array_column($period['current_node_info'] ?? [], null, 'id');
        $current_node_info  = $current_node_infos[$p_current_node_id] ?? [];  // todo
//        $current_node_info = $period['current_node_info']; // todo
        if (empty($current_node_info)) {
            throw new Exception("未找到可操作的当前节点!");
        }

        if (
            empty($current_node_info['participant'])
            && empty($period['change_user']['documentPersonnel'])
            && in_array(($period['purchase']['periods_type'] ?? null), [0, 2, 3, 4])
        ) {
            $flow_ui_participants = json_decode(Cache::get(self::FLOW_UI_PARTICIPANT, ''), true);
            if (empty($flow_ui_participants)) {
                $flow_ui_participants = \Curl::adminList(['role_id' => $this->role_id, 'status' => 1, 'page' => 1, 'limit' => 9999])['list'] ?? [];
            }
            $ui_user = array_shift($flow_ui_participants);

            $participant                                    = [ //参与人
                [
                    "genre"     => 1, //用户类型(写死1)
                    'we_com_id' => strval($ui_user['userid']),
                    'name'      => strval($ui_user['realname']),
                    'vos_uid'   => intval($ui_user['id']),
                    "role_id"   => strval($ui_user['roles'][0]['id'] ?? ''),
                ],
            ];
            $period['picture_distribution']['design_uid']   = $ui_user['id'];
            $period['picture_distribution']['creator_name'] = $ui_user['realname'];
            $period['change_user']['documentPersonnel'] = $ui_user['id'];

            Cache::set(self::FLOW_UI_PARTICIPANT, json_encode($flow_ui_participants));
            $remark       = '修改UI负责人: '. $ui_user['realname'];
            $not_remark       = false;
        } else {
            $participant = $current_node_info['participant'];
        }
        $request_data                                    = [
            "code"        => empty($param['instance_id']) ? strval($period['id']) : strval($param['instance_id']),
            "node_id"     => $param['target_node_id'],
            "process"     => [
                [
                    "genre"     => 1, //用户类型(写死1)
                    'we_com_id' => strval($vh_user['userid']),
                    'name'      => strval($vh_user['realname']),
                    'vos_uid'   => intval($vh_user['id']),
                    "role_id"   => strval($vh_user['roles'][0]['id'] ?? ''),
                ],
            ],
            "participant" => $participant
        ];
        $period['document_distribution']['creator_uid']  = $vh_user['id'];
        $period['document_distribution']['creator_name'] = $vh_user['realname'];
        #endregion

        \Curl::flowInstanceProcessModify($request_data);

        return [$period, compact('node_is_done', 'remark', 'status', 'not_remark')];
    }


    /**
     * @方法描述: 采购审核
     * <AUTHOR>
     * @Date 2023/7/27 11:46
     * @param $param
     * @return mixed|true
     * @throws Exception
     * @throws \think\db\exception\DbException
     */
    public
    function auditPurchase($period, $param, $extend)
    {
        if (!in_array($param['status'], [4, 3])) throw new Exception('审核状态错误');

        $node_is_done = 1; //节点是否完成 1 = 完成,
        $status       = $this->status_values[$param['status']]; //完成状态
        $remark       = ($this->status_text[$param['status']] ?? '未知状态') . ' ' . ($param['reject_cause'] ?? ''); //备注

        #region 采购审核
        $p_expect_time                     = !empty($param['expect_time']) ? strtotime($param['expect_time']) : null;
        $purchase                          = $period['audit']['purchase'] ?? [];
        $purchase['expect_time']           = $p_expect_time; //期望售卖时间
        $purchase['status']                = $param['status']; //审核状态:3=通过,4=驳回
        $purchase['reject_cause']          = $param['reject_cause'] ?? ''; //驳回原因
        $period['audit']['purchase']       = $purchase;
        if($p_expect_time) {
            $period['purchase']['expect_time'] = $p_expect_time;
        }
        #endregion 采购审核

        $p_current_node_id  = $param['current_node_id'];
        $current_node_infos = array_column($period['current_node_info'] ?? [], null, 'id');
        $current_node_info  = $current_node_infos[$p_current_node_id] ?? [];  // todo
//        $current_node_info = $period['current_node_info']; // todo
        if (empty($current_node_info)) {
            throw new Exception("未找到可操作的当前节点!");
        }

        if (
            !empty($current_node_info['process_node_ids'])
            && ($param['status'] == 3)
            && empty($period['change_user']['auditPurchase'])
            && in_array(($period['purchase']['periods_type'] ?? null), [1])
        ) {
            $flow_ui_participants = json_decode(Cache::get(self::FLOW_UI_PARTICIPANT, ''), true);
            if (empty($flow_ui_participants)) {
                $flow_ui_participants = \Curl::adminList(['role_id' => $this->role_id, 'status' => 1, 'page' => 1, 'limit' => 9999])['list'] ?? [];
            }
            $ui_user = array_shift($flow_ui_participants);

            $request_data = [
                "code"        => empty($param['instance_id']) ? strval($period['id']) : strval($param['instance_id']),
                "node_id"     => $current_node_info['process_node_ids'][0]['id'],
                "process"     => [ //参与人
                    [
                        "genre"     => 1, //用户类型(写死1)
                        'we_com_id' => strval($ui_user['userid']),
                        'name'      => strval($ui_user['realname']),
                        'vos_uid'   => intval($ui_user['id']),
                        "role_id"   => strval($ui_user['roles'][0]['id'] ?? ''),
                    ],
                ],
                "participant" => []
            ];
            #endregion


            $period['picture_distribution']['design_uid']   = $ui_user['id'];
            $period['picture_distribution']['creator_name'] = $ui_user['realname'];
            $period['change_user']['auditPurchase'] = $ui_user['id'];
            \Curl::flowInstanceProcessModify($request_data);
            $remark .= ' 修改了UI参与人: '. $ui_user['realname'];
            Cache::set(self::FLOW_UI_PARTICIPANT, json_encode($flow_ui_participants));
        }

        return [$period, compact('node_is_done', 'remark', 'status')];
    }

    /**
     * @方法描述: 采购主管终审
     * <AUTHOR>
     * @Date 2023/7/27 13:20
     * @param $param
     * @return mixed|true
     * @throws Exception
     * @throws \think\db\exception\DbException
     */
    public
    function auditLastPurchase($period, $param, $extend)
    {
        if (!in_array($param['status'], [4, 3])) throw new Exception('审核状态错误');

        $node_is_done = 1; //节点是否完成 1 = 完成,
        $status       = $this->status_values[$param['status']]; //完成状态
        $remark       = ($this->status_text[$param['status']] ?? '未知状态') . ' ' . ($param['reject_cause'] ?? ''); //备注

        #region 采购主管终审
        $p_expect_time                     = !empty($param['expect_time']) ? strtotime($param['expect_time']) : null;
        $purchase_last                     = $period['audit']['purchase_last'] ?? [];
        $purchase_last['expect_time']      = $p_expect_time; //期望售卖时间
        $purchase_last['status']           = $param['status']; //审核状态:3=通过,4=驳回
        $purchase_last['reject_cause']     = $param['reject_cause'] ?? ''; //驳回原因
        $period['audit']['purchase_last']  = $purchase_last;
        if($p_expect_time){
            $period['purchase']['expect_time'] = $p_expect_time;
        }
        #endregion 采购主管终审

        return [$period, compact('node_is_done', 'remark', 'status')];
    }

    /**
     * @方法描述: 文案主管审核
     * <AUTHOR>
     * @Date 2023/7/20 16:09
     * @param $param
     * @return mixed|true
     * @throws Exception
     * @throws \think\db\exception\DbException
     */
    public
    function document($period, $param, $extend)
    {
        if (!in_array($param['status'], [4, 3])) throw new Exception('审核状态错误');

        $node_is_done = 1; //节点是否完成 1 = 完成,
        $status       = $this->status_values[$param['status']]; //完成状态
        $remark       = ($this->status_text[$param['status']] ?? '未知状态') . ' ' . ($param['reject_cause'] ?? ''); //备注

        #region 文案主管审核
        $document                    = $period['audit']['document'] ?? [];
        $document['status']          = $param['status']; //审核状态:3=通过,4=驳回
        $document['reject_cause']    = $param['reject_cause'] ?? ''; //驳回原因
        $period['audit']['document'] = $document;
        #endregion 文案主管审核

        return [$period, compact('node_is_done', 'remark', 'status')];
    }


    /**
     * @方法描述: 采购执行资质审核
     * <AUTHOR>
     * @Date 2023/7/20 16:09
     * @param $param
     * @return mixed|true
     * @throws Exception
     * @throws \think\db\exception\DbException
     */
    public
    function purchaseExec($period, $param, $extend)
    {
        if (!in_array($param['status'], [4, 3])) throw new Exception('审核状态错误');

        $node_is_done = 1; //节点是否完成 1 = 完成,
        $status       = $this->status_values[$param['status']]; //完成状态
        $remark       = ($this->status_text[$param['status']] ?? '未知状态') . ' ' . ($param['reject_cause'] ?? ''); //备注

        #region 产品资质审核
        $purchase_exec                    = (!empty($period['audit']['purchase_exec']) && is_array($period['audit']['purchase_exec'])) ? $period['audit']['purchase_exec'] : [];
        $purchase_exec['status']          = $param['status']; //审核状态:3=通过,4=驳回
        $purchase_exec['reject_cause']    = $param['reject_cause'] ?? ''; //驳回原因
        $period['audit']['purchase_exec'] = $purchase_exec;
        #endregion 产品资质审核

        //#region  通过后 资质等更新到磐石
        $qualifications = array_column($period['qualification'], null, 'product_id');
        $wiki_products  = Db::connect('wiki')->name('products')->where('id', 'in', array_keys($qualifications))->column("id,product_attachment");
        foreach ($wiki_products as $wiki_product) {
            $product_attachment = [];
            if (!empty($wiki_product['product_attachment'])) {
                $product_attachment = explode(',', $wiki_product['product_attachment']);
            }
            if (!empty($qualifications[$wiki_product['id']]['customs_pass'])) {
                $product_attachment[] = $qualifications[$wiki_product['id']]['customs_pass'];
            }
            if (!empty($qualifications[$wiki_product['id']]['health'])) {
                $product_attachment[] = $qualifications[$wiki_product['id']]['health'];
            }
            Db::connect('wiki')->name('products')->where('id', $wiki_product['id'])->update(['product_attachment' => implode(',', array_values(array_unique($product_attachment)))]);
        }
        //#endregion  通过后 资质等更新到磐石

        return [$period, compact('node_is_done', 'remark', 'status')];
    }

    /**
     * @方法描述: 运营审核
     * <AUTHOR>
     * @Date 2023/7/27 13:50
     * @param $param
     * @return mixed|true
     * @throws Exception
     * @throws \think\db\exception\DbException
     */
    public
    function operation($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        #region 初始化变量
        $now    = time();
        $period = $this->getPeriod($param);
        #endregion 初始化变量

        #region 运营审核
        $operation               = $period['audit']['operation'] ?? [];
        $operation['status']     = $param['status']; //审核状态:3=通过,5=延期
        $operation['delay_time'] = !empty($param['delay_time']) ? strtotime($param['delay_time']) : null;; //延迟时间
        $period['audit']['operation'] = $operation;
        #endregion 运营审核

        $remark = ($this->status_text[$param['status']] ?? '') . ' ' . (empty($param['delay_time']) ? '' : "延期时间: {$param['delay_time']}"); //备注

        if ($param['status'] == 3) {
            Db::startTrans();
            try {
                $this->createPeriod($period);

                #region 数据处理
                if (!($period['operation']['task_exec'] ?? 1)) {
                    //删除秒级任务
                    \Curl::secondLevelSchedulerDelete([
                        'task_id' => $period['operation']['task_id'],
                    ]);
                }

                #region 记录日志
                $period['logs'][] =
                    [
                        'uid'         => $param['vh_uid'], //操作人ID
                        'name'        => $param['vh_vos_name'], //操作人姓名
                        'create_time' => $now, //记录时间
                        'operation'   => '运营通过', //事项
                        'remarks'     => $remark //备注
                    ];
                #endregion 记录日志

                $period['operation']['status']    = '运营通过'; //运营状态: 运营确认上架中,运营通过,运营未排期,运营延期
                $period['operation']['task_exec'] = 1; //计划任务状态: 0=未执行,1=已执行
                #endregion

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return $this->failed('操作失败: ' . $e->getMessage());
            }

        } elseif ($param['status'] == 5) {
            // 延期 则回到采购哪儿
            $task_id    = uuid();
            $delay_time = strtotime($param['delay_time']);
            $exec_time  = $delay_time; //延期三天

            if ($period['operation']['task_exec'] ?? 1) {
                //新增秒级任务
                //设置自动更改回状态的时间
                \Curl::secondLevelSchedulerAdd([
                    'task_id'           => $task_id,
                    'task_trigger_time' => $exec_time,
                    'task_url'          => env('ITEM.COMMODITIES_URL') . '/commodities/v3/periodsPool/timeExpires',
                    'task_data'         => json_encode([
                        'id'     => $period['id'],
                        'status' => '运营确认上架中',
                    ])
                ]);
            } else {
                //修改秒级任务
                \Curl::secondLevelSchedulerUpdate([
                    'old_task_id'       => $period['operation']['task_id'],
                    'new_task_id'       => $task_id,
                    'task_trigger_time' => $exec_time,
                    'task_url'          => env('ITEM.COMMODITIES_URL') . '/commodities/v3/periodsPool/timeExpires',
                    'task_data'         => json_encode([
                        'id'     => $period['id'],
                        'status' => '运营确认上架中',
                    ])
                ]);
            }

            #region 记录日志
            $period['logs'][] =
                [
                    'uid'         => $param['vh_uid'], //操作人ID
                    'name'        => $param['vh_vos_name'], //操作人姓名
                    'create_time' => $now, //记录时间
                    'operation'   => '运营延期', //事项
                    'remarks'     => $remark //备注
                ];
            #endregion 记录日志

            $period['purchase']['expect_time'] = $delay_time; //延期三天
            $period['operation']['status']     = '运营延期'; //运营状态: 运营确认上架中,运营通过,运营未排期,运营延期
            $period['operation']['task_exec']  = 0; //计划任务状态: 0=未执行,1=已执行
            $period['operation']['task_id']    = $task_id; //$task_id

        } else {
            throw new Exception("状态错误");
        }

        $this->model->where('_id', $period['_id'])->update($period);
        return $this->success($period);
    }

    /**
     * @方法描述: 重新提价
     * <AUTHOR>
     * @Date 2023/7/27 14:26
     * @param $period
     * @param $param
     * @param $extend
     * @return array
     */
    public
    function resend($param)
    {
        $now     = time();
        $period  = $this->getPeriod($param);
        $task_id = uuid();

        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        $purchase_expect_time = strtotime($param['purchase_expect_time']);
        $exec_time            = $purchase_expect_time + self::DELAYED; //过期时间

        if ($period['operation']['task_exec'] ?? 1) {
            //新增秒级任务
            //超时运营未设置则过期
            \Curl::secondLevelSchedulerAdd([
                'task_id'           => $task_id,
                'task_trigger_time' => $exec_time,
                'task_url'          => env('ITEM.COMMODITIES_URL') . '/commodities/v3/periodsPool/operatOvertime',
                'task_data'         => json_encode([
                    'id'     => $period['id'],
                    'status' => '运营未排期',
                ])
            ]);
        } else {
            //修改秒级任务
            \Curl::secondLevelSchedulerUpdate([
                'old_task_id'       => $period['operation']['task_id'],
                'new_task_id'       => $task_id,
                'task_trigger_time' => $exec_time,
                'task_url'          => env('ITEM.COMMODITIES_URL') . '/commodities/v3/periodsPool/operatOvertime',
                'task_data'         => json_encode([
                    'id'     => $period['id'],
                    'status' => '运营未排期',
                ])
            ]);
        }

        $period['purchase']['expect_time'] = $purchase_expect_time;
        $period['operation']['status']     = '运营确认上架中'; //运营状态: 运营确认上架中,运营通过,运营未排期,运营延期
        $period['operation']['task_exec']  = 0; //计划任务状态: 0=未执行,1=已执行
        $period['operation']['task_id']    = $task_id;

        #region 记录日志
        $period['logs'][] =
            [
                'uid'         => $param['vh_uid'], //操作人ID
                'name'        => $param['vh_vos_name'], //操作人姓名
                'create_time' => $now, //记录时间
                'operation'   => '重新提交', //事项
                'remarks'     => '重新提交', //备注
            ];
        #endregion 记录日志

        $this->model->where('_id', $period['_id'])->update($period);
        return $this->success([]);

    }

    /**
     * @方法描述: 图片分发
     * <AUTHOR>
     * @Date 2023/7/20 14:20
     * @param $param
     * @return mixed|true
     * @throws Exception
     * @throws \think\db\exception\DbException
     */
    public
    function uiPictureDistribution($period, $param, $extend)
    {
        $node_is_done = 1; //节点是否完成 1 = 完成,
        $status       = self::STATUS_FINISHED; //完成状态
        $remark       = '图片分发';

        #region 图片分发
        $picture_distribution                         = $period['picture_distribution'] ?? [];
        $picture_distribution['design_title']         = $param['design_title']; //酒款名
        $picture_distribution['design_cn_highlights'] = $param['design_cn_highlights']; //中文亮点
        $picture_distribution['design_score']         = $param['design_score']; //评分内容
        $picture_distribution['design_style']         = $param['design_style']; //风格
        $picture_distribution['design_website']       = $param['design_website']; //官网地址
        $picture_distribution['attachment']           = explode(',', $param['attachment']); //附件
        $picture_distribution['material']           = explode(',', $param['material']); //文案附件
        $picture_distribution['status']               = 1; //办理状态: 0=办理中,1=已完成
        $period['picture_distribution']               = $picture_distribution;
        isset($param['detail']) && $period['editor']['detail'] = $param['detail'];
        $this->create_period_json($period);
        #endregion

        return [$period, compact('node_is_done', 'remark', 'status')];
    }

    public
    function backlogQuantity($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        $model = $this->model;

        #region Other transaction_state
        $vh_user = \Curl::adminInfo([
            'admin_id' => $param['vh_uid'],
            'field'    => 'id,realname,userid,status,roles'
        ])[$param['vh_uid']] ?? null;
        if (!$vh_user) throw new Exception("未找到管理员");
        $vh_user['role_ids_arr'] = array_column($vh_user['roles'], 'id');
        $vh_user['role_ids']     = implode(',', $vh_user['role_ids_arr']);
        $vh_user['is_operate']   = (in_array(14, $vh_user['role_ids_arr']) || in_array(48, $vh_user['role_ids_arr'])); //14=运营, 48=秒发运营

        $model->where(function ($query) use ($vh_user) {

            if ($vh_user['is_operate']) {
                $query->whereOr('operation.status', 'in', ['运营确认上架中']);
            }

            $query->whereOr(function ($query) use ($vh_user) {
                $query->where('operation.status', 'in', ['运营未排期', '运营延期']);
                $query->where('purchase.purchase_uid', '=', $vh_user['id']);
            });

            $events             = [];
            $operate_periods    = \Curl::flowProcessing([
                "process"      => [//操作人信息
                    'genre'        => 1,//写死1
                    "role_id"      => strval($vh_user['role_ids']),
                    'name'         => strval($vh_user['realname']),
                    'we_com_id'    => strval($vh_user['userid']),
                    'vos_uid'      => intval($vh_user['id']),
                    'process_time' => time(),
                ],
                "customer_ids" => $events
            ])['list'] ?? [];
            $operate_period_ids = [];
            foreach ($operate_periods as $operate_period) {
                $operate_period_ids[] = intval($operate_period['master_instance_id']);
            }
            $query->whereOr('id', 'in', $operate_period_ids);
        });
        #endregion


        $total = $model->count();

        return $this->success(['processing' => $total]);
    }

    public
    function backlogQuantityIds($param)
    {
        $vh_user = \Curl::adminInfo([
            'admin_id' => $param['vh_uid'],
            'field'    => 'id,realname,userid,status,roles'
        ])[$param['vh_uid']] ?? null;
        if (!$vh_user) throw new Exception("未找到管理员");
        $vh_user['role_ids'] = implode(',', array_column($vh_user['roles'], 'id'));

        $flow_rocessing = \Curl::flowProcessing([
                "process"      => [//操作人信息
                    "role_id"      => strval($vh_user['role_ids']),
                    'name'         => strval($vh_user['realname']),
                    'we_com_id'    => strval($vh_user['userid']),
                    'vos_uid'      => intval($vh_user['id']),
                    'process_time' => time(),
//                    "status"       => strval($extend['status'])
                ],
                "customer_ids" => []//多个 空数组时不过滤自定义id
            ]
        );

        return $flow_rocessing['list'] ?? [];

    }


    public
    function create_period_json($period)
    {
        $items         = $period['items'];
        $wiki_products = Es::name(Es::PRODUCTS)->where([
            ['id', 'in', array_values(array_unique(array_column($items, 'product_id')))]
        ])->field('regions_name_cn,product_category_name,id,short_code,bar_code,produce_date,shelf_life,product_attachment,producing_area_name,product_keywords')->limit(0, 10)->select()->toArray();

        $data = [
            'banner_img'                => multiple_image_full_path($period['editor']['banner_img'] ?? ''),
            'product_img'               => multiple_image_full_path(
                empty($period['editor']['product_img']) ? '' : implode(',', $period['editor']['product_img'])
            ),
            'brief'                     => $period['editor']['brief'] ?? '',
            'buyer_id'                  => $period['purchase']['purchase_uid'] ?? '',
            'buyer_name'                => $period['purchase']['purchase_name'] ?? '',
            'buyer_review_status'       => '1', //采购审核状态
            'capacity'                  => implode(',', array_column($items, 'capacity')),
            "conversion_rate"           => 0, //未知的状态
            "copywriting_review_status" => 1, //文案审核状态
            "country"                   => array_column($items, 'country'), //国家
            "created_time"              => Date('Y-m-d H:i:s'),
            "creator_id"                => $period['document_distribution']['creator_uid'] ?? '',
            "creator_name"              => $period['document_distribution']['creator_name'] ?? '',
            "critical_value"            => '0',//临界值
            "current_winery"            => array_column($wiki_products, 'producing_area_name'),
            "current_winery_map"        => json_encode(array_column($wiki_products, 'producing_area_name', 'short_code')),
            "express_id"                => '1',//快递模板ID
            "id"                        => $period['id'],
            "import_type"               => $period['purchase']['import_type'] ?? '',
            "incremental"               => '0',//增量值
            "instruction"               => '',
            "invariant_number"          => '0',
            "inventory"                 => 0,
            "is_ap"                     => 0,
            "is_channel"                => 0,
            "is_cold_chain"             => $period['purchase']['shipping_conditions'] ?? 0,
            "is_cold_free_shipping"     => 0,
            "is_delete"                 => 0,
            "is_deposit"                => 0,
            "is_fail"                   => 0,
            "is_hidden_price"           => 0,
            "is_hot"                    => 0,
            "is_parcel_insurance"       => 0,
            "is_postpone"               => 0,
            "is_presell"                => $period['purchase']['is_presell'] ?? 0,
            "is_sold_out_lock"          => 0,
            "is_supplier_delivery"      => $period['purchase']['is_supplier_delivery'] ?? 0,
            "supplier_delivery_address"      => $period['purchase']['supplier_delivery_address'] ?? '',
            "is_support_coupon"         => 1,
            "is_support_reduction"      => 0,
            "is_support_ts"             => 0,
            "is_user_filter"            => 0,
            "label"                     => '',
            "limit_number"              => 0,
            "market_price"              => 0.00,
            "marketing_attribute"       => '0',
            "note_number"               => '0',
            "onsale_review_status"      => '0',
            'onsale_review_time'        => '',//运营审核时间
            'onsale_status'             => 0,
            'onsale_time'               => '', //上架时间
            'onsale_verify_status'      => '0', //运营二次确认上架（0：未确认，1：已确认）
            'operation_id'              => '', //商品编辑运营者id
            'operation_name'            => '',
            'operation_review_id'       => '', //商品编辑运营者id
            'operation_review_name'     => '',
            'order_count'               => '0',
            'pageviews'                 => '0',
            "payee_merchant_id"         => $period['purchase']['payee_merchant_id'] ?? '',
            "payee_merchant_name"       => $period['purchase']['payee_merchant_name'] ?? '',
            'periods_comment_count'     => '0',
            'periods_set_count'         => '0',
            'periods_type'              => $period['purchase']['periods_type'],
            'praise_count'              => '0',
            'predict_shipment_time'     => '',
            'price'                     => '0.00',
            'product_category'          => array_column($items, 'product_type_name'),
            'product_comment_count'     => '0',
            'product_id'                => implode(',', array_column($items, 'product_id')),
            'product_keyword'           => explode(',', implode(',', array_column($wiki_products, 'product_keywords'))),
            'product_main_category'     => array_column($wiki_products, 'product_category_name'),
            'purchased'                 => '0',
            'purchased_number'          => '0',
            'purchased_person'          => '0',
            'quota_rule'                => '',
            'regions'                   => array_column($wiki_products, 'regions_name_cn'),
            'saled_count'               => '0',
            'sell_time'                 => '',
            'sellout_sold_out'          => '0',
            'short_code'                => array_column($wiki_products, 'short_code'),
            'sold_out_time'             => '',
            'sort'                      => '0',
            'supplier'                  => $items['0']['supplier'] ?? '',
            'supplier_id'               => $items['0']['supplier_id'] ?? '',
            'title'                     => $period['editor']['title'] ?? '',
            'ts_template'               => '',
            'uninstruction'             => '',
            'version'                   => 0,
            'update_time'               => Date('Y-m-d H:i:s'),
            'vest_count'                => 0,
            'vest_purchased'            => 0,
            'virtual_comment_count'     => 0,
            'package_prices'            => 0,
            'quota_number'              => 9999,
            'shipment_time'             => Date('Y-m-d'),
            'title_map'                 => '',
            'content'                   => strip_tags($period['editor']['detail'] ?? ''),
            'detail'                    => $period['editor']['detail'] ?? '',
            'video'                     => $period['editor']['video'] ?? '',
            'video_cover'               => image_full_path($period['editor']['video_cover'] ?? ''),
            "winery"                    => array_column($wiki_products, 'producing_area_name'),
            'package'                   => json_encode([]),
            'service_policy_img'        => 'https://images.vinehoo.com/vinehoomini/v3/goods_detail/vh_ser_pol.png',
            'gold_area_img'             => 'https://images.vinehoo.com/vinehoomini/v3/goods_detail/goods_intro.png',
        ];

        $periods_types      = [0 => 'goods_flash_purchase.png', 1 => 'tail_cargo.png', 2 => 'cross_border.png',
                               3 => 'tail_cargo.png', 4 => '', 5 => '', 9 => '', 10 => '', 11 => ''];
        $data['channel_bg'] = 'https://images.vinehoo.com/vinehoomini/v3/goods_detail/' . $periods_types[$period['purchase']['periods_type']];

        $re['error_code'] = 0;
        $re['data']       = $data;
        $re['error_msg']  = '';
        $json_data        = json_encode($re);

        $onsale_status = $data['onsale_status'] ?? 0;
        // 上传到 oss
        $oss_re = (new \app\service\Periods($period['purchase']['periods_type']))->uploadFile($period['id'], $json_data, $onsale_status);
        return true;
    }

//超时未审核
    public
    function operatOvertime($param)
    {
        $now    = time();
        $period = $this->getPeriod($param);

        $period['operation']['status']    = $param['status']; //运营状态: 运营确认上架中,运营通过,运营未排期,运营延期
        $period['operation']['task_exec'] = 1; //计划任务状态: 0=未执行,1=已执行

        #region 记录日志
        $period['logs'][] =
            [
                'uid'         => null, //操作人ID
                'name'        => '系统', //操作人姓名
                'create_time' => $now, //记录时间
                'operation'   => $param['status'], //事项
                'remarks'     => '超时未排期', //备注
            ];
        #endregion 记录日志

        $this->model->where('_id', $period['_id'])->update($period);
        return $this->success([]);

    }

//延期时间到期 自动变为 运营确认上架中
    public
    function timeExpires($param)
    {
        $now       = time();
        $period    = $this->getPeriod($param);
        $task_id   = uuid();
        $exec_time = $now + self::DELAYED; //延期三天

        if ($period['operation']['task_exec'] ?? 1) {
            //新增秒级任务
            //超时运营未设置则过期
            \Curl::secondLevelSchedulerAdd([
                'task_id'           => $task_id,
                'task_trigger_time' => $exec_time,
                'task_url'          => env('ITEM.COMMODITIES_URL') . '/commodities/v3/periodsPool/operatOvertime',
                'task_data'         => json_encode([
                    'id'     => $period['id'],
                    'status' => '运营未排期',
                ])
            ]);
        } else {
            //修改秒级任务
            \Curl::secondLevelSchedulerUpdate([
                'old_task_id'       => $period['operation']['task_id'],
                'new_task_id'       => $task_id,
                'task_trigger_time' => $exec_time,
                'task_url'          => env('ITEM.COMMODITIES_URL') . '/commodities/v3/periodsPool/operatOvertime',
                'task_data'         => json_encode([
                    'id'     => $period['id'],
                    'status' => '运营未排期',
                ])
            ]);
        }

        $period['purchase']['expect_time'] = $now;
        $period['operation']['status']     = '运营确认上架中'; //运营状态: 运营确认上架中,运营通过,运营未排期,运营延期
        $period['operation']['task_exec']  = 0; //计划任务状态: 0=未执行,1=已执行
        $period['operation']['task_id']    = $task_id;

        #region 记录日志
        $period['logs'][] =
            [
                'uid'         => null, //操作人ID
                'name'        => '系统', //操作人姓名
                'create_time' => $now, //记录时间
                'operation'   => $param['status'], //事项
                'remarks'     => '延期时间到期', //备注
            ];
        #endregion 记录日志

        $this->model->where('_id', $period['_id'])->update($period);
        return $this->success([]);

    }

    public
    function createPeriod($p_period)
    {
        $p_p_items     = $p_period['items'] ?? [];
        $wiki_products = Es::name(Es::PRODUCTS)->where([
            ['id', 'in', array_column($p_p_items, 'product_id')]
        ])->field('id,erp_id,bar_code,product_keywords,product_category_name,tasting_notes,score,prize,drinking_suggestion')->select()->toArray();
        $wiki_products = array_column($wiki_products, null, 'id');

        $product_info      = [];
        $product_inventory = [];
        foreach ($p_p_items as &$p_p_item) {
            $p_p_item['product_category_name'] = $wiki_products[$p_p_item['product_id']]['product_category_name'] ?? '';
            $p_p_item['product_keyword']       = $wiki_products[$p_p_item['product_id']]['product_keywords'] ?? '';
            $p_p_item['bar_code']              = $wiki_products[$p_p_item['product_id']]['bar_code'] ?? '';
            $product_info[]                    = [
                'period'              => $p_period['id'],
                'product_id'          => $p_p_item['product_id'],
                'tasting_notes'       => $wiki_products[$p_p_item['product_id']]['tasting_notes'] ?? '',
                'score'               => $wiki_products[$p_p_item['product_id']]['score'] ?? '',
                'prize'               => $wiki_products[$p_p_item['product_id']]['prize'] ?? '',
                'drinking_suggestion' => $wiki_products[$p_p_item['product_id']]['drinking_suggestion'] ?? '',
                'short_code'          => $wiki_products[$p_p_item['product_id']]['short_code'] ?? '',
            ];


            $product_inventory[] = [
                'period'          => $p_period['id'],
                'erp_id'          => $p_p_item['erp_id'] ?? '',
                'periods_type'    => $p_period['purchase']['periods_type'],
                'product_id'      => $p_p_item['product_id'],
                'short_code'      => $p_p_item['short_code'],
                'is_use_comment'  => $p_p_item['is_use_comment'] ?? 0,
                'capacity'        => $p_p_item['capacity'] ?? '',
                'inventory'       => $p_p_item['actual_num'],
                'warehouse'       => $p_p_item['warehouse'],
                'warehouse_id'    => $p_p_item['warehouse_id'],
                'product_name'    => $p_p_item['product_name'],
                'en_product_name' => $p_p_item['en_product_name'],
                'costprice'       => $p_p_item['costprice'],
                'bar_code'        => $p_p_item['bar_code'],
                'title'           => $p_period['editor']['title'],
                'created_time'    => time(),
                'inventory_accum' => $p_p_item['actual_num'],
            ];
        }

        $data = [
            'id'                        => $p_period['id'],
            'title'                     => $p_period['editor']['title'] ?? '',
            'brief'                     => $p_period['editor']['brief'] ?? '',
            'banner_img'                => $p_period['editor']['banner_img'] ?? '',
            'product_img'               => implode(',', ($p_period['editor']['product_img'] ?? [])),
            'detail'                    => $p_period['editor']['detail'] ?? '',
            'video_cover'               => $p_period['editor']['video_cover'] ?? '',
            'video'                     => $p_period['editor']['video'] ?? '',
            'horizontal_img'            => $p_period['editor']['horizontal_img'] ?? '',
            'created_time'              => time(),
            'copywriting_review_status' => 2,
            'buyer_review_status'       => 3,
            'supplier'                  => $p_period['purchase']['supplier'] ?? '', //供应商
            'supplier_id'               => $p_period['purchase']['supplier_id'] ?? '', //供应商id
            'is_supplier_delivery'      => $p_period['purchase']['is_supplier_delivery'] ?? 0,
            'supplier_delivery_address'      => $p_period['purchase']['supplier_delivery_address'] ?? '',
            'import_type'               => $p_period['purchase']['import_type'] ?? 1,
            'is_presell'                => $p_period['purchase']['is_presell'] ?? 0,
            'creator_id'                => $p_period['document_distribution']['creator_uid'] ?? 0, // 文案UID
            'creator_name'              => $p_period['document_distribution']['creator_name'] ?? '', // 文案名称
            'buyer_id'                  => $p_period['purchase']['purchase_uid'] ?? 0, // 采购UID
            'buyer_name'                => $p_period['purchase']['purchase_name'] ?? '', // 采购名称
            'short_code'                => implode(',', array_column($p_p_items, 'short_code')),
            'product_id'                => implode(',', array_column($p_p_items, 'product_id')),
            'product_main_category'     => implode(',', array_column($p_p_items, 'product_category_name')),
            'product_category'          => implode(',', array_column($p_p_items, 'product_type_name')),
            'country'                   => implode(',', array_column($p_p_items, 'country')),
            'capacity'                  => implode(',', array_column($p_p_items, 'capacity')),
            'product_keyword'           => implode(',', array_column($p_p_items, 'product_keyword')),
            'is_user_filter'            => 0,

            'payee_merchant_id'   => $p_period['purchase']['payee_merchant_id'] ?? null,
            'payee_merchant_name' => $p_period['purchase']['payee_merchant_name'] ?? null,
            'is_gift_box'         => $p_period['purchase']['is_gift_box'] ?? 0,
            'shipping_conditions' => $p_period['purchase']['shipping_conditions'] ?? 0,
            'storage_conditions'  => $p_period['purchase']['storage_conditions'] ?? 0,
            'delivery_time_limit' => $p_period['purchase']['delivery_time_limit'] ?? 0,
            'design_uid'          => $p_period['picture_distribution']['design_uid'] ?? null,
            'design_name'         => $p_period['picture_distribution']['creator_name'] ?? '',
        ];
        // 白酒默认过滤
        if (strpos($data['product_category'], '白酒') !== false) {
            $data['is_user_filter'] = 1;
        }


        (new PeriodsProductInventory())->saveAll($product_inventory);
        (new PeriodsProductEvaluate())->saveAll($product_info);
        (new \app\service\Periods(intval($p_period['purchase']['periods_type'])))->getModel()->save($data);

        return true;
    }

    public
    function editorUpdate($param)
    {

        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        #region 初始化变量
        $now     = time();
        $period  = $origin_period = $this->getPeriod($param);
        $vh_user = \Curl::adminInfo([
            'admin_id' => $param['vh_uid'],
            'field'    => 'id,realname,userid,status,roles'
        ])[$param['vh_uid']] ?? null;
        if (!$vh_user) throw new Exception("未找到管理员");
        #endregion 初始化变量

        try {
            #region 文案编辑内容
            $update = [];
            if (isset($param['title'])) {
                $period['editor']['title'] = $param['title'];
                $update['title']           = $param['title'];
            }
            if (isset($param['brief'])) {
                $period['editor']['brief'] = $param['brief'];
                $update['brief']           = $param['brief'];
            }
            if (isset($param['banner_img'])) {
                $period['editor']['banner_img']               = $param['banner_img'];
                $period['picture_distribution']['banner_img'] = $param['banner_img'];
                $update['banner_img']                         = $param['banner_img'];
            }
            if (isset($param['horizontal_img'])) {
                $period['editor']['horizontal_img']               = $param['horizontal_img'];
                $period['picture_distribution']['horizontal_img'] = $param['horizontal_img'];
                $update['horizontal_img']                         = $param['horizontal_img'];
            }
            if (isset($param['product_img'])) {
                $period['editor']['product_img']               = explode(',', $param['product_img']);
                $period['picture_distribution']['product_img'] = explode(',', $param['product_img']);
                $update['product_img']                         = $param['product_img'];
            }
            if (isset($param['video_cover'])) {
                $period['editor']['video_cover'] = $param['video_cover'];
                $update['video_cover']           = $param['video_cover'];
            }
            if (isset($param['video'])) {
                $period['editor']['video'] = $param['video'];
                $update['video']           = $param['video'];
            }
            if (isset($param['detail'])) {
                $period['editor']['detail'] = $param['detail'];
                $update['detail']           = $param['detail'];
            }
            #endregion

            #region 关联更新
            if (!empty($update)) {
                (new \app\service\Periods($period['purchase']['periods_type']))->update($period['id'], $update);
            }
            #endregion 关联更新

            #region 记录日志
            $period['logs'][] =
                [
                    'uid'         => $vh_user['id'], //操作人ID
                    'name'        => $vh_user['realname'], //操作人姓名
                    'create_time' => $now, //记录时间
                    'operation'   => '编辑文案', //事项
                    'remarks'     => '修改了商品详情', //备注
                ];
            #endregion 记录日志

            $this->model->where('_id', $period['_id'])->update($period);
            $this->create_period_json($period);
        } catch (\Exception $e) {
            $this->model->where('_id', $origin_period['_id'])->update($origin_period);
            Log::error('更新数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('更新数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }

        return $this->success($period);
    }

    public
    function syncPeriods($param)
    {
        $now    = time();
        $period = $this->model->where('id', $param['id'])->find();
        if (!empty($period)) {
            if (isset($param['title'])) {
                $period['editor']['title'] = $param['title'];
            }
            if (isset($param['brief'])) {
                $period['editor']['brief'] = $param['brief'];
            }
            if (isset($param['banner_img'])) {
                $period['editor']['banner_img']               = $param['banner_img'];
                $period['picture_distribution']['banner_img'] = $param['banner_img'];
            }
            if (isset($param['horizontal_img'])) {
                $period['editor']['horizontal_img']               = $param['horizontal_img'];
                $period['picture_distribution']['horizontal_img'] = $param['horizontal_img'];
            }
            if (isset($param['product_img'])) {
                $period['editor']['product_img']               = explode(',', $param['product_img']);
                $period['picture_distribution']['product_img'] = explode(',', $param['product_img']);
            }
            if (isset($param['video_cover'])) {
                $period['editor']['video_cover'] = $param['video_cover'];
            }
            if (isset($param['video'])) {
                $period['editor']['video'] = $param['video'];
            }
            if (isset($param['detail'])) {
                $period['editor']['detail'] = $param['detail'];
            }

            if (isset($param['periods_type'])) {
                $period['purchase']['periods_type'] = $param['periods_type'];
            }
            if (isset($param['import_type'])) {
                $period['purchase']['import_type'] = $param['import_type'];
            }
            if (isset($param['buyer_id'])) {
                $period['purchase']['purchase_uid'] = $param['buyer_id'];
            }
            if (isset($param['buyer_name'])) {
                $period['purchase']['purchase_name'] = $param['buyer_name'];
            }
            if (isset($param['is_supplier_delivery'])) {
                $period['purchase']['is_supplier_delivery'] = $param['is_supplier_delivery'];
            }
            if (isset($param['supplier_delivery_address'])) {
                $period['purchase']['supplier_delivery_address'] = $param['supplier_delivery_address'];
            }
            if (isset($param['is_presell'])) {
                $period['purchase']['is_presell'] = $param['is_presell'];
            }
            if (isset($param['supplier'])) {
                $period['purchase']['supplier'] = $param['supplier'];
            }
            if (isset($param['supplier_id'])) {
                $period['purchase']['supplier_id'] = $param['supplier_id'];
            }
            if (isset($param['payee_merchant_id'])) {
                $period['purchase']['payee_merchant_id'] = $param['payee_merchant_id'];
            }
            if (isset($param['payee_merchant_name'])) {
                $period['purchase']['payee_merchant_name'] = $param['payee_merchant_name'];
            }

            #region 产品数据处理
            if (!empty($param['list'])) {
                $new_items = [];
                $list      = $param['list'];
                $list      = array_column($list, null, 'product_id');
                foreach ($period['items'] as $product_item) {
                    $product_extend = $list[$product_item['product_id']] ?? [];
                    if (!empty($product_extend)) {
                        isset($product_extend['costprice']) && $product_item['costprice'] = $product_extend['costprice']; //成本价
                        isset($product_extend['inventory']) && $product_item['actual_num'] = $product_extend['inventory'];
                        isset($product_extend['erp_id']) && $product_item['erp_id'] = strval($product_extend['erp_id']);
                    }
                    $new_items[] = $product_item;
                }
                $period['items'] = $new_items;
            }
            #endregion 产品数据处理

            $realname = request()->header('vinehoo-vos-name', null);
            if ($realname) {
                $realname = base64_decode($realname);
            }

            #region 记录日志
            $period['logs'][] =
                [
                    'uid'         => request()->header('vinehoo-uid', null), //操作人ID
                    'name'        => $realname, //操作人姓名
                    'create_time' => $now, //记录时间
                    'operation'   => '商品更新', //事项
                    'remarks'     => '商品编辑同步更新', //备注
                ];
            #endregion 记录日志
            $this->model->where('_id', $period['_id'])->update($period);
        }
        return true;
    }


    public function getCompanyByProducts($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

//        $products = Es::name(Es::PRODUCTS)->where([['id', 'in', explode(',', $param['product_ids'])]])->field('id,import_type,corp')->select()->toArray();
        $products = Db::connect('wiki')->name('products')->where([['id', 'in', explode(',', $param['product_ids'])]])->field('id,import_type,corp')->select()->toArray();

        $temp_corp = $corp = [];
        foreach ($products as $product) {
            if (!empty($product['corp'])) {
                $temp_corp[] = $product['corp'];
            }
        }
        //corp 收款公司,英文逗号拼接001科技002云酒008微醒
        //import 进口类型:1-地2-自进口3-跨境

        if (!empty($temp_corp)) {
            $corp = array_values(array_unique(explode(',', implode(',', $temp_corp))));
        }

        return $this->success(['corps' => $corp]);
    }

    public function supplierProductList($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        $list = Db::connect('wiki')->name('supplier_product')
            ->field('product_id,id as sku_id,supplier,supplier_id')
            ->where('short_code', $param['short_code'])->select()->toArray();

        $product_field = "id,capacity,cn_product_name,en_product_name,cn_product_name,country_name_cn,product_type_name,product_category,short_code,en_product_name,warehouse,warehouse_id,product_name,frontal_label_img,cn_back_label_img,en_back_label_img,package_img,product_attachment";
        $product       = Es::name(Es::PRODUCTS)->where([['short_code', '==', $param['short_code']]])->field($product_field)->find();

        foreach ($list as &$value) {
            $value['warehouse']         = $product['warehouse'] ?? null;//仓库
            $value['warehouse_id']      = $product['warehouse_id'] ?? null;//仓库ID
            $value['product_name']      = $product['cn_product_name'] = $product['cn_product_name'] ?? null;//中文名
            $value['en_product_name']   = $product['en_product_name'] ?? null;//英文名
            $value['short_code']        = $product['short_code'] ?? null;//短码
            $value['product_category']  = $product['product_category'] ?? null;//大类ID
            $value['product_type_name'] = $product['product_type_name'] ?? null;//小类中文
            $value['country']           = $product['country_name_cn'] ?? null;//国家
            $value['capacity']          = $product['capacity'] ?? null;//规格
            $value['costprice']         = null;//成本价
            $value['price']             = null;//售价
            $value['num']               = null;//售卖数量
            $value['actual_num']        = null;//实际库存
        }

        return $this->success(compact('list'));
    }

    /**
     * @方法描述: 修改UI设计师
     * <AUTHOR>
     * @Date 2023/11/6 10:32
     * @param $period
     * @param $param
     * @param $extend
     * @return array
     * @throws Exception
     */
    public function uiPersonnel($period, $param, $extend)
    {
        $node_is_done = 0; //节点是否完成 1 = 完成,
        $status       = self::STATUS_PROCESSING; //完成状态
        $remark       = '修改UI负责人: ';
        $not_remark   = false; //不记录log

        $users   = \Curl::adminInfo([
            'admin_id' => "{$param['vh_uid']},{$param['ui_uid']}",
            'field'    => 'id,realname,userid,status,roles'
        ]) ?? [];
        $vh_user = $users[$param['vh_uid']] ?? null;
        $ui_user = $users[$param['ui_uid']] ?? null;
        if (!$vh_user) throw new Exception("未找到管理员");
        if (!$ui_user) throw new Exception("未找到UI人员");
        $vh_user['role_ids']                           = implode(',', array_column($vh_user['roles'], 'id'));
        $ui_user['role_ids']                           = implode(',', array_column($ui_user['roles'], 'id'));
        $period['picture_distribution']['design_uid']  = $ui_user['id'];
        $period['picture_distribution']['design_name'] = $ui_user['realname'];
        $remark                                        .= $ui_user['realname'];

        #region 更新数据组装
        $p_current_node_id  = $param['target_node_id'];
        $current_node_infos = array_column($period['current_node_info'] ?? [], null, 'id');
        $current_node_info  = $current_node_infos[$p_current_node_id] ?? [];
        if (empty($current_node_info)) {
            throw new Exception("未找到可操作的当前节点!");
        }


        if (in_array(($period['purchase']['periods_type'] ?? null), [0, 2, 3, 4])) {
            //非秒发 修改参与人
            $participant = [ //参与人
                [
                    "genre"     => 1, //用户类型(写死1)
                    'we_com_id' => strval($ui_user['userid']),
                    'name'      => strval($ui_user['realname']),
                    'vos_uid'   => intval($ui_user['id']),
                    "role_id"   => strval($ui_user['roles'][0]['id'] ?? ''),
                ],
            ];

            $request_data = [
                "code"        => empty($param['instance_id']) ? strval($period['id']) : strval($param['instance_id']),
                "node_id"     => $param['target_node_id'],
                "process"     => $current_node_info['process_info'],
                "participant" => $participant
            ];
        } else {
            $request_data = [
                "code"        => empty($param['instance_id']) ? strval($period['id']) : strval($param['instance_id']),
                "node_id"     => $param['target_node_id'],
                "process"     => [
                    [
                        "genre"     => 1, //用户类型(写死1)
                        'we_com_id' => strval($ui_user['userid']),
                        'name'      => strval($ui_user['realname']),
                        'vos_uid'   => intval($ui_user['id']),
                        "role_id"   => strval($ui_user['roles'][0]['id'] ?? ''),
                    ],
                ],
                "participant" => $current_node_info['participant']
            ];
        }
        #endregion
        \Curl::flowInstanceProcessModify($request_data);

        return [$period, compact('node_is_done', 'remark', 'status', 'not_remark')];
    }
}



