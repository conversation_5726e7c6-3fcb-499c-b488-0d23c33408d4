<?php

namespace app\service\v3;

use app\BaseService2;
use app\ErrorCode;
use app\model\ParametersModel;
use app\service\es\Es;
use think\Exception;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;
use think\facade\Validate;
use app\validate\ParametersValidate;

/**
 * 商品信息参数配置
 * Class ParametersService
 * @package app\service\v3
 */
class ParametersService extends BaseService2
{
    protected $periods_type_name = [
        0 => '闪购',
        1 => '秒发',
        2 => '跨境',
        3 => '尾货',
    ];

    protected $model, $validate, $select_with;

    public function __construct()
    {
        $this->model = new ParametersModel();
        $this->validate    = ParametersValidate::class;
    }

    /**
     * @方法描述: 获取列表数据
     * <AUTHOR>
     * @Date 2024/01/18
     * @param $param
     * @return array|bool|mixed
     */
    public function list($param)
    {
        //region 获取数据
        Db::startTrans();
        try {
            $model = $this->model->select()->toArray();
            $list  = [];
            foreach ($model as $v) {
                if (empty($list[$v['periods_type']])) {
                    $list[$v['periods_type']] = [
                        'periods_type' => $v['periods_type'],
                        'periods_type_name' => $this->periods_type_name[$v['periods_type']],
                        'list' => []
                    ];
                }
                $list[$v['periods_type']]['list'][] = $v;
            }
            $data  = [
                'list'  => $list
            ];

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('查询数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('查询数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($this->listDataProcessing($data));
    }
    /**
     * @方法描述: 保存数据
     * <AUTHOR>
     * @Date 2024/01/18
     * @param $param
     * @return array|bool|mixed
     */
    public function save($data)
    {
        $param = $data['param'];
        $time = time();
        if (empty($param) || !is_array($param)) {
            return $this->failed('数据格式错误', ErrorCode::EXEC_ERROR);
        }
        
        #region验证
        foreach ($param as $k => $v) {
            $validate = $this->check(__FUNCTION__, $v);
            if ($validate !== true) {
                return $validate;
            }
            if (isset($v['created_time'])) {
                unset($param[$k]['created_time']);
            }
            $param[$k]['update_time'] = $time;
        }
        //endregion

        //region 保存数据
        Db::startTrans();
        try {
            $res = $this->model->saveAll($param);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('保存数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('保存数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success();
    }

    /**
     * @方法描述: 获取频道数据列表数据
     * <AUTHOR>
     * @Date 2024/01/18
     * @param $param
     * @return array|bool|mixed
     */
    public function channelList($param)
    {
        #region验证
        $validate = $this->check('channel_list', $param);
        if ($validate !== true) {
            return $validate;
        }
        //endregion

        //region 获取数据
        Db::startTrans();
        try {
            
            $list = $this->model
                ->where('periods_type', $param['periods_type'])
                ->where('is_display', 1)
                ->column('name,field_value,is_display,is_default');
            $data  = [
                'list'  => $list
            ];

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('查询数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('查询数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($this->listDataProcessing($data));

    }

    
}
