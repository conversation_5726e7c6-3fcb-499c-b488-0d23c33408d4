<?php

namespace app\service\v3;

use app\BaseService2;
use app\model\PurchaseOrdernoItems;
use app\validate\PurchaseOrdernoItemsValidate;

/**
 * 采购单表
 * Class PurchaseOrdernoItemsService
 * @package app\service\v3
 */
class PurchaseOrdernoItemsService extends BaseService2
{
    public function __construct()
    {
        $this->model       = new PurchaseOrdernoItems;
        $this->validate    = PurchaseOrdernoItemsValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/11/14 17:09
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {
            
            #region EQ id id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //id
            }
            #endregion
            
            #region EQ purchase_orderno_id 采购单ID
            if (isset($param['purchase_orderno_id']) && strlen($param['purchase_orderno_id']) > 0) {
                $query->where('purchase_orderno_id', "=", $param['purchase_orderno_id']); //采购单ID
            }
            #endregion
            
            #region LIKE short_code 简码
            if (isset($param['short_code']) && strlen($param['short_code']) > 0) {
                $query->where('short_code', "LIKE", "{$param['short_code']}%"); //简码
            }
            #endregion
            
            #region LIKE billing_name 开票名称
            if (isset($param['billing_name']) && strlen($param['billing_name']) > 0) {
                $query->where('billing_name', "LIKE", "{$param['billing_name']}%"); //开票名称
            }
            #endregion
            
            #region LIKE en_product_name 英文名
            if (isset($param['en_product_name']) && strlen($param['en_product_name']) > 0) {
                $query->where('en_product_name', "LIKE", "{$param['en_product_name']}%"); //英文名
            }
            #endregion
            
            #region LIKE unit 单位
            if (isset($param['unit']) && strlen($param['unit']) > 0) {
                $query->where('unit', "LIKE", "{$param['unit']}%"); //单位
            }
            #endregion
            
            #region LIKE capacity 规格
            if (isset($param['capacity']) && strlen($param['capacity']) > 0) {
                $query->where('capacity', "LIKE", "{$param['capacity']}%"); //规格
            }
            #endregion
            
            #region LIKE remark 备注
            if (isset($param['remark']) && strlen($param['remark']) > 0) {
                $query->where('remark', "LIKE", "{$param['remark']}%"); //备注
            }
            #endregion
            
            #region LIKE period 期数,多个之间/分割
            if (isset($param['period']) && strlen($param['period']) > 0) {
                $query->where('period', "LIKE", "{$param['period']}%"); //期数,多个之间/分割
            }
            #endregion
            
            #region LIKE number 数量
            if (isset($param['number']) && strlen($param['number']) > 0) {
                $query->where('number', "LIKE", "{$param['number']}%"); //数量
            }
            #endregion
            
            #region EQ price 含税单价
            if (isset($param['price']) && strlen($param['price']) > 0) {
                $query->where('price', "=", $param['price']); //含税单价
            }
            #endregion
            
            #region EQ tax_rate 税率
            if (isset($param['tax_rate']) && strlen($param['tax_rate']) > 0) {
                $query->where('tax_rate', "=", $param['tax_rate']); //税率
            }
            #endregion
            
            #region IN is_gift 是否赠品
            if (isset($param['is_gift']) && strlen($param['is_gift']) > 0) {
                $query->where('is_gift', "IN", $param['is_gift']); //是否赠品:0=否,1=是
            }
            #endregion
            
        };
    }

}



