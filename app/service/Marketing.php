<?php

namespace app\service;

use app\ErrorCode;
use app\model\PeriodsFlash;
use think\Response;
use think\facade\Db;

class Marketing {

    /**
     * 获取期数曝光方式
     * @param array $period_ids 查询期数
     * @return array
     */
    public function GetExposureMethod($period_ids) {
        $result = [];
        if (empty($period_ids)) {
            return $result;
        }
        $time = time();
        $map = [];

        // 统一处理函数
        $handle = function($data, $typeKey, $periodKey, $statusKey = 'status', $extra = null) use (&$map) {
            foreach ($data as $v) {
                $period = $v[$periodKey];
                $status = isset($v[$statusKey]) ? $v[$statusKey] : 0;
                if ($extra && is_callable($extra)) {
                    $status = $extra($v, $status);
                }
                if (empty($map[$typeKey][$period])) {
                    $map[$typeKey][$period] = [
                        'type' => $typeKey,
                        'status' => $status,
                    ];
                } else if ($map[$typeKey][$period]['status'] == 0 && $status == 1) {
                    $map[$typeKey][$period]['status'] = $status;
                }
            }
        };

        // 卡片
        $card = Db::table('vh_marketing.vh_card')
            ->alias('c')
            ->leftJoin('vh_marketing.vh_card_goods_live cg', 'cg.cid = c.id')
            ->where('cg.type', 1)
            ->whereIn('cg.relation_id', $period_ids)
            ->order('cg.id desc')
            ->column('c.status,cg.relation_id');
        $handle($card, '卡片', 'relation_id');

        // 栏目
        $column = Db::table('vh_marketing.vh_column')
            ->alias('c')
            ->leftJoin('vh_marketing.vh_column_goods cg', 'cg.cid = c.id')
            ->whereIn('cg.period', $period_ids)
            ->order('cg.id desc')
            ->column('c.status,cg.period');
        $handle($column, '栏目', 'period');

        // 专题活动
        $special_activity = Db::table('vh_marketing.vh_special_activity')
            ->alias('c')
            ->leftJoin('vh_marketing.vh_special_activity_goods cg', 'cg.activity_id = c.id')
            ->whereIn('cg.periods', $period_ids)
            ->order('cg.id desc')
            ->column('c.status,c.start_at,c.end_at,cg.periods');
        $handle($special_activity, '专题活动', 'periods', 'status', function($v, $status) use ($time) {
            return ($v['status'] == 2 && $v['start_at'] <= $time && $v['end_at'] >= $time) ? 1 : 0;
        });

        // 广告相关
        $ad_extends = Db::table('vh_marketing.vh_ad_path_extends')
            ->alias('e')
            ->leftJoin('vh_marketing.vh_ad a', 'a.id=e.aid')
            ->where('e.pid', 7)
            ->where('e.peid', 11)
            ->where(function ($query) use ($period_ids) {
                $query->whereOr([
                    ['e.ios_val', 'in', $period_ids],
                    ['e.android_val', 'in', $period_ids],
                    ['e.mini_val', 'in', $period_ids],
                    ['e.h5_val', 'in', $period_ids],
                    ['e.pc_val', 'in', $period_ids],
                ]);
            })
            ->column('a.status,a.type,a.start_time,a.end_time,e.ios_val,e.android_val,e.mini_val,e.h5_val,e.pc_val');
        foreach ($ad_extends as $v) {
            $period = 0;
            foreach (['ios_val', 'android_val', 'mini_val', 'h5_val', 'pc_val'] as $field) {
                if (!empty($v[$field])) {
                    $period = $v[$field];
                    break;
                }
            }
            $typeMap = [1 => 'banner', 2 => '开屏', 3 => '弹窗', 4 => '胶囊'];
            $key = $typeMap[$v['type']] ?? '未知';
            $status = ($v['status'] == 1 && $v['start_time'] <= $time && $v['end_time'] >= $time) ? 1 : 0;
            if (empty($map[$key][$period])) {
                $map[$key][$period] = [
                    'type' => $key,
                    'status' => $status,
                ];
            } else if ($map[$key][$period]['status'] == 0 && $status == 1) {
                $map[$key][$period]['status'] = $status;
            }
        }

        // 广告位
        $ad = Db::table('vh_marketing.vh_ad')
            ->where('type', 5)
            ->where('modul', 4)
            ->whereIn('modul_id', $period_ids)
            ->column('status,start_time,end_time,modul_id');
        $handle($ad, '广告位', 'modul_id', 'status', function($v, $status) use ($time) {
            return ($v['status'] == 1 && $v['start_time'] <= $time && $v['end_time'] >= $time) ? 1 : 0;
        });

        // 曝光瀑布流
        $ev_sort = Db::name('product_sort_by_ev')
            ->whereIn('period', $period_ids)
            ->column('start_time,end_time,period,type');
        foreach ($ev_sort as $v) {
            $status = ($v['start_time'] <= $time && $v['end_time'] >= $time) ? 1 : 0;
            $typeMap = [1 => '固定投放', 2 => '滑动投放'];
            $key = $typeMap[$v['type']] ?? '未知';
            $period = $v['period'];
            if (empty($map[$key][$period])) {
                $map[$key][$period] = [
                    'type' => $key,
                    'status' => $status,
                ];
            } else if ($map[$key][$period]['status'] == 0 && $status == 1) {
                $map[$key][$period]['status'] = $status;
            }
        }

        // 整理结果
        foreach ($map as $v) {
            foreach ($v as $kk => $vv) {
                $result[$kk][] = $vv;
            }
        }
        return $result;
    }

}
