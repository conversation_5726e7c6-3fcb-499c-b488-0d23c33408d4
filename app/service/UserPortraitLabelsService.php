<?php

namespace app\service;

use app\BaseService;
use app\model\UserPortraitLabels;
use app\validate\UserPortraitLabelsValidate;

/**
 * 用户画像标签
 * Class UserPortraitLabelsService
 * @package app\service
 */
class UserPortraitLabelsService extends BaseService
{
    public function __construct()
    {
        $this->model       = new UserPortraitLabels;
        $this->validate    = UserPortraitLabelsValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/05/24 10:22
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {
            
            #region EQ id 主键id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //主键id
            }
            #endregion
            
            #region EQ user_portrait_id 用户画像ID
            if (isset($param['user_portrait_id']) && strlen($param['user_portrait_id']) > 0) {
                $query->where('user_portrait_id', "=", $param['user_portrait_id']); //用户画像ID
            }
            #endregion
            
            #region LIKE name 标签
            if (isset($param['name']) && strlen($param['name']) > 0) {
                $query->where('name', "LIKE", "{$param['name']}%"); //标签
            }
            #endregion
            
        };
    }

}



