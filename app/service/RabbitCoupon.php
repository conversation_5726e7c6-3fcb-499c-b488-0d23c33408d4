<?php

namespace app\service;

use app\ErrorCode;
use app\model\PeriodsRabbitCoupon;
use think\Exception;
use think\facade\Db;
use think\facade\Log;
use think\Response;

class RabbitCoupon {

    /**
     * 添加商品
     * @param array $params
     * @return int
     */
    public function create(array $params): ?int
    {
        $result = PeriodsRabbitCoupon::create($params);
        return $result->id ?? false;
    }

    /**
     * 根据 id 获取商品信息
     * @param int $id
     * @param string $field
     * @return array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOne(int $id, string $field = '*'): ? array {
        $result = PeriodsRabbitCoupon::field($field)->find($id);
        return  $result ? $result->toArray() : null;
    }

    /**
     * 更新商品数据
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update(array $params)
    {
        // 更新商品
        unset($params['creator_id'], $params['creator_name']);
        // 如果是驳回状态改为待审核
        if (isset($params['onsale_review_status']) && $params['onsale_review_status'] == '4') {
            $params['onsale_review_status'] = 1;
        }
        $update = PeriodsRabbitCoupon::update($params);
        if (!$update) {
            return serviceReturn(false, $params['id'], '更新商品失败');
        }

        return serviceReturn(true, $update->id);
    }

    public function rabbitExchange($params)
    {
        $user=[
            'rabbit'=>0,
            'telephone_encrypt'=>'',
        ];
        if ($params['uid']) {
            // 查询用户
            $user_url = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
            $user_url = $user_url . '?uid=' . $params['uid'] . '&field=rabbit,telephone_encrypt';
            $user_list = get_url($user_url);
            $user_list = get_interior_http_response($user_list);
            $user_list = $user_list['list'] ?? [];
            $user = empty($user_list[0])?[]:$user_list[0];
        }
        if (empty($user)) {
            throw new Exception('未检测到用户信息');
        }
        if ( $user['rabbit'] == 0) {
            throw new Exception('抱歉，您的兔头数量不足',20114);
        }
        $rabbitCoupon= Db::name("periods_rabbit_coupon")->field('id,coupon_id,title,rabbit_price,price,inventory')->where([['onsale_status','=',2],['is_delete','=',0],['id','=',$params['id']]])->find();
        if (empty($rabbitCoupon)) {
            throw new Exception('未检测到有效优惠券');
        }
        //验证用户兔头数量
        if ($rabbitCoupon['inventory'] <=0) {
            throw new Exception('兔头商品库存不足',10001);
        }
        if (intval($user['rabbit']) < intval($rabbitCoupon['rabbit_price']) ) {
            throw new Exception('抱歉，您的兔头数量不足兑换',20114);
        }
        //验证优惠券是否在发放时间
        Db::startTrans();
        try {
            //优惠券发放
            $url = env('ITEM.COUPON_URL') . '/coupon/v3/coupon/grant';
            $insertCoupon=[
                'uids'=>[$params['uid']],
                'coupon_id'=>$rabbitCoupon['coupon_id'],
                'remark'=>"兔头兑换优惠券",
            ];
            $insertCoupon = post_url($url,$insertCoupon);
            $insertCoupon = json_decode($insertCoupon, true);
            if ($insertCoupon['error_code'] != '0') {
                Db::rollback();
                throw new Exception('优惠券兑换失败:'.$insertCoupon['error_msg'],$insertCoupon['error_code']);
            }
            $expire_time=$insertCoupon['data'];
           //$uu="http://test-wine.wineyun.com/couponrabbits-issue/v3/py-couponrabbits-issue/AddReward";
//            $uu=env("ITEM.COUPON_RABBITS_ISSUE_URL")."/couponrabbits-issue/v3/py-couponrabbits-issue/AddReward";
//            $phone_decode = cryptionDeal(2, [$user['telephone_encrypt']]);
//            if (empty($phone_decode)) {
//                Db::rollback();
//                throw new Exception("手机号获取失败:" . $user['telephone_encrypt']);
//            }
//            $telephone=$phone_decode[$user['telephone_encrypt']];
//            $couponRabbits=[
//                'platform'=>'v3',
//                'type'=>'coupon',
//                'issue_num'=>6,
//                'coupon_id'=>[$rabbitCoupon['coupon_id']],
//                'callback'=>'',
//                'uid'=>intval($params['uid']),
//                'telephone'=>$telephone,
//                'activity_name'=>'兔头兑换优惠券',
//            ];
//            $data= httpPostString($uu,json_encode($couponRabbits));
//            if ($data['error_code'] !=0 || $data['error_msg'] != 'success') {
//                Log::error($data);
//                Db::rollback();
//                throw new Exception('优惠券兑换失败:'.$data['error_msg']);
//            }
//            $expire_time=$data['data'];
            //扣除用户兔头数量
            $url = env('ITEM.USER_URL') . '/user/v3/user/updateAssets';
            $changeRecord=[
                'uid'=>$params['uid'],
                'change_type'=>2,
                'source'=>4,//兑换商品
                'reason'=>"兔头兑换",
                'rabbit'=>$rabbitCoupon['rabbit_price']
            ];
            $userResult = post_url($url,$changeRecord);
            $userResult = json_decode($userResult, true);
            if ($userResult['error_code'] != '0') {
                Log::error($userResult);
                Db::rollback();
                throw new Exception('兔头数量扣除失败:'.$userResult['error_msg']);
            }
            //兑换记录
            $url = env('ITEM.COUPON_URL') . '/coupon/v3/rabbitexchange/add';
            $exchangeData=[
                'period_id'=>$params['id'],
                'uid'=>$params['uid'],
                'coupon_id'=>$rabbitCoupon['coupon_id'],
                'title'=>$rabbitCoupon['title'],
                'price'=>$rabbitCoupon['price'],
                'rabbit_price'=>$rabbitCoupon['rabbit_price'],
                'created_time'=>time(),
            ];
            $exchange_time=$exchangeData['created_time'];
            $exchangeCoupon = post_url($url,$exchangeData);
            $exchangeCoupon = json_decode($exchangeCoupon, true);
            if ($exchangeCoupon['error_code'] != '0') {
                Log::error($exchangeData);
                Db::rollback();
                throw new Exception('兑换失败');
            }
            $exchange=$exchangeCoupon['data'];

            $result=[
                "expire_time"=>$expire_time?date("Y-m-d H:i:s",$expire_time):'',
                "exchange_time"=>date("Y-m-d H:i:s",$exchange_time),
                "exchange"=>$exchange,
                "title"=>$rabbitCoupon['title'],
                "price"=>$rabbitCoupon['price'],
                "rabbit_price"=>$rabbitCoupon['rabbit_price'],
            ];
            #减库存 增加已购
            $periodsPlusMinusData=[
                'period'=>$params['id'],
                'type'=>"order",
                'periods_type'=>5,
                'count'=>1
            ];
            $periodsPlusMinusResult=(new Periods(5))->periodsPlusMinus($periodsPlusMinusData);
            if ( $periodsPlusMinusResult && $periodsPlusMinusResult['status'] !=true) {
                Log::error($periodsPlusMinusResult);
                Db::rollback();
                throw new Exception($periodsPlusMinusResult['msg']);
            }
            (new PeriodsRabbitCoupon())->reduceNums($params['id'],'inventory');

            Db::commit();
            return  $result;
        } catch (\Exception $e) {
            Db::rollback();
            throw new Exception($e->getMessage());
        }
    }


    /**
     * 返回最高金额可用兔头优惠券
     * @return PeriodsRabbitCoupon|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getRabbitCoupon() {
        return PeriodsRabbitCoupon::where([
            ['onsale_status', '=', 2],
            ['sell_time', '<', time()],
            ['sold_out_time', '>', time()]
        ])->order('price', 'desc')->find();
    }

}
