<?php

// This file is auto-generated, don't edit it. Thanks.
namespace app\service;

use AlibabaCloud\SDK\Dm\V20151123\Dm;
use \Exception;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils;

use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Dm\V20151123\Models\SingleSendMailRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;

class AliSendEmail {

    /**
     * 使用AK&SK初始化账号Client
     * @param string $accessKeyId
     * @param string $accessKeySecret
     * @return Dm Client
     */
    public static function createClient($accessKeyId, $accessKeySecret){
        $config = new Config([
            // 您的 AccessKey ID
            "accessKeyId" => $accessKeyId,
            // 您的 AccessKey Secret
            "accessKeySecret" => $accessKeySecret
        ]);
        // 访问的域名
        $config->endpoint = "dm.aliyuncs.com";
        return new Dm($config);
    }

    /**
     * @return void
     */
    public static function main(array $send_data){
//        $send_data = [
//            "accountName" => "<EMAIL>",
//            "addressType" => 1,
//            "replyAddress" => "<EMAIL>",
//            "replyAddressAlias" => "酒云网",
//            "fromAlias" => "酒云网",
//            "textBody" => "期数【商品名称】预计发货时间：XXXX ，请及时订货",
//            "htmlBody" => "期数【商品名称】预计发货时间：XXXX ，请及时订货",
//            "subject" => "预售商品订货通知",
//            "toAddress" => "<EMAIL>",
//            "replyToAddress" => "true"
//        ];
        $client = self::createClient("LTAI5tAb7X7D7JzQ95UD5wz3",
            "******************************");
        $singleSendMailRequest = new SingleSendMailRequest($send_data);
        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            return $client->singleSendMailWithOptions($singleSendMailRequest, $runtime);
        }
        catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            // 如有需要，请打印 error
            Utils::assertAsString($error->message);
        }
    }
}
//$path = __DIR__ . \DIRECTORY_SEPARATOR . '..' . \DIRECTORY_SEPARATOR . 'vendor' . \DIRECTORY_SEPARATOR . 'autoload.php';
//if (file_exists($path)) {
//    require_once $path;
//}
//Sample::main(array_slice($argv, 1));