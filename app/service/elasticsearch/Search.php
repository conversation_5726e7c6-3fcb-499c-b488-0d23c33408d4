<?php
namespace app\service\elasticsearch;

/**
 * 创建es mapping
 * Class MappingService
 * @package App\Services\Elasticsearch
 */

use \app\service\elasticsearch\searches\Search as SearchInterface;

class Search
{
    private $search;
    private $response;
    public function setSearch(SearchInterface $search)
    {
        $this->search = $search;
        return $this;
    }
    public function setIndex($index)
    {
        $this->index = $index;
        return $this;
    }

    public function getResponse()
    {
        return $this->response;
    }

    public function doSearch()
    {
        $data = $this->extractArguments();
        $this->performRequest($data);
        return $this;
    }

    public function toArray()
    {
        $response = $this->response;

        if (empty($response)) {
            return [];
        }

        if (array_key_exists('_shards', $response)
            && !empty($response['_shards']) && is_array($response['_shards'])
            && array_key_exists('failed', $response['_shards']) && $response['_shards']['failed'] == 0) {
            if (array_key_exists('hits', $response)) {
                $data['total'] = $response['hits']['total'];
                $data['data'] = [];
                if (array_key_exists('hits', $response['hits'])) {
                    $hits = $response['hits']['hits'];
                    foreach ($hits as $item) {
                        if (array_key_exists('_source', $item)) {
                            $value = $item['_source'];
                            if (array_key_exists('highlight', $item)) {
                                $value['es_highlight'] = $item['highlight'];
                            }
                            array_push($data['data'], $value);
                        }
                    }
                    return $data;
                }
            }
        }
        return []; // 不影响调用方业务，不抛异常
    }

    private function extractArguments()
    {
        $index = $this->search->getIndex();
        $index = $this->parseIndex($index);
        $body = $this->search->getBody();

        $data = [
            'index' => $index,
            'body'  =>  $body,
        ];
        return $data;
    }

    private function performRequest($data)
    {
        $client = Client::getInstance();
        $response = $client->search($data);

        $this->response = $response;
        return $this;
    }


    private function parseIndex($index)
    {
        $esPrefix = env('ES.PREFIX');

        foreach ($index  as &$value){
            $value = $esPrefix.$value;
        }

        return $index;
    }
}