<?php
namespace app\service\elasticsearch\searches;

/**
 * 全文 查询
 * Class BoolSearch
 * @package App\Services\Elasticsearch\Searches
 */

use \app\service\elasticsearch\searches\Search as SearchInterface;

class FullSearch implements SearchInterface
{

    private $input;
    private $index = [];
    private $keyword = "";
    private $highlightFields = ["goods_name"]; // 高亮字段
    private $preTags = ["<em>"]; // 高亮html标签前部
    private $postTags = ["</em>"]; // 高亮html标签后部
    private $range = [];
    private $term = [];
    private $sort = [];
    private $page = 1; // 第一页开始
    private $limit = null;

    private $match = [];

    public function setInput($input)
    {
        $this->input = $input;


        $this->setIndex($input['index']);
        $this->setKeyword($input['keyword']);

        if (!empty($input['highlight_fields'])) {
            $this->setHighlightFields($input['highlight_fields']);
        } else {
            $this->setHighlightFields($this->highlightFields);
        }
        !empty($input['highlight_fields']) and $this->setHighlightFields($input['highlight_fields']);
        !empty($input['pre_tags']) and $this->setPreTags($input['pre_tags']);
        !empty($input['post_tags']) and $this->setPostTags($input['post_tags']);
        !empty($input['page']) and $this->setPage($input['page']);
        !empty($input['limit']) and $this->setLimit($input['limit']);
        !empty($input['range']) and $this->setRange($input['range']);
        !empty($input['term']) and $this->setTerm($input['term']);
        !empty($input['sort']) and $this->setSort($input['sort']);
        !empty($input['match']) and $this->setMatch($input['match']);
    }
    public function setKeyword($keyword) 
    {
        $this->keyword = $keyword;
    }

    public function setHighlightFields(array $highlightFields)
    {
        $emptyObject = new \stdClass();
        $this->highlightFields = array_fill_keys($highlightFields, $emptyObject);
    }

    public function setPreTags(array $preTags)
    {
        $this->preTags = $preTags;
    }

    public function setPostTags(array $postTags)
    {
        $this->postTags = $postTags;
    }

    public function setRange(array $range)
    {
        $this->range = $range;
    }

    public function setTerm(array $term)
    {
        $this->term = $term;
    }

    public function setSort(array $sort)
    {
        $this->sort = $sort;
    }

    public function setIndex($index)
    {
        $this->index = $index;
        return $this;
    }

    public function setMatch(array $match)
    {
        $this->match = $match;
        return $this;
    }

    public function addRange($range)
    {
        array_push($this->range, $range);
        return $this;
    }

    public function addSort($sort)
    {
        array_push($this->sort, $sort);

        return $this;
    }

    public function addMatch($match)
    {
        array_push($this->match, $match);
    }

    public function setPage($page)
    {
        $this->page = $page;
        return $this;
    }

    public function setLimit($limit)
    {
        $this->limit = $limit;
        return $this;
    }

    public function getIndex()
    {
        return $this->index;
    }

    public function getBody()
    {
        $body = $filter = [];

        $highlight = [
            'require_field_match' => false, // 多个字段可高亮 默认是查询字段
            'fields'=>$this->highlightFields // 高亮字段
        ];
        if (!empty($this->preTags)) {
            $highlight['pre_tags'] = $this->preTags;
        }
        if (!empty($this->postTags)) {
            $highlight['post_tags'] = $this->postTags;
        }
        $body['highlight'] = $highlight; // body高亮
        if (!is_null($this->limit)) {   // body limit
            $body['size'] = $this->limit;
        }
        if (!is_null($this->page) && !is_null($this->limit)) { // body offset
            $body['from'] = ($this->page - 1) * $this->limit;
        } else {
            $body['from'] = 0;
        }

        if (!empty($this->sort)) {
            $sort = [];

            foreach ($this->sort as $key=>$value) {
                array_push($sort, [
                    $key => [
                        'order'=> empty($value) ? 'desc' : $value
                    ]
                ]);
            }

            $body['sort'] = $sort; // body排序
        }

        if (!empty($this->range)) {
            foreach ($this->range as $key=>$value) {
                array_push($filter, [
                    'range' => $value
                ]);
            }
        }

        if (!empty($this->term)) {
            foreach ($this->term as $key=>$value) {
                array_push($filter, [
                    'term' => $value
                ]);
            }
        }

        $query = [
            'bool'=>[
                'must'=>[
                    ['bool'=>[
                        'filter'=>$filter
                    ]]
                ]
            ]
        ];

        if (!empty($this->keyword)) {
            $multiMatch = [
                'query' =>  $this->keyword,
            ];
            array_push($query['bool']['must'], [
                'multi_match' => $multiMatch,
            ]);
        }

        if (!empty($this->match)) {
            foreach ($this->match as $item) {
                array_push($query['bool']['must'], [
                    'match'=>$item
                ]);
            }
        }
        $body['query'] = $query; // body 查询

        return $body;
    }
}