<?php
namespace app\service\elasticsearch;

use app\service\elasticsearch\types\Document;
use app\service\elasticsearch\Search;
use app\service\elasticsearch\searches\BoolSearch;
use app\service\elasticsearch\searches\FullSearch;

/**
 * Class Service
 * @package App\Services
 */

class ElasticSearchService
{
    static function sendOperate($data, $operate = 'create')
    {
        return (new Document())->sendOperate($data, $operate)->$operate();
    }

    /**
     * 获取文档列表
     * @param $data
     */
    static function getDocumentList($input, $type = "bool")
    {

        $response = self::search($input, $type);

        return $response;// 不影响调用方业务，不抛异常
    }
    
    /**
     * 查询
     * @param array $input
     * @param string $type
     * @return array|null
     */
    static function search($input = [], $type = 'bool')
    {
        $response = null;
        switch ($type) {
            case "other":break;
            case "full":
                $response = self::fullSearchDocument($input);
                break;
            default:
                $response = self::synthesizeBoolSearchDocument($input);
        }
        return $response;
    }
    /**
     * bool 查询
     * @param array $input
     * @return array
     */
    static function synthesizeBoolSearchDocument($input = [])
    {
        $booleSearch = new BoolSearch();
        $booleSearch->setInput($input);

        $search = new Search();
        $response = $search->setSearch($booleSearch)->doSearch()->toArray();
        return $response;
    }

    /**
     * 全文 查询
     * @param array $input
     * @return $this|array
     */
    static function fullSearchDocument(array $input)
    {
        if (empty($input) || !is_array($input) || empty($input['index'])) {
            return [];
        }

        $fullSearch = new FullSearch();
        $fullSearch->setInput($input);
        $search = new Search();
        $response = $search->setSearch($fullSearch)->doSearch()->toArray();
        return $response;
    }
}