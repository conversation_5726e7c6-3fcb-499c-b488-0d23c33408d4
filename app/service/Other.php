<?php

namespace app\service;

use app\ErrorCode;
use app\model\Fictitious;
use app\model\GeneratorId;
use app\model\GroupOrderInventory;
use app\model\PeriodsFilter;
use app\model\PeriodsFlash;
use app\model\PeriodsPraiseRecord;
use app\model\PeriodsProductEvaluate;
use app\model\PeriodsProductInventory;
use app\model\PeriodsRemark;
use app\model\PeriodsStatusChangeRecord;
use app\model\PeriodsTaskLog;
use app\model\PeriodsTsTemplate;
use app\model\PeriodsUserCollection;
use think\facade\Db;
use think\facade\Log;


class Other {


    /**
     * 获取可筛选商品国家、类型、关键词
     * @return PeriodsFilter[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function filterList() {
        return PeriodsFilter::select();
    }

    /**
     * 仓库列表
     * @return Fictitious[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function fictitiousList(array $params) {
        $where[] = ['fictitious_pid', '>', 0];
        if (isset($params['channel_types']) && $params['channel_types'] != '') {
            $where[] = ['channel_types', 'like', '%'.$params['channel_types'].'%'];
        }
        if (isset($params['fictitious_name']) && $params['fictitious_name'] != '') {
            $where[] = ['fictitious_name', 'like', '%'.$params['fictitious_name'].'%'];
        }
        if (isset($params['is_my']) && $params['is_my'] != '') {
            $where[] = ['is_my', '=', $params['is_my']];
        }
        return Fictitious::where($where)->select();
    }

    /**
     * 添加仓库
     * @param array $params
     * @return array
     */
    public function fictitiousAdd(array $params) {
        $params['created_time'] = time();
        $params['update_time'] = time();
        $exixts = Fictitious::where('fictitious_name', $params['fictitious_name'])->value('fictitious_id');
        if ($exixts) {
            return serviceReturn(false, $params['fictitious_name'], '仓库名称已存在');
        }
        $result = Fictitious::create($params);
        return serviceReturn(true, $result, '添加成功');
    }


    /**
     * 添加萌芽同步仓库
     * @param array $params
     * @return array
     */
    public function syncFictitious(array $params) {
        $exixts = Fictitious::where('fictitious_name', $params['fictitious_name'])->value('fictitious_id');
        if ($exixts) {
            return serviceReturn(false, $params['fictitious_name'], '仓库名称已存在');
        }
        $result = Fictitious::create($params);
        return serviceReturn(true, $result, '添加成功');
    }

    /**
     * 修改仓库
     * @param int $id
     * @param array $params
     * @return Fictitious
     */
    public function fictitiousUp(int $id, array $params) {
        unset($params['id']);
        $params['update_time'] = time();
        return Fictitious::where('id', $id)->update($params);
    }

    /**
     * 查询期数状态操作日志
     * @param int $period
     * @return PeriodsStatusChangeRecord[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function periodsStatusChangeRecord(int $period) {
        return PeriodsStatusChangeRecord::where('period', $period)->order('id', 'desc')->select();
    }

    /**
     * 添加拼团库存数据
     * @param array $params
     * @return GroupOrderInventory|\think\Model
     */
    public function createGroupOrderInventory(array $params) {
        $params['created_time'] = time();
        return GroupOrderInventory::create($params);
    }

    /**
     * 复制期数
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function copyPeriod(array $params) {

        $service = new Periods((int) $params['periods_type']);
        $to_service = new Periods((int) $params['to_periods']);
        // 复制期数基本信息
        if ($params['copy_type'] == 1) {
            // 查询期数
            $fields = 'title,brief,banner_img,product_img,detail,product_id,
            creator_name,creator_id,buyer_id,buyer_name,purchasing_said';
            $period_info = $service->getOne((int)$params['period'], $fields);
            $period_info['banner_img'] = str_replace(env('ALIURL'), "", implode(",", $period_info['banner_img']));
            $period_info['product_img'] = str_replace(env('ALIURL'), "", implode(",", $period_info['product_img']));
            if (empty($period_info)) {
                return serviceReturn(false, $period_info, '未查询到期数');
            }
            $now_time = time();
            $period_info['onsale_status'] = 0;
            $period_info['buyer_review_status'] = 0;
            $period_info['onsale_review_status'] = 0;
            $period_info['created_time'] = $now_time;
            $period_info['update_time'] = $now_time;
            $period_info['id'] = $params['gid'];
            $period_info['is_fail'] = 1;
            // 手动创建
            $period_info['is_manual_create'] = 0;
            // 复制期数
            $result = $to_service->create($period_info);
            if (!$result) {
                return serviceReturn(false, $period_info, '复制期数失败');
            }
            return serviceReturn(true, $period_info, '复制成功');

        }
        // 复制期数所有信息
        $period_info = $service->getOne((int)$params['period']);
        if (empty($period_info)) {
            return serviceReturn(false, $period_info, '未查询到期数');
        }
        // 手动创建
        $period_info['is_manual_create'] = 0;

        if (($period_info['payee_merchant_id'] ?? '') == 5) {
            if (\think\facade\Db::name('periods_product_inventory')->where('period', $period_info['id'])->where('erp_id', 'in', ['034', '34'])->count()) {
                return serviceReturn(false, [], '收款单位是【微醺】时，仓库不能绑定到【034】');
            }
        }
        //商品复制到【尾货、秒发】频道时，【暂存】字段需要修改为【不支持暂存】，其他频道时同步【暂存】字段，都但需要清除暂存时间
        $period_info['latest_storage_time'] = '';
        if (in_array($params['to_periods'], [1, 3])) {
            $period_info['is_support_ts'] = 0;
        }

        $period_info['banner_img']  = str_replace(env('ALIURL'), "", implode(",", $period_info['banner_img']));
        $period_info['product_img'] = str_replace(env('ALIURL'), "", implode(",", $period_info['product_img']));
        if (isset($period_info['video_cover']) && $period_info['video_cover'] != '') {
            $period_info['video_cover'] = str_replace(env('ALIURL'), "", $period_info['video_cover']);
        }
        $period                               = $period_info['id'];
        $period_info['onsale_verify_status']  = 0;
        $period_info['predict_shipment_time'] = strtotime($period_info['predict_shipment_time']);
        $period_info['onsale_time']           = strtotime($period_info['onsale_time']);
        $period_info['sell_time']             = strtotime($period_info['sell_time']);
        $period_info['sold_out_time']         = strtotime($period_info['sold_out_time']);
        $period_info['created_time']          = time();
        $period_info['id']                    = $params['gid'];
        $period_info['limit_number']          = $period_info['invariant_number'];
        $period_info['operation_id']              = $params['user_id'];
        $period_info['operation_name']            = $params['user_name'];
        $period_info['operation_review_id']       = $params['user_id'];
        $period_info['operation_review_name']     = $params['user_name'];
        $period_info['pageviews']                 = 0;
        $period_info['purchased']                 = 0;
        $period_info['vest_purchased']            = 0;
        $period_info['is_channel']                = 0;
        if ($params['periods_type'] != 0 || $params['to_periods'] != 3 || !in_array($period_info['onsale_status'], [1, 2])) {
            $period_info['copywriting_review_status'] = 2;
            $period_info['buyer_review_status']       = 3;
            $period_info['onsale_review_status']      = 3;
            $period_info['marketing_attribute']       = 0;
            $period_info['supplier']                  = '';
            $period_info['supplier_id']               = '';
            $period_info['payee_merchant_id']         = '';
            $period_info['payee_merchant_name']       = '';
            $period_info['onsale_time']               = '';
            $period_info['sell_time']                 = '';
            $period_info['sold_out_time']             = '';
            $period_info['predict_shipment_time']     = '';
            $period_info['is_fail']                   = 1;
            $period_info['is_support_invoicing']      = 1;

            if (isset($period_info['is_after_sale'])) {
                unset($period_info['is_after_sale']);
            }
            // 复制默认非订金
            if (isset($period_info['is_deposit'])) {
                unset($period_info['is_deposit']);
            }
            if (isset($period_info['is_deposit_period'])) {
                unset($period_info['is_deposit_period']);
            }
            if (isset($period_info['label'])) {
                unset($period_info['label']);
            }
            // 闪购默认非秒杀
            if ($params['periods_type'] == 0) {
                $period_info['is_seckill'] = 0;
            }

            // 尾货不支持暂存
            if ($params['to_periods'] == 3) {
                $period_info['is_support_ts'] = 0;
            }
        } else {
            unset($period_info['is_seckill']);
            unset($period_info['is_after_sale']);
        }
        $period_info['onsale_status']         = 0;

        if (!empty($period_info['buyer_id'])) {
            $oerate_info = Db::name('buyer_related')->alias('t1')
                ->join('operate_related t2', 't1.operation_review_id=t2.operation_review_id')
                ->where('t1.buyer_id', $period_info['buyer_id'])
                ->where('t1.delete_time', null)
                ->where('t2.delete_time', null)
                ->field('t2.operation_review_id,t2.operation_review_name')
                ->find();
            if (!empty($oerate_info)) {
                $periods_remark[] = [
                    'period'        => $params['gid'],
                    'periods_type'  => $params['periods_type'],
                    'remark'        => "由采购：" . ($period_info['buyer_name'] ?? '') . "-更换到采购：" . ($period_info['buyer_name'] ?? '') . "。",
                    'operator'      => $params['user_id'],
                    'operator_name' => $params['user_name'],
                    'created_time'  => time(),
                ];
                Db::name('periods_remark')->insertAll($periods_remark);
                $period_info['operation_review_id']   = $oerate_info['operation_review_id'];
                $period_info['operation_review_name'] = $oerate_info['operation_review_name'];
            }
        }
        // 复制期数
        $result = $to_service->create($period_info);
        if (!$result) {
            return serviceReturn(false, $result, '复制期数失败');
        }
        // 查询期数套餐
        $package_service = new Package((int)$params['periods_type']);
        $to_package_service = new Package((int)$params['to_periods']);
        $package_list = $package_service->getPackage(['period_id' => $period], true);
        foreach ($package_list as &$v) {
            unset($v['id']);
            $v['id'] = $this->getGeneratorID(2);
            if (!isset($v['id']) || !$v['id']) {
                return serviceReturn(false, $period_info, '生成期数套餐失败');
            }
            $v['created_time'] = time();
            $v['period_id'] = $result;

            // 复制默认非订金
            if (isset($v['is_deposit'])) {
                unset($v['is_deposit']);
            }
            if (isset($v['deposit_coupon_threshold'])) {
                unset($v['deposit_coupon_threshold']);
            }
            if (isset($v['deposit_coupon_value'])) {
                unset($v['deposit_coupon_value']);
            }
            if (isset($v['deposit_price'])) {
                unset($v['deposit_price']);
            }
            if (isset($v['deposit_coupon_id'])) {
                unset($v['deposit_coupon_id']);
            }
            if (!empty($v['package_img'])) {
                $v['package_img'] = str_replace(env('ALIURL'), "", $v['package_img']);
            }

            $result_pac = $to_package_service->copyCreate($v);
            if (!$result_pac) {
                return serviceReturn(false, $v, '复制 '. $v['package_name']. '时失败');
            }
        }

        unset($k, $v);
        // 查询库存列表
        $pro_inv_model = new PeriodsProductInventory();
        $product_inv = $pro_inv_model->where('period', $period)
            ->select()
            ->toArray();

        if (!empty($product_inv)) {
            foreach ($product_inv as &$v) {
                unset($v['id']);
                $v['period'] = $result;
                $v['inventory'] = 0;
                $v['inventory_accum'] = 0;
                $v['order'] = 0;
                $v['created_time'] = time();
                $v['periods_type'] = $params['to_periods'];
                if ($params['periods_type'] != 0 || $params['to_periods'] != 3 || !in_array($period_info['onsale_status'], [1, 2])) {
                } else {
                    $v['costprice'] = null;
                }
//                $v['warehouse'] = null;
//                $v['warehouse_id'] = null;
//                $v['erp_id'] = null;
            }
        }
        // 添加库存
        $result_pro_inv = $pro_inv_model->saveAll($product_inv);
        if (!$result_pro_inv) {
            return serviceReturn(false, $result_pro_inv, '添加期数套餐成功，但未添加库存');
        }
        return serviceReturn(true);
    }

    public function syncLeftover(array $params)
    {

        $service    = new Periods((int)$params['periods_type']);
        $to_service = new Periods((int)$params['to_periods']);

        // 复制期数所有信息
        $period_info = $service->getOne((int)$params['period']);
        if (empty($period_info)) {
            return serviceReturn(false, $period_info, '未查询到期数');
        }
        // 手动创建
        $period_info['is_manual_create'] = 0;
        if (($period_info['payee_merchant_id'] ?? '') == 5) {
            if (\think\facade\Db::name('periods_product_inventory')->where('period', $period_info['id'])->where('erp_id', 'in', ['034', '34'])->count()) {
                return serviceReturn(false, [], '收款单位是【微醺】时，仓库不能绑定到【034】');
            }
        }
        if (!in_array($period_info['onsale_status'], [2, 3, 4])) {
            return serviceReturn(false, [], '未上架或待售期数不支持操作');
        }
        
        // 一键转尾货时需要判断供应商主体是否和磐石一致，如果不一致则清空供应商
        if (!empty($period_info['supplier_id'])) {
            $supplier = Db::table('vh_wiki.vh_supplier')
                ->where('id', $period_info['supplier_id'])
                ->field('id,supplier_name,corp')
                ->find();
            // 默认清空供应商
            $period_info['supplier_id'] = 0;
            if (!empty($supplier)) {
                $corp_arr = explode(',', $supplier['corp']);
                //供应商只有一个主体使用供应商主体
                if (count($corp_arr) == 1) {
                    $corp_id = payeeMerchantIdCodeExchange($corp_arr[0], 1);
                    if (!empty($corp_id)) {
                        $corp_name = getPayeeMerchantNameID($corp_id);
                        $period_info['supplier_id'] = $supplier['id'];
                        $period_info['supplier'] = $supplier['supplier_name'];
                        $period_info['payee_merchant_id'] = $corp_id;
                        $period_info['payee_merchant_name'] = $corp_name;
                    }
                }
            }
        }
        if (empty($period_info['supplier_id'])) {
            $period_info['supplier_id'] = 0;
            $period_info['supplier'] = '';
            $period_info['payee_merchant_id'] = 0;
            $period_info['payee_merchant_name'] = '';
        }

        $period_info['banner_img']  = str_replace(env('ALIURL'), "", implode(",", $period_info['banner_img']));
        $period_info['product_img'] = str_replace(env('ALIURL'), "", implode(",", $period_info['product_img']));
        if (isset($period_info['video_cover']) && $period_info['video_cover'] != '') {
            $period_info['video_cover'] = str_replace(env('ALIURL'), "", $period_info['video_cover']);
        }
        
        //商品复制到【尾货、秒发】频道时，【暂存】字段需要修改为【不支持暂存】，其他频道时同步【暂存】字段，都但需要清除暂存时间
        $period_info['latest_storage_time'] = '';
        if (in_array($params['to_periods'], [1, 3])) {
            $period_info['is_support_ts'] = 0;
        }

        $now = time();
        $period                               = $period_info['id'];
        $period_info['onsale_verify_status']  = 0;
        $period_info['predict_shipment_time'] = $now;
        $period_info['onsale_time']           = $now;
        $period_info['sell_time']             = $now;
        $period_info['sold_out_time']         = $now + (7 * 24 * 3600);
        $period_info['created_time']          = $now;
        $period_info['id']                    = $params['gid'];
        $period_info['limit_number']          = $period_info['invariant_number'];
        $period_info['operation_id']          = $params['user_id'];
        $period_info['operation_name']        = $params['user_name'];
        $period_info['operation_review_id']   = $params['user_id'];
        $period_info['operation_review_name'] = $params['user_name'];
        $period_info['pageviews']             = 0;
        $period_info['purchased']             = 0;
        $period_info['vest_purchased']        = 0;
        $period_info['sellout_sold_out']      = 0;
        $period_info['onsale_status']         = 0;
        $period_info['is_channel']            = 0;

        unset($period_info['is_seckill']);
        unset($period_info['is_after_sale']);

        if (!empty($period_info['buyer_id'])) {
            $oerate_info = Db::name('buyer_related')->alias('t1')
                ->join('operate_related t2', 't1.operation_review_id=t2.operation_review_id')
                ->where('t1.delete_time', null)
                ->where('t2.delete_time', null)
                ->where('t1.buyer_id', $period_info['buyer_id'])
                ->field('t2.operation_review_id,t2.operation_review_name')
                ->find();
            if (!empty($oerate_info)) {
                $periods_remark[] = [
                    'period'        => $params['gid'],
                    'periods_type'  => $params['periods_type'],
                    'remark'        => "由采购：" . ($period_info['buyer_name'] ?? '') . "-更换到采购：" . ($period_info['buyer_name'] ?? '') . "。",
                    'operator'      => $params['user_id'],
                    'operator_name' => $params['user_name'],
                    'created_time'  => time(),
                ];
                Db::name('periods_remark')->insertAll($periods_remark);
                $period_info['operation_review_id']   = $oerate_info['operation_review_id'];
                $period_info['operation_review_name'] = $oerate_info['operation_review_name'];
            }
        }
        // 复制期数
        $result = $to_service->create($period_info);
        if (!$result) {
            return serviceReturn(false, $result, '复制期数失败');
        }
        // 查询期数套餐
        $package_service    = new Package((int)$params['periods_type']);
        $to_package_service = new Package((int)$params['to_periods']);
        $package_list       = $package_service->getPackage(['period_id' => $period], true);
        foreach ($package_list as &$v) {
            unset($v['id']);
            $v['id'] = $this->getGeneratorID(2);
            if (!isset($v['id']) || !$v['id']) {
                return serviceReturn(false, $period_info, '生成期数套餐失败');
            }
            $v['created_time'] = time();
            $v['period_id']    = $result;

            // 复制默认非订金
            if (isset($v['is_deposit'])) {
                unset($v['is_deposit']);
            }
            if (isset($v['deposit_coupon_threshold'])) {
                unset($v['deposit_coupon_threshold']);
            }
            if (isset($v['deposit_coupon_value'])) {
                unset($v['deposit_coupon_value']);
            }
            if (isset($v['deposit_price'])) {
                unset($v['deposit_price']);
            }
            if (isset($v['deposit_coupon_id'])) {
                unset($v['deposit_coupon_id']);
            }
            if (!empty($v['package_img'])) {
                $v['package_img'] = str_replace(env('ALIURL'), "", $v['package_img']);
            }

            $result_pac = $to_package_service->copyCreate($v);
            if (!$result_pac) {
                return serviceReturn(false, $v, '复制 ' . $v['package_name'] . '时失败');
            }
        }

        unset($k, $v);
        // 查询库存列表
        $pro_inv_model = new PeriodsProductInventory();
        $product_inv   = $pro_inv_model->where('period', $period)
            ->select()
            ->toArray();

        if (!empty($product_inv)) {
            foreach ($product_inv as &$v) {
                unset($v['id']);
                $v['period']          = $result;
                $v['inventory']       = 0;
                $v['inventory_accum'] = 0;
                $v['order']           = 0;
                $v['created_time']    = time();
                $v['periods_type']    = $params['to_periods'];
                if ($params['periods_type'] != 0 || $params['to_periods'] != 3 || !in_array($period_info['onsale_status'], [1, 2])) {
                } else {
                    $v['costprice'] = null;
                }
//                $v['warehouse'] = null;
//                $v['warehouse_id'] = null;
//                $v['erp_id'] = null;
            }
        }
        // 添加库存
        $result_pro_inv = $pro_inv_model->saveAll($product_inv);
        if (!$result_pro_inv) {
            return serviceReturn(false, $result_pro_inv, '添加期数套餐成功，但未添加库存');
        }
        return serviceReturn(true);
    }


    /**
     * 生成自增id
     * @param int $id
     * @return false|mixed
     */
    public function getGeneratorID(int $id) {
        GeneratorId::startTrans();
        $g_id = GeneratorId::where('id', $id)->value('g_id');
        $r_id = $g_id + 1;
        $inc = GeneratorId::where('id', $id)->inc('g_id', 1)->update();
        if ($inc) {
            GeneratorId::commit();
            return $r_id;
        } else {
            GeneratorId::rollback();
        }
        return false;
    }

    /**
     * 更新任务为已执行
     * @param string $task_id
     * @param string $params
     */
    public function updateTaskStatus(string $task_id, string $params = '') {
        PeriodsTaskLog::where('task_id', $task_id)->update(['is_exec' => 1, 'params' => $params]);
    }


    /**
     * 期数是否点赞
     * @param int $uid
     * @param int $period
     * @return int
     */
    public function isPraise(int $uid, int $period) {
        $result = PeriodsPraiseRecord::where(['praise_uid' => $uid, 'period' => $period])->value('id');
        if (!$result) {
            return 0;
        }
        return  1;
    }


    /**
     * 获取用户收藏统计
     * @param int $userid
     * @return int
     */
    public function getUserCollectionCount(int $userid): int
    {
        return PeriodsUserCollection::where('user_id', $userid)->count();
    }

    /**
     * 查询套裁商品详细
     * @param int $period
     * @param int $product
     * @param string $field
     * @return PeriodsProductInventory|array|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPackageProductInfo(int $period, int $product, string $field = '*') {
        return PeriodsProductInventory::where(['period' => $period, 'product_id' => $product])
            ->field($field)
            ->find();
    }

    /**
     * 更新套餐绑定产品信息
     * @param int $id
     * @param array $data
     * @return int
     */
    public function updatePackageProductInfo(int $id,  array $data): int
    {
        return PeriodsProductInventory::where('id', $id)->update($data);
    }

    /**
     * 批量添加期数产品信息
     * @param array $data
     * @return int
     */
    public function createProductEvaluate(array $data): int
    {
        return PeriodsProductEvaluate::insertAll($data);
    }

    /**
     *
     * @param array $where
     * @return PeriodsProductEvaluate|array|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getProductEvaluate(array $where)
    {
        return PeriodsProductEvaluate::where($where)->find();
    }

    /**
     * 查询产品信息
     * @param array $params
     * @return array|\think\Response
     */
    public function getProductList(array $params) {
        $params['short_code']  = '';
        $params['product_main_category']= '';
        if ($params['product_id'] != '') {
            $params['product_id'] = trim($params['product_id'], ',');
            $product_ids = explode(',', $params['product_id']);
            // 查询产品信息
            $product_url = env('ITEM.WINE_WIKI_URL').'/wiki/v3/product/fieldsdatarr';
            $field  = 'id, short_code, tasting_notes, score, prize, drinking_suggestion, product_category';
            $product_info = post_url($product_url, ['ids' => $product_ids, 'fields' => $field]);
            if (!is_null(json_decode($product_info))) {
                $result_pro = json_decode($product_info, true);
                if ($result_pro['error_code'] == 0) {
                    $product_list = $result_pro['data']['list'];
                } else {
                    return  throwResponse(null, ErrorCode::EXEC_ERROR, $result_pro['error_msg']);
                }
            }
            if (!empty($product_list)) {
                foreach ($product_list as &$value) {
                    $value['period'] = $params['id'];
                    $value['product_id'] = $value['id'];
                    $value['created_time'] = time();
                    $params['short_code'] .= $value['short_code']. ',';
                    $params['product_main_category'] .= $value['product_category_name']. ',';
                    unset($value['id'], $value['product_category_name'], $value['product_category']);
                }
                unset($value);
            }
        }
        $re['short_code'] = trim($params['short_code'], ',');
        $re['product_main_category'] = trim($params['product_main_category'], ',');
        return $re;
    }


    /**
     * 更新期数产品信息
     * @param int $period
     * @param array $product
     */
    public function createProductInfo(int $period, array $product = []) {
        if (!empty($product)) {
            foreach ($product as $val) {
                $per_pro_id = PeriodsProductEvaluate::where(['period' => $period, 'product_id' => $val['product_id']])
                    ->value('id');
                // 存在期数产品信息，更新
                if ($per_pro_id) {
                    PeriodsProductEvaluate::where('id', $per_pro_id)->update($val);
                } else {
                    // 不存在期数产品信息，添加
                    $val['period'] = $period;
                    $val['created_time'] = time();
                    PeriodsProductEvaluate::insert($val);
                }
            }
        }
    }


    /**
     * 推送钉钉工作通知
     * @param $uid 用户后台id
     * @param $text
     * @return string
     */
    public function sendDingText($uid, $text): string
    {
        // 获取用户手机号
        $get_user_url = env('ITEM.AUTHORITY_URL'). '/authority/v3/admin/info?admin_id='.$uid.'&field=telephone';
        $get_user_phone = get_url($get_user_url);
        $get_user_phone = json_decode($get_user_phone, true);
        if (!empty($get_user_phone['data']) && $get_user_phone['error_code'] == 0) {
            $user_phone = $get_user_phone['data'][$uid] ?? '';
            if ($user_phone) {
                // 根据手机号查询用户 ding_uid
                $ding_url_url = env('ITEM.DINTALK_SYSTEM_NOTICE_URL') .
                    '/dingtalk/v3/users/mobile/uid?mobile='. $user_phone;

                $get_ding_uid = get_url($ding_url_url);
                $get_ding_uid = json_decode($get_ding_uid, true);
                if ($get_ding_uid['error_code'] == 0 && $get_ding_uid['uid']) {
                    $ding_uid = $get_ding_uid['uid'];
                    $url = env('ITEM.DINTALK_SYSTEM_NOTICE_URL') . '/dingtalk/v3/sysnotice/sendtext';
                    $postData = [
                        'dingtalk_uid' => $ding_uid,
                        'text' => $text
                    ];
                    $ding = post_url($url, $postData);
                    return $ding;
                }
            }
        }
        return '';
    }


    /**
     * 更新期数产品信息
     * @param $period
     * @param $product_id
     * @param $data
     * @return int
     */
    public function updatePeriodsProductInventory($period, $product_id, $data): int
    {
        return PeriodsProductInventory::where(['period' => $period, 'product_id' => $product_id])->update($data);
    }

    /**
     * 更新期数产品信息(根据期数)
     * @param $period
     * @param $product_id
     * @param $data
     * @return bool|string
     */
    public function updatePeriodsProductByPeriod($period, $params)
    {
        $data = [];
        if (isset($params['warehouse']) && !empty($params['warehouse'])) {
            $data['warehouse'] = $params['warehouse'];
        }
        if (isset($params['warehouse_id']) && !empty($params['warehouse_id'])) {
            $data['warehouse_id'] =  $params['warehouse_id'];
        }
        if (isset($params['erp_id']) && !empty($params['erp_id'])) {
            $data['erp_id'] =  $params['erp_id'];
        }

        Db::startTrans();
        try {
            $info = PeriodsProductInventory::where('period', $period)
                ->field('id,period,periods_type,short_code,warehouse,erp_id')
                ->select()->toArray();
            if (!empty($info)) {
                $periods_type = $info[0]['periods_type'];
                $remark_base = [
                    'period' => $period,
                    'periods_type' => $periods_type,
                    'remark' => '',
                    'operator' => $params['creator_id'],
                    'operator_name' => $params['creator_name'],
                    'created_time' => time(),
                ];
                $remark = [];
                foreach ($info as $v) {
                    $new_erp_id = $data['erp_id'] ?? '';
                    $new_warehouse = $data['warehouse'] ?? '';
                    if ($v['erp_id'] == $new_erp_id || empty($new_erp_id)) {
                        continue;
                    }
                    $val = $remark_base;
                    $val['remark'] = "{$v['short_code']}：【{$v['warehouse']}（{$v['erp_id']}）】换绑到【{$new_warehouse}（{$new_erp_id}）】";
                    $remark[] = $val;
                }
                if (!empty($remark)) {
                    PeriodsRemark::insertAll($remark);
                }
                
                PeriodsProductInventory::where('period', $period)->update($data);
            }
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('updatePeriodsProductByPeriod失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $e->getMessage();
        }
        
    }

    /**
     * 获取商品产品库存信息列表
     * @param $period
     * @return PeriodsProductInventory[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPeriodsProductInventory($period) {
        return PeriodsProductInventory::where([
            ['period', '=', $period],
            ['is_use_comment', '<>', 1]
        ])->select();
    }

    /**
     * 添加暂存模板
     * @param array $data
     * @return PeriodsTsTemplate|\think\Model
     */
    public function addTsTemplate(array $data) {
        return PeriodsTsTemplate::create($data);
    }

    /**
     * 更新暂存模板
     * @param array $data
     * @return bool
     */
    public function upTsTemplate(array $data): bool
    {
        return PeriodsTsTemplate::where('id', $data['id'])->update($data);
    }

    /**
     * 暂存列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getTsTemplateList() {
        return PeriodsTsTemplate::select()->toArray();
    }

    /**
     * 开启/关闭 暂存模板
     * @param array $data
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function enabledTsTemplate(array $data): array
    {
        // 查看是否存在其它开启模板
        if ($data['is_enabled'] == 1) {
            $exists = PeriodsTsTemplate::where([['id', '<>', $data['id']], ['is_enabled','=', 1]])->find();
            if (!empty($exists)) {
                return serviceReturn(false, [], '已开启其它暂存模板,请先关闭');
            }
        }
        $result = PeriodsTsTemplate::where('id', $data['id'])->update(['is_enabled' => $data['is_enabled']]);
        return serviceReturn(true, $result, '更新成功');
    }

    /**
     * 更新变动供应商
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateSupplier() {
        // 更新供应商
        $id_arr = PeriodsFlash::where([['supplier_id', '>', 0], ['id', '>', 110207]])->column('id');
        foreach ($id_arr as $i) {
//        $i = 110560;
            // 查询期数供应商
            $info = PeriodsFlash::where([['id', '=', $i], ['supplier_id', '>', 0]])
                ->field('id, supplier, supplier_id')
                ->find();
            if (!empty($info)) {
                // 获取最新供应商名称
                $url = 'https://callback.vinehoo.com/wine-wiki/wiki/v3/supplier/logbulkquery?ids='.$info['supplier_id'];
                $re = get_url($url);
                $re = json_decode($re, true);
                if (!empty($re['data']['list'])) {
                    $w = $re['data']['list'][0];
                    // 不一致更新供应商
                    if ($info['supplier'] != $w['nowname'] || $info['supplier_id'] != $w['nowid']) {
                        PeriodsFlash::where('id', $i)->update(['supplier' => $w['nowname'], 'supplier_id' => $w['nowid']]);
                    }
                }
                Log::write('供应商更新id：'. $i);
            }
        }
        return true;
    }

}