<?php

namespace app\service;

use app\ErrorCode;
use app\model\PeriodsRabbit;
use think\Response;

class Rabbit {

    /**
     * 添加商品
     * @param array $params
     * @return int
     */
    public function create(array $params): ?int
    {
        $result = PeriodsRabbit::create($params);
        return $result->id ?? false;
    }

    /**
     * 根据 id 获取商品信息
     * @param int $id
     * @param string $field
     * @return array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOne(int $id, string $field = '*'): ? array {
        $result = PeriodsRabbit::field($field)->find($id);
        return  $result ? $result->toArray() : null;
    }

    /**
     * 更新商品数据
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update(array $params)
    {
        // 更新商品
        unset($params['creator_id'], $params['creator_name']);
        // 如果是驳回状态改为待审核
        if (isset($params['onsale_review_status']) && $params['onsale_review_status'] == '4') {
            $params['onsale_review_status'] = 1;
        }
        $update = PeriodsRabbit::update($params);
        if (!$update) {
            return serviceReturn(false, $params['id'], '更新商品失败');
        }

        return serviceReturn(true, $update->id);
    }

}
