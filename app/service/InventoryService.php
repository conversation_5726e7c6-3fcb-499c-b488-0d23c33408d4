<?php

namespace app\service;

use think\facade\Validate;
use think\facade\Db;
use app\service\WorkWeixinService;
use app\service\es\Es;
use think\facade\Log;

class InventoryService
{
    /**
     * 库存调整
     * <AUTHOR>
     * @Date 2023/12/14
     * @param array $params
     * @return bool|string
     */
    public function update($params)
    {
        #数据验证
        $validate = Validate::rule([
            'user_id|操作人ID' => 'require|number|>:0',
            'period_id|期数' => 'require|number|>:0',
            'product_id|产品ID' => 'require|number|>:0',
            'nums|数量' => 'require|number|>:0',
            'action|动作' => 'require|in:dec,inc',
        ]);
        if (!$validate->check($params)) {
            return $validate->getError();
        }
        $time = time();
        Db::startTrans();
        try {
            // 同一期数不同简码可以同时发起审批；有处理审批中的【同期数、同简码】的数据不允许修改库存，提示：【该简码正处理审批中】
            $exist = Db::name('periods_inventory_update_log')
                ->where([
                    ['period_id', '=', $params['period_id']],
                    ['product_id', '=', $params['product_id']],
                    ['approval_status', '=', 1],
                ])
                ->lock(true)
                ->value('id');
            if (!empty($exist)) {
                throw new \Exception('该简码正处理审批中。');
            }
            // 查询产品信息
            $periods_product = Db::name('periods_product_inventory')
                ->where([
                    'period' => $params['period_id'],
                    'product_id' => $params['product_id'],
                ])
                ->findOrEmpty();
            if (empty($periods_product)) {
                throw new \Exception('产品信息获取失败');
            }

            // 查询操作人部门
            $admin = getMiddleAdminInfo($params['user_id']);
            if (empty($admin)) {
                throw new \Exception('操作人信息获取失败。');
            }
            // 默认非运营部人员
            $is_operation = false;
            // 判断是否运营部
            if (!empty($admin['roles']) && is_array($admin['roles'])) {
                foreach ($admin['roles'] as $v) {
                    // 运营
                    if ($v['id'] == 14) {
                        $is_operation = true;
                        break;
                    }
                }
            }
            $periods = Es::name('periods')
                ->where([['_id', '=', intval($params['period_id'])]])
                ->field('id,buyer_id,title,is_channel')
                ->find();
            if (empty($periods)) {
                throw new \Exception('商品信息获取失败。');
            }

            // 操作记录
            $log = [
                'period_id' => $params['period_id'],
                'periods_type' => $periods_product['periods_type'],
                'product_id' => $params['product_id'],
                'bar_code' => $periods_product['bar_code'],
                'short_code' => $periods_product['short_code'],
                'action' => $params['action'],
                'nums' => $params['nums'],
                'status' => 0,
                'approval_status' => 0,
                'operator' => $admin['realname'],
                'operator_id' => $params['user_id'],
                'update_time' => $time,
                'created_time' => $time,
            ];
            $inventory = $periods_product['inventory'] ?? 0;
            // 动作
            if ($params['action'] == 'inc') {
                $action = '+';
                $action_name = '增加';
                $inventory = intval($inventory+$params['nums']);
            } else {
                $action = '-';
                $action_name = '减少';
                $inventory = intval($inventory-$params['nums']);
            }

            // 非运营人员非跨境发送审批
            if (!$is_operation && $periods_product['periods_type'] != 2) {
                $log['approval_status'] = 1;
                // 记录日志
                $logid = Db::name('periods_inventory_update_log')->insertGetId($log);
                
                $wxService = new WorkWeixinService();
                $res = $wxService->SendDingtalkApproval([
                    'userid' => $admin['userid'],
                    'dept_id' => $admin['dept_id'],
                    'id' => $logid,
                    'period_id' => $params['period_id'],
                    'period_title' => $periods['title'],
                    'product_name' => $periods_product['product_name'],
                    'short_code' => $periods_product['short_code'],
                    'action' => $params['action'] == 'inc' ? '增加' : '减少',
                    'nums' => $params['nums'],
                ]);
                // 审批发送失败
                if (empty($res) || !is_array($res)) {
                    throw new \Exception(empty($res) ? '操作审批发送失败。' : $res);
                }
                if (!isset($res['error_code']) || intval($res['error_code']) !== 0) {
                    throw new \Exception($res['error_msg'] ?? '操作审批发送失败。');
                }
                // 备注
                $remark = "发送审批，简码：{$periods_product['short_code']}，数量：{$action}{$params['nums']}";

            } else { // 运营人员直接修改
                $url = env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/sys/update';
                $body = json_encode([
                    'period_id' => intval($params['period_id']),
                    'product_id' => intval($params['product_id']),
                    'nums' => intval($params['nums']),
                    'action' => strval($params['action']),
                ]);
                $header = [
                    'vinehoo-uid:' . $params['user_id'],
                    'vinehoo-vos-name:' . base64_encode($admin['realname']),
                ];
                $res = curlRequest($url, $body, $header);
                $log['status'] = 1;
                if (!isset($res['error_code']) || intval($res['error_code']) !== 0) {
                    $log['status'] = 2;
                    $log['error_msg'] = $res['error_msg'] ?? '库存操作未响应';
                    throw new \Exception($res['error_msg'] ?? '库存操作未响应，请刷新核实是否操作成功。');
                }
                // 记录日志
                Db::name('periods_inventory_update_log')->insert($log);
                //期数的库存被人为修改后，需要通知到对应的采购。
                // $message = "您上架的{$params['period_id']}期【{$periods_product['short_code']}】库存设置{$action_name}了{$params['nums']}，请留意。";
                $channelstr = '';
                if (!empty($periods['is_channel']) && intval($periods['is_channel']) === 1) {
                    $channelstr = '渠道';
                }
                $message = "第{$params['period_id']}期{$channelstr}商品{$periods['title']}（{$periods_product['short_code']}：{$periods_product['product_name']}），库存由{$periods_product['inventory']}变动为{$inventory}";
                $this->NotifyProcurement($periods, $message);

                // 备注
                $remark = "成功，简码：{$periods_product['short_code']}，数量：{$action}{$params['nums']}";
            }

            // 添加商品备注
            Db::name('periods_remark')->insert([
                'period' => $params['period_id'],
                'periods_type' => $periods_product['periods_type'],
                'remark' => $remark,
                'operator' => $params['user_id'],
                'operator_name' => $admin['realname'],
                'created_time' => $time,
            ]);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return $e->getMessage();
        }

        return true;
    }

    /**
     * 库存调整审批回调
     * <AUTHOR>
     * @Date 2023/12/14
     * @param array $params
     * @return bool|string
     */
    public function callbackUpdate($params)
    {
        // 企业微信审批处理服务
        $WorkWeixin = new WorkWeixinService();
        $WorkWeixin->title = '修改商品库存审批';
        // 数据验证
        $param = $WorkWeixin->verifyData($params);
        if (!is_array($param)) {
            return $param;
        }
        // 判断审批是否完成
        if (!in_array($param['status'], ['COMPLETED', 'TERMINATED'])) {
            return false;
        }
        // 记录ID
        $logid = 0;
        foreach ($param['form_component_values'] as $v) {
            $value = $v['value'] !== 'null' ? trim($v['value']) : '';
            switch ($v['name']) {
                case "ID":
                    $logid = $value;
                    break;
            }
        }

        Db::startTrans();
        try {
            // 查询记录
            $info = Db::name('periods_inventory_update_log')
                ->where('id', $logid)
                ->lock(true)
                ->findOrEmpty();
            if (empty($info)) {
                $WorkWeixin->sendNotify('未查询到ID对应操作记录。', true);
            }
            if ($info['status'] == 1) {
                $WorkWeixin->sendNotify('ID对应操作记录已执行成功', true);
            }
            // 查询产品信息
            $periods_product = Db::name('periods_product_inventory')
                ->where([
                    'period' => $info['period_id'],
                    'product_id' => $info['product_id'],
                ])
                ->findOrEmpty();
            if (empty($periods_product)) {
                $WorkWeixin->sendNotify('产品信息获取失败。', true);
            }
            $inventory = $periods_product['inventory'] ?? 0;
            // 动作
            if ($info['action'] == 'inc') {
                $action = '+';
                $action_name = '增加';
                $inventory = intval($inventory+$info['nums']);
            } else {
                $action = '-';
                $action_name = '减少';
                $inventory = intval($inventory-$info['nums']);
            }

            $update = [
                'status' => 0,
                'approval_status' => 3,
                'error_msg' => '',
                'update_time' => time(),
            ];
            // 审批通过
            if ($param['result'] == 'agree') {
                // 发送修改
                $url = env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/sys/update';
                $body = json_encode([
                    'period_id' => intval($info['period_id']),
                    'product_id' => intval($info['product_id']),
                    'nums' => intval($info['nums']),
                    'action' => strval($info['action']),
                ]);
                $header = [
                    'vinehoo-uid:' . $info['operator_id'],
                    'vinehoo-vos-name:' . base64_encode($info['operator']),
                ];
                $res = curlRequest($url, $body, $header);

                $update['status'] = 1;
                if (!isset($res['error_code']) || intval($res['error_code']) !== 0) {
                    $update['status'] = 2;
                    $update['error_msg'] = $res['error_msg'] ?? '库存操作未响应';
                    $remark = "失败（{$update['error_msg']}），简码：{$info['short_code']}，数量：{$action}{$info['nums']}";
                } else {
                    //期数的库存被人为修改后，需要通知到对应的采购。
                    $periods = Es::name('periods')
                    ->where([['_id', '=', intval($info['period_id'])]])
                    ->field('id,buyer_id,title,is_channel')
                    ->find();
                //期数的库存被人为修改后，需要通知到对应的采购。
                // $message = "您上架的{$info['period_id']}期【{$info['short_code']}】库存设置{$action_name}了{$info['nums']}，请留意。";
                    $channelstr = '';
                    if (!empty($periods['is_channel']) && intval($periods['is_channel']) === 1) {
                        $channelstr = '渠道';
                    }
                    $message = "第{$info['period_id']}期{$channelstr}商品{$periods['title']}（{$periods_product['short_code']}：{$periods_product['product_name']}），库存由{$periods_product['inventory']}变动为{$inventory}";
                    $this->NotifyProcurement($periods, $message);

                    // 备注
                    $remark = "成功，简码：{$info['short_code']}，数量：{$action}{$info['nums']}";
                }
                $update['approval_status'] = 2;
                
            } else {
                // 撤销
                if ($param['status'] == 'TERMINATED') {
                    $update['approval_status'] = 4;
                    $remark = "审批撤销，简码：{$info['short_code']}，数量：{$action}{$info['nums']}";

                } else {// 驳回
                    $remark = "审批驳回，简码：{$info['short_code']}，数量：{$action}{$info['nums']}";
                }
            }

            // 添加商品备注
            Db::name('periods_remark')->insert([
                'period' => $info['period_id'],
                'periods_type' => $info['periods_type'],
                'remark' => $remark,
                'operator' => $info['operator_id'],
                'operator_name' => $info['operator'],
                'created_time' => time(),
            ]);

            // 更新记录日志
            Db::name('periods_inventory_update_log')->where('id', $info['id'])->update($update);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return $e->getMessage();
        }


        return true;
    }

    /**
     * 期数的库存被人为修改后，需要通知到对应的采购。
     * <AUTHOR>
     * @Date 2024/01/18
     * @param array $periods 期数数据
     * @param string $message 通知消息内容
     * @return bool|string
     */
    public function NotifyProcurement($periods, $message)
    {
        if (!empty($periods['buyer_id'])) {
            // 查询采购人信息
            $admin = getMiddleAdminInfo($periods['buyer_id']);
        }
        if (empty($admin['userid'])) {
            Log::Error("期数的库存被人为修改后，通知对应的采购，采购人信息查询失败，期数：{$periods['id']}！");
            return '采购人信息查询失败';
        }

        $WorkWeixin = new WorkWeixinService();
        $WorkWeixin->uid = $admin['userid'];
        $WorkWeixin->sendNotify($message);

        return true;
    }

    /**
     * 查询修改库存提示
     * <AUTHOR>
     * @Date 2023/12/14
     * @param array $params
     * @return array
     */
    public function queryPrompt($params)
    {
        #数据验证
        $validate = Validate::rule([
            'period_id|期数' => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            return $validate->getError();
        }
        $unpay_order_count = Es::name('orders')->where([
            ['period', '=', $params['period_id']],
            ['sub_order_status', '=', 0]
        ])->count();

        return [
            'unpay_order' => !empty($unpay_order_count) ? true : false,
        ];
    }
}
