<?php

namespace app\service;
use think\facade\Validate;

class WorkWeixinService
{
    public $user = [];
    public $access_token, $uid, $title;

    public function __construct()
    {
        $this->access_token = $this->getToken();
    }

    /**
     * @方法描述:获取企业内部应用的access_token
     * <AUTHOR>
     * @Date   2022/09/16
     * @return string access_token
     */
    public function getToken()
    {
        $url = env('ITEM.WECHART_URL') . '/wechat/v3/wecom/accesstoken';
        $resq = curlRequest($url, [], [], 'GET') ?? '';

        return $resq['access_token'] ?? '';
    }

    /**
     * 验证审批数据
     * @param array $param 请求参数
     * @param array|string
     */
    public function verifyData($param)
    {
        if (empty($param['process_instance'])) {
            return '请求参数格式错误';
        }
        $data = $param['process_instance'];
        #数据验证
        $validate = Validate::rule([
            'form_component_values|审批内容' => 'require|array',
            'originator_userid|发送人编号' => 'require',
        ]);
        if (!$validate->check($data)) {
            return $validate->getError();
        }
        #判断审批是否完成
        if (!isset($data['result']) || !isset($data['status'])) {
            return '请求参数格式错误';
        }
        #发起者uid
        $this->uid = $data['originator_userid'];
        return $data;
    }

    /**
     * 发送企业微信中台通知
     * @param string $dingtalk_uid 用户ID
     * @param string $text 发送的文本内容
     * @param bool $is_throw 执行完成是否抛异常
     * @param int $type 推送类型：0-所有，1-只推送甘高寒
     * @return bool|string true成功false失败
     */
    public function sendNotify($text, $is_throw = false,$type = 0)
    {
        $result = true;

        $data = [
            'userid'  => $this->uid,
            'content' => $text . '，' . date('Y-m-d H:i:s'),
            'msgtype' => 'text',
            'agentid' => 0,
        ];
        
        if (!empty($this->title)) {
            $data['content'] = $this->title . '，' . $data['content'];
        }

        $url = env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send';
        if ($type === 0) {
            // 推送审批提交人
            if (!empty($this->uid)) {
                $resq = curlRequest($url, json_encode($data));
                if (!isset($resq['error_code']) || intval($resq['error_code']) !== 0) {
                    $result = $resq['error_msg'] ?? '发送工作通知失败';
                }
            }

            // 推送龙飞
            // $data['userid'] = "LongFei";
            // curlRequest($url, json_encode($data));
        }
        // 推送自己
        $data['userid'] = "GanGaoHan";
        curlRequest($url, json_encode($data));
        // 推送测试
        // $data['userid'] = "XuanYiChang";
        // curlRequest($url, json_encode($data));
        if ($is_throw) {
            throw new \Exception($result);
        }

        return $result;
    }



    /**
     * @Describe:发送审批
     * <AUTHOR>
     * @Date 2023/04/19
     * @param array $param 参数
     * @param int $type 审批类型：1-修改商品库存
     * @return bool|mixed|string
     */
    public function SendDingtalkApproval($param, $type = 1)
    {
        if (empty($param['userid']) && empty($param['dept_id'])) {
            #管理员id
            $adminid = request()->header('vinehoo-uid');
            $admin = getMiddleAdminInfo($adminid);
            if (empty($admin)) {
                return '当前登录账号信息查询失败。';
            }
            $param['dept_id'] = $admin['dept_id'] ?? '';
            $param['userid'] = $admin['userid'] ?? '';
        }

        $dept_id_arr = $param['dept_id'] ?? '';
        $dept_id = json_decode($dept_id_arr, true)[0] ?? 0;
        if (empty($dept_id)) {
            return '当前登录账号部门信息查询失败。';
        }
        if (empty($param['userid'])) {
            return '当前登录账号企业微信ID查询失败。';
        }
        switch (intval($type)) {
            #修改商品库存
            case 1:
                $form_component_values = $this->GetUpdateGoodsInventoryApprovalParam($param);
                $process_code          = empty(env('COMMODITIES.INVENTORY_PROCESS_CODE')) ? "C4WopLCXPipgwr6vonCHpUDCHopX5DarbqN5v4LEF" : env('COMMODITIES.INVENTORY_PROCESS_CODE');
                break;

            #采购付款单
            case 2:
                $form_component_values = $this->GetPurchasePaymentApprovalParam($param);
                $process_code          = 'C4c4ebsx97SrXyBsfYWxsQDkdfjAeErhMqBUKMy8T';
                break;
        }


        $data = [
            'form_component_values' => $form_component_values,
            'process_code'          => $process_code,
            'dept_id'               => intval($dept_id),
            'originator_user_id'    => $param['userid'],
        ];
        $url = env('ITEM.WECHART_URL') . '/wechat/v3/wecom/approval/create';
        return curlRequest($url, json_encode($data, JSON_UNESCAPED_UNICODE));
    }

    /**
     * @Describe:获取修改商品库存审批参数
     * <AUTHOR>
     * @Date 2023/12/14
     * @param array $param 请求参数
     * @return array 审批参数
     */
    public function GetUpdateGoodsInventoryApprovalParam($param)
    {
        $form_component_values = [];
        $form_component_values[] = [
            'name'  => 'ID',
            'value' => strval($param['id']),
        ];

        $form_component_values[] = [
            'name'  => '期数',
            'value' => intval($param['period_id']),
        ];

        $form_component_values[] = [
            'name'  => '简码',
            'value' => strval($param['short_code']),
        ];

        $form_component_values[] = [
            'name'  => '操作类型',
            'value' => strval($param['action']),
        ];

        $form_component_values[] = [
            'name'  => '数量',
            'value' => intval($param['nums']),
        ];

        $form_component_values[] = [
            'name'  => '商品标题',
            'value' => $param['period_title'],
        ];

        $form_component_values[] = [
            'name'  => '产品中文名',
            'value' => $param['product_name'],
        ];

        return $form_component_values;
    }

    /**
     * @Describe:获取采购付款单审批参数
     * <AUTHOR>
     * @Date 2025/5/13
     * @param array $param 请求参数
     * @return array 审批参数
     */
    public function GetPurchasePaymentApprovalParam($param)
    {
        $form_component_values = [];
        $form_component_values[] = [
            'name'  => '公司',
            'value' => strval($param['company']),
        ];
        $pre_payment_flag = '非预付款';
        if (!empty($param['pre_payment_flag']) && $param['pre_payment_flag'] == '是') {
            $pre_payment_flag = '预付款';
        }
        $form_component_values[] = [
            'name'  => '是否预付款',
            'value' => $pre_payment_flag,
        ];
        $form_component_values[] = [
            'name'  => '单据类型',
            'value' => strval($param['bill_type']),
        ];
        $form_component_values[] = [
            'name'  => '付款单号',
            'value' => strval($param['bill_no']),
        ];
        $form_component_values[] = [
            'name'  => '单据日期',
            'value' => strval($param['bill_date']),
        ];
        $form_component_values[] = [
            'name'  => '供应商',
            'value' => strval($param['supplier']),
        ];
        $form_component_values[] = [
            'name'  => '部门',
            'value' => strval($param['department']),
        ];
        $form_component_values[] = [
            'name'  => '业务员',
            'value' => strval($param['salesman']),
        ];
        $form_component_values[] = [
            'name'  => '付款银行',
            'value' => strval($param['payment_bank_name']),
        ];
        $form_component_values[] = [
            'name'  => '付款账号',
            'value' => strval($param['payment_bank_account']),
        ];
        $form_component_values[] = [
            'name'  => '收款银行',
            'value' => strval($param['receipt_bank_name']),
        ];
        $form_component_values[] = [
            'name'  => '收款账号',
            'value' => strval($param['receipt_bank_account']),
        ];
        $form_component_values[] = [
            'name'  => '币种',
            'value' => strval($param['currency']),
        ];
        $form_component_values[] = [
            'name'  => '金额',
            'value' => strval($param['original_amount']),
        ];
        $form_component_values[] = [
            'name'  => '备注',
            'value' => strval($param['remark']),
        ];


        $form_item = [];
        foreach ($param['items'] as $item) {
            if (bccomp(strval($item['original_amount']), '0', 2) == 0) {
                continue;
            }
            $form_item[] = [
                ['name' => '订单号', 'value' => strval($item['order_no'])],
                ['name' => '简码', 'value' => strval($item['short_code'])],
                ['name' => '中文名', 'value' => strval($item['cn_product_name'])],
                ['name' => '期数', 'value' => strval($item['period'])],
                ['name' => '金额', 'value' => strval($item['original_amount'])],
                ['name' => '数量', 'value' => strval($item['number'])],
                ['name' => '备注', 'value' => strval($item['remark'] ?? '')],
            ];
        }
        $form_component_values[] = [
            'name'  => '明细',
            'value' => $form_item
        ];
        return $form_component_values;
    }

}
