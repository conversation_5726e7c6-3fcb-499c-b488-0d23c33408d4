<?php

namespace app\service;

use app\model\PeriodsProductInventory;
use app\model\ProductKeyword;
use app\model\ProductProperty;
use app\model\ProductCategory;
use app\model\Products as ProductModel;
use app\model\ProductsGrapeCollection;
use app\model\ProductType;
use app\model\ProductUnit;
use app\model\ProductsUpdateRecord;
use app\service\elasticsearch\ElasticSearchService;
use think\facade\Db;


class Products {

    // 仓库
    private $warehouse = 'xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa';

    /**
     * 添加产品
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public function create(array $params): ?array
    {
        $result = ProductModel::create($params);
        // 删除图片域名
        $params['frontal_label_img'] =  deleteDomain($params['frontal_label_img'] ?? '');
        $params['cn_back_label_img'] = deleteDomain($params['cn_back_label_img'] ?? '');
        $params['en_back_label_img'] = deleteDomain($params['en_back_label_img'] ?? '');
        $params['package_img'] = deleteDomain($params['package_img'] ?? '');
        // 萌芽产品属性
        $data['store_code'] = $this->warehouse;
        $data['goods_name'] = $params['cn_product_name'];
        $data['en_goods_name'] = $params['en_product_name'];
        $data['bar_code'] = $params['bar_code'];
        $data['short_code'] = $params['cn_product_name'];
        $data['goods_years'] = $params['grape_picking_years'] ?? null;
        $data['price'] = $params['costprice'] ?? null;
        $data['capacity'] = $params['capacity'] ?? null;
        $data['shelf_life'] = $params['shelf_life'] ?? null;
        $data['produce_date'] = '' ?? null;
        $data['weight'] = $params['weight'] ?? null;
        $data['country'] = $params['country'] ?? null;
        $data['producing_area'] = $params['producing_area'] ?? null;
        $data['chateau'] = $params['chateau'] ?? null;
        $data['goods_unit'] = $params['product_unit'] ?? null;
        $data['goods_type'] = $params['product_category_code'] ?? null;
        $data['goods_type1'] = $params['product_type_code'] ?? null;
        $data['goods_form'] = $params['product_form'] ?? null;
        $data['is_giveaway'] = $params['is_gift'] ?? null;
        $data['addition'] = $params['is_addition'] ?? null;
        $data['image'] = $params['product_extra_image'] ?? null;
        $data['annex'] = $params['product_attachment'] ?? null;
        $data['length'] = $params['product_length'] ?? null;
        $data['width'] = $params['product_width'] ?? null;
        $data['high'] = $params['product_height'] ?? null;
        $data['positive_img'] = $params['frontal_label_img'] ?? null;
        $data['back_img'] = $params['cn_back_label_img'] ?? null;
        $data['en_back_img'] = $params['en_back_label_img'] ?? null;
        $data['package_img'] = $params['package_img'] ?? null;

        // 酒类增加葡萄占比
        if ($params['product_category'] == 1) {
            if ($result->id) {
                // 葡萄占比列表
//                $grape_collection = json_decode($params['grape_collection'] ?? '', true);
                if (!empty($params['grape_collection'])) {
                    foreach ($params['grape_collection'] as &$val) {
                        $val['product_id'] = $result->id;
                        $val['created_time'] = time();
                    }
                    (new ProductsGrapeCollection)->saveAll($params['grape_collection']);
                }
            }
        }

        // 同步产品到萌芽
        $sync_my = post_url(env("SYNC_MY") . '/sync/goods', $data);

        $sync_my = json_decode($sync_my, true);
        if ($sync_my['errorCode'] == '-1') {
            return serviceReturn(false, $data, '萌芽同步失败 - '. $sync_my['msg']);
        }

        return serviceReturn(true, $result->id);
    }

    /**
     * 根据 id 获取产品信息
     * @param int $id
     * @param string $field
     * @return array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOne(int $id, string $field = '*'): ?array {
        $result =  ProductModel::field($field)->find($id);
        return  $result ? $result->toArray() : null;
    }

    /**
     * 获取所有产品条码
     * @return array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getBarcode(): ?array {
//        $result =  ProductModel::field('bar_code')->select();
        $result =  ProductModel::column('bar_code');
        return  $result ?? [];
    }

    /**
     * @param array $params
     * @return array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update(array $params): ?array
    {
        $update_record = [];
        // 更新前产品数据
        $update_record['before'] = json_encode($this->getOne($params['id']));
        // 更新产品
        $update = ProductModel::update($params);
        if (!$update) {
            return serviceReturn(false, $params['id'], '更新产品失败');
        }
        // 添加产品更新记录
        $update_record['after'] = json_encode($params);
        $update_record['created_time'] = $params['update_time'];
        $update_record['operator_id']  = $params['operator_id'];
        $update_record['product_id']  = $params['id'];
        $create_record = ProductsUpdateRecord::create($update_record);

        if (!$create_record) {
            return serviceReturn(false, $params['id'], '添加产品更新记录失败');
        }

        return serviceReturn(true, $update);

    }

    /**
     * 根据 id 获取产品列表
     *
     * @param string $id
     * @return array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getListById(string $id, int $period = 0): ?array
    {
//        return [];

        // 字符串id转数组
        $id = explode(",", $id);
        if (empty($id)) {
            return  [];
        }
        $product_url = env('ITEM.WINE_WIKI_URL').'/wiki/v3/product/morethan';
        $product_info = post_url($product_url, ['ids' => $id]);
        $product_list = [];
        if (!is_null(json_decode($product_info))) {
            $result_pro = json_decode($product_info, true);
            if ($result_pro['error_code'] == 0) {
                $product_list = $result_pro['data']['list'];
            }
        }

        $other_ser = new Other();
        if (!empty($product_list)) {
            foreach ($product_list as &$val) {
                // 查询库存
                if ($period > 0 ) {
                    $val['inventory'] = null;
                    $val['warehouse'] = null;
                    $product_inventory = PeriodsProductInventory::where(['product_id' => $val['id'], 'period' => $period])
                        ->field('id,inventory,warehouse,warehouse_id,is_use_comment,is_hidden_param,custom_product_name')->find();
                    if ($product_inventory) {
                        $val['inventory'] = $product_inventory['inventory'];
                        $val['warehouse'] = $product_inventory['warehouse'];
                        $val['is_use_comment'] = $product_inventory['is_use_comment'] ?? 0;
                        $val['is_hidden_param'] = $product_inventory['is_hidden_param'] ?? 0;
                        $val['product_inventory_id'] = $product_inventory['id'] ?? null;
                        $val['custom_product_name'] = $product_inventory['custom_product_name'] ?? '';
                    }
                }
                // 查询产品评价详细
                $product_ev = $other_ser->getProductEvaluate(['period' => $period, 'product_id' => $val['id']]);
                if (!empty($product_ev)) {
                    $val['tasting_notes'] = $product_ev['tasting_notes'];
                    $val['score'] = $product_ev['score'];
                    $val['prize'] = $product_ev['prize'];
                    $val['drinking_suggestion'] = $product_ev['drinking_suggestion'];
                }
                $val['refer_costprice'] = $val['costprice'];
                // 产品当期成本价
                $val['costprice'] = PeriodsProductInventory::where(['period' => $period, 'product_id' => $val['id']])
                    ->value('costprice');

            }
            // 按照id重新排序
            $product_list = arrayByarraySort($product_list, $id);

        }
        return $product_list;

//        $field = 'id,cn_product_name,en_product_name,bar_code,grape_picking_years,short_code,product_type,product_category,
//            product_type,chateau,producing_area,country,product_keywords_id,residual_sugar,alcohol,
//            capacity,brewing,tasting_notes,score,prize,drinking_suggestion';
//        $result = ProductModel::whereIn('id',$id)->field($field)->select()->map(function($item) use ($period) {
//            // 查询产品关键词
//            $item->keywork = ProductKeyword::whereIn('id', $item->product_keywords_id);
//            // 产品葡萄含量
//            if ($item->product_category == 1) {
//                $item->grape = ProductsGrapeCollection::where('product_id', $item->id)
//                ->field('id,grape_name,propotion')->find();
//            }
//            // 查询库存
//            if ($period > 0 ) {
//                $product_inventory = PeriodsProductInventory::where(['product_id' => $item->id, 'period' => $period])
//                    ->field('inventory,warehouse,warehouse_id')->find();
//                if ($product_inventory) {
//                    $item->inventory = $product_inventory['inventory'];
//                    $item->warehouse = $product_inventory['warehouse'];
//                }
//            }
//            return $item;
//        });
    }

    /**
     * 产品单位列表
     *
     * @return array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getUnit(): ?array
    {
        $field = 'id,name';
        $result = ProductUnit::field($field)->select();
        return  $result ? $result->toArray() : null;
    }

    /**
     * 产品类别列表
     *
     * @return array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getCategory(): ?array
    {
        $field = 'id,code,name';
        $result = ProductCategory::field($field)->select();
        return  $result ? $result->toArray() : null;
    }

    /**
     * 根据父 id 获取类型列表
     *
     * @param int $fid
     * @return array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getTypeByFID(int $fid): ?array
    {
        $field = 'id,code,name';
        $result = ProductType::where('pid', $fid)->field($field)->select();
        return  $result ? $result->toArray() : null;
    }

    /**
     * 根据条件和字段返回商品详细信息
     * @param array $where
     * @param string $field
     * @return ProductModel|array|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getProductInfo(array $where, string $field = '*') {
        return ProductModel::where($where)
            ->field($field)
            ->append(['product_type_name', 'product_category_name'])
            ->select()
            ->map(function ($item) {
                // 获取萌芽仓库产品信息
                $data['bar_code'][] = $item->bar_code;
                $data['store_code'] = $this->warehouse;
                $data_string = http_build_query($data);
                $url = env('ITEM.DISTRIBUTE_URL').'/query/goodsGetFictitiousCount';
                $warehouse_inventory = post_url($url, $data_string);
                // 获取返回值
                $sync_my = json_decode($warehouse_inventory, true);
                if ($sync_my['status'] == 'success') {
                    $item->warehouse = $sync_my['data'][$item->bar_code] ?? [];
                } else {
                    $item->warehouse = $sync_my['data'];
                    $this->my_msg = $sync_my['msg'];
                }
                return $item;
        });
    }

    /**
     * 根据简码获取仓库及库存
     * @param string $bra_code
     * @return array|mixed
     */
    public function warehouse(string $bra_code, $payee_merchant_id = null) {
        // 获取萌芽仓库产品信息
//        $data['bar_code'][] = '010046701091';
        $data['short_code'][] = $bra_code;
        $data['store_code'] = $this->warehouse;
        $data_string = http_build_query($data);
        $url = env('ITEM.DISTRIBUTE_URL').'/query/goodsGetFictitiousCount';
        $warehouse_inventory = post_url($url, $data_string);
        // 获取返回值
        $sync_my = json_decode($warehouse_inventory, true);
        if ($sync_my['status'] == 'success') {
            $result  = $sync_my['data'][$bra_code] ?? [];
            if ($payee_merchant_id == 5) {
                foreach ($result as $k => $item) {
                    if (in_array($item['fictitious_id'], ['34', '034'])) {
                        unset($result[$k]);
                    }
                }
            }
            $result = array_values($result);
        } else {
            $result = $sync_my['msg'];
        }
        return $result;
    }

    public function getGoodsProperty($type_id)
    {
        if (!is_array($type_id)) {
            $type_id = [$type_id];
        }

//        $data = $this->name('goods_property')
//            ->field('
//                pid,
//                fields,
//                name,
//                is_req,
//                mutual_type,
//                character_type,
//                length,
//                data_type,
//                data_sheet,
//                content
//            ')
//            ->whereIn('pid',$type_id)
//            ->order('sort', 'asc')
//            ->select()->toArray();

        $data = ProductProperty::field('pid,fields,name,is_req,mutual_type,character_type,
                length,data_type,data_sheet,content')
            ->whereIn('pid', $type_id)
            ->order('sort','asc')
            ->select()->toArray();

        $return = [];
        foreach ($data as $v) {
            $list = $select = [];
            switch ($v['data_type']) {
                case 2:
//                    $list = $this->name($v['data_sheet'])
//                        ->field('id,code,name')
//                        ->where('pid',$v['pid'])
//                        ->select()->toArray();
                    $list = Db::table('vh_'.$v['data_sheet'])->field('id,code,name')
                        ->where('pid', $v['pid'])
                        ->select()->toArray();
                    break;
                case 3:
                    $select = json_decode(base64_decode($v['content']));
                    break;
            }
            switch ($v['mutual_type']) {
                case 'text':
                    $message = '请正确输入'.$v['name'];
                    break;
                case 'file':
                    $message = '请正确上传'.$v['name'];
                    break;
                default:
                    $message = '请正确选择'.$v['name'];
            }
            $return[$v['pid']][$v['fields']] = [
                [
                    'required'       => $v['is_req']==2?true:false,
                    'message'        => $message,
                    'trigger'        => 'blur',
                    'character_type' => $v['character_type'],
                    'mutual_type'    => $v['mutual_type'],
                    'length'         => $v['length'],
                    'list'           => $list??[],
                    'select'         => $select??[],
                ]
            ];
        }

        return $return;
    }

    /**
     * 商品统计
     * @param $params
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getAccessStatisticsCount($params)
    {
        $where =[];
        if (!empty($param['start_time']) && !empty($param['end_time'])) {//统计时间
            $stime = strtotime($param['start_time']);
            $etime = strtotime($param['end_time']);
            $where[]=['access_date', 'between', [$stime, $etime]];
        }
        $orderBy=['id desc'];
        if (isset($param['sort']) && !empty($param['sort'])) {
            switch ($param['sort']){
                case 1: //按照流量正序
                    $orderBy=['access_count asc'];
                    break;
                case 2: //按照流量降序
                    $orderBy=['access_count desc'];
                    break;
                case 3: //按照下单人数正序
                    $orderBy=['sold_user_count asc'];
                    break;
                case 4: //按照下单人数降序
                    $orderBy=['sold_user_count desc'];
                    break;

                case 7: //按照销售正序
                    $orderBy=['sold_nums asc'];
                    break;
                case 8: //按照销售降序
                    $orderBy=['sold_nums desc'];
                    break;

            }
        }
        ##增加频道筛选
        $list = Db::name('access_statistics_count')
            ->field('id,access_date,goods_id,SUM(sold_nums) as sold_nums,SUM(conversion_rate) as conversion_rate,SUM(sold_user_count) as sold_user_count,COUNT(*) as num')
            ->group("goods_id")
            ->where($where)->order($orderBy)->select()->toArray();
        $goods_ids = array_column($list,'goods_id');
        $es = new ElasticSearchService();
        $arr = array(
            'index' => ['periods'],
            'terms' => [['id' => $goods_ids]],
            'source' => ['id','periods_type','title', 'onsale_time'],#商品频道  名称 上架时间
        );
        $data = $es->getDocumentList($arr);
        $goodsInfo = [];
        foreach ($data['data'] as $temp){
            $goodsInfo[$temp['id']] = $temp;
        }
        $page = $param['page'] ?? 1;
        $limit = $param['limit'] ?? 10;
        $offset = ($page - 1) * $limit;
        $list['total']=count($list);
        $list['list']=array_slice($list, $offset, $limit);
        return $list;

    }
}
