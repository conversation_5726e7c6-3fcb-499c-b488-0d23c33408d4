<?php

namespace app\service;

use app\ErrorCode;
use app\model\UserPortrait;
use app\model\UserPortraitLogs;
use app\validate\UserPortraitValidate;
use think\Exception;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\Log;

/**
 * 用户画像
 * Class UserPortraitService
 * @package app\service
 */
class UserPortraitService
{

    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/05/24 10:02
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id 主键id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //主键id
            }
            #endregion

            #region LIKE name 标题
            if (isset($param['name']) && strlen($param['name']) > 0) {
                $query->where('name', "LIKE", "%{$param['name']}%"); //标题
            }
            #endregion

            #region LIKE subhead 副标题
            if (isset($param['subhead']) && strlen($param['subhead']) > 0) {
                $query->where('subhead', "LIKE", "%{$param['subhead']}%"); //副标题
            }
            #endregion

            #region IN status 状态
            if (isset($param['status']) && strlen($param['status']) > 0) {
                $query->where('status', "IN", $param['status']); //状态:0=禁用,1=启用
            }
            #endregion

            #region >= start_created_time 开始创建时间
            if (isset($param['start_created_time']) && strlen($param['start_created_time']) > 0) {
                $query->whereTime('created_time', '>=', $param['start_created_time']); //开始创建时间
            }
            #endregion

            #region < end_created_time 结束创建时间
            if (isset($param['end_created_time']) && strlen($param['end_created_time']) > 0) {
                $query->whereTime('created_time', '<', $param['end_created_time']); //结束创建时间
            }
            #endregion

            #region >= start_update_time 开始更新时间
            if (isset($param['start_update_time']) && strlen($param['start_update_time']) > 0) {
                $query->whereTime('update_time', '>=', $param['start_update_time']); //开始更新时间
            }
            #endregion

            #region < end_update_time 结束更新时间
            if (isset($param['end_update_time']) && strlen($param['end_update_time']) > 0) {
                $query->whereTime('update_time', '<', $param['end_update_time']); //结束更新时间
            }
            #endregion

        };
    }

    //新增
    public function add($param)
    {
        try {
            validate(UserPortraitValidate::class)
                ->scene('add')
                ->check($param);

//            if(count($param['labels']) > 8){
//                throw new Exception("最多配置8个选项");
//            }

            Db::startTrans();
            UserPortrait::where('status', '<>', 0)->update(['status' => 0]); //

            $param['status'] = 1;
            $model           = new UserPortrait();
            $model->save($param);

            $model->labels()->saveAll($param['labels']);
            Db::commit();

        } catch (Exception $e) {
            Db::rollback();
            throw new \think\Exception($e->getMessage());
        }
        return [];
    }


    //列表
    public function list($param)
    {
        validate(UserPortraitValidate::class)
            ->scene('list')
            ->check($param);

        #region where查询条件,分页,查询数据的处理
        $where = $this->builderWhere($param);

        $limit     = $param['limit'] ?? 10;
        $page      = $param['page'] ?? 1;
        $pagestart = ($page - 1) * $limit;
        $field     = $param['fields'] ?? "*";
        //endregion

        $list  = UserPortrait::with('labels')
            ->field($field)
            ->where($where)
            ->limit($pagestart, $limit)
            ->order('id', "DESC")
            ->select();
        $total = UserPortrait::where($where)->count();

        $data = [
            'list'  => $list,
            'total' => $total
        ];
        return $data;
    }


    //用户是否展示标签
    public function activeUserPortrait($param)
    {
        validate(UserPortraitValidate::class)
            ->scene('activeUserPortrait')
            ->check($param);

        //1.找到最新的启用活动
        $user_portrait = UserPortrait::with('labels')->where('status', 1)->find();
        if (!$user_portrait) {
            return null;
        }

        //2.判断用户是否提交过这个的记录
        $log = UserPortraitLogs::where('user_portrait_id', $user_portrait['id'])
            ->where('uid', $param['uid'])
            ->find();

        if ($log) {
            return null;
        }

        //region打乱排序
        $data = $user_portrait->toArray();
        $labels = $data['labels'];
        $new_labels = [];
        while(!empty($labels)){
            $key = array_rand($labels);
            $new_labels[] = $labels[$key];
            unset($labels[$key]);
        }
        $data['labels'] = $new_labels;
        #endregion

        return $data;
    }


    //用户提交喜好标签
    public function postUserPortrait($param)
    {
        try {
            validate(UserPortraitValidate::class)
                ->scene('postUserPortrait')
                ->check($param);

            Db::startTrans();

            $user_portrait = UserPortrait::with('labels')->where('status', 1)->where('id', $param['user_portrait_id'])->find();
            if (!$user_portrait) throw new Exception("采集状态错误");

            $labels = array_column(collect($user_portrait['labels'])->toArray(), 'name', 'id');

            $user_portrait_labels_ids = explode(',', $param['user_portrait_labels_id']);
            foreach ($user_portrait_labels_ids as $user_portrait_labels_id) {
                if (!in_array($user_portrait_labels_id, array_keys($labels))) throw new Exception("选择的标签错误");
            }

            $log = UserPortraitLogs::where([
                'uid'              => $param['uid'],
                'user_portrait_id' => $param['user_portrait_id'],
            ])->find();
            if ($log) throw new Exception("重复提交");

            $user_url  = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
            $user_url  = $user_url . '?uid=' . $param['uid'] . '&field=uid,telephone,avatar_image,nickname';
            $user_list = get_url($user_url);
            $user_list = get_interior_http_response($user_list);
            $user_list = $user_list['list'] ?? [];
            $user      = empty($user_list[0]) ? [] : $user_list[0];
            if (empty($user)) throw new Exception("用户ID错误");

            $label_text_arr = [];
            foreach ($user_portrait_labels_ids as $user_portrait_labels_id) {
                $label_text       = $this->obliqueLineToMinus($labels[$user_portrait_labels_id]);
                $log_data[]       = [
                    'uid'                     => $param['uid'],
                    'user_portrait_id'        => $param['user_portrait_id'],
                    'user_portrait_labels_id' => $user_portrait_labels_id,
                    'label'                   => $label_text,
                    'plaintext_telephone'     => $user['telephone'] ?? '',
                    'avatar_image'            => $user['avatar_image'] ?? '',
                    'nickname'                => $user['nickname'] ?? '',
                ];
                $label_text_arr[] = $label_text;
            }

            (new UserPortraitLogs)->saveAll($log_data);

            $url        = env('ITEM.RECOMMEND_URL') . '/go-recommend/v3/user/info';
            $header     = ["vinehoo-uid:{$param['uid']}"];
            $code       = httpCurl($url, 'get', [], 10, $header);
            $userResult = json_decode($code, true);

            if ($userResult['error_code'] != '0') {
                Log::error("获取用户数据失败: uid:" . $param['uid'] . ' ret_data-' . $code);
                $up_data = [
                    'uid'       => intval($param['uid']),
                    'labels'    => array_unique($label_text_arr),
                    "subscribe" => [],
                    "comment"   => ""
                ];
            } else {
                $up_data = $userResult['data'];

                foreach ($label_text_arr as $label_str) {
                    $up_data['labels'][] = $label_str;
                }
                $up_data['labels'] = array_unique($up_data['labels']);
            }

            $url2        = env('ITEM.RECOMMEND_URL') . '/go-recommend/v3/user/modify';
            $code2       = httpCurl($url2, 'post', json_encode($up_data), 3, $header);
            $userResult2 = json_decode($code2, true);

            if ($userResult2['error_code'] != '0') {
                Log::error("更新用户推荐标签错误: param:" . json_encode($up_data) . ' ret_data-' . $code);
                throw new Exception("更新用户推荐标签错误: " . ($userResult2['error_msg'] ?? ''));
            }

            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            throw new \think\Exception($e->getMessage());
        }
        return [];
    }

    //斜线转成-号
    public function obliqueLineToMinus($string)
    {
        $arr = [
            '/'  => '-',
            '\\' => '-',
        ];

        $string = str_replace(array_keys($arr), array_values($arr), $string);

        return $string;
    }


    //反馈
    public function feedback($param)
    {
        try {
            validate(UserPortraitValidate::class)
                ->scene('feedback')
                ->check($param);

            if (in_array($param['genre'], ['second_goods'])) {
                $up_data = [
                    "feed_backs" => [
                        [
                            "genre"         => strval($param['genre']),//类型 posts=帖子, news=酒闻, wine=酒闻, auction=拍卖, party=酒会, second_goods=秒发商品
                            "feedback_type" => strval($param['feedback_type']),//反馈行为(read已读,cart加入购物车,like收藏/喜欢,lose_interest不感兴趣,have_bought已经买了,content_repetition(内容重复))
                            "item_id"       => strval($param['item_id']),//项目id
                            "user_id"       => strval($param['uid']),//用户id
                            "timestamp"     => time()//发生行为的时间戳
                        ]
                    ]
                ];

                $url    = env('ITEM.RECOMMEND_URL') . '/go-recommend/v3/feedback/save';
                $header = ["vinehoo-uid:{$param['uid']}"];
                $code   = httpCurl($url, 'post', json_encode($up_data), 3, $header);
                $result = json_decode($code, true);

                if ($result['error_code'] != '0') {
                    Log::error("反馈添加错误: param:" . json_encode($up_data) . ' ret_data-' . $code);
                    throw new Exception("反馈添加错误: " . ($result['error_msg'] ?? ''));
                }
            }
        } catch (Exception $e) {
            throw new \think\Exception($e->getMessage());
        }
        return [];
    }

    //批量反馈
    public function batchFeedback($param)
    {
        $feed_backs = [];
        foreach ($param['items'] as $item) {
            validate(UserPortraitValidate::class)
                ->scene('feedback')
                ->check($item);

            if (in_array($item['genre'], ['second_goods'])) {

                $feed_backs[] = [
                    "genre"         => strval($item['genre']),//类型 posts=帖子, news=酒闻, wine=酒闻, auction=拍卖, party=酒会, second_goods=秒发商品
                    "feedback_type" => strval($item['feedback_type']),//反馈行为(read已读,cart加入购物车,like收藏/喜欢,lose_interest不感兴趣,have_bought已经买了,content_repetition(内容重复))
                    "item_id"       => strval($item['item_id']),//项目id
                    "user_id"       => strval($item['uid']),//用户id
                    "timestamp"     => time()//发生行为的时间戳
                ];
            }
        }

        if (!empty($feed_backs)) {
            try {
                $up_data = ["feed_backs" => $feed_backs];

                $url    = env('ITEM.RECOMMEND_URL') . '/go-recommend/v3/feedback/save';
                $header = ["vinehoo-uid:{$item['uid']}"]; //if判断了的 $item['uid'] 必定会存在
                $code   = httpCurl($url, 'post', json_encode($up_data), 3, $header);
                $result = json_decode($code, true);

                if ($result['error_code'] != '0') {
                    Log::error("反馈添加错误: param:" . json_encode($up_data) . ' ret_data-' . $code);
                    throw new Exception("反馈添加错误: " . ($result['error_msg'] ?? ''));
                }
            } catch (Exception $e) {
                throw new \think\Exception($e->getMessage());
            }
        }

        return [];
    }

}



