<?php

namespace app\service;

use app\model\PeriodsOffsaleProductRecord;
use app\model\PeriodsProductInventory;
use app\model\PeriodsSecondMerchants;
use app\model\PeriodsSecondMerchantsSet;
use app\model\PeriodsStatusChangeRecord;
use app\model\PeriodsVest;

class SecondMerchants {

    /**
     * 添加商品
     * @param array $params
     * @return false|mixed
     */
    public function create(array $params)
    {
        $result = PeriodsSecondMerchants::create($params);
        return $result->id ?? false;
    }

    /**
     * 根据 id 获取商品信息
     * @param int $id
     * @param string $field
     * @return array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOne(int $id, string $field = '*'): ?array {
        $result = PeriodsSecondMerchants::field($field)->find($id);
        return  $result ? $result->toArray() : null;
    }

    /**
     * 更新商品数据
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update(array $params): array
    {
        // 如果是驳回状态改为待审核
//        if (isset($params['onsale_review_status']) && $params['onsale_review_status'] == '4') {
//            $params['onsale_review_status'] = 1;
//        }
        // 更新商品
        $update = PeriodsSecondMerchants::update($params);
        if (!$update) {
            return serviceReturn(false, $params['id'], '更新商品失败');
        }

        return serviceReturn(true, $update->id);
    }

    /**
     * 复制期数套餐
     * @param array $data
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function copyPeriod(array $data): array
    {
        $period = PeriodsSecondMerchants::where('id', 105789)->find()->toArray();
        $period['id'] = $data['id'];
        $period['supplier_id'] = $data['supplier_id'];
        $period['supplier'] = $data['supplier'];
        $period['predict_shipment_time'] = strtotime($period['predict_shipment_time']);
        $period['onsale_time'] = strtotime($period['onsale_time']);
        $period['sell_time'] = strtotime($period['sell_time']);
        $period['sold_out_time'] = strtotime($period['sold_out_time']);
        $add_period = PeriodsSecondMerchants::create($period);
        if (empty($add_period)) {
            return serviceReturn(false, $add_period, '复制期数失败');
        }
        $package = PeriodsSecondMerchantsSet::where('period_id', 105789)->find()->toArray();
        $package['id'] = $data['package_id'];
        $add_package = PeriodsSecondMerchantsSet::create($package);
        if (empty($add_package)) {
            return serviceReturn(false, $add_package, '复制套餐失败');
        }
        return serviceReturn(true);
    }

    /**
     * 更新字段
     * @param int $id
     * @param array $data
     * @return PeriodsSecondMerchants
     */
    public function updateField(int $id, array $data)
    {
        return PeriodsSecondMerchants::where('id', $id)->update($data);
    }

    /**
     * 更新条件
     * @param array $where
     * @param array $data
     * @return PeriodsSecondMerchants
     */
    public function updateWhereField(array $where, array $data)
    {
        return PeriodsProductInventory::where($where)->update($data);
    }

    //商家秒发期数批量下架
    public function batchSoldOut($param)
    {
        $periods  = $param['periods'];
        $up_data  = $param['data'];
        $uid      = request()->header('vinehoo-uid', '0');
        $username = request()->header('vinehoo-vos-name', '');
        if ($username) {
            $username = base64_decode($username);
        }

        // 删除该商品产品记录
        PeriodsOffsaleProductRecord::where('period', 'in', $periods)->delete();

        // 查看是否有马甲
        $vest_id = PeriodsVest::where([
            ['period_id', 'in', $periods],
            ['status', '=', 0]
        ])->column('id');
        if (!empty($vest_id)) {
            // 马甲结束执行
            $url                  = env('ITEM.GOODS_VEST_URL') . '/vest/v3/vest/stopVest';
            $vest_data['vest_id'] = implode(',', $vest_id);
            $vest_headers[]       = 'vinehoo-uid:' . $uid;
            post_url($url, $vest_data, $vest_headers);
        }

        $result = PeriodsSecondMerchants::where('id', 'in', $periods)->update($up_data);

        #region 添加状态操作日志
        $template_data = [
            'type'          => 3,
            'periods_type'  => 9,
            'created_time'  => date('Y-m-d H:i:s', time()),
            'status_type'   => $up_data['onsale_status'],
            'status_name'   => PeriodsSecondMerchants::ONSALE_STATUS[$up_data['onsale_status']],
            'operator_id'   => $uid,
            'operator_name' => $username,
        ];
        $scr_data_list = [];
        foreach ($periods as $period) {
            $scr_data           = $template_data;
            $scr_data['period'] = $period;
            $scr_data_list[]    = $scr_data;
        }
        (new PeriodsStatusChangeRecord)->saveAll($scr_data_list);
        #endregion 添加状态操作日志

        return true;
    }



}
