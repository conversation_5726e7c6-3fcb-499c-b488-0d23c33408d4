<?php

namespace app\service\lib;

use think\facade\Validate;

class WorkWeixinService 
{
    public $user = [];
    public $access_token, $uid, $title;

    public function __construct()
    {
        $this->access_token = $this->getToken();
    }

    /**
     * @方法描述:获取企业内部应用的access_token
     * <AUTHOR>
     * @Date   2022/09/16
     * @return string access_token
     */
    public function getToken()
    {
        $url = env('ITEM.WECHART_URL') . '/wechat/v3/wecom/accesstoken';
        $resq = curl_request($url, [], [], 'GET');
        $resq = json_decode($resq, true);

        return $resq['access_token'] ?? '';
    }

    /**
     * 验证审批数据
     * @param array $param 请求参数
     * @param array 请求参数
     */
    public function verifyData($param)
    {
        if (empty($param['process_instance'])) {
            throw new \Exception('请求参数格式错误');
        }
        $data = $param['process_instance'];
        #数据验证
        $validate = Validate::rule([
            'form_component_values|审批内容' => 'require|array',
            'originator_userid|发送人编号' => 'require',
        ]);
        if (!$validate->check($data)) {
            throw new \Exception($validate->getError());
        }
        #判断审批是否完成
        if (!isset($data['result']) || !isset($data['status'])) {
            throw new \Exception("请求参数格式错误");
        }
        #发起者uid
        $this->uid = $data['originator_userid'];
        return $data;
    }

    /**
     * 发送企业微信中台通知
     * @param string $dingtalk_uid 用户ID
     * @param string $text 发送的文本内容
     * @param int $type 推送类型：0-所有，1-只推送甘高寒
     * @return bool true成功false失败
     */
    public function sendNotify($text, $throw_error = true, $type = 0)
    {
        $result = true;

        $content = $text . '，' . date('Y-m-d H:i:s');
        if (!empty($this->title)) {
            $content ="{$this->title}，{$content}";
        }
        if (!empty($this->user['realname'])) {
            $content = "提交人（{$this->user['realname']}），" . $content;
        }

        $data = [
            'userid'  => $this->uid,
            'content' => $content,
            'msgtype' => 'text',
            'agentid' => 0,
        ];
        $url = env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send';
        if ($type === 0) {
            // 推送审批提交人
            if (!empty($this->uid)) {
                $resq = curl_request($url, json_encode($data));
                $resq = json_decode($resq, true);
                if (!isset($resq['error_code']) || intval($resq['error_code']) !== 0) {
                    $result = $resq['error_msg'] ?? '发送工作通知失败';
                }
            }

        }
        // 推送自己
        $data['userid'] = "GanGaoHan";
        curl_request($url, json_encode($data));
        // 推送测试
        // $data['userid'] = "XuanYiChang";
        // curlRetryRequest($url, json_encode($data));


        #抛出异常
        if ($throw_error === true) {
            throw new \Exception($result === true ? $text : $result);
        }

        return $result;
    }

    /**
     * @方法描述:根据企微ID中台管理员信息
     * <AUTHOR>
     * @Date 2023/05/26
     * @param string $telephone 字段
     * @return array [分页起始值,返回条数]
     */
    function queryMiddleAdminInfo()
    {
        $url = env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info';
        $body = [
            'admin_id' => $this->uid,
            'field'    => 'id,realname,userid',
        ];
        $res = curl_request($url, $body, [], "GET");
        $res = json_decode($res, true);
        $data = $res['data'] ?? [];
        $this->user = [
            'id' => 0,
            'realname' => $this->uid,
            'userid'   => $this->uid,
        ];
        if (!empty($data)) {
            foreach ($data as $v) {
                $this->user = $v;
            }
        }

        return $this->user;
    }

    /**
     * 创建智能表格
     * @param string $title 文档标题
     * @param array $sheets 文档表格字段
     * @return array
     */
    public function CreateSmartTable ($title, $admin_users = ['GanGaoHan'])
    {
        $body = [
            'access_token' => $this->access_token,
            'title' => $title,
            'sheets' => [
                ['title' => 'Sheet1'],
            ],
            'admin_users' => $admin_users
        ];
        $url = env('ITEM.WECHART_URL') . '/wechat/v3/wecom/smartdoc/create';
        $resq = curl_request($url, json_encode($body));
        
        return json_decode($resq, true);
    }

    /**
     * 添加表格字段
     * @param string $title 文档标题
     * @param array $sheets 文档表格字段
     * @return array
     */
    public function AddSmartTableFields ($docid, $sheet_id, $fields)
    {
        $body = [
            'access_token' => $this->access_token,
            'docid' => $docid,
            'sheet_id' => $sheet_id,
            'fields' => $fields
        ];
        $url = env('ITEM.WECHART_URL') . '/wechat/v3/wecom/smartdoc/addfields';
        $resq = curl_request($url, json_encode($body));
        
        return json_decode($resq, true);
    }

    /**
     * 添加表格记录
     * @param string $title 文档标题
     * @param array $sheets 文档表格字段
     * @return array
     */
    public function AddSmartTableRecords ($docid, $sheet_id, $records)
    {
        $body = [
            'access_token' => $this->getToken(),
            'docid' => $docid,
            'sheet_id' => $sheet_id,
            'records' => $records
        ];
        $url = env('ITEM.WECHART_URL') . '/wechat/v3/wecom/smartdoc/addrecord';
        $resq = curl_request($url, json_encode($body));

        return json_decode($resq, true);
    }

    /**
     * 更新智能表格记录
     * @param string $title 文档标题
     * @param array $sheets 文档表格字段
     * @return array
     */
    public function updateRecords ($docid, $sheet_id, $records)
    {
        $body = [
            'access_token' => $this->getToken(),
            'docid' => $docid,
            'sheet_id' => $sheet_id,
            'records' => $records
        ];
        $url = env('ITEM.WECHART_URL') . '/wechat/v3/wecom/smartdoc/updaterecords';
        $resq = curl_request($url, json_encode($body));

        return json_decode($resq, true);
    }

    /**
     * 删除智能文档
     * @param string $docid 文档ID
     * @param array $sheets 文档表格字段
     * @return array
     */
    public function DeleteSmartTable ($docid)
    {
        $body = [
            'access_token' => $this->access_token,
            'doc_id' => $docid,
        ];
        $url = env('ITEM.WECHART_URL') . '/wechat/v3/wecom/smartdoc/delete';
        $resq = curl_request($url, json_encode($body));

        return json_decode($resq, true);
    }

    /**
     * 获取智能表格记录
     * @param string $docid 文档ID
     * @param string $sheet_id 表格ID
     * @param array $fields 表格字段：['字段1', '字段2']
     * @return array 文档数据
     */
    public function GetSmartTableRecord ($docid, $sheet_id, $fields)
    {
        $body = [
            'access_token' => $this->access_token,
            'docid' => $docid,
            'sheet_id' => $sheet_id,
            'offset' => 0,
            'limit' => 0,
            'field_titles' => $fields,
        ];
        $url = env('ITEM.WECHART_URL') . '/wechat/v3/wecom/smartdoc/records';
        $resq = curl_request($url, json_encode($body));
        $res = json_decode($resq, true);

        return $res['records'] ?? [];
    }

}
