<?php

namespace app\service;

use app\ErrorCode;
use app\model\PeriodsLeftover;
use app\model\PeriodsLeftoverSet;
use app\model\PeriodsProductInventory;
use think\Response;
use think\facade\Cache;
use think\facade\Db;
use app\service\Periods as PeriodsSer;
use app\service\es\Es;
use think\facade\Log;

class Leftover {

    /**
     * 添加商品
     * @param array $params
     * @return int
     */
    public function create(array $params): ?int
    {
        $result = PeriodsLeftover::create($params);
        return $result->id ?? false;
    }

    /**
     * 根据 id 获取商品信息
     * @param int $id
     * @param string $field
     * @return array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOne(int $id, string $field = '*'): ?array {
        $result = PeriodsLeftover::field($field)->find($id);
        return  $result ? $result->toArray() : null;
    }

    /**
     * 更新商品数据
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update(array $params): array
    {
        // 如果是驳回状态改为待审核
        if (isset($params['onsale_review_status']) && $params['onsale_review_status'] == '4') {
            $params['onsale_review_status'] = 1;
        }
        // 更新商品
        $update = PeriodsLeftover::update($params);
        if (!$update) {
            return serviceReturn(false, $params['id'], '更新商品失败');
        }

        return serviceReturn(true, $update->id);
    }

    /**
     * 获取接口
     * @param array $data
     * @return PeriodsLeftover[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getLeftoverFieldAll(array $data) {
        return PeriodsLeftover::field($data['field'])->whereIn('id', $data['period'])->select();
    }

    /**
     * 批量上架尾货商品（需要打上渠道标识）
     * @param array $params
     * @return bool|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function batchOnSaleChannel(array $params) {
        $is_exist = Cache::get('BatchOnSaleLeftoverChannelCommand');
        if ($is_exist) {
            return '请勿重复点击';
        }

        Cache::set('BatchOnSaleLeftoverChannelCommand', json_encode($params), 3600);
        
        $rootPath = app()->getRootPath();
        // 执行操作
        exec("php {$rootPath}think batch_on_sale_leftover_channel > /dev/null 2>&1 &");
        return true;
    }

    /**
     * 批量上架尾货商品（需要打上渠道标识）
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function batchOnSaleChannelCreate(array $params) {
        // 供应商信息
        $supplier = Db::table('vh_wiki.vh_supplier')
            ->where([
                ['supplier_name', '=', $params['supplier']],
                ['delete_time', '=', 0],
            ])
            ->field('id,supplier_name,corp')
            ->find();
        if (empty($supplier)) {
            throw new \Exception('供应商不存在');
        }
        $corp_arr = explode(',', $supplier['corp']);
        if (empty($corp_arr)) {
            throw new \Exception('供应商收款公司获取失败');
        }
        if (count($corp_arr) > 1) {
            throw new \Exception('供应商收款公司不能超过1个');
        }
        $corp_id = payeeMerchantIdCodeExchange($corp_arr[0], 1);
        if (empty($corp_id)) {
            throw new \Exception('供应商收款公司获取失败');
        }
        $corp_name = getPayeeMerchantNameID($corp_id);
        
        // 采购信息
        $buyer_id = Db::table('vh_authority.vh_admins')
            ->where('realname', $params['buyer_name'])
            ->value('id');
        if (empty($buyer_id)) {
            throw new \Exception('采购不存在');
        }
        
        $operation_review_id = $params['creator_id'];
        $operation_review_name = $params['creator_name'];
        //采购人员关联
        $buyer_related = Db::table('vh_commodities.vh_buyer_related')
            ->alias('br')
            ->leftJoin('vh_authority.vh_admins a', 'a.id=br.operation_review_id')
            ->where('br.buyer_id', $buyer_id)
            ->where('(br.delete_time = 0 or br.delete_time is null)')
            ->field('a.id,a.realname')
            ->find();
        if (!empty($buyer_related)) {
            $operation_review_id = $buyer_related['id'];
            $operation_review_name = $buyer_related['realname'];
        }

        // 产品信息
        $products = Db::table('vh_wiki.vh_products')
            ->alias('p')
            ->leftJoin('vh_wiki.vh_product_type pt', 'pt.id=p.product_type')
            ->leftJoin('vh_wiki.vh_product_category pc', 'pc.id=p.product_category')
            ->leftJoin('vh_wiki.vh_country_base cb', 'cb.id=p.country_id')
            ->where('p.short_code', $params['short_code'])
            ->field('p.id,pt.name as product_type,p.capacity,pc.name as product_category,p.product_keywords_id,p.cn_product_name,p.en_product_name,p.bar_code,p.short_code,cb.country_name_cn,p.product_attachment_status')
            ->find();
        if (empty($products)) {
            throw new \Exception('产品档案不存在：' . $params['short_code']);
        }
        // 上架时供应商名称如果包含【闪购】，则不验证关单卫检
        if (strpos(strval($params['supplier']), '闪购') === false && $products['product_attachment_status'] != 2 && $params['import_type'] == 1) {
            throw new \Exception("简码 {$params['short_code']} 关单卫检未通过审批或未上传。");
        }
        
        // 产品关键词
        $product_keyword = Db::table('vh_wiki.vh_product_keyword')
            ->whereIn('id', explode(',', $products['product_keywords_id'] ?? ''))
            ->column('name');

        //仓库
        $warehouse = Db::name('virtual_warehouse')
            ->where('virtual_name', $params['warehouse'])
            ->field('physical_name,virtual_name,virtual_id,erp_id')
            ->find();
        if (empty($warehouse)) {
            throw new \Exception('仓库不存在');
        }
        
        $time = time();
        $detail = "<p>{$params['title']}</p>";
        foreach ($params['image_url'] as $k => $v) {
            $detail .= '<p><img src="' . env('ALIURL') . $v . '" alt="" /></p>';
        }

        Db::startTrans();
        try {
            $period_info = [
                'id' => getGeneratorID(1),
                'title' => $params['title'],
                'brief' => $params['title'],
                'banner_img' => $params['image_url'][0] ?? '',
                'product_img' => implode(',', $params['image_url'] ?? []),
                'detail' => $detail,
                'creator_name' => $params['creator_name'],
                'creator_id' => $params['creator_id'],
                'price' => $params['price'],
                'market_price' => $params['price'],
                'is_channel' => 1,
                'is_support_coupon' => 0,
                'predict_shipment_time' => $time + (7 * 86400),
                'onsale_time' => $time,
                'sell_time' => $time,
                'sold_out_time' => $time + (7 * 86400),
                'onsale_status' => 0,//2,
                'copywriting_review_status' => 2,
                'buyer_review_status' => 3,
                'onsale_review_status' => 3,
                'onsale_review_time' => $time,
                'onsale_verify_status' => 0,//1,
                'import_type' => $params['import_type'],
                'operation_id' => $params['creator_id'],
                'operation_name' => $params['creator_name'],
                'operation_review_id' => $operation_review_id,
                'operation_review_name' => $operation_review_name,
                'supplier' => $params['supplier'],
                'supplier_id' => $supplier['id'],
                'buyer_id' => $buyer_id,
                'buyer_name' => $params['buyer_name'],
                'product_id' => $products['id'],
                'created_time' => $time,
                'update_time' => $time,
                'limit_number' => 1,
                'invariant_number' => 1,
                'critical_value' => 1,
                'incremental' => 1,
                'product_category' => $products['product_type'],
                'country' => $products['country_name_cn'],
                'capacity' => $products['capacity'],
                'short_code' => $params['short_code'],
                'product_main_category' => $products['product_category'],
                'product_keyword' => implode(',', $product_keyword ?? []),
                'payee_merchant_id' => $corp_id,
                'payee_merchant_name' => $corp_name,
            ];
            $period = $this->create($period_info);
            if (empty($period)) {
                throw new \Exception('商品创建失败');
            }

            // 创建套餐
            $package_info = [
                'id' => getGeneratorID(2),
                'period_id' => $period,
                'package_name' => $params['package_name'],
                'price' => $params['price'],
                'market_price' => $params['price'],
                'associated_products' => json_encode([[
                    'product_id' => $products['id'],
                    'nums' => 1,
                    'isGift' => 0,
                ]]),
                'inventory' => $params['inventory'],
                'is_onsale' => 1,
                'created_time' => $time,
                'update_time' => $time,
            ];
            $package_result = PeriodsLeftoverSet::create($package_info);
            if (empty($package_result->id)) {
                throw new \Exception('套餐创建失败');
            }

            // 产品库存
            $periods_product_inventory = [
                'period' => $period,
                'periods_type' => 3,
                'product_id' => $products['id'],
                'title' => $params['title'],
                'product_name' => $products['cn_product_name'],
                'en_product_name' => $products['en_product_name'],
                'bar_code' => $products['bar_code'],
                'short_code' => $products['short_code'],
                'inventory' => $params['inventory'],
                'inventory_accum' => $params['inventory'],
                'costprice' => $params['cost'],
                'warehouse' => $warehouse['physical_name'] . '-' . $warehouse['virtual_name'],
                'warehouse_id' => $warehouse['virtual_id'],
                'erp_id' => $warehouse['erp_id'],
                'created_time' => $time,
                'capacity' => $products['capacity'],
            ];
            $result = PeriodsProductInventory::create($periods_product_inventory);
            if (empty($result->id)) {
                throw new \Exception('商品库存创建失败');
            }

            Db::commit();

            // 添加渠道加密
            $redis_config = config('cache.stores.redis');
            $redis_config['select'] = 0;
            $conn = new \think\cache\driver\Redis($redis_config);
            $conn->sadd('vinehoo.details.channel', intval($period));

            // 延时500毫秒等待数据同步es
            usleep(800000);
            try {
                Es::name(Es::PERIODS)->update([
                    'id' => intval($period),
                    'package_prices' => $params['price'],
                ]);
            } catch (\Exception $e) {
                Log::error('batchOnSaleChannelCreate package_prices error：' . $e->getMessage());
            }
            // 生成json文件
            (new PeriodsSer(3))->create_period_json($period, 3);

        } catch (\Exception $e) {
            Db::rollback();
            throw new \Exception($e->getMessage());
        }

        return $period;
    }

}
