<?php

namespace app\service;

use app\ElasticSearchConnection;
use app\model\PeriodsProductInventory;
use app\model\PeriodsProductInventoryOrder;
use app\model\PeriodsRemark;
use app\model\PurchaseOrderno;
use app\service\Periods;
use think\Exception;

class Purchase {

    /**
     * 更新期数订货
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateInventoryOrder(array $params, int $operator, string $operator_name): array
    {
        $is_inventory_order = false;
        $time = time();
        $oper_type = array('减少订货量', '增加订货量');
        foreach ($params as $val) {
            if (!isset($val['order']) || (int)$val['order'] < 1) {
                $val['order'] = 0;
            }
            if (!empty($val['predict_shipment_time']) && !empty($val['current_shipment_time'])) {
                if (strtotime($val['predict_shipment_time']) < strtotime($val['current_shipment_time'])) {
                    return serviceReturn(false, [], '超卖发货时间不能小于当前发货时间。');
                }
            }

            $result = 1;
            if ($val['order'] > 0) {
                if (empty($val['predict_shipment_time'])) {
                    return serviceReturn(false, [], '修改订货量必须填写超卖发货时间');
                }

                $is_inventory_order = true;
                $result = 0;
                // 更新订货量
                if ($val['type'] == 1) {
                    $result = PeriodsProductInventory::where('id', $val['id'])->inc('order', $val['order'])
                        ->update();
                }
                // 更新订货量
                if ($val['type'] == 0) {
                    $result = PeriodsProductInventory::where('id', $val['id'])->dec('order', $val['order'])
                        ->update();
                }
            }

            // 记录日志
            if ($result > 0 && !empty($val['predict_shipment_time'])) {
                $order_log = [];
                $order_log['period'] = $val['period'];
                $order_log['short_code'] = $val['short_code'];
                $order_log['type'] = $val['type'];
                $order_log['order'] = $val['order'];
                $order_log['operator'] = $operator_name;
                $order_log['operator_id'] = $operator;
                $order_log['predict_shipment_time'] = strtotime($val['predict_shipment_time']);
                $order_log['created_time'] = $time;
                $log_re = PeriodsProductInventoryOrder::insert($order_log);
                if (!$log_re) {
                    return serviceReturn(false, $order_log, $val['short_code']. ' 日志记录失败');
                }
                // 添加期数备注
                $periods_type = PeriodsProductInventory::where('id', $val['id'])
                    ->field('periods_type,short_code')->find();
                if (!empty($periods_type)) {
                    $r['period'] = $val['period'];
                    $r['periods_type'] = $periods_type['periods_type'];
                    $r['remark'] = date('Y-m-d H:i:s', $time). $operator_name . '【'.$periods_type['short_code'].'】'.
                    $oper_type[$val['type']].$val['order'] . '，超卖发货时间：'.$val['predict_shipment_time'];
                    $r['operator'] = $operator;
                    $r['operator_name'] = $operator_name;
                    $r['created_time'] = $time;
                    PeriodsRemark::insert($r);
                }
            }
        }
        // 修改当前发货时间
        if (!empty($params[0]['current_shipment_time'])) {
            $periods_ser = new Periods($params[0]['periods_type']);
            $periods_ser->updateField(
                intval($params[0]['period']),
                'predict_shipment_time',
                strtotime($params[0]['current_shipment_time'])
            );
        }

        // 查询期数日志
        $order_list = PeriodsProductInventoryOrder::where('period', $params[0]['period'])->select()->toArray();

        if ($is_inventory_order) {
            // 更新 es 订货量
            $es = new ElasticSearchConnection();
            $params = [
                'index' => 'vinehoo.periods',
                'id' => $params[0]['period'],
                'body' => [
                    'doc' => [
                        'is_inventory_order' => 1
                    ]
                ]
            ];
            $es->connection()->update($params);
        }
        return serviceReturn(true, $order_list, '更新成功');
    }

    /**
     * 期数列表
     * @param int $period
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getInventoryOrderList(int $period): array
    {
        $i_o = PeriodsProductInventory::where(['period' => $period, 'is_use_comment' => 0])
            ->field('id,product_name,period,periods_type,costprice,short_code,product_id,inventory,warehouse,order')
            ->select()
            ->toArray();
        $p_i = [];
        if (!empty($i_o)) {
            $product_id = array_column($i_o, 'product_id');
            // 查询最低成本价
            $product_info = PeriodsProductInventory::where([
                ['product_id', 'in', $product_id],
                ['costprice', '>', 0],
            ])
            ->field('product_id,min(costprice)')
            ->group('product_id')
            ->column('product_id,min(costprice) as costprice','product_id');

            // 初始赋值
            foreach ($i_o as &$val) {
                $val['saled'] = 0;
                // 成本价
                $val['costprice'] = floatval($val['costprice']);
                // 历史最低成本
                $val['min_costprice'] = 0;
                $product = $product_info[$val['product_id']] ?? [];
                if (!empty($product['costprice'])) {
                    $val['min_costprice'] = floatval($product['costprice']);
                }
            }

            unset($val);
            // 获取简码已售
            $url = env('ITEM.USER_CACHE_URL'). '/commodities/GetSaleBottleNums';
            $req_data = [['period' => $period, 'period_type' => (int)$i_o[0]['periods_type']]];
            $req_data = json_encode($req_data);
            $url_header[] = 'Content-Type: application/json;charset=utf-8';
            $url_req = post_url($url, $req_data, $url_header);
            if ($url_req) {
                $url_req = json_decode($url_req, true);
                // 赋值已售库存
                if ($url_req['error_code'] == 0) {
                    foreach ($i_o as &$val) {
                        foreach ($url_req['data'][$period] as $k => $v) {
                            if ($k == $val['short_code']) {
                                $val['saled'] = $v;
                            }
                        }
                    }
                    unset($val, $v);
                }
            }
            // 查询最新超卖发货时间
            $predict_shipment_time = PeriodsProductInventoryOrder::where('period', $period)
                ->order('created_time desc')
                ->value('predict_shipment_time');
            $periods_ser = new Periods((int)$i_o[0]['periods_type']);
            $p_i = $periods_ser->getOne($period, 'id,predict_shipment_time');
            $p_i['periods_type'] = intval($i_o[0]['periods_type']);
            $p_i['current_shipment_time'] = $p_i['predict_shipment_time'];
            $p_i['predict_shipment_time'] = !empty($predict_shipment_time) ? date('Y-m-d H:i:s', $predict_shipment_time) : '';
        }
        // 查询期数日志
        $order_list = PeriodsProductInventoryOrder::where('period', $period)->select()->toArray();
        $re['list'] = $i_o;
        $re['period_info'] = $p_i;
        $re['order_log'] = $order_list;
        return $re;
    }

    /**
     * 获取产品历史成本价
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getProductHistoricalCostprice($params)
    {
        $list = PeriodsProductInventory::where([
            ['product_id', '=', $params['product_id']],
            ['period', '<>', $params['period']],
            ['costprice', '>', 0],
        ])
        ->field('period,costprice')
        ->select()->toArray();

        return ['list' => $list];
    }

    // 获取期数简码库存信息
    public function getProductInventoryList(array $period, string $field = 'id,short_code,inventory'): array
    {
        return PeriodsProductInventory::where([['period', 'in', $period], ['is_use_comment', '=', 0]])
            ->field($field)
            ->select()
            ->toArray();
    }

    /**
     * 添加采购单
     * @param array $params
     * @return int|string
     */
    public function addPurchaseOrderno(array $params)
    {
        return PurchaseOrderno::insert($params);
    }

    /**
     * 采购单列表
     * @param array $params
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     */
    public function getPurchaseOrdernoList(array $params) {
        // 分页
        $limit = 15;
        if (isset($params['limit']) && $params['limit']) {
            $limit = $params['limit'];
        }
        return PurchaseOrderno::with(['items'])->whereFindInSet('period', $params['period'])->paginate([
                'list_rows'=> $limit,
                'var_page' => 'page',
            ]);
    }

    /**
     * 批量货期期数最新采购单
     * @param array $period
     * @return mixed
     */
    public function getPurchaseNewOrdernoField(int $period, string $field) {
        return PurchaseOrderno::whereFindInSet('period', $period)->order('created_time', 'desc')->value($field);
    }

    /**
     * 删除期数采购单
     * @param array $period
     * @return mixed
     */
    public function delPurchaseOrderno(int $id)
    {
        $model = PurchaseOrderno::where('id', $id)->find();

        if ($model['source'] == 1) {
            //自动的需要验证
            if ($model['operate_status'] == 1) {
                throw new Exception('已推送U8C,不可移除');
            }
        }

        return $model->delete();
    }

}