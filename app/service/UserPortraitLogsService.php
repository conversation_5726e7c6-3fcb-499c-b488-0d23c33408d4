<?php

namespace app\service;

use app\model\UserPortraitLogs;
use app\validate\UserPortraitLogsValidate;

/**
 * 用户画像标签
 * Class UserPortraitLogsService
 * @package app\service
 */
class UserPortraitLogsService
{


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/05/24 10:22
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id 主键id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //主键id
            }
            #endregion

            #region EQ uid 用户ID
            if (isset($param['uid']) && strlen($param['uid']) > 0) {
                $query->where('uid', "=", $param['uid']); //用户ID
            }
            #endregion

            #region EQ user_portrait_id 用户画像ID
            if (isset($param['user_portrait_id']) && strlen($param['user_portrait_id']) > 0) {
                $query->where('user_portrait_id', "=", $param['user_portrait_id']); //用户画像ID
            }
            #endregion

            #region EQ user_portrait_labels_id 标签ID
            if (isset($param['user_portrait_labels_id']) && strlen($param['user_portrait_labels_id']) > 0) {
                $query->where('user_portrait_labels_id', "=", $param['user_portrait_labels_id']); //标签ID
            }
            #endregion

            #region LIKE label 标签
            if (isset($param['label']) && strlen($param['label']) > 0) {
                $query->where('label', "LIKE", "{$param['label']}%"); //标签
            }
            #endregion

            #region >= start_created_time 开始创建时间
            if (isset($param['start_created_time']) && strlen($param['start_created_time']) > 0) {
                $query->whereTime('created_time', '>=', $param['start_created_time']); //开始创建时间
            }
            #endregion

            #region < end_created_time 结束创建时间
            if (isset($param['end_created_time']) && strlen($param['end_created_time']) > 0) {
                $query->whereTime('created_time', '<', $param['end_created_time']); //结束创建时间
            }
            #endregion

        };
    }

    public function listByUserPortrait($param)
    {
        validate(UserPortraitLogsValidate::class)
            ->scene('listByUserPortrait')
            ->check($param);

        #region where查询条件,分页,查询数据的处理
        $where = $this->builderWhere($param);

        $limit     = $param['limit'] ?? 10;
        $page      = $param['page'] ?? 1;
        $pagestart = ($page - 1) * $limit;
        $field     = $param['fields'] ?? "*";
        //endregion

        $list  = UserPortraitLogs::where($where)
            ->limit($pagestart, $limit)
            ->order('id', "DESC")
            ->select();
        $total = UserPortraitLogs::where($where)->count();

        $data = [
            'list'  => $list,
            'total' => $total
        ];
        return $data;
    }
}



