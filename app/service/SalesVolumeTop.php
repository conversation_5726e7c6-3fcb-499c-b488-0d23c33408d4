<?php

namespace app\service;

use Elasticsearch\ClientBuilder;
use think\facade\Db;
use think\facade\Log;

class SalesVolumeTop
{
    protected $es;

    public function __construct()
    {
        $hosts[] = [
            'host' => env('ES.HOST','127.0.0.1'),
            'port' => env('ES.PORT',9200),
            'user' => env('ES.USER','root'),
            'pass' => env('ES.PASS','vinehoo666')
        ];

        // 连接 es
        $this->es = ClientBuilder::create()->setHosts($hosts)->build();
    }

    /**
     * 更新TOP
     * @author: jx
     * @date: 2023-05-11
     * @param string $type 类型：月（month）、周（week）
     * @return array
     */
    public function updateTop(string $type): array
    {
        try {
            // 获取分类列表
            list($res, $msg, $typeList) = $this->getTypeList($type);
            if (!$res) {
                $msg = '获取TOP分类列表失败：' . $msg;
                Log::error($msg);
                return [false, $msg];
            }

            // 获取各分类TOP并更新
            foreach ($typeList as $v) {
                list($res, $msg, $data) = $this->getTop(['type' => $type, 'data' => $v['data']]);
                if (!$res) {
                    $msg = '[' . $v['name'] . ']获取TOP失败：' . $msg;
                    Log::error($msg);
                    return [false, $msg];
                }
                Log::info('[' . $v['name'] . ']获取TOP成功：' . json_encode($data));

                // 更新TOP
                list($res, $msg) = $this->saveTop(['name' => $v['redis'], 'data' => $data]);
                if (!$res) {
                    $msg = '[' . $v['name'] . ']更新TOP失败：' . $msg;
                    Log::error($msg);
                    return [false, $msg];
                }
                Log::info('[' . $v['name'] . ']更新TOP成功');
            }

            return [true, ''];
        } catch (\Exception $e) {
            return [false, $e->getMessage()];
        }
    }

    /**
     * 获取TOP分类列表
     * @author: jx
     * @date: 2023-05-11
     * @param string $type 类型：月（month）、周（week）
     * @return array
     */
    private function getTypeList(string $type): array
    {
        try {
            switch ($type) {
                case 'month':
                    $list = [
                        [
                            'name' => '红葡萄酒上月销量',
                            'redis' => 'month_h',
                            'data' => Db::connect('wiki')->name('product_type')->where('fid', 1)->column('name'),
                        ],
                        [
                            'name' => '白葡萄酒上月销量',
                            'redis' => 'month_b',
                            'data' => Db::connect('wiki')->name('product_type')->where('fid', 6)->column('name'),
                        ],
                        [
                            'name' => '烈酒上月销量',
                            'redis' => 'month_l',
                            'data' => Db::connect('wiki')->name('product_type')->where('fid', 22)->column('name'),
                        ],
                        [
                            'name' => '起泡&香槟上月销量',
                            'redis' => 'month_xq',
                            'data' => ['香槟', '起泡酒'],
                        ],
                    ];
                    break;

                case 'week':
                    // 酒类中所有的上级分类id
                    $typeFidAll = Db::connect('wiki')->name('product_type')
                        ->where('pid', 1)
                        ->where('fid', '>', 0)
                        ->distinct(true)
                        ->column('fid');
                    // 酒类中所有的子级分类名称
                    $typeNameAll = Db::connect('wiki')->name('product_type')
                        ->where('pid', 1)
                        ->whereNotIn('id', $typeFidAll)
                        ->column('name');
                    $list = [
                        [
                            'name' => '上周销量',
                            'redis' => 'week_all',
                            'data' => $typeNameAll,
                        ],
                    ];
                    break;

                default:
                    $list = [];
            }
            return [true, '', $list];
        } catch (\Exception $e) {
            return [false, $e->getMessage(), []];
        }
    }

    /**
     * 获取TOP
     * @author: jx
     * @date: 2023-05-11
     * @param array $param
     * @return array
     */
    private function getTop(array $param): array
    {
        try {
            // 期数销量列表
            $orderQtyList = $this->getOrderQtyList(['type' => $param['type']]);
            // 期数id列表
            $periodIdList = array_keys($orderQtyList);
            // 期数信息列表
            $periodList = $this->getPeriodList(['idList' => $periodIdList, 'productCategoryList' => $param['data']]);
            // 获取简码排名
            $shortCodeTop = $this->getShortCodeTop($periodList, $orderQtyList);
            // 获取排名前三的期数
            $periodTop = [];
            foreach ($shortCodeTop as $v) {
                $period = $this->getPeriodInfo($v);
                if (isset($period[0]['id'])) {
                    $periodId = (int) $period[0]['id'];
                    if (!in_array($periodId, $periodTop)) {
                        $periodTop[] = $periodId;
                        if (count($periodTop) == 3) {
                            break;
                        }
                    }
                }
            }
            return [true, '', $periodTop];
        } catch (\Exception $e) {
            return [false, $e->getMessage(), []];
        }
    }

    /**
     * 获取期数销量列表
     * @author: jx
     * @date: 2023-05-11
     * @param array $param
     * @return array
     */
    private function getOrderQtyList(array $param): array
    {
        // es 查询条件
        $esWhere['bool']['must'][] = ['terms' => ['sub_order_status' => [1, 2, 3]]];
        $esWhere['bool']['must'][] = ['terms' => ['order_type' => [1,9]]];
        if ($param['type'] == 'month') {
            // 上月一日
            $lastMonth = date('Y-m-01 00:00:00', strtotime('last month'));
            // 本月一日
            $thisMonth = date('Y-m-01 00:00:00');
            // 大于等于上月一日，小于本月一日
            $esWhere['bool']['must'][] = ['range' => ['payment_time' => ['gte' => $lastMonth, 'lt' => $thisMonth]]];
        } elseif ($param['type'] == 'week') {
            if (date('w') == 1) {
                // 当前周一，取上周一
                $lastMondayTime = strtotime('last monday');
            } else {
                // 当前非周一，向前推一周，再取周一
                $lastMondayTime = strtotime('-1 week last monday');
            }
            // 上周一
            $lastMondayDay = date('Y-m-d 00:00:00', $lastMondayTime);
            // 本周一
            $thisMondayDay = date('Y-m-d 00:00:00', strtotime('+1 week', $lastMondayTime));
            // 大于等于上周一，小于本周一
            $esWhere['bool']['must'][] = ['range' => ['payment_time' => ['gte' => $lastMondayDay, 'lt'  => $thisMondayDay]]];
        }

        $params = [
            'index' => 'vinehoo.orders',
            'type' => '_doc',
            '_source' => ['period', 'order_qty'],
            'body' => [
                'query' => $esWhere,
                'size' => 10000,
            ]
        ];

        // es 查询
        $es_data = $this->es->search($params);

        // es 解析后数据
        $data = get_es_result($es_data);

        $list = [];
        foreach ($data['list'] as $v) {
            if (!isset($list[$v['period']])) {
                $list[$v['period']] = $v['order_qty'];
            } else {
                $list[$v['period']] += $v['order_qty'];
            }
        }

        return $list;
    }

    /**
     * 获取期数信息列表
     * @author: jx
     * @date: 2023-05-11
     * @param array $param
     * @return array
     */
    private function getPeriodList(array $param): array
    {
        // es 查询条件
        $esWhere['bool']['must'][] = ['terms' => ['_id' => $param['idList']]];
        $esWhere['bool']['must'][] = ['match' => ['is_channel' => '0']];
        $should = [];
        foreach ($param['productCategoryList'] as $v) {
            $should[] = ['match' => ['product_category' => $v]];
        }
        $esWhere['bool']['must'][] = ['bool' => ['should' => $should]];

        $params = [
            'index' => 'vinehoo.periods',
            'type' => '_doc',
            '_source' => ['id', 'short_code'],
            'body' => [
                'query' => $esWhere,
                'size' => 10000,
            ]
        ];

        // es 查询
        $es_data = $this->es->search($params);

        // es 解析后数据
        $data = get_es_result($es_data);

        return $data['list'];
    }

    /**
     * 获取简码排名
     * @author: jx
     * @date: 2023-05-11
     * @param array $periodList 期数列表
     * @param array $orderQtyList 期数订单列表
     * @return array
     */
    private function getShortCodeTop(array $periodList, array $orderQtyList): array
    {
        // 组装简码销量列表
        $shortCodeList = [];
        foreach ($periodList as $v) {
            // 循环计算期数中简码的订单销量
            foreach ($v['short_code'] as $val) {
                if (!isset($shortCodeList[$val])) {
                    $shortCodeList[$val] = $orderQtyList[$v['id']];
                } else {
                    $shortCodeList[$val] += $orderQtyList[$v['id']];
                }
            }
        }

        // 根据值进行降序排序
        arsort($shortCodeList);

        // 返回简码排名
        return array_keys($shortCodeList);
    }

    /**
     * 获取期数详情
     * @author: jx
     * @date: 2023-05-11
     * @param string $shortCode
     * @return array
     */
    private function getPeriodInfo(string $shortCode): array
    {
        // es 查询条件
        $esWhere['bool']['must'][] = ['term' => ['onsale_status' => 2]];
        $esWhere['bool']['must'][] = ['bool' => ['should' => ['match' => ['short_code' => $shortCode]]]];

        $params = [
            'index' => 'vinehoo.periods',
            'type' => '_doc',
            '_source' => ['id'],
            'body' => [
                'query' => $esWhere,
                'size' => 1,
            ]
        ];

        // es 查询
        $es_data = $this->es->search($params);

        // es 解析后数据
        $data = get_es_result($es_data);

        return $data['list'];
    }

    /**
     * 更新TOP
     * @author: jx
     * @date: 2023-05-11
     * @param array $param
     * @return array
     */
    private function saveTop(array $param): array
    {
        try {
            $redis = new \Redis();
            $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
            $redis->auth(env('cache.PASSWORD'));
            $redis->select(6);
            $redis->hSet('period.top', $param['name'], json_encode($param['data']));
            return [true, ''];
        } catch (\Exception $e) {
            return [false, $e->getMessage()];
        }
    }
}

