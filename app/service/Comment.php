<?php

namespace app\service;

use app\model\PeriodsComment;
use app\model\PeriodsCommentLike;
use app\model\PeriodsProductInventory;
use app\model\PeriodsTaskLog;
use app\service\elasticsearch\ElasticSearchService;
use think\cache\driver\Redis;
use think\facade\Cache;
use think\facade\Db;

class Comment
{

    /**
     * 获取评论
     * @param array $params
     * @return mixed
     */
    public function getComment(array $params)
    {
        $where = [];
        // 简码搜索
        if (isset($params['short_code']) && $params['short_code']) {
            $where[] = ['a.short_code', 'in', $params['short_code']];
        }
        // 期数搜索
        if (isset($params['period']) && $params['period']) {
            $where[] = ['a.period', '=', $params['period']];
        }
        // 分页
        $limit = 15;
        if (isset($params['limit']) && $params['limit']) {
            $limit = $params['limit'];
        }
        return PeriodsProductInventory::where($where)
            ->alias('a')
            ->join('periods_comment b', 'a.period = b.period')
            ->field('a.short_code, a.product_name, a.en_product_name, a.bar_code, a.period, b.hot_num')
            ->group('a.short_code')
            ->paginate([
                'list_rows'=> $limit,
                'var_page' => 'page',
            ])
            ->map(function ($item) {
                // 历史期数
                $item->history_periods_count = PeriodsProductInventory::where('a.short_code', $item->short_code)
                    ->alias('a')
                    ->join('periods_comment b', 'a.period = b.period')
                    ->group('b.period')
                    ->count();
                // 总评论数
                $item->comment_count = PeriodsComment::where('b.short_code', $item->short_code)
                    ->alias('a')
                    ->join('periods_product_inventory b', 'a.period = b.period')
                    ->count();
                // 已选评论总数
                $item->selected_count = PeriodsComment::where(['b.short_code' => $item->short_code, 'is_show' => 1])
                    ->alias('a')
                    ->join('periods_product_inventory b', 'a.period = b.period')
                    ->count();
                // 产品已购瓶数
                $item->sells_count = 0;
                return $item;
            });
    }

    /**
     * 根据简码获取期数评论
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPeriodsByShortCode(array $params): array
    {
        // 简码包含评论的期数
        $period_list = PeriodsComment::alias('a')
            ->rightJoin('periods_product_inventory b', 'a.period = b.period')
            ->group('b.period')
            ->field('b.period, b.periods_type')
            ->where('b.short_code', $params['short_code'])
            ->select()
            ->toArray();
        foreach ($period_list as &$val) {
            // 查询期数上架时间
            $period_ser = new Periods((int)$val['periods_type']);
            $period_info = $period_ser->getOne($val['period'], 'title, onsale_time');
            $val['title'] = $period_info['title'] ?? '';
            $val['onsale_time'] = $period_info['onsale_time'] ?? '';
            unset($period_ser, $period_info);
            // 精选评论总数
            $val['goods_count'] = PeriodsComment::alias('a')
                ->join('periods_product_inventory b', 'a.period = b.period')
                ->where([
                    'a.period' => $val['period'],
                    'b.short_code' => $params['short_code'],
                    'a.is_goods' => 1
                ])
                ->count();
            // 总评论数
            $val['count'] = PeriodsComment::alias('a')
                ->join('periods_product_inventory b', 'a.period = b.period')
                ->where([
                    'a.period' => $val['period'],
                    'b.short_code' => $params['short_code'],
                ])
                ->count();
            // 已选评论
            $val['selected_count'] = PeriodsComment::alias('a')
                ->join('periods_product_inventory b', 'a.period = b.period')
                ->where([
                    'a.period' => $val['period'],
                    'b.short_code' => $params['short_code'],
                    'a.is_show' => 1
                ])
                ->count();
            // 已购瓶数
            $val['sells_count'] = 0;
        }
        return $period_list;
    }

    /**
     * 根据期数，产品简码查询评论
     * @param array $params
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getCommentByPeriodAndShortCode(array $params)
    {
        // 查询评论列表
        $field = 'a.id, a.uid, a.period, b.short_code, a.content, a.likenums, a.is_goods,
         a.is_show, a.pid, a.created_time,a.emoji_image, a.hot_num';
        return PeriodsComment::alias('a')
            ->join('periods_product_inventory b', 'a.period = b.period')
            ->where([
                'a.period' => $params['period'],
                'b.short_code' => $params['short_code'],
                'a.pid' => 0
            ])
            ->field($field)
            ->select()->map(function ($item) {
                $item->is_buy = 0;
                // 查询用户
//                $user = esGetOne($item->uid, 'vinehoo.user');
                $u_u = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
                $u_u = $u_u . '?uid=' . $item->uid . '&field=uid,user_level,avatar_image,nickname';
                $u_l = get_url($u_u);
                $u_l = get_interior_http_response($u_l);
                $u_l = $u_l['list'] ?? [];
                $item->avatar_image = $u_l[0]['avatar_image'] ?? '';
                if ($item->avatar_image != '') {
                    $item->avatar_image = env('ALIURL') . $item->avatar_image;
                }
                $item->nickname = $u_l[0]['nickname'] ?? '';
                $item->user_level = $u_l[0]['user_level'] ?? '';
                // redis 链接
                $redis = new \Redis();
                $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
                $redis->auth(env('cache.PASSWORD'));
                $redis->select(3);
                // 是否已购
//                $item->is_buy = $this->getESOrderBuy((int)$item->uid, (int)$item->period);
                $item->is_buy = $redis->zScore('vinehoo.hought.' . $item->period, $item->uid);
                if ($item->is_buy > 0) {
                    $item->is_buy = 1;
                }

                // 查询回复
                $item->reply = PeriodsComment::where('first_id', $item->id)
                    ->field('id, uid, content, likenums, emoji_image, created_time')
                    ->select()
                    ->map(function ($reply_item) {
                        // redis 链接
                        $redis = new \Redis();
                        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
                        $redis->auth(env('cache.PASSWORD'));
                        $redis->select(3);
                        // 查询用户
//                        $user = esGetOne($reply_item->uid, 'vinehoo.user');
                        // 查询用户
                        $user_url = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
                        $user_url = $user_url . '?uid=' . $reply_item->uid . '&field=uid,user_level,avatar_image,nickname';
                        $user_list = get_url($user_url);
                        $user_list = get_interior_http_response($user_list);
                        $user_list = $user_list['list'] ?? [];
                        $reply_item->avatar_image = $user_list[0]['avatar_image'] ?? '';
                        if ($reply_item->avatar_image != '') {
                            $reply_item->avatar_image = env('ALIURL') . $reply_item->avatar_image;
                        }
                        $reply_item->nickname = $user_list[0]['nickname'] ?? '';
                        $reply_item->user_level = $user_list[0]['user_level'] ?? '';
                        // 是否已购
//                        $reply_item->is_buy = $this->getESOrderBuy((int)$reply_item->uid, (int)$reply_item->period);
                        $reply_item->is_buy = $redis->zScore('vinehoo.hought.' . $reply_item->period, $reply_item->uid);
                        if ($reply_item->is_buy > 0) {
                            $reply_item->is_buy = 1;
                        }

                        return $reply_item;
                    })->toArray();
                return $item;
            });
    }

    /**
     * 查询 es 订单是否已购
     * @param int $uid 用户 id
     * @param int $period 期数
     * @return int
     */
    public function getESOrderBuy(int $uid, int $period): int
    {
        $es = new ElasticSearchService();
        $query = [
            'index' => ['orders'],
            'match' => [
                ['uid' => $uid],
                ['period' => $period],
            ],
            'terms' => [['sub_order_status' => [1, 2, 3]]]
        ];
        $order = $es->getDocumentList($query);
        return empty($order['data']) ? 0 : 1;
    }


    /**
     * 更新评论
     * @param array $params
     * @return int
     */
    public function upPeriodComment(array $params): int
    {
        return PeriodsComment::where('id', $params['id'])->update($params);
    }

    /**
     * 根据回复id更新评论
     * @param array $params
     * @return int
     */
    public function upPeriodCommentByReplyId(array $params): int
    {
        return PeriodsComment::where('reply_id', $params['id'])->update($params);
    }

    /**
     * 审核商品评论
     * @param array $data
     * @return int
     */
    public function periodCommentAudit(array $data): int
    {
        return PeriodsComment::where('id', $data['id'])->update($data);
    }

    /**
     * 查询评论用户信息
     * @param array $params
     * @return mixed|null
     */
    public function getUserCommentInfo(array $params)
    {
        // 查询用户
        $user_url = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
        $user_url = $user_url . '?uid=' . $params['uid'] . '&info_type=2';
        $result = get_url($user_url);
        // redis 链接
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(3);
        $user_info = get_interior_http_response($result);
        if (!empty($user_info['list'])) {
            foreach ($user_info['list'] as &$val) {
                $val['is_buy'] = $redis->sIsMember('vinehoo.hought.' . $params['period'], $val['uid']);
            }
        }
        return $user_info;
    }

    /**
     * 添加评论
     * @param array $params
     * @return int|mixed
     */
    public function createComment(array $params)
    {
        if (isset($params['automatic_task']) && !empty($params['automatic_task'])) {
            $params['created_time'] = strtotime($params['automatic_task_time']);
        }
        if(empty($params['label'])){
        // 查询马甲账号
        $vest_user_label = Db::table('vh_user.vh_vest_user')
            ->where([
                ['uid', 'in', $params['uid']],  // 用户ID
                ['admin_id', '=', request()->header('vinehoo-uid')],  // 用户ID
                ['is_del', '=', 0],
            ])
            ->order('label desc')
            ->value('label');
        $params['label'] = str_replace(',', '/', ($vest_user_label ?? ''));
        }
        // 添加评论
        $params['source'] = $params['source'] ?? 1;
        $result = PeriodsComment::create($params);
        $commit_id = $result->id ?? 0;
        // 如果自动任务
        if (isset($params['automatic_task']) && !empty($params['automatic_task'])) {
            $this->task($commit_id, strtotime($params['automatic_task_time']));
        }
        return $commit_id;
    }

    // 建立秒级任务任务
    public function task(int $id, int $trigger_time)
    {
        // 建立秒级任务地址
        $task_url = env('item.SLS_URL') . '/services/v3/task/add';
        // 更新秒级任务地址
//        $up_task_url = env('item.SLS_URL') . '/services/v3/task/update';
        // 回调地址
        $re_url = env('item.COMMODITIES_URL') . '/commodities/v3/comment/automaticTaskComment';
        $task_id = uuid();
        $task_data['task_trigger_time'] = $trigger_time;
        $task_data['task_url'] = $re_url;
        $task_data['task_data'] = (string)json_encode([
                'comment' => $id,
                'audit_status' => 1,
                'task_id' => $task_id
            ]
        );
        $task_data['task_id'] = $task_id;
        $task_data = json_encode($task_data);
        $url_header[] = 'vinehoo-client: tp6-commodities';
        $url_header[] = 'vinehoo-client-version: v3';
        $url_header[] = 'Content-Type: application/json;charset=utf-8';
        $url_header[] = 'Content-Length: ' . strlen($task_data);
        $t1 = post_url($task_url, $task_data, $url_header);
        $t1 = json_decode($t1, true);
        // 记录任务日志
        $task_log_data['task_id'] = $task_id;
        $task_log_data['period'] = $id;
        $task_log_data['task_trigger_time'] = $trigger_time;
        $task_log_data['response'] = $t1['error_code'] ?? null;
        $task_log_data['remark'] = '评论：' . $id . ' 自动上架任务';
        $task_log_data['created_time'] = time();
        $task_log_data['type'] = 4;
        PeriodsTaskLog::create($task_log_data);
    }

    /**
     * 后台用户评论列表
     * @param array $params
     * @return array
     */
    public function getCommentList(array $params): array
    {
        $where = [];
        // 期数查询
        if (isset($params['period']) && !empty($params['period'])) {
            $where[] = ['a.period', '=', $params['period']];
        }
        // 内容查询
        if (isset($params['content']) && !empty($params['content'])) {
            $where[] = ['a.content', 'like', '%'.$params['content'].'%'];
        }
        if (isset($params['label']) && !empty($params['label'])) {
            $where[] = ['a.label', 'like', '%' . $params['label'] . '%'];
        }
        if(preg_match("/\d/", $params['is_show'] ?? '')){
            // 状态：0-禁用/待审核(只有自己可见) 1-启用/审核通过 2-审核驳回 3-删除'
            $where[] = ['a.is_recommend', '=', $params['is_show']];
        }
        if (preg_match("/\d/", $params['source'] ?? '')) {
            $where[] = ['a.source', $params['source'] == 1 ? '<=' : "=", $params['source']];
        }
        // 用户查询
        if (isset($params['uname']) && !empty($params['uname'])) {
            $user_url = env('ITEM.USER_URL') . '/user/v3/user/nicknameMatchUser';
            $user_url = $user_url . '?nickname=' . $params['uname'] . '&limit=10';
            $user_list = get_url($user_url);
            $user_list = get_interior_http_response($user_list);
            $user_list = $user_list['list'] ?? [];
            $uid = [];
            if (!empty($user_list)) {
                foreach ($user_list as $value) {
                    array_push($uid, $value['uid']);
                }
            }

            if (!empty($uid)) {
                $where[] = ['a.uid', 'in', $uid];
            }
        }
        // 用户标签搜索
        if (!empty($params['user_label_name'])) {
            $userLabelName = explode(',', trim($params['user_label_name']));
            $userLabelIds = getUserByLabelName($userLabelName);
            if (!$userLabelIds) {
                $userLabelIds = [-1];
            }
            $where[] = ['a.uid', 'in', $userLabelIds];
        }

        $limit = $params['limit'] ?? 10;
        // 评论数据
        $list = PeriodsComment::alias('a')
            ->leftJoin('periods_product_inventory b', 'a.period = b.period')
            ->field('a.id, a.uid, a.period, b.title, a.content, a.uname, a.created_time, a.likenums,a.vest_is_buy,
                a.first_id, a.pid, a.p_uid, a.audit_status, a.is_goods, a.is_show, a.is_recommend, a.hot_num, 
                a.emoji_image, a.label, a.source')
            ->group('a.id')
            ->order('a.id', 'desc')
            ->where($where)
            ->paginate([
                'list_rows'=> $limit,
                'var_page' => 'page',
            ]);
        // 分页
        $result['total'] = $list->total() ?? 0;
        $result['list'] = $list->getCollection()->toArray() ?? [];
        // 获取用户信息
        $uid = '';
        // redis 链接
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(3);
        if (!empty($result['list'])) {
            foreach ($result['list'] as &$val) {
                $uid .= $val['uid'] . ",";
                // 是否已购
//                $val['is_buy'] = $this->getESOrderBuy((int)$val['uid'], (int)$val['period']);
                $val['is_buy'] = $redis->zScore('vinehoo.hought.' . $val['period'], $val['uid']);
                if ($val['is_buy'] > 0) {
                    $val['is_buy'] = 1;
                }

                if ($val['vest_is_buy'] == 0) {
                    $val['vest_is_buy'] = $val['is_buy'];
                }

            }
            unset($val);
            $uid = trim($uid,',');
            // 查询马甲账号
            $vest_user = Db::table('vh_user.vh_vest_user')
                ->where([
                    ['uid', 'in', $uid],
                    ['is_del', '=', 0]
                ])
                ->column('id','uid');
            // 查询用户
            $user_url = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
            $user_url = $user_url . '?uid=' . $uid . '&field=uid,user_level,nickname,type,user_label';
            $user_list = get_url($user_url);
            $user_list = get_interior_http_response($user_list);
            $user_list = $user_list['list'] ?? [];
            // 写入用户等级
            foreach ($result['list'] as &$val) {
                $val['user_level'] = 0;
                $val['user_label_name'] = 0;
                foreach ($user_list as $v) {
                    if ($v['uid'] == $val['uid']) {
                        $val['user_level'] = $v['user_level'];
                        $val['nickname'] = $v['nickname'];
                        $val['uname'] = $v['nickname'];
                        $val['is_official'] = (int) $v['type'] ?? 0;
                        if (!empty($v['user_label'])) {
                            $user_label = json_decode($v['user_label'], true);
                            $val['user_label_name'] = implode('、', array_column($user_label, 'name'));
                        }
                    }
                }
                $val['is_vest_user'] = 0;
                if (!empty($vest_user[$val['uid']])) {
                    $val['is_vest_user'] = 1;
                }
            }
        }
        return $result;
    }

    /**
     * 添加喜欢评论
     * @param array $data
     * @return PeriodsCommentLike|array|\think\Model
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function likeComment(array $data) {
        $exists = PeriodsCommentLike::where(['uid' => $data['uid'], 'comment_id' => $data['comment_id']])->find();
        if (intval($data['action']) === 0) {// 点赞
            if (!empty($exists)) {
                return [];
            }
            $result = PeriodsCommentLike::create($data);
            if (!empty($result)) {
                PeriodsComment::where('id', $data['comment_id'])->inc('likenums')->update();
            }
        } else {//取消点赞
            if (empty($exists)) {
                return [];
            }
            $result = $exists->delete();
            if (!empty($result)) {
                $comment = PeriodsComment::where('id', $data['comment_id'])->find();
                if (!empty($comment)) {
                    $likenums = $comment['likenums'] ?? 0;
                    $likenums = intval($likenums - 1) > 0 ? intval($likenums - 1) : 0;
                    PeriodsComment::where('id', $data['comment_id'])->update(['likenums' => $likenums]);
                }
                
            }
        }
        
        return $result;
    }

    // 批量添加评论
    public function createCommentAll(array $data): int
    {
        return PeriodsComment::insertAll($data);
    }

}