<?php

namespace app\service;

use app\ErrorCode;
use app\model\PeriodsAuction;
use app\model\PeriodsAuctionBidRecord;
use app\model\PeriodsAuctionFollow;
use app\model\PeriodsAuctionSet;
use app\model\PeriodsFlash;
use app\model\PeriodsUserAuction;
use app\Request;
use think\Response;

class Auction {

    /**
     * 添加商品
     * @param array $params
     * @return int
     */
    public function create(array $params): ?int
    {
        $result = PeriodsAuction::create($params);
        return $result->id ?? false;
    }

    /**
     * 根据 id 获取商品信息
     * @param int $id
     * @param string $field
     * @return array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOne(int $id, string $field = '*'): ? array {
        $result = PeriodsAuction::field($field)->find($id);
        return  $result ? $result->toArray() : null;
    }

    /**
     * 更新商品数据
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update(array $params)
    {
        // 更新商品
        unset($params['creator_id'], $params['creator_name']);
        // 如果是驳回状态改为待审核
        if (isset($params['onsale_review_status']) && $params['onsale_review_status'] == '4') {
            $params['onsale_review_status'] = 1;
        }
        $update = PeriodsAuction::update($params);
        if (!$update) {
            return serviceReturn(false, $params['id'], '更新商品失败');
        }

        return serviceReturn(true, $update->id);
    }

    /**
     * 拍卖运营信息更新
     * @param array $params
     * @return false|mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function auctionSet(array $params) {

        $field = 'onsale_time,sell_time,sold_out_time,onsale_review_status,onsale_verify_status, onsale_status';
        $period_info = $this->getOne($params['period'], $field);
        if (empty($period_info)) {
            return 0;
        }
        $periods_ser = new Periods(11);
        if ($period_info['onsale_verify_status'] == 1) {
            // 拍品状态，建立自动任务（上架，开拍）
            if ($params['onsale_time'] == $params['sell_time'] && $params['sell_time'] > time()) {
                $params['onsale_status'] = 0;
                $periods_ser->task($params['period'], $params['sell_time'], 1, 2);
            } elseif ($params['onsale_time'] != $params['sell_time']) {
                if ($params['onsale_time'] > time()) {
                    $params['onsale_status'] = 0;
                    $periods_ser->task($params['period'], $params['onsale_time'], 0);
                }
                if ($params['sell_time'] < time() && $params['onsale_time'] > time()) {
                    $params['onsale_status'] = 1;
                    $periods_ser->task($params['period'], $params['sell_time'], 1, 2);
                }
            }
            // 拍品下架自动任务
            if ($params['sold_out_time'] > time()) {
                $periods_ser->task($params['period'], $params['sold_out_time'], 2, 3);
            }
            // 拍卖截拍自动任务
            if ($params['closing_auction_time'] > time()) {
                $periods_ser->task($params['period'], $params['closing_auction_time'], 5, 4);
            }
        }

        $result = PeriodsAuction::update($params);
        return $result->id ?? false;

    }

    /**
     * 添加用户拍卖出价记录
     * @param array $data
     * @return PeriodsAuctionBidRecord|\think\Model
     */
    public function createDidRecord(array $data) {
        $data['anon_id'] = $data['anon_id'] ?? 'JY'.mt_rand(100000, 999999);
        $data['created_time'] = time();
        return PeriodsAuctionBidRecord::create($data);
    }

    /**
     * 查询出价记录
     * @param array $params
     * @return PeriodsAuctionBidRecord[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getBidRecord(array $params) {
        if (empty($params)) {
            return  [];
        }
        $where = [];
        $where['bid_status'] = 1;
        if (isset($params['period']) && !empty($params['period'])) {
            $where['period'] = $params['period'];
        }
        if (isset($params['uid']) && !empty($params['uid'])) {
            $where['uid'] = $params['uid'];
        }
        if (isset($params['bid_status']) && !empty($params['bid_status'])) {
            $where['bid_status'] = $params['bid_status'];
        }
        $limit = $params['limit'] ?? 100;
//        return PeriodsAuctionBidRecord::where($where)
//            ->order('created_time', 'desc')
//            ->limit($limit)
//            ->select();
        return $this->getAuctionBidList((int)$params['period']);
    }

    /**
     * 关注拍卖
     * @param array $data
     * @return PeriodsAuctionFollow|\think\Model
     */
    public function createAuctionFollow(array $data) {
        return PeriodsAuctionFollow::create($data);
    }

    /**
     * 拍卖首页商品
     * @return PeriodsAuction[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getAuctionIndex() {
        return PeriodsAuction::whereIn('onsale_status', [1,2,4])
            ->field('id,onsale_status,title,onsale_time,sell_time,sold_out_time,closing_auction_time,price,
            final_auction_price,banner_img,product_img,pageviews')
            ->order(['sort' => 'desc', 'sell_time' => 'desc'])
            ->select()->map(function ($item) {
                $item->bid_count = $this->getAuctionBidList((int)$item->id)['count'];
                // 最新价
                $item->final_auction_price = $this->getAuctionNewBidPrice((int)$item->id) ? : $item->price;
                return $item;
            });
    }

    /**
     * 删除关注拍卖
     * @param int $period
     * @param int $user_id
     * @return bool
     */
    public function delAuctionFollow(int $period, int $user_id): bool
    {
        return PeriodsAuctionFollow::where(['period' => $period, 'user_id' => $user_id])->delete();
    }

    /**
     * 用户关注拍卖列表
     * @param int $uid
     * @return mixed
     */
    public function getMyAuctionFollow(int $uid) {
        return PeriodsAuctionFollow::alias('a')->leftJoin('periods_auction b', 'a.period = b.id')
            ->field('a.id, b.id as period, b.title, b.onsale_status, b.onsale_time, b.sell_time, b.sold_out_time,
             b.closing_auction_time, b.price, b.final_auction_price, b.banner_img, b.product_img,pageviews')
            ->where('a.user_id', $uid)
            ->whereIn('b.onsale_status', '1,2,3')
            ->order('b.closing_auction_time', 'asc')
            ->select()->map(function ($item) {
                $item->onsale_time = date('Y-m-d H:i:s', (int)$item->onsale_time);
                $item->sell_time = date('Y-m-d H:i:s', (int)$item->sell_time);
                $item->sold_out_time = date('Y-m-d H:i:s', (int)$item->sold_out_time);
                $item->closing_auction_time = date('Y-m-d H:i:s', (int)$item->closing_auction_time);
                $b_i = explode(",", $item->banner_img);
                foreach ($b_i as &$b) {
                    $b = env('ALIURL').$b;
                }
                $item->banner_img = $b_i;
                $p_i = explode(",", $item->product_img);
                foreach ($p_i as &$v) {
                    $v = env('ALIURL').$v;
                }
                $item->product_img = $p_i;
                return $item;
            });
    }

    /**
     * 用户拍下列表
     * @param int $uid
     * @return mixed
     */
    public function getUserAuction(int $uid) {
        return PeriodsUserAuction::alias('a')->leftJoin('periods_auction b', 'a.period = b.id')
            ->field('a.id, a.pay_status, b.id as period, b.title, b.onsale_status, b.onsale_time, b.sell_time,
             b.sold_out_time, b.closing_auction_time, b.price, b.final_auction_price, b.banner_img, b.product_img, 
             b.pageviews, a.created_time, b.is_support_ts, b.is_cold_chain, b.predict_shipment_time')
            ->where(['a.user_id' => $uid, 'a.pay_status' => 0])
            ->order('a.created_time', 'asc')
            ->select()->map(function ($item) {
                $item->onsale_time = date('Y-m-d H:i:s', (int)$item->onsale_time);
                $item->sell_time = date('Y-m-d H:i:s', (int)$item->sell_time);
                $item->sold_out_time = date('Y-m-d H:i:s', (int)$item->sold_out_time);
                $item->closing_auction_time = date('Y-m-d H:i:s', (int)$item->closing_auction_time);
                $b_i = explode(",", $item->banner_img);
                foreach ($b_i as &$b) {
                    $b = env('ALIURL').$b;
                }
                $item->banner_img = $b_i;
                $p_i = explode(",", $item->product_img);
                foreach ($p_i as &$v) {
                    $v = env('ALIURL').$v;
                }
                $item->product_img = $p_i;
                $item->package = PeriodsAuctionSet::where(['period_id' => $item->period, 'is_hidden' => 0])->find();
                return $item;
            });
    }

    /**
     * 获取前端拍卖商品详情
     * @param int $period
     * @param int $uid
     * @return PeriodsAuction|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getIndexAuctionDetail(int $period, int $uid) {
        $info = PeriodsAuction::whereIn('id', $period)
            ->field('id,onsale_status,onsale_time,sell_time,sold_out_time,closing_auction_time,price,
            final_auction_price,banner_img,product_img,pageviews,delay_period,markup')
            ->find();
        // 增加浏览量
        PeriodsAuction::where('id', $period)->inc('pageviews')->update();
        $info['follow'] = 0;
        $info['final_auction_price'] = $info['price'];
        if (!empty($info)) {
            // 统计关注
            $info['follow'] = PeriodsAuctionFollow::where('period', $period)->count();
            // 最新出价
            $info['final_auction_price'] = $this->getAuctionNewBidPrice($period);
            $info['is_follow'] = 0;
            if ($uid) {
                $user_follow = PeriodsAuctionFollow::where(['period' => $period, 'user_id' => $uid])->value('id');
                if ($user_follow) {
                    $info['is_follow'] = 1;
                }
            }
        }
        return $info;
    }

    /**
     * 获取 redis 最新出价价格
     * @param int $period
     * @return int|mixed
     */
    public function getAuctionNewBidPrice(int $period) {
        // 最新价格
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(11);
        $key = "auction_{$period}_history";
        $bid = $redis->lRange($key, 0, 0) ?? [];
        if (!empty($bid)) {
            $bid = json_decode($bid[0], true);
            return $bid['bid_price'];
        }
        return 0;
    }

    /**
     * 获取最新出价记录
     * @param int $period
     * @return array|mixed
     */
    public function getAuctionNewBid(int $period) {
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(11);
        $key = "auction_{$period}_history";
        $bid = $redis->lRange($key, 0, 0) ?? [];
        if (!empty($bid)) {
            return json_decode($bid[0], true);
        }
        return [];
    }

    /**
     * 获取出价记录
     * @param int $period
     * @return array
     */
    public function getAuctionBidList(int $period): array
    {
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(11);
        $key = "auction_{$period}_history";
        $count = $redis->lLen($key) ?? 0;
        $list = $redis->lRange($key, 0, -1) ?? [];
        if (!empty($list)) {
            foreach ($list as &$val) {
                $val = json_decode($val, true);
            }
        }
        return ['list' => $list, 'count' => $count];
    }

    /**
     * 批量设置 redis 键值对
     */
    public function setAuctionRedis(array $auction) {
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(11);
        if (!empty($auction)) {
            foreach ($auction as $key => $val) {
                $redis->set($key, $val);
            }
        }
    }

    /**
     * 批量删除 redis 键值对
     */
    public function delAuctionRedis(array $auction) {
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(11);
        if (!empty($auction)) {
            foreach ($auction as $key => $val) {
                $redis->del($key);
            }
        }
    }

    /**
     * 更新用户拍卖创建订单
     * @param int $period
     * @param int $uid
     * @param int $pay_status
     * @return static
     */
    public function updateUserAuction(int $period, int $uid, int $pay_status)
    {
        return PeriodsUserAuction::where(['period' => $period, 'user_id' => $uid])
            ->update(['pay_status' => $pay_status]);
    }

    /**
     * 获取用户竞拍中期数
     * @param int $uid
     * @return mixed
     */
    public function getUserAuctionNow(int $uid) {
        return PeriodsAuction::alias('a')->field('a.id')
            ->leftJoin('periods_auction_bid_record b', 'a.id = b.period')
            ->where(['a.onsale_status' => 2, 'b.uid' => $uid])
            ->find();
    }

}
