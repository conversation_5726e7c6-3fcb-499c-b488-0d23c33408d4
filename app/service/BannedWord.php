<?php

namespace app\service;

use app\ErrorCode;
use app\model\BannedWord as BannedWordModel;


use think\Response;

class BannedWord {

    /**
     * 添加违禁词
     * @param array $params
     * @return int
     */
    public function create(array $params): ?int
    {
        $result = BannedWordModel::create($params);
        return $result->id ?? false;
    }

    /**
     * 删除违禁词
     *
     * @param int $id
     * @return bool|null
     */
    public function delete(int $id): ?bool
    {
        return BannedWordModel::destroy($id);
    }


    /**
     * 违禁词列表
     *
     * @return BannedWordModel[]|array|bool|\think\Collection|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list()
    {
        return BannedWordModel::select();
    }

}
