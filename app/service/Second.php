<?php

namespace app\service;

use app\ErrorCode;
use app\model\PeriodsSecond;
use think\Response;
use think\facade\Db;

class Second
{

    /**
     * 添加商品
     * @param array $params
     * @return false|mixed
     */
    public function create(array $params)
    {
        $result = PeriodsSecond::create($params);
        return $result->id ?? false;
    }

    /**
     * 根据 id 获取商品信息
     * @param int $id
     * @param string $field
     * @return array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOne(int $id, string $field = '*'): ?array
    {
        $result = PeriodsSecond::field($field)->find($id);
        return  $result ? $result->toArray() : null;
    }

    /**
     * 更新商品数据
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update(array $params): array
    {
        // 如果是驳回状态改为待审核
        if (isset($params['onsale_review_status']) && $params['onsale_review_status'] == '4') {
            $params['onsale_review_status'] = 1;
        }
        // 更新商品
        $update = PeriodsSecond::update($params);
        if (!$update) {
            return serviceReturn(false, $params['id'], '更新商品失败');
        }

        return serviceReturn(true, $update->id);
    }

    /**
     * 获取商品ES筛选条件
     * <AUTHOR>
     * @Date   2023/06/29
     * @param array $param 请求参数
     * @param array $esWhere es查询条件
     * @return array 筛选条件
     */
    static function getFilterCriteria($param, $esWhere = [])
    {
        $second_id = $param['second_id'] ?? 1;
        switch (intval($second_id)) {
            case 1: //白葡萄酒
                $esWhere['bool']['must'][] = ['terms' => [
                    'product_category' => ["白葡萄酒", "干白葡萄酒", "半干白葡萄酒", "半甜白葡萄酒", "甜白葡萄酒"]
                ]];
                break;
            case 2: //红葡萄酒
                $esWhere['bool']['must'][] = ['terms' => [
                    'product_category' => ["红葡萄酒", "干红葡萄酒", "半干红葡萄酒", "半甜红葡萄酒", "甜红葡萄酒"]
                ]];
                break;
            case 3: //起泡香槟
                $esWhere['bool']['must'][] = ['terms' => [
                    'product_category' => ["起泡酒", "干型起泡酒", "半干起泡酒", "半甜起泡酒", "甜型起泡酒", "香槟"]
                ]];
                break;
            case 4: //烈酒
                $esWhere['bool']['must'][] = ['terms' => [
                    'product_category' => ["烈酒", "白兰地", "威士忌", "金酒", "伏特加", "朗姆酒", "龙舌兰"]
                ]];
                break;
            case 5: //其他
                $esWhere['bool']['must_not'][] = ['terms' => [
                    'product_category' => ["白葡萄酒", "干白葡萄酒", "半干白葡萄酒", "半甜白葡萄酒", "甜白葡萄酒", "红葡萄酒", "干红葡萄酒", "半干红葡萄酒", "半甜红葡萄酒", "甜红葡萄酒", "起泡酒", "干型起泡酒", "半干起泡酒", "半甜起泡酒", "甜型起泡酒", "香槟", "烈酒", "白兰地", "威士忌", "金酒", "伏特加", "朗姆酒", "龙舌兰"]
                ]];
                break;
        }

        if (!empty($param['key'])) {
            switch ($param['key']) {
                case 'price': // 价格
                    if (strpos($param['value'], '以上') !== false) {
                        $value = substr($param['value'], 0, strpos($param['value'], '以上'));
                        $esWhere['bool']['must'][] = [
                            'range' => [
                                'price' => ['gt' => $value]
                            ]
                        ];
                    } else {
                        $between_price = explode('-', $param['value']);
                        if ((int)$between_price[0] <= 0) {
                            $between_price[0] = 0;
                        }
                        if (!empty($between_price)) {
                            $esWhere['bool']['must'][] = [
                                'range' => [
                                    'price' => [
                                        'gte' => $between_price[0],
                                        'lte' => $between_price[1]
                                    ]
                                ]
                            ];
                        }
                    }
                    break;
                default:
                    $param['value'] = $param['value'] ?? '';
                    if ($param['value'] == '其他') {
                        #查询子类
                        $subclasses = Db::name('periods_second_sub_classes')
                            ->where([
                                ['main_id', '=', $second_id],
                                ['status', '=', 1],
                                ['keywords', '=', $param['key']],
                            ])
                            ->value('list');
                        $subclasses = explode(',', $subclasses);
                        $key_list = [];
                        foreach ($subclasses as $v) {
                            if (!empty($v) && $v != '其他') {
                                $key_list[] = $v;
                            }
                        }
                        $esWhere['bool']['must_not'][] = ['terms' => [
                            $param['key'] => $key_list,
                        ]];
                    } else {
                        $esWhere['bool']['must'][] = ['match_phrase' => [$param['key'] => $param['value']]];
                    }
                    break;
            }
        }

        return $esWhere;
    }
}
