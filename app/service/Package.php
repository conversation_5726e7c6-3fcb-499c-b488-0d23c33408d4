<?php

namespace app\service;

use app\model\GeneratorId;
use app\model\PackageObiRecord;
use app\model\PeriodsAuctionSet;
use app\model\PeriodsCrossSet;
use app\model\PeriodsFlashSet;
use app\model\PeriodsLeftoverSet;
use app\model\PeriodsOffsaleProductRecord;
use app\model\PeriodsProductInventory;
use app\model\PeriodsProductInventoryOrder;
use app\model\PeriodsRabbitSet;
use app\model\PeriodsSecondMerchantsSet;
use app\model\PeriodsSecondSet;
use app\model\SetMealUpdateRecord;
use app\service\elasticsearch\ElasticSearchService;
use app\service\Periods as PeriodsSer;
use think\Env;
use think\Model;
use think\facade\Db;
use think\cache\driver\Redis;
use think\facade\Log;
use app\service\es\Es;
use Elasticsearch\Endpoints\Ml\Forecast;

class Package
{

    // 数据模型
    protected $model = null;

    // 频道类型
    protected $type = 0;

    // 设置数据模型
    function __construct(int $periods_type)
    {
        $this->type = $periods_type ?? 0;
        switch ($periods_type) {
            case '0':
                // 闪购
                $this->model = new PeriodsFlashSet();
                break;
            case '1':
                // 秒发
                $this->model = new PeriodsSecondSet();
                break;
            case '2':
                // 跨境
                $this->model = new PeriodsCrossSet();
                break;
            case '3':
                // 尾货
                $this->model = new PeriodsLeftoverSet();
                break;
            case '4':
                // 兔头
                $this->model = new PeriodsRabbitSet();
                break;
            case '9':
                // 商家秒发
                $this->model = new PeriodsSecondMerchantsSet();
                break;
            case '11':
                $this->model = new PeriodsAuctionSet();
                break;
        }
    }

    /**
     * 添加商品套餐
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function create(array $params): array
    {
        // 是否为第一个套餐
        $exists_package = $this->model::where(['period_id' => $params['period_id'], 'is_hidden' => 0])->value('id');
        // 更新商品售价
        if (empty($exists_package)) {
            // 如果为第一个套餐不允许隐藏
//            if ($params['is_hidden'] == 1) {
//                return serviceReturn(false, [], '请勿隐藏第一个套餐');
//            }
            $periods_ser = new PeriodsSer($this->type);
            // 更新商品默认价格
            $up_data['price'] = $params['price'] ?? 0;
            $up_data['market_price'] = $params['market_price'] ?? 0;
            // 如果为兔头商品，更新兔头价格
            if ($this->type == 4) {
                $up_data['rabbit'] = $params['rabbit'] ?? 0;
            }
            $periods_ser->updateInfoById((int)$params['period_id'], $up_data);
        }
        $result = $this->model::create($params);
        return serviceReturn(true, $result, '添加成功');
    }

    /**
     * 创建自选套餐所有匹配套餐
     * @param $data
     * @return array|\think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function createCustomPackage($data)
    {
        try {
            Db::startTrans();
            $pkg_ids = array_column($data, 'id');
            $sub_package = $this->model::whereIn('source_package_id', $pkg_ids)
                ->column('id,source_package_id', 'source_package_id');

            $period_ids = array_column($data, 'period_id');
            $periods_product_map = Db::name('periods_product_inventory')
                ->whereIn('period', $period_ids)
                ->group('product_id')
                ->column('product_id,short_code', 'product_id');
            
            foreach ($data as $v) {
                if (!empty($sub_package[$v['id']])) {
                    continue;
                }
                $products_data = json_decode($v['associated_products'], true);
                $products_value = [];
                foreach ($products_data as $p) {
                    if (!is_array($p['product_id'])) {
                        $p['product_id'] = [$p['product_id']];
                    }
                    foreach ($p['product_id'] as $product_id) {
                        $products_value[] = [
                            'product_id' => intval($product_id ?? 0),
                            'nums' => 1,
                            'isGift' => $p['isGift'] ?? 0,
                        ];
                    }
                }
                $product_count = $v['custom_product_count'];

                // 生成所有可能的组合
                $combinations = [];
                $this->combine($products_value, $product_count, 0, [], $combinations);

                // 为每个组合创建套餐
                foreach ($combinations as $combination) {
                    $sub_package = $v;
                    $short_code = [];
                    foreach ($combination as $product) {
                        $periods_product = $periods_product_map[$product['product_id'] ?? ''] ?? [];
                        if (!empty($periods_product['short_code'])) {
                            $short_code[] = $periods_product['short_code'];
                        }
                    }
                    if (!empty($short_code)) {
//                        $sub_package['package_name'] = '自选：' . implode('+', $short_code);
                    }
                    $sub_package['id'] = $this->getGeneratorID(2);
                    $sub_package['package_img'] = str_replace(env('ALIURL'), '', $sub_package['package_img']);
                    $sub_package['associated_products'] = json_encode($combination);
                    $sub_package['is_hidden_package'] = 1; // 设置为隐藏状态
                    $sub_package['source_package_id'] = $v['id'];
                    $sub_package['is_custom_package'] = 0;
                    $sub_package['custom_product_count'] = 0;
                    // $sub_package['is_hidden'] = 1; // 设置为隐藏状态

                    $this->model::create($sub_package);
                }
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            echo $e->getMessage();exit;
            return $e->getMessage();
        }

        return true;
    }

    /**
     * 递归生成组合
     * @param array $products 产品列表
     * @param int $count 每组产品数量
     * @param int $start 开始位置
     * @param array $current 当前组合
     * @param array &$result 结果数组
     */
    private function combine($products, $count, $start, $current, &$result)
    {
        if (count($current) == $count) {
            $result[] = $current;
            return;
        }

        for ($i = $start; $i < count($products); $i++) {
            $current[] = $products[$i];
            $this->combine($products, $count, $i + 1, $current, $result);
            array_pop($current);
        }
    }

    /**
     * 秒发添加商品套餐
     * @param $data
     * @return array|\think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function createArr($data)
    {
        #region 验证及格式化数据
        // 参数
        $params = $data;

        $period_id = (int)$params[0]['period_id'];
        // 添加商品套餐
        $result = 0;
        // 验证设置某一个套餐设置了套餐限量，全部需要设置
        $is_set = 0;
        $is_no_set = 1;
        // 对比次数，防止全部隐藏时无法对比
        $contrast_count = 0;
        if (count($params) > 1) {
            foreach ($params as &$validate) {
                $validate['inventory'] = $validate['inventory'] ?? 0;
                // 只判断非隐藏套餐
                if ($validate['is_hidden'] == 1) {
                    continue;
                }
                $validate['unlimited'] = (int)$validate['unlimited'];
                if ($validate['unlimited'] > 0) {
                    $is_set = 1;
                } else {
                    $is_no_set = 0;
                }
                $validate['inventory_accum'] = $validate['inventory'];
                $contrast_count++;
            }
            if ($contrast_count > 1 && ($is_set != $is_no_set)) {
//                return throwResponse([], ErrorCode::EXEC_ERROR, '套餐限量设置不一致');
//                return serviceReturn(false, [], '套餐限量设置不一致');

            }
        }

        // 检测套餐数据格式
        $package_prices = [];
        foreach ($params as &$value) {
//            $associated_products = json_decode($value['associated_products'], true);
            $associated_products = $value['associated_products'];
            if (empty($associated_products)) {
//                return throwResponse([], ErrorCode::EXEC_ERROR, '套餐数据格式有误');
                return serviceReturn(false, [], '套餐数据格式有误');


            }
            foreach ($associated_products as &$val) {
                if ($value['is_mystery_box'] != 1) {
                    $val['product_id'] = (int)$val['product_id'];
                    if ($val['product_id'] <= 0) {
//                        return throwResponse([], ErrorCode::EXEC_ERROR, '套餐产品设置有误');
                        return serviceReturn(false, [], '套餐产品设置有误');
                    }
                }
                $val['nums'] = (int)$val['nums'];
                if ($val['nums'] <= 0) {
//                    return throwResponse([], ErrorCode::EXEC_ERROR, '套餐产品数量设置有误');
                    return serviceReturn(false, [], '套餐产品数量设置有误');

                }
            }
            // 检测成功转回 json
            $value['associated_products'] = (string)json_encode($associated_products);
            // 取出未隐藏套餐价格
            if ($value['is_hidden'] == 0) {
                array_push($package_prices, $value['price']);
            }
        }
        #endregion 验证及格式化数据

        // 更新 es 套餐价格
        if (!empty($package_prices)) {
            sort($package_prices);
            $package_prices_str = implode('/', $package_prices);
            $es_ser = new ElasticSearch();
//            $es_ser->updatePeriodsById($period_id, ['package_prices' => $package_prices_str]);
        }
        unset($value, $val);
        $err = '';
//        $period = new Periods((int)$params[0]['periods_type']);
        $period = new Periods(9);
        // 查询当前期数运营审核状态
        $period_info = $period->getOne((int)$params[0]['period_id'], 'onsale_review_status,onsale_status');

        if (empty($period_info)) {
            return serviceReturn(false, [], '未获取到期数：' . $params[0]['period_id']);

        }
        $package_validate = new \think\Validate();
        // 验证是否存在新增套餐，解决只有一个套餐隐藏新增同时出现时，套餐无法隐藏问题
        // 添加套餐
        foreach ($params as $cv) {
            if (!isset($cv['id']) || empty($cv['id'])) {
                // 获取商品套餐id
                $cv['id'] = $this->getGeneratorID(2);
                if (!$cv['id']) {
                    return serviceReturn(false, [], '生成商品套餐失败');

                }
                // 如果运营已审核产品，直接上架
                if ($period_info['onsale_review_status'] == 3) {
                    $cv['is_onsale'] = 1;
                }
                $cv['created_time'] = time();
                $cv['inventory_accum'] = $cv['inventory'] ?? 0;
                // 参数验证
                $validate_params = $package_validate->check($cv);
                if (!$validate_params) {
                    return serviceReturn(false, [], $package_validate->getError());

                }
                unset($validate_params);
                $result = $this->create($cv);
                if (!$result['status']) {
                    $err = $result['msg'];
                }
            }
        }
        if ($err) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, $err);
        } else {
            $result = $result['data'];
        }
        // 更新套餐
        foreach ($params as $v) {
            // 如果传入 id 更新套餐是否隐藏属性
            if (isset($v['id']) && $v['id'] != '') {
                if (isset($v['package_name']) && strlen($v['package_name']) > 150) {
                    return serviceReturn(false, [], '套餐名称长度不能超过 50 字符');

                }
                // 如果运营已审核产品，只更新隐藏套餐
                if ($period_info['onsale_review_status'] == 3) {
                    $pac_result = $this->update([
                        'id' => $v['id'],
                        'is_hidden' => $v['is_hidden'],
                        'unlimited' => $v['unlimited'],
                        'is_onsale' => 1
                    ]);
                } else {
                    // 如果未审核更新全部套餐属性
                    $pac_result = $this->update($v);
                }
                if ($pac_result['status'] == true) {
                    $result = $pac_result['data'];
                } else {
                    $err = $pac_result['msg'];
                }
            }
        }

        // 添加套餐编辑人
//        $params['creator_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
//        $params['creator_name'] = $request->header('vinehoo-vos-name', '');
//        if ($params['creator_name']) {
//            $params['creator_name'] = base64_decode($params['creator_name']);
//        }
//
//        $up_data = ['operation_id' => $params['creator_id'], 'operation_name' => $params['creator_name']];
//
//        $period->updateInfoById($period_id, $up_data);

        if ($err) {
            return serviceReturn(false, $result, $err);
        }

        return serviceReturn(true);

    }

    /**
     * 生成自增id
     * @param int $id
     * @return false|mixed
     */
    public function getGeneratorID(int $id) {
        GeneratorId::startTrans();
        $g_id = GeneratorId::where('id', $id)->value('g_id');
        $r_id = $g_id + 1;
        $inc = GeneratorId::where('id', $id)->inc('g_id', 1)->update();
        if ($inc) {
            GeneratorId::commit();
            return $r_id;
        } else {
            GeneratorId::rollback();
        }
        return false;
    }

    /**
     * 复制添加商品套餐
     *
     * @param array $params
     * @return int|null
     */
    public function copyCreate(array $params): ?int
    {
        $result = $this->model::create($params);
        return $result->id ?? false;
    }

    /**
     * 更新套餐
     * @param array $params
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function update(array $params): array
    {
        // 更新前产品数据
        $package_info = $this->getOne($params['id']);
        // 隐藏套餐，查看是否存在其它套餐
        $count = $this->model::where([
            ['period_id', '=', $package_info['period_id']],
            ['id', '<>', $params['id']],
            ['is_hidden', '=', 0]
        ])->count();
        if ($params['is_hidden'] == 1 && $count < 1) {
            return serviceReturn(false, $params['id'], '不可隐藏套餐，套餐内无其它可用套餐');
        }

        // 更新套餐
        $result = $this->model::update($params);

        // 如果隐藏了套餐，重置商品价格，取第一个套餐价格
        //        if ($params['is_hidden'] == 1) {
            $pack_info = $this->model::where([
                ['period_id', '=', $package_info['period_id']],
                ['is_hidden', '=', 0]
            ])
                ->order('price asc')
                ->find();
            $periods_ser = new PeriodsSer($this->type);
            // 更新商品默认价格
            $up_data['price'] = $pack_info['price'] ?? 0;
            $up_data['market_price'] = $pack_info['market_price'] ?? 0;
            // 如果为兔头商品，更新兔头价格
            if ($this->type == 4) {
                $up_data['rabbit'] = $pack_info['rabbit'] ?? 0;
            }
            $periods_ser->updateInfoById((int)$package_info['period_id'], $up_data);
        //        }

        if (!$result) {
            return serviceReturn(false, $params['id'], '更新套餐失败');
        }

        return serviceReturn(true, $result->id);
    }


    /**
     * 根据期数批量上架套餐
     */
    public function updateAllByPeriod(int $period, array $data)
    {
        // 查询套餐是否存在
        $package_one = $this->model::where(['period_id' => $period, 'is_hidden' => 0])->find();
        if (empty($package_one)) {
            return serviceReturn(false, $package_one, '未查询到绑定套餐');
        }

        $result = $this->model::where('period_id', $period)->update($data);
        if ($result) {
            return serviceReturn(true, $result, '更新成功');
        }
        return serviceReturn(false, $result, '未更新数据');
    }

    /**
     * 根据 id 获取套餐信息
     *
     * @param int $id
     * @return array|null
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOne(int $id): ?array
    {
        $result = $this->model::find($id);
        return $result ? $result->toArray() : null;
    }


    /**
     * 减少套餐库存
     * @param int $package
     * @param int $count
     * @return mixed
     */
    public function decInventory(int $package, int $count)
    {
        return $this->model::where('id', $package)
            ->dec('inventory', $count)
            ->update();
    }

    /**
     * 根据条件获取套餐列表
     *
     * @param array $query
     * @return array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPackage(array $query, $all_package = false): ?array
    {
        $where = [];
        if (!$all_package) {
            $where[] = ['is_hidden_package', '=', 0];
        }
        $data = $this->model::where($query)->where($where)->order('is_hidden asc,price asc')->select();
        if (!empty($data)) {
            $result = $data->toArray();
            foreach ($result as &$v) {
                $v['package_img'] = !empty($v['package_img']) ? env('ALIURL').$v['package_img']:'';
            }
            return $result;
        }

        return null;
    }


    /**
     * 验证套餐库存是否充足
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function validateInventory(array $params): array
    {
        // 获取当前套餐信息
        $package_info = $this->model::where([
            'id' => $params['id'],
            'is_hidden' => 0
        ])->find();
        if (empty($package_info)) {
            return serviceReturn(false, $package_info, '未查询到套餐');
        }
        // 期数
        $period = $package_info->period_id;
        // 套餐产品
        $product = json_decode($package_info->associated_products, true);
        // 不限套餐库存
        if ($package_info->inventory == -1) {
            // 查询产品库存是否充足
            foreach ($product as $v) {
                $product_inventory = PeriodsProductInventory::where([
                    'period' => $period,
                    'product_id' => $v['product_id']
                ])->value('inventory');
                if ($v['nums'] > $product_inventory) {
                    return serviceReturn(false, $product_inventory, '产品库存不足!');
                }
            }
            return serviceReturn(true, $package_info);
        }
        // 是否限购
        if ($package_info->limit_number != -1 && $params['count'] > $package_info->inventory) {
            return serviceReturn(false, $package_info, '购买数量已超限购');
        }
        // 扣除套餐可购库存
//        $one_boy_inv = $this->model::where('id', $params['id'])
//            ->dec('one_boy_inventory', $params['count'])
//            ->update();
//        // 增加扣减记录
//        if ($one_boy_inv) {
//            $obi_record['package_id'] = $params['id'];
//            $obi_record['periods_type'] = $this->type;
//            $obi_record['count'] = -$params['count'];
//            $obi_record['created_time'] = time();
//            PackageObiRecord::create($obi_record);
//        }
        return serviceReturn(true, $package_info);
    }

    public function sendBackInventory(array $params)
    {
        // 退还类型，1：只退可购数量（未支付） 2：可购库存真实库存退还库存（已支付）
        switch ($params['type']) {
            case 1:
                // 增加套餐可购库存
                $one_boy_inv = $this->model::where('id', $params['id'])
                    ->inc('one_boy_inventory', $params['count'])
                    ->update();
                // 增加扣减记录
                $obi_record['package_id'] = $params['id'];
                $obi_record['periods_type'] = $this->type;
                $obi_record['count'] = $params['count'];
                $obi_record['created_time'] = time();
                PackageObiRecord::create($obi_record);
                break;
            case 2:
                // 增加套餐可购库存
                $one_boy_inv = $this->model::where('id', $params['id'])
                    ->inc('inventory', $params['count'])
                    ->update();
                // 增加扣减记录
                $obi_record['package_id'] = $params['id'];
                $obi_record['periods_type'] = $this->type;
                $obi_record['count'] = $params['count'];
                $obi_record['created_time'] = time();
                PackageObiRecord::create($obi_record);
                break;
        }

        return serviceReturn(true, $params['count']);

    }

    /**
     * 根据期数查询期数套餐列表
     * @param array $params
     * @return PeriodsCrossSet[]|PeriodsFlashSet[]|PeriodsLeftoverSet[]|PeriodsSecondSet[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function packageList(array $params)
    {
        $where = [
            ['period_id', '=', $params['period']],
            ['is_hidden', '=', 0]
        ];

        // 只查询库存大于 0
        if (isset($params['inv']) && $params['inv'] > 0) {
            $where[] = ['inventory', '>', 0];
        }
//        print_r($where);
//        exit();
        return $this->model::where($where)->order('id asc')->select()->toArray();
    }


    /**
     * 套餐 + 套餐包含产品列表
     * @param array $params
     * @return PeriodsCrossSet[]|PeriodsFlashSet[]|PeriodsLeftoverSet[]|PeriodsSecondSet[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function packageProductList(array $params)
    {
        $params['is_hidden'] = $params['is_hidden'] ?? 0;
        $where = [];
        $where['period_id'] = $params['period'];
        if (!$params['is_hidden']) {
            $where['is_hidden'] = 0;
        }
        $isShow = 0;
        if (!empty($params['uid'])){
            $es = new ElasticSearchService();
            $esParams = [
                'index' => ['periods'],
                'terms' => [ ['id' => [$params['period']]]],
                'source' => ['id', 'buyer_id', 'buyer_name']
            ];
            $res = $es->getDocumentList($esParams);
            $buyerInfo = $res['data'][0] ?? [];

            $res = get_url(env('ITEM.ERP_PREPARED_URL').'/prepared/v3/prepareds/detail?prepared_uid='.$params['uid']);
            $res = json_decode($res, true);
            $prepared = $res['data'] ?? [];
            $isShow = 0;
            if (empty($prepared)){
                if($buyerInfo['buyer_id'] == $params['uid']) $isShow = 1;
            }elseif($prepared['is_all_inquire_prepared'] == 1){
                $isShow = 1;
            }else{
                
                if (!empty($prepared['dep_inquire_items'])){
                    # 可查询制单人部门
                    $deptIdArr = array_column($prepared['dep_inquire_items'], 'dep_id');
                    $supplyChainStaffArr = Db::table('vh_supplychain.vh_staff')->whereIn('dept_id', $deptIdArr)->column('realname');
                    if (in_array($buyerInfo['buyer_name'], $supplyChainStaffArr)) $isShow = 1;
                }
                if (!empty($prepared['prepared_inquire_items'])){
                    // 可查询制单人
                    $authorityUidArr = array_column($prepared['prepared_inquire_items'], 'uid');
                    if (in_array($buyerInfo['buyer_id'], $authorityUidArr)) $isShow = 1;
                }
            }
        }

        $list = $this->model::where($where)->select()->toArray();
        $period_ids = $product_ids = [];
        foreach ($list as $v) {
            $period_ids[] = $v['period_id'];
            $product_list = json_decode($v['associated_products'] ?? '', true) ?? [];
            foreach ($product_list as $vv) {
                if (empty($vv['product_id'])) {
                    continue;
                }
                $product_id_arr = is_array($vv['product_id']) ? $vv['product_id'] : [$vv['product_id']];
                foreach ($product_id_arr as $p) {
                    $product_ids[] = $p;
                }
            }
        }

        $product_data = Db::table('vh_commodities.vh_periods_product_inventory')
            ->alias('pi')
            ->leftJoin('vh_wiki.vh_products p', 'p.id = pi.product_id')
            ->leftJoin('vh_wiki.vh_product_type pt', 'pt.id = p.product_type')
            ->where([
                ['pi.period', 'in', $period_ids],
                ['pi.product_id', 'in', $product_ids],
            ])
            ->column('pi.*,p.product_type,pt.fid as product_type_fid');
        $product_data_map = [];
        foreach ($product_data as $item) {
            $product_data_map["{$item['period']}:{$item['product_id']}"] = $item;
        }

        $periods_ser = new PeriodsSer($this->type);
        $period_info = $periods_ser->getOne($params['period'], 'id,is_cold_chain,is_cold_free_shipping');

        // 获取快递费和包材费
        $body = [
            'delivery_place' => 1,//发货地：1-南通 2-重庆
            'province_id' => 10,
            'city_id' => 108,
            'is_cold_chain' => 0,//是否冷链：0-否 1- 是
            'is_original_package' => 0,//是否原箱：0-否 1- 是
            'express_type' => 2,//快递方式：1-顺丰 2- 京东
            'is_insured' => 0,//是否保价：0-否 1-是
            'goods_info' => [],
        ];
        // 是否冷链
        if (($period_info['is_cold_chain'] ?? 0) == 1 || ($period_info['is_cold_free_shipping'] ?? 0) == 1) {
            $body['express_type'] = 1;
            $body['is_cold_chain'] = 1;
        }
        $url = env('ITEM.ORDERS_URL').'/orders/v3/regionExpressFee/query';
        foreach ($list as $k => $v) {
            $body['goods_info'] = [];
            // 是否原箱
            if ((!empty($v['is_original_package']) && $v['is_original_package'] == 1) || 
            (!empty($v['force_original_package']) && $v['force_original_package'] == 1)) {
                $body['express_type'] = 2;
                $body['is_original_package'] = 1;
                $body['is_cold_chain'] = 0;
            }

            $product_list = json_decode($v['associated_products'] ?? '', true) ?? [];
            foreach ($product_list as $vv) {
                if (empty($vv['product_id'])) {
                    continue;
                }
                $product_id = is_array($vv['product_id']) ? $vv['product_id'][0] ?? '' : $vv['product_id'];
                $product_info = $product_data_map["{$v['period_id']}:{$product_id}"] ?? [];
                if (empty($product_info)) {
                    continue;
                }
                $body['goods_info'][] = [
                    'short_code' => $product_info['short_code'] ?? '',
                    'nums' => $vv['nums'] ?? 1,
                ];

                // 白酒
                if (($product_info['product_type'] ?? 0)  == 21 || ($product_info['product_type_fid'] ?? 0) == 21) {
                    $body['is_cold_chain'] = 0;
                    $body['express_type'] = 2;
                    $body['is_insured'] = 1;
                    $body['order_money'] = $v['price'];
                }
            }
            $res = curlRequest($url, json_encode($body));

            $list[$k]['express_fee'] = [
                'express_fee' => $res['data']['express_fee'] ?? 0,
                'package_materials_fee' => $res['data']['package_materials_fee'] ?? 0,
            ];
        }


        foreach ($list as &$val) {
            $product_list = json_decode($val['associated_products'] ?? '', true) ?? [];
            $s_product_data = [];
            foreach ($product_list as $vv) {
                if (empty($vv['product_id'])) {
                    continue;
                }
                $product_id_arr = is_array($vv['product_id']) ? $vv['product_id'] : [$vv['product_id']];

                foreach ($product_id_arr as $p) {
                    $product_info = $product_data_map["{$val['period_id']}:{$p}"] ?? [];
                    if (empty($product_info)) {
                        continue;
                    }
                    $product_info['nums'] = $vv['nums'] ?? 0;
                    if ($isShow == 0) $product_info['costprice'] = '';
                    $s_product_data[] = $product_info;
                }
            }
            $val['product'] = $s_product_data;
        }
        
        // return $this->model::where($where)->select()->map(function ($item) use ($isShow) {
        //     $product_list = json_decode($item->associated_products, true);
        //     $product_data = [];
        //     foreach ($product_list as $key => $val) {
        //         $product_data[] = PeriodsProductInventory::where([
        //             'period' => $item->period_id,
        //             'product_id' => $val['product_id']
        //         ])->find();
        //         $product_data[$key]['nums'] = $val['nums'];
        //         if ($isShow == 0) $product_data[$key]['costprice'] = '';
        //     }
        //     $item->product = $product_data;
        //     return $item;
        // });

        return $list;
    }

    /**
     * 根据期数查询套餐，并获取套餐剩余库存
     * @param int $period
     * @return PeriodsCrossSet[]|PeriodsFlashSet[]|PeriodsLeftoverSet[]|PeriodsSecondSet[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function packageInventory(int $period)
    {
        $list = $this->model::where([
            'period_id' => $period,
            'is_hidden' => 0,
        ])->order('price asc')->select()->toArray();

        $product_ids = $period_ids = [];
        foreach ($list as $k => $v) {
            $list[$k]['package_img'] = image_full_path($v['package_img'] ?? '');
            $period_ids[] = $v['period_id'];

            $product_list = json_decode($v['associated_products'], true);
            foreach ($product_list as $val) {
                if (!is_array($val['product_id'])) {
                    $product_ids[] = $val['product_id'];
                } else {
                    $product_ids = array_merge($product_ids, $val['product_id']);
                }
            }
        }

        // 查询套餐产品库存
        $product_inventory = PeriodsProductInventory::where([
            ['period', 'in', $period_ids],
            ['product_id', 'in', $product_ids],
        ])->column('period,product_id,inventory,product_name,custom_product_name');
        $product_inventory_map = [];
        foreach ($product_inventory as $v) {
            $product_inventory_map[$v['period'] . ':' . $v['product_id']] = $v;
        }
        foreach ($list as $k => $v) {
            $product_list = json_decode($v['associated_products'], true);
            // 如果套餐不限量计算套餐库存
            if ($v['unlimited'] == 1) {
                $product_inventory_arr = [];
                foreach ($product_list as $val) {
                    if (!is_array($val['product_id'])) {
                        $val['product_id'] = [$val['product_id']];
                    }

                    foreach ($val['product_id'] as $product_id) {
                        $product_info = $product_inventory_map[$v['period_id'] . ':' . $product_id] ?? [];
                        $inventory = $product_info['inventory'] ?? 0;
                        if ($inventory > 0) {
                            $product_inventory_arr[] = floor($inventory / $val['nums']);
                        } else {
                            $product_inventory_arr[] = 0;
                        }
                    }
                }
                // 取最小可用库存
                sort($product_inventory_arr);
                $v['inventory'] = $product_inventory_arr[0] ?? 0;
            }

            $v['product_list'] = [];
            foreach ($product_list as $val) {
                if (!is_array($val['product_id'])) {
                    $val['product_id'] = [$val['product_id']];
                }
                
                foreach ($val['product_id'] as $product_id) {
                    $product_info = $product_inventory_map[$v['period_id'] . ':' . $product_id] ?? [];
                    $product_info['nums'] = $val['nums'];
                    $v['product_list'][] = $product_info;
                }
            }
            $list[$k] = $v;
        }

        return $list;
    }

    /**
     * 根据期数查询商家秒发套餐，并获取套餐剩余库存
     * @param int $period
     * @return PeriodsCrossSet[]|PeriodsFlashSet[]|PeriodsLeftoverSet[]|PeriodsSecondSet[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function vmallPackageInventory(int $period)
    {
        return $this->model::where([
            'period_id' => $period,
            'is_hidden' => 0
        ])
            ->order('id')
            ->select()->toArray();
//            ->map(function ($item) {
//                $product_list = json_decode($item->associated_products, true);
//                // 如果套餐不限量计算套餐库存
//                if ($item->unlimited == 1) {
//                    $product_inventory_arr = [];
//                    foreach ($product_list as $val) {
//                        // 查询套餐产品库存
//                        $product_inventory = PeriodsProductInventory::where([
//                            'period' => $item->period_id,
//                            'product_id' => $val['product_id']
//                        ])->value('inventory');
//                        if ($product_inventory > 0) {
//                            $product_inventory_arr[] = floor($product_inventory / $val['nums']);
//                        } else {
//                            $product_inventory_arr[] = 0;
//                        }
//                    }
//                    // 取最小可用库存
//                    sort($product_inventory_arr);
//                    $item->inventory = $product_inventory_arr[0] ?? 0;
//                }
//                return $item;
//            });
    }

    /**
     * @param int $period
     * @param int $package_id
     * @return PeriodsCrossSet[]|PeriodsFlashSet[]|PeriodsLeftoverSet[]|PeriodsRabbitSet[]|PeriodsSecondSet[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPeriodsPackageProductInventory(int $period, int $package_id) {
        return $this->model::where([
            'period_id' => $period,
            'id' => $package_id,
            'is_hidden' => 0
        ])->select()->map(function ($item) {
            $product_list = json_decode($item->associated_products, true);
            foreach ($product_list as &$val) {
                if ($item->is_mystery_box == 0) {
                // 查询套餐产品库存
                    $product_inventory = PeriodsProductInventory::where([
                        'period' => $item->period_id,
                        'product_id' => $val['product_id']
                    ])->value('inventory');
                    $val['inventory'] = $product_inventory;
                } elseif ($item->is_mystery_box == 1) {
                    foreach ($val['product_id'] as &$v) {
                        $product_inventory = PeriodsProductInventory::where([
                            'period' => $item->period_id,
                            'product_id' => $v
                        ])->value('inventory');
                        $val['inventory'][$v] = $product_inventory;
                    }
                }
            }
            $item->package_product = $product_list;
            return $item;
        })->toArray();
    }

    /**
     * 查询未隐藏套餐价格
     * @param $period
     * @return array
     */
    public function packageListPrice($period): array
    {
        return $this->model::where([
            'period_id' => $period,
            'is_hidden' => 0
        ])->column('price');
    }

    /**
     * 查询兔头商店未隐藏套餐价格
     * @param $period
     * @return array
     */
    public function rabbitPackageListPrice($period): array
    {
        return $this->model::where([
            'period_id' => $period,
            'is_hidden' => 0
        ])->column('rabbit');
    }

    /**
     * 查询期数是否存在订金套餐
     * @param $period
     * @return int
     */
    public function getIsDeposit($period)
    {
        return $this->model::where([
            'period_id'  => $period,
            'is_hidden'  => 0,
            'is_deposit' => 1,
        ])->value('id');
    }

    /**
     * @param array $params
     * @return array|mixed|Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPackageOne(array $params) {
        return $this->model::where(
            ['period_id' => $params['period']],
            ['id'=> 'package_id']
        )->find();
    }

    /**
     * @param array $ids
     * @return mixed
     */
    public function getPackageByPeriods(array $ids) {
        return $this->model::where([
            ['period_id', 'in', $ids],
            ['is_hidden', '=', 0],
            ['preferential_reduction', '>', 0]])
            ->field('period_id,preferential_reduction')
            ->order('id', 'asc')
            ->group('period_id')
            ->select()
            ->toArray();
    }
    /**
     * @param array $params
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updatePackage(array $params, $where = []) {
        // 更新套餐
        return $this->model::update($params, $where);
    }

    /**
     * 根据期数查询期数订金套餐列表
     * @param string $period
     * @return PeriodsCrossSet[]|PeriodsFlashSet[]|PeriodsLeftoverSet[]|PeriodsSecondSet[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function depositPackageList($period, $fields = '', $where_arr = [])
    {
        $where = [
            ['period_id', '=', $period],
            ['is_hidden', '=', 0]
        ];
        $where = array_merge($where , $where_arr);
        $fields = $fields == '' ? '*' : $fields;
        return $this->model::where($where)->field($fields)->select()->toArray();
    }

    /**
     * 条件查询套餐是否存在
     * @param $period
     * @return int
     */
    public function ConditionalQueryPackageExists($where)
    {
        return $this->model::where($where)->value('id');
    }

    /**
     * 获取剩余订货量及预计发货时间
     * @param array $params
     * @return PeriodsCrossSet[]|PeriodsFlashSet[]|PeriodsLeftoverSet[]|PeriodsSecondSet[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPeriodsRemainingOrderQuantity($params)
    {
        $periods_ser = new \app\service\Periods((int)$params['periods_type']);
        $period = $periods_ser->getOne((int)$params['period'], 'id,predict_shipment_time');

        $result = [];
        // 获取简码已售
        $url_resq_data = GetSaleBottleNums([['period' => (int)$params['period'], 'period_type' => $this->type]]);
        $sold = $url_resq_data[$params['period']] ?? [];
        if (!empty($sold)) {
            // 查询订货记录
            $inventory_order = PeriodsProductInventoryOrder::where('period', $params['period'])
                ->order('created_time desc')
                ->column('short_code,created_time,predict_shipment_time');
            $i_o = [];
            if (!empty($inventory_order)) {
                foreach ($inventory_order as $v) {
                    if (empty($i_o[$v['short_code']])) {
                        $i_o[$v['short_code']] = $v;
                    }
                }
            }
            // 查询订货量
            $order_num = PeriodsProductInventory::where('period', $params['period'])->column('product_id,short_code,order,warehouse_id','short_code');

            foreach ($order_num as $v) {
                // 预计发货时间
                $predict_shipment_time = 0;
                
                if (!empty($period['predict_shipment_time'])) {
                    $predict_shipment_time = strtotime($period['predict_shipment_time']);
                }

                if (!empty($i_o[$v['short_code']])) {
                    $predict_shipment_time = intval($i_o[$v['short_code']]['predict_shipment_time']);
                }
                // 已售数量
                $sold_nums = $sold[$v['short_code']] ?? 0;
                // 剩余数量
                $residue_nums = intval($v['order']-$sold_nums);
                // 代发期数
                if ($v['warehouse_id'] == 34) {
                    $predict_shipment_time = SupplierShipPredictShipmentTimeLogic($predict_shipment_time);
                }
                $result[] = [
                    'product_id'   => $v['product_id'],
                    'residue_nums' => $residue_nums,
                    'predict_shipment_time' => $predict_shipment_time,
                ];
            }

        }

        return $result;
    }

    /**
     * 获取活动套餐价格
     * @param array $params 请求参数
     * @param object $package 套餐数据
     * @return array 套餐数据
     */
    public function getSpecialActivityPackage($package, $params)
    {
        $time = time();
        $aid = $periods = 0;
        if (!empty($params['activity_periods'])) {
            // 解析活动期数
            $res = analysisActivityPeriods($params['activity_periods']);
            $aid = $res['aid'];
            $periods = $res['periods'];
            if ($params['period'] != $periods) {
                $periods = 0;
            }
        }
        
        if (empty($params['activity_periods']) || $aid == 0 || $periods == 0 || $periods != $params['period']) {
            $this->setUserActivityPeriods();
            return $package;
        }

        $this->setUserActivityPeriods($params['activity_periods']);

        // 获取活动套餐价
        $package_id = array_column($package->toArray(), 'id');
        $activity_package = Db::table("vh_marketing.vh_special_activity")
            ->alias('a')
            ->leftJoin("vh_marketing.vh_special_activity_package pkg",'pkg.activity_id=a.id')
            ->where([
                ['a.id','=',$aid],
                ['a.start_at','<=',$time],
                ['a.end_at','>',$time],
                ['pkg.package_id','in',$package_id],
            ])
            ->column('package_id,price','package_id');
        foreach ($package as &$v) {
            $pkg = $activity_package[$v['id']] ?? [];
            if (empty($pkg)) {
                continue;
            }

            $v['price'] = strval(floatval($pkg['price']));
            $v['newcomer_price'] = $pkg['price'];
        }

        return $package;
    }
    

    /**
     * 设置用户访问活动期数
     * @param string $activity_periods 活动期数
     */
    public function setUserActivityPeriods($activity_periods = '')
    {
        // 保存用户访问商品记录
        $uid = request()->header('vinehoo-uid', null);
        if (!empty($uid)) {
            $field_id = Db::table('vh_user.vh_user_fields')->where([
                ['uid','=',$uid],
                ['field_name','=','activity_periods'],
            ])->value('id');
            // $redis = new Redis();
            // $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
            // $redis->auth(env('cache.PASSWORD'));
            // $redis->select(3);
            // $key = 'vinehoo.user.cache.data.'.$uid;
            // $data = $redis->hmget($key, ['activity_periods']);
            $info = [
                'uid' => $uid,
                'field_name' => 'activity_periods',
                'field_value' => $activity_periods,
                'update_time' => time(),
            ];

            if (empty($field_id) && empty($activity_periods)) {
                return;
            }

            // 添加标识
            if (empty($field_id) && !empty($activity_periods)) {
                try {
                    Db::table('vh_user.vh_user_fields')->insert($info);
                } catch (\Exception $e) {
                    Log::err("vh_user_fields ERROR UID：{$uid}，activity_periods：{$activity_periods}");
                }
                
            }

            // 修改标识
            if (!empty($field_id)) {
                Db::table('vh_user.vh_user_fields')->where('id', $field_id)->update($info);
            }
        }
    }

    /**
     * 跨境套餐根据SKU拆分为不同的子套餐
     * @param array $params 请求参数
     * @return array
     */
    public function split($package_id, $main_package = [])
    {
        $time = time();
        if (empty($main_package)) {
            // 查询主套餐信息
            $main_package = Db::name('periods_cross_set')
                ->field('id,period_id,package_name,price,associated_products')
                ->where('id', $package_id)
                ->findOrEmpty();
        }
        
        if (empty($main_package)) {
            return '套餐信息获取失败。';
        }
        $main_package['price'] = sprintf("%.2f", $main_package['price']);
        $main_package['price_ratio'] = 100;

        $associated_products = json_decode($main_package['associated_products'], true);
        // 总产品数
        $total_count = count($associated_products);
        if ($total_count < 2) {
            return [$main_package];
        }
        $product_id = [];
        foreach ($associated_products as $v) {
            // 盲盒套餐直接返回
            if (is_array($v['product_id'])) {
                return [$main_package];
            }
            $product_id[] = $v['product_id'];
        }
        // 查询子套餐
        $sub_pkg = Db::name('periods_cross_set')
            ->where([
                ['period_id', '=', $main_package['period_id']],
                ['pid', '=', $main_package['id']],
                ['is_hidden', '=', 1],
            ])
            ->column('id,period_id,package_name,price,price_ratio,associated_products');
        if (!empty($sub_pkg)) {
            foreach ($sub_pkg as &$s) {
                $ap = json_decode($s['associated_products'], true);
                $s['price'] = floatval($s['price']);
                $s['price_ratio'] = floatval($s['price_ratio']);
                $s['product_id'] = $ap[0]['product_id'] ?? 0;
            }
            return $sub_pkg;
        }

        // 查询成本价
        $costprice = Db::name('periods_product_inventory')
            ->where([
                ['period', '=', $main_package['period_id']],
                ['product_id', 'in', $product_id],
            ])
            ->column('costprice', 'product_id');
        // 总成本
        $totalcost = '0';
        // 成本正序排序
        foreach ($associated_products as $k => $v) {
            // 成本
            $cost = $costprice[$v['product_id'] ?? 0] ?? 0;
            $nums = $v['nums'] ?? 0;
            $cost = bcmul(strval($cost), strval($nums), 2);
            $associated_products[$k]['cost'] = $cost;
            //总成本
            $totalcost = bcadd($totalcost, strval($cost), 2);
        }
        array_multisort(array_column($associated_products, 'cost'), SORT_ASC, $associated_products);

        // 使用占比
        $use_ratio = '0';
        // 使用价格
        $use_price = '0';
        $priceArr = [];
        $costprice_all = array_values(array_unique($costprice));
        // 全部无成本
        $is_no_cost = false;
        if (count($costprice_all) == 1 && floatval($costprice_all[0]) == 0) {
            $is_no_cost = true;
        }
        foreach ($associated_products as $k => &$a) {
            if (empty($a['product_id'])) {
                continue;
            }
            // 是否最后一条
            $is_end = ($k + 1) == $total_count;
            // 成本
            $cost = floatval($a['cost']);
            if ($is_end) {
                $a['ratio'] = bcsub('1', $use_ratio, 4);
                $a['price'] = bcsub(strval($main_package['price']), $use_price, 2);
            } else {
                if ($is_no_cost) {
                    $a['ratio'] = bcdiv('1', strval($total_count), 4);
                } else {
                    $a['ratio'] = $cost > 0 ? bcdiv(strval($cost), strval($totalcost), 4) : '0';
                }
                $a['price'] = bcmul(strval($main_package['price']), strval($a['ratio']), 2);
                $a['price'] = floatval($a['price']) > 0 ? strval($a['price']) : '0.01';
            }
            // 使用占比
            $use_ratio = bcadd($use_ratio, $a['ratio'], 4);
            // 使用价格
            $use_price = bcadd($use_price, $a['price'], 2);
            $priceArr[] = $a['price'];
        }

        Db::startTrans();
        try {
            $result = $add_data = [];
            foreach ($associated_products as $k => $v) {
                if (empty($v['product_id'])) {
                    continue;
                }
                // 套餐名称
                $package_name = $v['sub_package_name'] ?? $main_package['package_name'];
                // 新增套餐
                $pkg_info = [
                    'id' => $this->getGeneratorID(2),
                    'period_id' => $main_package['period_id'],
                    'package_name' => $v['sub_package_name'] ?? $main_package['package_name'],
                    'product_id' => $v['product_id'],
                    'price' => floatval($v['price']),
                    'price_ratio' => floatval(bcmul($v['ratio'], '100', 2)),
                ];
                $result[] = $pkg_info;

                unset($pkg_info['product_id']);
                $pkg_info['pid'] = $main_package['id'];
                $pkg_info['is_hidden'] = 1;
                $pkg_info['created_time'] = $time;
                $pkg_info['update_time'] = $time;
                $pkg_info['associated_products'] = json_encode([[
                    'product_id' => $v['product_id'],
                    'nums' => $v['nums'],
                    'is_gift' => 0,
                ]]);
                $add_data[] = $pkg_info;
                

                $associated_products[$k]['sub_package_price'] = floatval($v['price']);
                $associated_products[$k]['sub_package_id'] = $pkg_info['id'];
                unset($associated_products[$k]['ratio']);
                unset($associated_products[$k]['price']);
                unset($associated_products[$k]['cost']);
            }

            if (!empty($add_data)) {
                Db::name('periods_cross_set')
                    ->where('id', $package_id)
                    ->update(['associated_products' => json_encode($associated_products)]);
                    
                Db::name('periods_cross_set')->insertAll($add_data);
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('插入数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $e->getMessage();
        }
        return $result;
    }

    /**
     * 查询套餐发货仓
     * @param array $package_id 套餐ID
     * @return array
     */
    public function getPackageShippingWarehouse($package_id)
    {
        $es_package = Es::name('periods_set')
            ->where([
                ['_id','in',$package_id]
            ])->select()->toArray();
        $package_info = [];
        foreach ($es_package as $v) {
            $package_info[$v['id']] = $v;
        }

        $product_id = $period_id = [];
        foreach ($package_info as $v) {
            if (empty($v['associated_products'])) {
                continue;
            }
            $associated_products = json_decode($v['associated_products'], true);
            foreach ($associated_products as $a) {
                if (empty($a['product_id'])) {
                    continue;
                }
                if (is_array($a['product_id'])) {
                    $product_id = array_merge($product_id, $a['product_id']);

                } else {
                    $product_id[] = $a['product_id'];
                }
            }
            $period_id[] = $v['period_id'] ?? 0;
        }

        // 查询仓库
        $periods_product = [];
        if (!empty($period_id) && !empty($product_id)) {
            $period_product = Db::name('periods_product_inventory')
                ->where([
                    ['period', 'in', $period_id],
                    ['product_id', 'in', $product_id],
                ])
                ->column('period,product_id,warehouse_id,short_code');
            foreach ($period_product as $v) {
                $periods_product[$v['period'].$v['product_id']] = $v;
            }
        }

        $result = [];
        foreach ($package_id as $v) {
            $info = $package_info[$v] ?? [];
            $result[$v] = [];
            if (empty($info) || empty($info['associated_products'])) {
                continue;
            }

            $associated_products = json_decode($info['associated_products'], true);
            foreach ($associated_products as $a) {
                if (empty($a['product_id'])) {
                    continue;
                }
                if (!is_array($a['product_id'])) {
                    $product_id = [$a['product_id']];
                }

                foreach ($product_id as $prid) {
                    $p_product = $periods_product[$info['period_id'].$prid] ?? [];
                    if (!empty($p_product)) {
                        $result[$v][$p_product['short_code']] = $p_product['warehouse_id'];
                    }
                }
            }
        }

        return $result;
    }

    /**
     * 删除套餐
     * @param array $params 请求参数
     * @return bool|string
     */
    public function del($params)
    {
        $package = $this->model::where([
            'id' => $params['package_id'],
            'period_id' => $params['period_id'],
        ])->find();
        if (empty($package)) {
            return '套餐不存在';
        }
        $periods_ser = new PeriodsSer($this->type);
        $period = $periods_ser->getOne($params['period_id'], 'id,onsale_verify_status');
        if (empty($period)) {
            return '期数不存在';
        }
        if ($period['onsale_verify_status'] == 1) {
            return '期数二次确认上架后不能删除';
        }

        try {
            // 删除es数据
            Es::name(Es::PERIODS_PACKAGE)->deleteId($params['package_id']);

            $package->delete();

        } catch (\Exception $e) {
            return $e->getMessage();
        }
        
        return true;
    }

    /**
     * 秒发库存扣减库存后小于6瓶后需要企微通知陈泓州
     * @param array $params 请求参数
     * @return bool
     */
    public function sendSecondInventoryMessage($period)
    {
        Db::startTrans();
        try {
            $notice_data = Db::name('periods_product_inventory')->where([
                ['period', '=', $period],
                ['is_use_comment', '=', 0],
                ['inventory', '<', 6]
            ])->lock(true)->column('period,short_code,inventory');
            if (!empty($notice_data)) {
                // 7天内不重复通知
                $e_time = time() - (7*86400);
                $short_code = array_column($notice_data, 'short_code');
                $notice_short_code = Db::name('send_inventory_message')->where([
                    ['period', '=', $period],
                    ['short_code', 'in', $short_code],
                    ['notice_time', '>=', $e_time],
                ])->column('short_code');
                $Weixin = new \app\service\WorkWeixinService();
                $Weixin->uid = 'ChenHongZhou';
                $notice_short_code_add = [];
                foreach ($notice_data as $k => $v) {
                    if (!in_array($v['short_code'], $notice_short_code)) {
                        $Weixin->sendNotify("{$v['period']}期数{$v['short_code']}简码售卖库存为{$v['inventory']}");
                        $notice_short_code_add[] = [
                            'period'      => $v['period'],
                            'short_code'  => $v['short_code'],
                            'notice_time' => time(),
                        ];
                    }
                }
                if (!empty($notice_short_code_add)) {
                    Db::name('send_inventory_message')->insertAll($notice_short_code_add);
                }
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::write('send_inventory_message ERROR: ' . $e->getMessage());
        }
    }

    /**
     * 自选专题活动创建自选套餐
     * @param array $params 请求参数
     * @return array|string
     */
    public function createCustomActivityPackage($params)
    {
        // 固定期数
        $period_id = 99999;
        // 测试环境期数
        if (!empty(env('APP_DEBUG'))) {
            $period_id = 88889;
        }
        $time = time();
        $activity_goods = $params['activity_goods'] ?? [];

        $result = ['period_id' => $period_id, 'package_id' => $params['package_id'] ?? 0];
        try{
            Db::startTrans();
            // 固定期数产品库存
            $gd_periods_product = Db::name('periods_product_inventory')
                ->where('period', $period_id)
                ->column('id,product_id,inventory', 'product_id');

            if (empty($params['activity_goods'])) {
                // 查询活动商品
                $activity_goods = Db::table('vh_marketing.vh_custom_activity_goods')
                    ->where('activity_id', $params['id'])
                    ->column('period,associated_products');
            }
            
            $period_ids = array_column($activity_goods, 'period');
            // 查询期数产品库存
            $periods_product = Db::name('periods_product_inventory')
                ->whereIn('period', $period_ids)
                ->where('is_use_comment', 0)
                ->column('period,product_id,title,product_name,en_product_name,bar_code,short_code,inventory,costprice,inventory_accum,warehouse,warehouse_id,erp_id,capacity,is_hidden_param,custom_product_name');
            $periods_product_map = [];
            foreach ($periods_product as $v) {
                $periods_product_map["{$v['period']}:{$v['product_id']}"] = $v;
            }

            $product_ids = $products_value = $product_inv_add = [];
            foreach ($activity_goods as $v) {
                $associated_products = json_decode($v['associated_products'], true);
                foreach ($associated_products as $p) {
                    $product_ids[] = $p['product_id'];
                    $periods_product_info = $periods_product_map["{$v['period']}:{$p['product_id']}"] ?? [];
                    if (empty($periods_product_info)) {
                        throw new \Exception("期数{$v['period']}产品{$p['product_id']}库存不存在");
                    }
                    // 添加产品库存
                    $periods_product_info['created_time'] = $time;
                    $periods_product_info['period'] = $period_id;
                    $periods_product_info['periods_type'] = 0;

                    // 判断产品库存
                    $gd_period_product_inv = $gd_periods_product[$p['product_id']] ?? [];
                    if (empty($gd_period_product_inv)) {
                        $product_inv_add[$p['product_id']] = $periods_product_info;

                    } else {// 更新产品库存
                        if ($gd_period_product_inv['inventory'] != $periods_product_info['inventory']) {
                            Db::name('periods_product_inventory')
                                ->where('id', $gd_period_product_inv['id'])
                                ->update([
                                    'inventory' => $periods_product_info['inventory'],
                                    'inventory_accum' => $periods_product_info['inventory_accum'],
                                ]);
                        }
                    }
                }
                $products_value[] = $associated_products;
            }

            if (!empty($product_inv_add)) {
                Db::name('periods_product_inventory')->insertAll(array_values($product_inv_add));
            }

            // 生成所有可能的组合
            $combinations = [];
            $this->combine($products_value, $params['custom_quantity'], 0, [], $combinations);
            // 子套餐
            $sub_package = [];

            // 主套餐
            $main_package_products = json_encode([[
                'product_id' => $product_ids,
                'nums' => 1,
                'is_gift' => 0,
            ]]);
            $package_insert = [
                'period_id' => $period_id,
                'package_name' => $params['activity_name'],
                'price' => $params['custom_price'],
                'market_price' => $params['custom_price'],
                'associated_products' => $main_package_products,
                'created_time' => $time,
                'updated_time' => $time,
                'is_custom_package' => 1,
                'is_hidden' => 1,
                'is_hidden_package' => 1,
                'inventory' => 0,
                'inventory_accum' => 0,
                'custom_product_count' => $params['custom_quantity'],
            ];
            if (empty($params['package_id'])) {
                $params['package_id'] = $this->getGeneratorID(2);
                $package_insert['id'] = $params['package_id'];
                // 创建自选套餐
                $this->model::create($package_insert);
                $result['package_id'] = $params['package_id'];

            } else {
                $this->model::where('id', $params['package_id'])->update([
                    'associated_products' => $main_package_products,
                ]);
                // 查询子套餐
                $sub_package = $this->model::where('source_package_id', $params['package_id'])->column('id,associated_products');
            }

            // 创建子套餐
            foreach ($combinations as $c) {
                $current_product = [];
                foreach ($c as $cc) {
                    $current_product = array_merge($current_product, $cc);
                }
                $current_map = [];
                foreach ($current_product as $p) {
                    $current_map[$p['product_id']] = $p['nums'];
                }
                
                foreach ($sub_package as $sp) {
                    $associated_products = json_decode($sp['associated_products'], true);
                    $associated_map = [];
                    foreach ($associated_products as $ap) {
                        $associated_map[$ap['product_id']] = $ap['nums'];
                    }
                    
                    if ($associated_map == $current_map) {
                        continue 2; 
                    }
                }

                // 创建子套餐
                $package_insert['id'] = $this->getGeneratorID(2);
                $package_insert['associated_products'] = json_encode($current_product);
                $package_insert['is_hidden'] = 1; // 设置为隐藏状态
                $package_insert['is_hidden_package'] = 1; // 设置为隐藏状态
                $package_insert['source_package_id'] = $params['package_id'];
                $package_insert['is_custom_package'] = 0;
                $package_insert['custom_product_count'] = 0;
                $package_insert['is_custom_package'] = 0;
                $this->model::create($package_insert);
            }
            
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::write('自选专题活动创建自选套餐 ERROR: ' . $e->getMessage());
            return $e->getMessage();
        }
        
        return $result;
    }
}