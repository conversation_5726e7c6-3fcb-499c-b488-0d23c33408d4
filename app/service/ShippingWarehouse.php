<?php
declare(strict_types=1);

namespace app\service;

use app\ErrorCode;
use app\model\PhysicalWarehouse;
use app\model\VirtualWarehouse;
use think\facade\Db;

/**
 * 发货仓管理
 * Class ShippingWarehouse
 * @package app\controller
 */
class ShippingWarehouse
{
    /**
     * 添加实体仓
     * @param array $data
     * @return PhysicalWarehouse|\think\Model
     */
    public function createPhysicalWarehouse(array $data)
    {
        return PhysicalWarehouse::create($data);
    }

    /**
     * 添加虚拟仓
     * @param array $data
     * @return VirtualWarehouse|\think\Model
     */
    public function createVirtualWarehouse(array $data)
    {
        return VirtualWarehouse::create($data);
    }

    /**
     * AND 条件查询实体仓库
     * @param array $where
     * @param string $field
     * @return PhysicalWarehouse|array|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPhysicalWarehouseInfo(array $where, string $field = '*')
    {
        return PhysicalWarehouse::where($where)->field($field)->find();
    }

    /**
     * AND 条件查询虚拟仓库
     * @param array $where
     * @param string $field
     * @return PhysicalWarehouse|array|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getVirtualWarehouseInfo(array $where, string $field = '*')
    {
        return VirtualWarehouse::where($where)->field($field)->find();
    }

    /**
     * 更新实体仓库
     * @param array $data
     * @return PhysicalWarehouse
     */
    public function upPhysicalWarehouse(array $data): PhysicalWarehouse
    {
        return PhysicalWarehouse::update($data);
    }

    /**
     * 更新虚拟
     * @param array $data
     * @return VirtualWarehouse
     */
    public function upVirtualWarehouse(array $data): VirtualWarehouse
    {
        return VirtualWarehouse::update($data);
    }

    /**
     * 查询实体仓列表
     * @param array $data
     * @param int $limit
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function getPhysicalWarehouseList(array $data, int $limit = 15): array
    {
        $where = [];
        if (isset($data['name']) && $data['name'] != '') {
            $where[] = ['name', 'like', '%' . $data['name'] . '%'];
        }
        $list = PhysicalWarehouse::where($where)->paginate([
            'list_rows' => $limit,
            'var_page' => 'page'
        ]);
        // 分页
        $result['total'] = $list->total() ?? 0;
        $result['list'] = $list->getCollection()->toArray() ?? [];
        return $result;
    }

    /**
     * 查询虚拟仓列表
     * @param array $data
     * @param int $limit
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function getVirtualWarehouseList(array $data, int $limit = 15): array
    {
        $where = [];

        if (isset($data['virtual_name']) && $data['virtual_name'] != '') {
            $where[] = ['virtual_name', 'like', '%' . $data['virtual_name'] . '%'];
        }

        if (isset($data['periods_type']) && $data['periods_type'] != '') {
            $where[] = ['channel_types', 'like',  '%'. $data['periods_type'] . '%'];
        }

        if (isset($data['physical_id']) && $data['physical_id'] != '') {
            $where[] = ['physical_id', '=', $data['physical_id']];
        }
        if (isset($data['company_ids']) && $data['company_ids'] != '') {
            $where[] = ['company_ids', 'like', "%{$data['company_ids']}%"];
        }

        $list = VirtualWarehouse::where($where)->paginate([
            'list_rows' => $limit,
            'var_page' => 'page'
        ])->map(function ($item) {
            $item->physical_name = PhysicalWarehouse::where('id', $item->physical_id)->value('name');
            if ($item->channel_types !== null) {
                $channel_type = explode(",", $item->channel_types);
                $val = [];
                $type = ['0' => '闪购', '1' => '秒发', '2' => '跨境', '3' => '尾货', '4' => '兔头商城','7' => '三方', '9' => '商家秒发',
                    '10' => '商家闪购', '11' => '拍卖'];
                foreach ($channel_type as $v) {
                    if ($v != '') {
                        array_push($val, $type[$v]);
                    }
                }
                unset($v);
                $item->channel_types_name = implode("、", $val);
                $item->channel_types = explode(',', $item->channel_types);
                $channel_type_arr = [];
                foreach ($item->channel_types as $v) {
                    if ($v != '') {
                        $channel_type_arr[] = (int)$v;
                    }
                }
                $item->channel_types = $channel_type_arr;
                unset($v);
            }
            if ($item->company_ids !== null) {
                $cin_name =      [
                    '1' => '重庆云酒佰酿电子商务有限公司',//（银联）
                    '2' => '佰酿云酒（重庆）科技有限公司', //（银联）
                    '3' => '重庆云酒佰酿电子商务有限公司',
                    '4' => '佰酿云酒（重庆）科技有限公司',
                    '5' => '渝中区微醺酒业商行',
                    '10' => '海南一花一世界科技有限公司',
                ];
                $c_names = [];
                if(empty($item->company_ids)){
                    $item->company_ids = [];
                    $item->company_ids_name = '';
                }else{
                $company_ids = explode(',', $item->company_ids);
                foreach ($company_ids as &$cid){
                    $c_names[] = $cin_name[$cid];
                    $cid = intval($cid);
                }
                $item->company_ids = $company_ids;
                $item->company_ids_name = implode(',',$c_names);
                }
            }else{
                $item->company_ids = [];
                $item->company_ids_name = '';
            }
            return $item;
        });
        // 分页
        $result['total'] = $list->total() ?? 0;
        $result['list'] = $list->getCollection()->toArray() ?? [];
        return $result;
    }

    /**
     * 查询所有实体仓下面的绑定了频道的虚拟仓需
     * @return PhysicalWarehouse[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPhysicalAndVirtualList()
    {
        return PhysicalWarehouse::field('id, name')->select()
            ->map(function ($item) {
                $item->virtual_warehouse = VirtualWarehouse::where([
                    ['physical_id', '=', $item->id],
                    ['channel_types', '<>', '']
                ])->field('id,virtual_name,erp_id')->select();
                return $item;
            });
    }

    /**
     * 查询产品选择虚拟仓
     * @param array $params
     * @return mixed
     */
    public function getVirtualListByChannelType(array $params)
    {
        $where = [];
        if (isset($params['channel_types']) && $params['channel_types'] != '') {
            $where[] = ['a.channel_types', 'like', '%' . $params['channel_types'] . '%'];
        }
        if (isset($params['fictitious_name']) && $params['fictitious_name'] != '') {
            $where[] = ['a.virtual_name', 'like', '%' . $params['fictitious_name'] . '%'];
        }
        if (isset($params['payee_merchant_id']) && $params['payee_merchant_id'] != '') {
            $where[] = ['a.company_ids', 'like', '%' . $params['payee_merchant_id'] . '%'];//公司
        }
        if (isset($params['is_my']) && $params['is_my'] != '') {
            $where[] = ['b.type', '=', $params['is_my']];
        }
        return VirtualWarehouse::alias('a')->leftJoin('physical_warehouse b', 'a.physical_id = b.id')
            ->field('b.id as fictitious_pid, a.virtual_name as fictitious_name, a.virtual_id as fictitious_id, 
            b.name, a.channel_types, a.id, a.erp_id, a.company_ids')
            ->where($where)->select()->map(function ($item) {
                $item->fictitious_name = $item->name . '-' . $item->fictitious_name;
                return $item;
            });
    }

    /**
     * 根据 erp_id（编码）查询虚拟仓列表
     * @param array $params
     * @return VirtualWarehouse[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getVirtualByVirtualId(array $params)
    {
        return VirtualWarehouse::whereIn('erp_id', $params['erp_id'])->select();
    }

    /**
     * 添加临时库存
     * @param array $params
     * @return array
     */
    public function addTempWarehouse(array $params) {
        // 简码是否存在
        $re = Db::connect('orders')->table('vh_warehouse')
            ->where(['short_code' => trim($params['short_code']), 'warehouse' => (int)$params['warehouse']])
            ->value('id');
        if ($re) {
            return serviceReturn(false, ErrorCode::EXEC_ERROR, '简码已存在');
        }
        $params['short_code'] = trim($params['short_code']);
        $re = Db::connect('orders')->table('vh_warehouse')->insert($params);
        if ($re) {
            return serviceReturn(true, ErrorCode::EXEC_ERROR, '添加成功');
        } else {
            return serviceReturn(false, ErrorCode::EXEC_ERROR, '添加失败');
        }
    }

    /**
     * 更新库存
     * @param array $params
     * @return int
     */
    public function updateTempWarehouseInventory(array $params): int
    {
        try {
            if ($params['type'] == 'inc') {
                $re = Db::connect('orders')->table('vh_warehouse')
                    ->where('id', $params['id'])
                    ->inc('inventory', (int)$params['num'])
                    ->update();
            } elseif ($params['type'] == 'dec') {
                $re = Db::connect('orders')->table('vh_warehouse')
                    ->where('id', $params['id'])
                    ->dec('inventory', (int)$params['num'])
                    ->update();
            } else {
                return 0;
            }
            return $re;

        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * 获取临时仓库
     * @param array $params
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function getTempWarehouse(array $params): array
    {
        $where = [];
        if (isset($params['warehouse']) && $params['warehouse'] != '') {
            $where[] = ['warehouse', '=', (int)$params['warehouse']];
        }
        if (isset($params['warehouse_name']) && $params['warehouse_name'] != '') {
            $where[] = ['warehouse_name', '=', $params['warehouse_name']];
        }
        if (isset($params['short_code']) && $params['short_code'] != '') {
            $where[] = ['short_code', '=', $params['short_code']];
        }
        if (isset($params['bar_code']) && $params['bar_code'] != '') {
            $where[] = ['bar_code', '=', $params['bar_code']];
        }
        if (!isset($params['limit']) || (int)$params < 1) {
            $limit = 15;
        } else {
            $limit = (int) $params['limit'];
        }
        $list = Db::connect('orders')->table('vh_warehouse')
            ->where($where)
            ->paginate([
                'list_rows' => $limit,
                'var_page' => 'page'
            ])->map(function ($item) {
                $item['created_time'] = date('Y-m-d H:i:s', (int)$item['created_time']);
//                $item->updated_time = date('Y-m-d H:i:s', (int)$item->updated_time);
                return $item;
            });
        // 分页
        $result['total'] = $list->total() ?? 0;
        $result['list'] = $list->getCollection()->toArray() ?? [];
        return $result;

    }

    /**
     * 导入临时仓库数据
     * @param array $data
     * @return bool
     */
    public function importWarehouse(array $data): bool
    {
        $es_ser = new ElasticSearch();
        $warehouse = ['北京OT' => 0, '上海OT'=> 1, '重庆光环店' => 2];
        // 导入数据
        foreach ($data as $v) {
            // 简码是否存在
            $re = Db::connect('orders')->table('vh_warehouse')
                ->where('short_code', trim($v['A']))
                ->where(['short_code' => $v['A'], 'warehouse' => $warehouse[$v['C']]])
                ->value('id');
            if ($re) {
                continue;
            }
            // es 产品
            $product_info = $es_ser->getProductByShortCode($v['A']);
            if (empty($product_info)) {
                continue;
            }
            $d['warehouse'] = $warehouse[$v['C']];
            $d['inventory'] = $v['B'];
            $d['short_code'] = trim($v['A']);
            $d['product_id'] = $product_info['id'];
            $d['bar_code'] = $product_info['bar_code'];
            $d['cn_product_name'] = $product_info['cn_product_name'];
            $d['en_product_name'] = $product_info['en_product_name'];
            $d['capacity'] = $product_info['capacity'];
            $d['canning_years'] = $product_info['canning_years'];
            $d['created_time'] = time();
            // 添加库存
            try {
                Db::connect('orders')->table('vh_warehouse')->insert($d);
                unset($d);
            } catch (\Exception $e) {
                continue;
            }
        }
        return true;
    }

    /**
     * 获取代发仓列表
     * @param array $params
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function getDfWarehouseList(array $params): array
    {
        $list = Db::name('virtual_warehouse')
            ->where('is_supplier_delivery', 1)
            ->column('id,virtual_name,virtual_id,erp_id,channel_types');
        return ['list' => $list];

    }
}