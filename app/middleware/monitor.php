<?php
declare (strict_types=1);

namespace app\middleware;

use think\facade\Log;

class monitor
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle($request, \Closure $next)
    {
        $start_time = microtime(true);

        $response = $next($request);

        $end_time = microtime(true);
        $execution_time = ($end_time - $start_time);
        //判断是否超过一秒
        if ($execution_time > 1) {
            //file_put_contents(app()->getRuntimePath() . '/log/console.log', sprintf("时间:%s,路由:%s,执行时长:%s秒\n", date('Y-m-d H:i:s'), $request->pathinfo(), $execution_time), FILE_APPEND);
        }

        return $response;
    }

    public function end(\think\Response $response)
    {
        try {
            $responseData = $response->getData();
            $server = request()->server();
            // 回调行为
            $ip = $server['REMOTE_ADDR'];
            $method = $server['REQUEST_METHOD'];
            if ($method != "POST") {
                return true;
            }
            $hostName = $server['SERVER_NAME'];
            $serverPath = $server['REQUEST_URI'];
            Log::info(sprintf(
                '请求时间:%s|IP地址:%s|路径:%s|返回:%s|参数:%s|请求头:%s',
                date('Y-m-d H:i:s', $server['REQUEST_TIME']),
                $ip,
                $serverPath,
                json_encode($responseData, JSON_UNESCAPED_UNICODE),
                json_encode(request()->param(), JSON_UNESCAPED_UNICODE),
                json_encode(request()->header(), JSON_UNESCAPED_UNICODE)
            ));
        } catch (\Exception $e) {
            print_r($e->getMessage());
            exit;
        }
    }
}
