<?php
declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\model\InterfaceCallLog;
use app\model\PeriodsProductInventory;
use app\model\PeriodsStatusChangeRecord;
use app\service\elasticsearch\ElasticSearchService;
use app\service\Other;
use app\service\Package;
use app\service\Periods;
use app\service\Products;
use app\service\SecondMerchants as PeriodsSecondMerchant;
use app\Request;
use app\validate\Buyer;
use app\validate\Operations;
use app\validate\SecondCopywriter;
use Elasticsearch\ClientBuilder;
use think\Exception;
use think\Model;


class SecondMerchants extends BaseController
{

    /**
     * 添加尾货商品文案
     *
     * @param Request $request
     * @return false|string|\think\Response
     */
    public function create(Request $request)
    {
        #region 数据获取及验证
        // 参数
        $params = $request->post();
        // 获取商品id
        $params['id'] = $this->getGeneratorID(1);
        if (!$params['id']) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '创建秒发商品失败');
        }
        // 操作信息
        $params['creator_id'] = $request->header('vinehoo-uid', '0');
        // 获取当前操作人
        $params['creator_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['creator_name']) {
            $params['creator_name'] = base64_decode($params['creator_name']);
        }
        // 参数验证
        $validate = new SecondCopywriter();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        // 时间戳转换
//        if (isset($params['onsale_time'])) {
//            $params['onsale_time'] = strtotime($params['onsale_time']);
//        }
//        if (isset($params['sold_out_time'])) {
//            $params['sold_out_time'] = strtotime($params['sold_out_time']);
//        }
//        if (isset($params['sell_time'])) {
//            $params['sell_time'] = strtotime($params['sell_time']);
//        }
//        if (isset($params['predict_shipment_time'])) {
//            $params['predict_shipment_time'] = strtotime($params['predict_shipment_time']);
//        }
        $params['created_time'] = time();
        #endregion 数据获取及验证

        #region 数据的格式化处理
        $period = (int)$params['id'];
        $period_ser = new Periods(9);
        // 上架时间
//        if (isset($params['onsale_time']) && $params['onsale_time'] > time()) {
//            if ($params['onsale_time'] == $params['sell_time']) {
//                $period_ser->task($period, $params['sell_time'], 1, 2);
//            } else {
//                $period_ser->task($period, $params['onsale_time'], 0, 1);
//            }
//            $params['onsale_status'] = 0;
//        } elseif ($params['onsale_time'] < time()) {
//            $params['onsale_status'] = 1;
//        }
//        // 开售时间
//        if (isset($params['sell_time']) && $params['sell_time'] > time()) {
//            $period_ser->task($period, $params['sell_time'], 1, 2);
//        } elseif ($params['sell_time'] < time()) {
//            $params['onsale_status'] = 2;
//        }
        // 下架时间
//        if (isset($params['sold_out_time']) && $params['sold_out_time'] > time()) {
//            $period_ser->task($period, $params['sold_out_time'], 2, 3);
//        }
        // 不变初始值
        if (isset($params['limit_number']) && !empty($params['limit_number'])) {
            $params['invariant_number'] = $params['limit_number'];
        }
        // 产品简码
        if ($params['short_code'] != '') {
            $params['short_code'] = trim($params['short_code'], ',');
        }
        if ($params['product_main_category'] != '') {
            $params['product_main_category'] = trim($params['product_main_category'], ',');
        }
        #endregion 数据的格式化处理

        #region 添加商家秒发商品
        $second = new PeriodsSecondMerchant();
        $result = $second->create($params);
        if (!$result) {
            return throwResponse($result, ErrorCode::EXEC_ERROR, '产品秒发添加失败');
        }
        #endregion 添加商家秒发商品

        #region 添加套餐
        $package = [];
        if (!empty($params['package'])) {
//            $val = json_decode($params['package'], true);
            foreach ($params['package'] as &$val) {
                $val['period_id'] = $params['id'];
                $val['periods_type'] = 9;
                $val['unlimited'] = 1;
                array_push($package, $val);
            }
            unset($val);
        }

        $package_c = new Package(9);
        $package_result = $package_c->createArr($package);
        if ($package_result['status'] == false) {
            return throwResponse([] , ErrorCode::PARAM_ERROR, $package_result['msg']);
        }
        #endregion 添加套餐

        #region 添加套餐产品对应库存
        $product_list = [];
        $now_time = time();
        $pcps = PeriodsProductInventory::where('period', $params['join_period_id'])->column('*','short_code');
        if (!empty($params['product_list'])) {
//            $product_list = json_decode($params['product_list'], true);
            foreach ($params['product_list'] as &$val) {
                $val['period'] = $params['id'];
                $val['costprice'] = $pcps[$val['short_code']]['costprice'] ?? 0;
                $val['periods_type'] = 9;
                $val['title'] = $params['title'];
                $val['created_time'] = $now_time;
                array_push($product_list, $val);
            }
            unset($val);
        }
        $ppi_model = new PeriodsProductInventory();
        $ppi_result = $ppi_model->saveAll($product_list);
        if (empty($ppi_result->toArray())) {
            return throwResponse([] , ErrorCode::PARAM_ERROR, '产品成本保存失败');
        }
        #endregion 添加套餐产品对应库存

        // 添加商品产品评分饮用建议
//        if (!empty($params['product_info'])) {
//            $other_ser = new Other();
//            $other_ser->createProductInfo($result, $params['product_info']);
//        }
        // 生成json文件
        $period_ser->createNewJsonFile($period, 9);
        return throwResponse($result);

    }

    /**
     * 添加编辑采购信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateBuyerInfo(Request $request)
    {
        // 参数
        $params = $request->post();
        // 参数验证
        $validate = new Buyer();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }

        $second = new PeriodsSecond();

        // 添加采购信息
        $result = $second->update($params);

        if (!$result['status']) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, '采购信息添加失败');
        }

        return throwResponse($result['data']);

    }

    /**
     * 添加编辑运营信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update(Request $request)
    {
        // 参数
        $params = $request->post();
        // 参数验证
        $validate = new Operations();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }

        // 验证商品 id
        $update_validate = $this->updateValidate($params);
        if (!empty($update_validate)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $update_validate);
        }

        $second = new PeriodsSecondMerchant();
        // 验证绑定产品
        $period_info = $second->getOne((int)$params['id'], 'product_id, buyer_review_status');
        if (empty($period_info)) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, '未查询到期数信息');
        }
        $old_product_id = explode(',', $period_info['product_id']);
        $product_id = explode(',', $params['product_id']);
        $is_del_product = 0;
        foreach ($old_product_id as $v) {
            if (empty($v)) {
                continue;
            }
            if (!in_array($v, $product_id)) {
                $is_del_product = 1;
            }
        }
        if ($is_del_product == 1 && $period_info['buyer_review_status'] == 3) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, '商品不允许删除绑定产品');
        }

        // 添加运营信息 时间戳转换
//        if (isset($params['predict_shipment_time'])) {
//            $params['predict_shipment_time'] = strtotime($params['predict_shipment_time']);
//        }
//        if (isset($params['onsale_time'])) {
//            $params['onsale_time'] = strtotime($params['onsale_time']);
//        }
//        if (isset($params['sell_time'])) {
//            $params['sell_time'] = strtotime($params['sell_time']);
//        }
//        if (isset($params['sold_out_time'])) {
//            $params['sold_out_time'] = strtotime($params['sold_out_time']);
//        }
        $period = (int)$params['id'];
        $period_ser = new Periods(9);
//        // 上架时间
//        if (isset($params['onsale_time']) && $params['onsale_time'] > time()) {
//            if ($params['onsale_time'] == $params['sell_time']) {
//                $period_ser->task($period, $params['sell_time'], 1, 2);
//            } else {
//                $period_ser->task($period, $params['onsale_time'], 0, 1);
//            }
//            $params['onsale_status'] = 0;
//        } elseif ($params['onsale_time'] < time()) {
//            $params['onsale_status'] = 1;
//        }
//        // 开售时间
//        if (isset($params['sell_time']) && $params['sell_time'] > time()) {
//            $period_ser->task($period, $params['sell_time'], 1, 2);
//        } elseif ($params['sell_time'] < time()) {
//            $params['onsale_status'] = 2;
//        }
//        // 下架时间
//        if (isset($params['sold_out_time']) && $params['sold_out_time'] > time()) {
//            $period_ser->task($period, $params['sold_out_time'], 2, 3);
//        }
        // 限购规则转 json 字符串
//        if (isset($params['quota_rule']) && !empty($params['quota_rule'])) {
//            $params['quota_rule'] = json_encode($params['quota_rule']);
//        }

        // 如果前端返回图片域名，则删除域名
        if (isset($params['banner_img']) && $params['banner_img'] != '') {
            $params['banner_img'] = str_replace(env('ALIURL'), "", $params['banner_img']);
        }
        if (isset($params['product_img']) && $params['product_img'] != '') {
            $params['product_img'] = str_replace(env('ALIURL'), "", $params['product_img']);
        }
        if (isset($params['video_cover']) && $params['video_cover'] != '') {
            $params['video_cover'] = str_replace(env('ALIURL'), "", $params['video_cover']);
        }
        if (isset($params['horizontal_img']) && $params['horizontal_img'] != '') {
            $params['horizontal_img'] = str_replace(env('ALIURL'), "", $params['horizontal_img']);
        }
        // 添加套餐
        $package = [];
        if (!empty($params['package'])) {
//            $package = json_decode($params['package'], true);
            foreach ($params['package'] as &$val) {
                $val['period_id'] = $params['id'];
                $val['periods_type'] = 9;
                $val['unlimited'] = 1;
                array_push($package, $val);
            }
            unset($val);
        }
        $package_c = new Package(9);
        $package_result = $package_c->createArr($package);
        if ($package_result['status'] == false) {
            return throwResponse([] , ErrorCode::PARAM_ERROR, $package_result['msg']);
        }
        // 添加套餐产品对应库存
        $product_list = [];
        $now_time = time();
        if (!empty($params['product_list'])) {
//            $product_list = json_decode($params['product_list'], true);
            foreach ($params['product_list'] as &$val) {
                $val['period'] = $params['id'];
                $val['periods_type'] = 9;
                $val['title'] = $params['title'];
                $val['created_time'] = $now_time;
                array_push($product_list, $val);
            }
        }
        $ppi_model = new PeriodsProductInventory();
        $ppi_result = $ppi_model->saveAll($product_list);
        if (empty($ppi_result->toArray())) {
            return throwResponse([] , ErrorCode::PARAM_ERROR, '产品成本保存失败');
        }
        $result = $second->update($params);

        if (!$result['status']) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, '运营信息添加失败');
        }

        // 添加商品产品评分饮用建议
        if (!empty($params['product_info'])) {
            $other_ser = new Other();
            $other_ser->createProductInfo((int)$params['id'], $params['product_info']);
        }

        $periods_ser = new Periods(9);
        // 更新 CDN
        $periods_ser::CDNrefreshObject((int)$params['id']);

        return throwResponse($result['data']);

    }

    /**
     * 商品详细
     *
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(Request $request)
    {
        // 参数
        $params = $request->get();
        $params['id'] = (int)$params['id'];
        // 验证商品 id
        $update_validate = $this->updateValidate($params);
        if (!empty($update_validate)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $update_validate);
        }

        $second = new PeriodsSecondMerchant();

        // 详细信息
        $result = $second->getOne($params['id']);
        if (empty($result)) {
            return throwResponse($result, ErrorCode::PARAM_ERROR, '期数不存在');
        }
        // 套餐信息
        $package_ser = new Package(9);
        $where['period_id'] = $params['id'];
        $result['package'] = $package_ser->getPackage($where);
        // 产品信息
        $products_ser = new Products();
        $result['product_list'] = [];
        if ($result['product_id']) {
            $result['product_list'] = $products_ser->getListById($result['product_id'], $params['id']);
        }
        $product_inventory = PeriodsProductInventory::field('id, product_id, costprice')
            ->where('period', $params['id'])
            ->select()
            ->toArray();
        $result['product_inventory'] = $product_inventory;
        // 是否存在上架记录
//        $periods = new Periods(0);
//        $result['is_onsale_record'] = 0;
//        $onsale_record = $periods->getOnSaleRecord($params['id'], 1);
//        if ($onsale_record) {
//            $result['is_onsale_record'] = 1;
//        }
        $label_ser = new \app\service\Label();
        // 标签查询
        $result['label_arr'] = [];
        if (!empty($result['label'])) {
            $result['label_arr'] = $label_ser->getLabelNameByIds($result['label']);
        }
        return throwResponse($result);

    }

    /**
     * 更新产品参数验证
     *
     * @param array $params
     * @return string
     */
    protected function updateValidate(array $params): string
    {
        // 提示信息
        $message = '';

        // 商品 id 验证
        if (!isset($params['id']) || empty($params['id'])) {
            $message .= '请选择商品';
        }

        return $message;
    }


    /**
     * 秒发 ES 列表
     * @param Request $request
     * @return \think\Response
     */
    public function getSecondES(Request $request): \think\Response
    {

        $from = (int)$request->get('page', 0);
        $size = (int)$request->get('limit', 15);

        if ($from > 0) {
            $from = ($from - 1) * $size;
        } else {
            $from = 0;
        }

//        $esWhere['bool']['must'] = [];
        $param = $request->get();
        $second_id = $param['second_id'] ?? 1;
        // 秒发频道
        $esWhere['bool']['must'][] = ['match' => ['periods_type' => 1]];
        $esWhere['bool']['must'][] = ['terms' => ['second_ids' => [$second_id]]];

        // 默认查询，待售中，在售中，售完不下架
//        $esWhere['bool']['must'][] = ['match' => ['onsale_status' => 2]];
        $esWhere['bool']['should'][] = ['match' => ['onsale_status' => 1]];
        $esWhere['bool']['should'][] = ['match' => ['onsale_status' => 2]];
        $esWhere['bool']['should'][] = ['match' => ['sellout_sold_out' => 1]];
        $esWhere['bool']['minimum_should_match'] = 1;


        if (isset($param['key']) && $param['key'] != '') {
            // 价格搜索
            if ($param['key'] == 'price') {
                $between_price = explode('-', $param['value']);
                if ((int)$between_price[0] <= 0) {
                    $between_price[0] = 0;
                }
                if (!empty($between_price)) {
                    $esWhere['bool']['must'][] = [
                        'range' => [
                            'price' => [
                                'gte' => $between_price[0],
                                'lte' => $between_price[1]
                            ]
                        ]
                    ];
                }
            } else {
                $param['value'] = $param['value'] ?? '';
                $esWhere['bool']['must'][] = ['match_phrase' => [$param['key'] => $param['value']]];
            }
        }
//        // 国家查询
//        if (isset($param['country']) && $param['country'] != '') {
//            $esWhere['bool']['must'][] = ['match_phrase' => ['country' => $param['country']]];
//        }
//        // 分类查询
//        if (isset($param['product_category']) && $param['product_category'] != '') {
//            $esWhere['bool']['must'][] = ['match_phrase' => ['product_category' => $param['product_category']]];
//        }
//        // 关键词查询
//        if (isset($param['product_keyword']) && $param['product_keyword'] != '') {
//            $esWhere['bool']['must'][] = ['match_phrase' => ['product_keyword' => $param['product_keyword']]];
//        }
        // 排序
        $sort = [['sort' => 'desc'], ['onsale_time' => 'desc']];
        // 指定字段
        $field = ['id', 'country', 'title', 'brief', 'banner_img', 'horizontal_img', 'sort', 'second_ids',
            'purchased', 'vest_purchased', 'price', 'market_price', 'periods_type', 'product_img'];

        $params = [
            'index' => 'vinehoo.periods',
            'type' => '_doc',
            '_source' => $field,
            'body' => [
                'query' => $esWhere,
                'sort' => $sort,
                'size' => $size,
                'from' => $from
            ]
        ];
        // es 配置
        $hosts = [
            [
                'host' => env('ES.HOST', '127.0.0.1'),
                'port' => env('ES.PORT', 9200),
                'user' => env('ES.USER', 'root'),
                'pass' => env('ES.PASS', 'vinehoo666')
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);

        // 遍历列表取出限购数量
        if (!empty($data['list'])) {
            foreach ($data['list'] as &$val) {
                if (isset($val['quota_rule']) && !empty($val['quota_rule'])) {
                    $val['quota_number'] = json_decode($val['quota_rule'], true)['quota_number'] ?? 0;
                }
                // 题图
                if (isset($val['banner_img']) && $val['banner_img'] != '') {
                    $val['banner_img'] = env('ALIURL') . $val['banner_img'];
                }
                // 产品图
                if (isset($val['product_img']) && $val['product_img'] != '') {
                    $val['product_img'] = explode(",", $val['product_img']);
                    foreach ($val['product_img'] as &$v) {
                        $v = env('ALIURL') . $v;
                    }
                    unset($v);
                }
                // 秒发竖图
                if (isset($val['horizontal_img']) && $val['horizontal_img'] != '') {
                    $val['horizontal_img'] = explode(",", $val['horizontal_img']);
                    foreach ($val['horizontal_img'] as &$v) {
                        $v = env('ALIURL') . $v;
                    }
                    $val['horizontal_img'] = $val['horizontal_img'][0] ?? '';
                    unset($v);
                }
                // 视频封面图增加域名
                if (isset($val['video_cover']) && $val['video_cover'] != '') {
                    $val['video_cover'] = env('ALIURL') . $val['video_cover'];
                }
                $val['is_gift'] = 0;
                $val['is_reduction'] = 0;
            }
        }
        $result['list'] = $data['list'];
        $result['total'] = $data['total'];
        return throwResponse($result);
    }

    /**
     * 秒发可筛选项
     * @param Request $request
     * @return \think\Response
     */
    public function getSecondPeriodsFilter(Request $request): \think\Response
    {
        // 获取筛选项字段
        $field = ['id', 'country', 'product_category', 'product_keyword'];
        $param = $request->get();
        $second_id = $param['second_id'] ?? 1;
        // 秒发频道
        $esWhere['bool']['must'][] = ['match' => ['periods_type' => 1]];
        $esWhere['bool']['must'][] = ['terms' => ['second_ids' => [$second_id]]];

        $params = [
            'index' => 'vinehoo.periods',
            'type' => '_doc',
            '_source' => $field,
            'body' => [
                'query' => $esWhere,
            ]
        ];
        // es 配置
        $hosts = [
            [
                'host' => env('ES.HOST', '127.0.0.1'),
                'port' => env('ES.PORT', 9200),
                'user' => env('ES.USER', 'root'),
                'pass' => env('ES.PASS', 'vinehoo666')
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);
        $result['product_category'] = [];
        $result['country'] = [];
        $result['product_keyword'] = [];
        if (!empty($data['list'])) {
            foreach ($data['list'] as $val) {
//                $val['country'] = explode(",", $val['country']);
                if (!empty($val['country'])) {
                    foreach ($val['country'] as $v) {
                        if (in_array($v, $result['country'])) {
                            continue;
                        }
                        if ($v) {
                            array_push($result['country'], $v);
                        }
                    }
                    unset($v);
                }
//                $val['product_category'] = explode(",", $val['product_category']);
                if (!empty($val['product_category'])) {
                    foreach ($val['product_category'] as $v) {
                        if (in_array($v, $result['product_category'])) {
                            continue;
                        }
                        if ($v) {
                            array_push($result['product_category'], $v);
                        }
                    }
                    unset($v);
                }
                if (!empty($val['product_keyword'])) {
                    foreach ($val['product_keyword'] as $v) {
                        if (in_array($v, $result['product_keyword'])) {
                            continue;
                        }
                        if ($v) {
                            array_push($result['product_keyword'], $v);
                        }
                    }
                    unset($v);
                }
            }
        }

        // 价格配置
        $price_url = env('ITEM.WINE_WIKI_URL') . '/wiki/v3/screen/index?type=4';
        $price_list = get_url($price_url);
        $price_list = get_interior_http_response($price_list);
        $price_list = $price_list['list'] ?? [];
        $price_list_name = [];
        if (!empty($price_list)) {
            foreach ($price_list as $value) {
                $price_list_name[] = $value['name'];
            }
        }
        $re[0]['name'] = '国家';
        $re[0]['key'] = 'country';
        $re[0]['list'] = $result['country'] ?? [];
        $re[1]['name'] = '类别';
        $re[1]['key'] = 'product_category';
        $re[1]['list'] = $result['product_category'] ?? [];
        $re[2]['name'] = '价格';
        $re[2]['key'] = 'price';
        $re[2]['list'] = $price_list_name;
        $re[3]['name'] = '关键词';
        $re[3]['key'] = 'product_keyword';
        $re[3]['list'] = $result['product_keyword'] ?? [];
        return throwResponse($re);
    }

    /**
     * 商家秒发列表
     * @param Request $request
     * @return \think\Response
     */
    public function getSecondMerchants(Request $request): \think\Response
    {

        $from = (int)$request->get('page', 1);
        $size = (int)$request->get('limit', 15);

        $where = [];
        $range = [];
        $match_phrase = [];
        $therms = [];

        $sort = [['id' => 'desc'], ['onsale_time' => 'desc']];
        // 未删除
        $where[] = ['is_delete' => 0];
        $where[] = ['periods_type' => 9];

        // 商品标题
        if ($request->get('title')) {
            $where[] = ['title' => ['query' => $request->get('title'), 'operator' => 'and']];
        }

        // 期数
        if ($request->get('id')) {
            $where[] = ['id' => (int)$request->get('id')];
        }

        // 商品编号
        if ($request->get('commodity_code')) {
            $where[] = ['commodity_code' => $request->get('commodity_code')];
        }

        // 渠道
        if ($request->get('product_channel') != '') {
            $where[] = ['product_channel' => (int)$request->get('product_channel')];
        }

        // 商家 id
        if ($request->get('supplier_id')) {
            $where[] = ['supplier_id' => (int)$request->get('supplier_id')];
        }

        // 文案审核状态
        if ($request->get('copywriting_review_status') != '') {
            $where[] = ['copywriting_review_status' => (int)$request->get('copywriting_review_status')];
        }

        // 价格区间
        if ($request->get('price_min')) {
            $range[] = ['price' => [
                'gte' => $request->get('price_min'),
            ]];
        }
        if ($request->get('price_max')) {
            $range[] = ['price' => [
                'lte' => $request->get('price_max')
            ]];
        }

        // 平台审核
        if ($request->get('platform_review') && $request->get(['platform_review']) > 0) {
            $where[] = ['copywriting_review_status' => 1];
        }

        // 平台审核列表
        if ($request->get('pending_review') && $request->get(['pending_review']) > 0) {
//            $where[] = ['onsale_review_status' => 4];
            $therms[] = ['copywriting_review_status' => [0, 1]];
        }

        // 商品主类别
        if ($request->get('product_category')) {
            $therms[] = ['product_main_category' => [$request->get('product_category')]];
        }

        // 查询审核状态
        if ($request->get('onsale_review_status') != '') {
            $where[] = ['onsale_review_status' => $request->get('onsale_review_status')];
        }

        // 上架状态包含待售中和已上架
        if ($request->get('onsale_status_t')) {
            $therms[] = ['onsale_status' => [1, 2]];
            unset($sort);
            $sort = [['onsale_time' => 'desc'], ['id' => 'desc']];
        }

        // 上架状态
        if ($request->get('onsale_status') != '') {
            // 下架状态查询，已下架，已售罄
            if ($request->get('onsale_status') == 3) {
                $therms[] = ['onsale_status' => [3, 4]];
            } else {
                $where[] = ['onsale_status' => $request->get('onsale_status')];
            }
        }

        // 简码搜索
        if ($request->get('short_code')  != '') {
            $therms[] = ['short_code' => [$request->get('short_code')]];
        }

        // 上架时间
        if ($request->get('onsale_time_start')) {
            $range[] = ['onsale_time' => [
                'gte' => $request->get('onsale_time_start'),
                'lt' => $request->get('onsale_time_end')]
            ];
        }

        // 下架时间
        if ($request->get('sold_out_time_start')) {
            $range[] = ['sold_out_time' => [
                'gte' => $request->get('sold_out_time_start'),
                'lt' => $request->get('sold_out_time_end')]
            ];
        }

        $params = [
            'index' => ['periods'],
            'match' => $where,
            'match_phrase' => $match_phrase,
            'terms' => $therms,
            'range' => $range,
            'page' => $from,
            'limit' => $size,
            'sort' => $sort,
        ];
        $es = new ElasticSearchService();

        $data = $es->getDocumentList($params);

        $period_ids = '';
        if (!empty($data['data'])) {
            foreach ($data['data'] as &$val) {
                $period_ids .= $val['id'] . ',';
                if (isset($val['quota_rule']) && !empty($val['quota_rule'])) {
                    $val['quota_number'] = json_decode($val['quota_rule'], true)['quota_number'] ?? 0;
                }
                // 图片新增域名
                if (isset($val['banner_img']) && $val['banner_img'] != '') {
                    $val['banner_img'] = env('ALIURL') . $val['banner_img'];
                }
                unset($v);
                // 视频封面图增加域名
                if (isset($val['video_cover']) && $val['video_cover'] != '') {
                    $val['video_cover'] = env('ALIURL').$val['video_cover'];
                }
                $val['inventory'] = 0;
                // 下架操作时间戳转换
                if ($val['off_sell_time']) {
                    $val['off_sell_time'] = date('Y-m-d H:i:s', $val['off_sell_time']);
                }
            }
            unset($val);
            // 操作日志提交时间
            if ($request->get('platform_review') !== null) {
                $record = PeriodsStatusChangeRecord::field('period, created_time')
                    ->where(['type' => 4, 'status_type' => 1])
                    ->whereIn('period', $period_ids)
                    ->order('created_time', 'desc')
                    ->group('period')
                    ->select()
                    ->toArray();
                if (!empty($record)) {
                    foreach ($data['data'] as &$val) {
                        $val['submit_time'] = '';
                        foreach ($record as &$v) {
                            if ($val['id'] == $v['period']) {
                                $val['submit_time'] = $v['created_time'];
                            }
                        }
                    }
                }
            }
        }
        $period_ids = trim($period_ids, ',');
        // 获取期数总库存
        $vmall_url = env('ITEM.VMALL_URL'). '/vmall/v3/shopstock/getinventoryRemaining';
        $vmall_url .= '?good_ids='. $period_ids;

        $re_vmall = get_url($vmall_url);
        if ($re_vmall) {
            try {
                $re_vmall_data = json_decode($re_vmall, true);
                foreach ($data['data'] as &$v) {
                    foreach ($re_vmall_data['data'] as $vv) {
                        if ($v['id'] == $vv['good_id']) {
                            $v['inventory'] = $vv['good_stock'];
                        }
                    }
                }
                unset($v, $vv);
            } catch (\Exception $e) {
                // 记录异常
                InterfaceCallLog::create([
                   'function_name' => 'getSecondMerchants',
                   'model' => 'vmall',
                   'method' => 'GET',
                   'url' => $vmall_url,
                   'params' => $period_ids,
                   'response' => $re_vmall,
                   'remark' => '获取商家秒发总库存接口',
                   'create_time' => date('Y-m-d H:i:s', time())
               ]);
            }
        }

        $totalNum = $data['total']['value'];
        $result['list'] = array_values($data['data']);
        $result['total'] = $totalNum;
        return throwResponse($result);
    }

    /**
     * 审核商家期数
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function operationAudit(Request $request): \think\Response
    {
        $params = $request->post();
        $period = (int) $params['period'] ?? 0;
        $periods_type = (int) $params['periods_type'] ?? 9;
        if (!$period) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '请选择期数');
        }
        // 更新数据
        $data = [];
        // 审核人 id
        $data['operation_review_id'] = $request->header('vinehoo-uid', '0');
        // 审核人名称
        $data['operation_review_name'] = $request->header('vinehoo-vos-name', '');
        if ($data['operation_review_name']) {
            $data['operation_review_name'] = base64_decode($data['operation_review_name']);
        }
        $onsale_review_status = $params['onsale_review_status'] ?? 0;
        // 驳回
        if ($onsale_review_status == 4) {
            if (!isset($params['off_sell_remark']) || empty($params['off_sell_remark'])) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, '请说明驳回原因');
            }
            $data['onsale_review_status'] = 4;
            $data['copywriting_review_status'] = 0;
            $data['onsale_review_time'] = time();
            $data['off_sell_remark'] = $params['off_sell_remark'];
        }
        $period_ser = new Periods($periods_type);
        // 期数详细
        $period_info = $period_ser->getOne($period, 'sell_time');

        if (empty($period_info)) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '未查询到期数信息');
        }
        // 通过
        if ($onsale_review_status == 3) {
            $sell_time = 0;
            if ($period_info['sell_time']) {
                $sell_time = strtotime($period_info['sell_time']);
            }
            // 开售时间
            if ($sell_time > time()) {
                $period_ser->task($period, $sell_time, 1, 2);
            } elseif ($sell_time < time()) {
                $data['onsale_status'] = 2;
                $data['onsale_review_time'] = time();
            }
            $data['onsale_verify_status'] = 1;
            $data['onsale_review_status'] = 3;
            $data['copywriting_review_status'] = 2;
            $data['off_sell_type'] = null;
        }

        // 更新审核状态
        $result = $period_ser->updateInfoById($period, $data);
        return throwResponse($result);
    }

    /**
     * 复制期数
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function copyPeriod(Request $request): \think\Response
    {
        $params = $request->get();
        // 获取商品id
        $params['id'] = $this->getGeneratorID(1);
        if (!$params['id']) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '创建秒发商品失败');
        }
        $params['package_id'] = $this->getGeneratorID(2);
        if (!$params['package_id']) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '创建秒发商品套餐失败');
        }
        $sm_ser = new PeriodsSecondMerchant();
        $copy = $sm_ser->copyPeriod($params);
        if ($copy['status'] == false) {
            return throwResponse($copy['data'], ErrorCode::EXEC_ERROR, $copy['msg']);
        }
        return throwResponse(['period' => $params['id'], 'package_id' => $params['package_id']]);
    }

    /**
     * 更新马甲已购
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateVestPurchased(Request $request): \think\Response
    {
        $id = $request->post('id', 0);
        $vest_purchased = $request->post('vest_purchased', 0);
        if ((int)$id < 1) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '期数错误');
        }
        if ((int)$vest_purchased < 1) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '更新数量请输入正整数');
        }
        $sm_ser = new PeriodsSecondMerchant();
        $result = $sm_ser->updateField((int)$id ,['vest_purchased' => $vest_purchased]);
        if (!$result) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, '未更新数据');
        }
        return throwResponse(1);
    }

    /**
     * 修改期数成本
     * @param Request $request
     * @return \think\Response
     */
    public function updateCost(Request $request): \think\Response
    {
        $params = $request->post();
        $sm_ser = new PeriodsSecondMerchant();
        $where = ['period' => $params['period'], 'short_code' => $params['short_code']];
        $result = $sm_ser->updateWhereField($where ,['costprice' => $params['costprice']]);
        return throwResponse($result);
    }


}
