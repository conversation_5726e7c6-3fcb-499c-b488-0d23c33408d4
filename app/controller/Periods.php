<?php
declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\model\InterfaceCallLog;
use app\model\PeriodsAuctionFollow;
use app\model\PeriodsReservation;
use app\model\PeriodsTaskLog;
use app\model\PeriodsUserAuction;
use app\Request;
use app\service\Auction;
use app\service\Comment as CommentSer;
use app\service\es\Es;
use app\service\Package as PackageSer;
use app\service\Periods as PeriodsSer;
use app\service\v3\PeriodsPoolService;
use app\validate\Comment;
use app\validate\PeriodsPlushMinus;
use app\validate\PeriodsProductInventory;
use app\validate\PeriodsProductUpInventory;
use app\validate\PeriodsRemark;
use app\validate\Purchase;
use PhpOffice\PhpSpreadsheet\IOFactory;
use think\Db;
use think\facade\App;
use think\facade\Db as MDb;
use think\facade\Validate as DataValidate;
use think\facade\Log;

class Periods extends BaseController
{
    /**
     * 商品内容采购审核
     * @param Request $request
     * @return \think\Response
     */
    public function review(Request $request)
    {
        $params = $request->post();
        // 添加套餐编辑人
        $params['user_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['user_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['user_name']) {
            $params['user_name'] = base64_decode($params['user_name']);
        }
        #数据验证
        $validate = DataValidate::rule([
            'period|期数'    => 'require|number|>:0',
            'periods_type|商品频道'    => 'require|number',
            'user_id|操作人ID' => 'require|number|>:0',
            'user_name|操作人' => 'require|max:255',
            'buyer_review_status|审核状态' => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        
        $periods = new PeriodsSer($params['periods_type']);

        if (in_array($params['buyer_review_status'], $periods->buyer_review_tg_status)) {
            // 秒发、跨境强制科技公司
            if (in_array(intval($params['periods_type']), [1, 2])) {
                $params['payee_merchant_id'] = 2;
                $params['payee_merchant_name'] = '佰酿云酒（重庆）科技有限公司';
            }

            #数据验证
            $validate = DataValidate::rule([
                'supplier_id|供应商ID' => 'require|number|>:0',
                'supplier|供应商' => 'require|max:255',
                'payee_merchant_id|收款商户ID' => 'require|number|in:1,2,5,10',
                'payee_merchant_name|收款商户名称' => 'require|max:255',
            ]);
            if (!$validate->check($params)) {
                return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
            }
        }

        
        $result = $periods->review($params);

        if ($result['status'] == false) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, $result['msg']);
        }

        return throwResponse($result['data']);
    }

    /**
     * 运营内容审核
     * @param Request $request
     * @return \think\Response
     */
    public function onSale(Request $request): \think\Response
    {
        $params = $request->post();
        if (!isset($params['period']) || !isset($params['periods_type'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择审核商品');
        }
        $params['periods_type'] = (int)$params['periods_type'] ?? 0;
        $periods = new PeriodsSer($params['periods_type']);
        // 添加套餐编辑人
        $params['user_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['user_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['user_name']) {
            $params['user_name'] = base64_decode($params['user_name']);
        }
        // 运营审核验证
        $params['version'] = $params['version'] ?? 0;
        $periods_info = $periods->getOne((int)$params['period'], 'version, operation_id');
        if ($params['version'] != $periods_info['version']) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '数据已变更请重新审核');
        }
        // 除了兔头商品，其他的频道都需要限制【运营】和【审核】不能为同一人
        // if (!in_array($params['periods_type'], [4, 5]) && $periods_info['operation_id'] == $params['user_id']) {
        //     return throwResponse([], ErrorCode::PARAM_ERROR, '运营和审核不能是同一人。');
        // }
        $result = $periods->onsale($params);

        return throwResponse($result);

    }

    /**
     * 运营上下架审核
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function offSale(Request $request): \think\Response
    {
        $params = $request->post();
        if (!isset($params['period']) || !isset($params['periods_type'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择审核商品');
        }
        $params['periods_type'] = (int)$params['periods_type'] ?? 0;
        $periods = new PeriodsSer($params['periods_type']);
        // 添加套餐编辑人
        $params['user_id'] = $request->header('vinehoo-uid', '0');
        // 获取当前操作人
        $params['user_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['user_name']) {
            $params['user_name'] = base64_decode($params['user_name']);
        }
        
        if ($params['onsale_status'] == 3 && empty($params['remark'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '手动下架备注不能为空');
        }

        $result = $periods->offSale($params);

        if ($result['status'] == false) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, $result['msg']);
        }

        return throwResponse($result['data']);
    }


    /**
     * 商家商品上下架
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function vmallOffSale(Request $request): \think\Response
    {
        $params = $request->post();
        if (!isset($params['period']) || !isset($params['periods_type'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择审核商品');
        }
        $params['periods_type'] = (int)$params['periods_type'] ?? 0;
        $periods = new PeriodsSer($params['periods_type']);
        // 添加套餐编辑人
        $params['user_id'] = $request->header('vinehoo-uid', '0');
        // 获取当前操作人
        $params['user_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['user_name']) {
            $params['user_name'] = base64_decode($params['user_name']);
        }
        $result = $periods->vmallOffSale($params);

        if ($result['status'] == false) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, $result['msg']);
        }

        return throwResponse($result['data']);
    }

    /**
     * 删除期数
     * @param Request $request
     * @return \think\Response
     */
    public function delete(Request $request): \think\Response
    {
        $period = $request->post('period', 0);
        $periods_type = $request->post('periods_type', 0);
        // 删除期数
        if ((int)$period > 0) {
            $periods = new PeriodsSer((int)$periods_type);
            $periods->delete((int)$period);
        }
        return throwResponse([]);
    }

    /**
     * 更新商品信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update(Request $request): \think\Response
    {
        $params = $request->post();
        // 参数验证
        $params['periods_type'] = (int)$params['periods_type'] ?? 0;
        $period = (int)$params['period'] ?? 0;
        if (!isset($params['express_id']) || ((int)$params['express_id']) < 1) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '快递方式必选');
        }
        // 选择期数频道
        $periods = new PeriodsSer($params['periods_type']);
        // 时间戳转换
        if (isset($params['onsale_time'])) {
            $params['onsale_time'] = strtotime($params['onsale_time']);
        }
        if (isset($params['sold_out_time'])) {
            $params['sold_out_time'] = strtotime($params['sold_out_time']);
        }
        if (isset($params['sell_time'])) {
            $params['sell_time'] = strtotime($params['sell_time']);
        }
        if (isset($params['predict_shipment_time'])) {
            $params['predict_shipment_time'] = strtotime($params['predict_shipment_time']);
        }
        // if ($params['periods_type'] == '1' || $params['periods_type'] == '2' || $params['periods_type'] == '4') {
        if (isset($params['payee_merchant_id'])) {
            unset($params['payee_merchant_id']);
        }
        if (isset($params['payee_merchant_name'])) {
            unset($params['payee_merchant_name']);
        }
        // }
        
        $field = 'id,supplier_id,payee_merchant_id';
        if ($params['periods_type'] <= 4) {
            $field .= ',is_channel';
        }
        if ($params['periods_type'] == 0) {
            $field .= ',is_seckill,onsale_verify_status';
        }
        
        $period_info = $periods->getOne($period, $field);
        if (empty($period_info)) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '期数信息查询失败');
        }
        if (empty($period_info['supplier_id'])) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, '供应商不能为空');
        }
        if (empty($period_info['payee_merchant_id'])) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, '收款商户不能为空');
        }
        if ($params['periods_type'] == 0) {
            if (isset($params['is_seckill']) && $params['is_seckill'] != $period_info['is_seckill'] && $period_info['onsale_verify_status'] == 1) {
                return throwResponse(null, ErrorCode::EXEC_ERROR, '秒杀状态不可更改！');
            }
        }

        unset($params['period'], $params['periods_type']);

        // 非渠道销售不能存在同产品同时售卖，判断依据是【商品售卖区间】开始时间-下架时间。不能存在相同存货
        if (isset($params['is_channel']) && intval($params['is_channel']) === 0 && isset($params['sell_time']) && isset($params['sold_out_time'])) {
            $res = $periods->VerifySimultaneousSales($period, $params['sell_time'], $params['sold_out_time']);
            if ($res !== true) {
                return throwResponse(null, ErrorCode::EXEC_ERROR, $res);
            }
        }

        // 更新后状态变更为待审核
//        $params['onsale_review_status'] = 1;
        $result = $periods->update($period, $params);
        if (is_string($result)){
            return throwResponse([], ErrorCode::EXEC_ERROR, $result);
        }
        return throwResponse($result);
    }

    /**
     * 更新商品上下架时间信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updatePeriodsTime(Request $request): \think\Response
    {
        $params = $request->post();

        // 参数验证
        $params['periods_type'] = (int)$params['periods_type'] ?? 0;
        $period = (int)$params['period'] ?? 0;
        // 选择期数频道
        $periods = new PeriodsSer($params['periods_type']);
        // 时间戳转换
        if (isset($params['onsale_time'])) {
            $params['onsale_time'] = strtotime($params['onsale_time']);
        }
        if (isset($params['sold_out_time'])) {
            $params['sold_out_time'] = strtotime($params['sold_out_time']);
        }
        if (isset($params['sell_time'])) {
            $params['sell_time'] = strtotime($params['sell_time']);
        }
        if (isset($params['predict_shipment_time'])) {
            $params['predict_shipment_time'] = strtotime($params['predict_shipment_time']);
        }
        $fields = 'id';
        if ($params['periods_type'] == 9) {
            $fields .= ',off_sell_type';
        }
        if ($params['periods_type'] < 4) {
            $fields .= ',is_channel';
        }
        $period_info = $periods->getOne($period, $fields);
        if ($params['periods_type'] == 9) {
            // 系统下架或手动下架，重新走提交流程
            if ($period_info['off_sell_type'] == '0' || $period_info['off_sell_type'] == '1') {
                // 重新走提交流程
                $params['copywriting_review_status'] = 1;
                $params['onsale_review_status'] = 1;
            }
        }
        if (empty($period_info['is_channel']) && isset($params['sell_time']) && isset($params['sold_out_time'])) {
            // 非渠道销售不能存在同产品同时售卖，判断依据是【商品售卖区间】开始时间-下架时间。不能存在相同存货
            $res = $periods->VerifySimultaneousSales($period, $params['sell_time'], $params['sold_out_time']);
            if ($res !== true) {
                return throwResponse(null, ErrorCode::EXEC_ERROR, $res);
            }
        }

        if(isset($period_info['is_channel']) && ($period_info['is_channel'] == 0) && empty($params['is_channel']) && ($params['periods_type'] == 2)){
            $params['is_channel'] = $period_info['is_channel'];
        }
        unset($params['period'], $params['periods_type']);
        // 更新后状态变更为待审核
//        $params['onsale_review_status'] = 1;
        $result = $periods->update($period, $params);
        if (is_string($result)){
            return throwResponse([], ErrorCode::EXEC_ERROR, $result);
        }

        return throwResponse($result);
    }

    /**
     * 更新商品上下架时间信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function vmallUpdatePeriodsTime(Request $request): \think\Response
    {
        $params = $request->post();
        // 参数验证
        $params['periods_type'] = (int)$params['periods_type'] ?? 0;
        $period = (int)$params['period'] ?? 0;
        // 选择期数频道
        $periods = new PeriodsSer($params['periods_type']);
        // 时间戳转换
        if (isset($params['onsale_time'])) {
            $params['onsale_time'] = strtotime($params['onsale_time']);
        }
        if (isset($params['sold_out_time'])) {
            $params['sold_out_time'] = strtotime($params['sold_out_time']);
        }
        if (isset($params['sell_time'])) {
            $params['sell_time'] = strtotime($params['sell_time']);
        }
        if (isset($params['predict_shipment_time'])) {
            $params['predict_shipment_time'] = strtotime($params['predict_shipment_time']);
        }
        if ($params['periods_type'] == 9) {
            $period_info = $periods->getOne($period, 'off_sell_type');
            // 系统下架或手动下架，重新走提交流程
            if ($period_info['off_sell_type'] == '0' || $period_info['off_sell_type'] == '1') {
                // 重新走提交流程
                $params['copywriting_review_status'] = 1;
                $params['onsale_review_status'] = 1;
            }
        }
        unset($params['period'], $params['periods_type']);
        // 更新后状态变更为待审核
//        $params['onsale_review_status'] = 1;
        $result = $periods->vmallUpdate($period, $params);
        return throwResponse($result);
    }

    /**
     * 更新期数不重要信息
     * @param Request $request
     * @return \think\Response
     */
    public function updateUncommonlyUsed(Request $request)
    {
        $params = $request->post();
        // 参数验证
        if (!isset($params['periods_type']) || !isset($params['period'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择审核商品');
        }
        // uid
        $uid = $request->header('vinehoo-uid') ?? 0;
        // 获取当前操作人
        $vos_name = $request->header('vinehoo-vos-name', '');
        if (!empty($vos_name)) {
            $vos_name = base64_decode($vos_name);
        }
        $params['periods_type'] = (int)$params['periods_type'] ?? 0;
        $period = (int)$params['period'] ?? 0;
//        if ($params['periods_type'] > 4) {
//            return throwResponse(0);
//        }
        // 选择期数频道
        $periods = new PeriodsSer($params['periods_type']);
        // 更新数组
        $data = [];
        // 更新排序
        if (isset($params['sort'])) {
            $data['sort'] = $params['sort'];
        }
        // 是否爆款
        if (isset($params['is_hot'])) {
            $data['is_hot'] = $params['is_hot'];
        }
        // 采购类型
        if (isset($params['import_type'])) {
            $data['import_type'] = $params['import_type'];
        }
        // 供应商
        if (isset($params['supplier'])) {
            $data['supplier'] = $params['supplier'];
        }
        // 供应商 id
        if (isset($params['supplier_id'])) {
            $data['supplier_id'] = $params['supplier_id'];
        }
        // 冷链包邮
        if (isset($params['is_cold_free_shipping'])) {
            $data['is_cold_free_shipping'] = $params['is_cold_free_shipping'];
        }
        // 自动延期
        if (isset($params['is_postpone'])) {
            $data['is_postpone'] = $params['is_postpone'];
        }
        // 是否售完不下架
        if (isset($params['sellout_sold_out'])) {
            $data['sellout_sold_out'] = $params['sellout_sold_out'];
        }
        // 是否售完不下架
        if ($params['periods_type'] == 9 && isset($params['stores'])) {
            $data['stores'] = $params['stores'];
        }
        // 是否失效
        if (isset($params['is_fail'])) {
            $data['is_fail'] = $params['is_fail'];
        }
        $period_info = $periods->getModel()
            ->where('id', $period)
            ->field('predict_shipment_time,sort')->find();

        // 预计发货时间修改
        if (isset($params['predict_shipment_time']) && $params['predict_shipment_time'] != '') {
            if (!($params['p_not_update'] ?? 0)) {
                $data['predict_shipment_time'] = strtotime($params['predict_shipment_time']);
            }

            $re_str                = '';
            $predict_shipment_time = $period_info['predict_shipment_time'] ?? 0;
            if ($predict_shipment_time) {
                $re_str .= "原发货时间：" . date('Y-m-d H:i:s', $predict_shipment_time) . "，";
            }
            $re_str      .= "修改后发货时间：{$params['predict_shipment_time']}";
            $remark_data = [
                'period'        => $period,
                'periods_type'  => $params['periods_type'],
                'remark'        => $re_str,
                'operator'      => $params['p_admin_id'] ?? 0,
                'operator_name' => $params['p_admin_name'] ?? '系统',
                'created_time'  => time(),
            ];
        }
        if ($params['periods_type'] == 9 && isset($params['stores'])) {
            $data['stores'] = $params['stores'];
        }

        $result = $periods->update($period, $data);
        if (is_string($result)){
            return throwResponse([], ErrorCode::EXEC_ERROR, $result);
        }
        // 更新排序保存操作记录
        if (isset($params['sort'])) {
            $body = $params;
            $body['uid'] = $uid;
            $body['vos_name'] = $vos_name;
            $body['route_url'] = 'sort';
            $body['old_sort'] = $period_info['sort'] ?? 0;
            $periods->SaveOperationRecord($body, 'sort');
        }

        if(!empty($remark_data)){
            \think\facade\Db::name('periods_remark')->insert($remark_data);
        }
        return throwResponse($result);
    }

    /**
     * 创建商品备注
     * @param Request $request
     * @return \think\Response
     */
    public function createRemark(Request $request): \think\Response
    {
        $params = $request->post();
        $validate = new PeriodsRemark();
        // 操作信息
        $params['operator'] = $request->header('vinehoo-uid') ?? 0;
        // 获取当前操作人
        $params['operator_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['operator_name']) {
            $params['operator_name'] = base64_decode($params['operator_name']);
        }
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $params['periods_type'] = (int)$params['periods_type'] ?? 0;
        $params['created_time'] = time();
        $periods = new PeriodsSer($params['periods_type']);
        $result = $periods->createRemark($params);
        if (!$result) {
            return throwResponse($result, ErrorCode::EXEC_ERROR, '添加备注失败');
        }

        return throwResponse($result);
    }

    /**
     * 获取商品备注列表
     *
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function remarkList(Request $request)
    {
        $params = $request->get();
        if (!isset($params['period']) || (int)$params['period'] < 1) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择期数');
        }
        $periods = new PeriodsSer(0);
        $limit = $request->get('limit', '50');
        $where = function ($query) use ($params) {
            if (isset($params['remark']) && strlen($params['remark']) > 0) {
                $query->where('remark', 'like', "%{$params['remark']}%");
            }

            if (isset($params['operator']) && strlen($params['operator']) > 0) {
                $query->where('operator', $params['operator']);
            }

            if (isset($params['department']) && strlen($params['department']) > 0) {
                $operators = \Curl::getSpecifyList($params['department']) ?? [];
                $query->where('operator', 'in', array_column($operators, 'id'));
            }
        };
        $re = \app\model\PeriodsRemark::where('period', $params['period'])->where($where)->order('id', 'desc')->paginate([
            'list_rows' => $limit,
            'var_page' => 'page',
        ]);
        $result['total'] = $re->total() ?? 0;
        $result['list'] = $re->getCollection()->toArray() ?? [];
        $info = Es::name('periods')->where([['_id','=',$params['period']]])->find();
        // 查询最新超卖发货时间
        $oversell_time = \app\model\PeriodsProductInventoryOrder::where('period', $params['period'])
            ->order('created_time desc')
            ->value('predict_shipment_time');
        $info['oversell_shipment_time'] = empty($oversell_time) ? '' : date('Y-m-d H:i:s', $oversell_time);
        $result['info'] = $info;
        return throwResponse($result);
    }

    public function remarkUserList(Request $request)
    {
        $params = $request->get();
        $where = [
          ['operator' ,'<>', 0],
          ['operator_name' ,'<>', ''],
        ];

        if(isset($params['operator_name']) && strlen($params['operator_name']) > 0){
            $where[] = ['operator_name', 'LIKE', "%{$params['operator_name']}%"];
        }

        if(isset($params['period']) && strlen($params['period']) > 0){
            $where[] = ['period', '=', $params['period']];
        }

        $where[] = ['operator_name', '<>', ''];
       $list =  \app\model\PeriodsRemark::where($where)->group('operator')->field('operator,operator_name')->select()->toArray();
        return throwResponse(compact('list'));
    }

    /**
     * 增减库存已购
     *
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function periodsPlusMinus(Request $request): \think\Response
    {
        $params = $request->post();
        $validate = new PeriodsPlushMinus();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $params['periods_type'] = $params['periods_type'] ?? 0;
        $periods = new PeriodsSer((int)$params['periods_type']);
        $result = $periods->periodsPlusMinus($params);
        if ($result['status'] == false) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, $result['msg']);
        }
        return throwResponse($result['data']);
    }

    /**
     * 添加商品评论
     * @param Request $request
     * @return \think\Response
     */
    public function createComment(Request $request): \think\Response
    {
        $params = $request->post();
        $validate = new Comment();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        // uid
        if (!isset($params['uid']) || empty($params['uid'])) {
            $params['uid'] = $request->header('vinehoo-uid');
        }
        $params['uname'] = $request->header('vinehoo-vos-name', '');
        if ($params['uname']) {
            $params['uname'] = base64_decode($params['uname']);
        }
        $params['periods_type'] = (int)$params['periods_type'] ?? 0;
        // 商家秒发查询原期数
        if ($params['periods_type'] == 9) {
            $period_info = MDb::name('periods_second_merchants')
                ->where('id', $params['period'] ?? 0)
                ->field('join_period_id,join_periods_type')
                ->findOrEmpty();
            if (!empty($period_info['join_period_id'])) {
                $params['m_period'] = $params['period'];
                $params['period'] = $period_info['join_period_id'];
                $params['periods_type'] = $period_info['join_periods_type'];
            }
        }

        $periods = new PeriodsSer($params['periods_type']);
        $params['audit_status'] = 0;
        $params['created_time'] = time();
        $params['source'] = $params['source'] ?? 1;
        $result = $periods->createComment($params);
        $first_id = $params['first_id'] ?? 0;
        $p_id = $params['pid'] ?? 0;
        // 评论
        $type = 1;
        if ($p_id) {
            // 回复
            $type = 2;
        }
        if ($result) {
            // 发布评论审核
            $url = env('ITEM.AUDIT_URL') . '/contentaudit/v3/comment/add';
            $audit_data = [
                'is_backstage' => 0,
                'uid' => $params['uid'],
                'nickname' => $params['uname'],
                'source' => 1,
                'type' => $type,
                'first_id' => $first_id,
                'data_id' => $params['period'],
                'comment_id' => $result->id,
                'comment_content' => $params['content'],
                'reply_uid' => $params['p_uid'] ?? 0,
                'reply_id' => $params['pid'] ?? 0,
                'reply_content' => $params['reply_content'] ?? '',
                'data_content' => $params['title'] ?? '',
                'expression' => $params['emoji_image'] ?? ''
            ];
            $audit_result = post_url($url, $audit_data);
            $audit_result = json_decode($audit_result, true);
            if ($audit_result['error_code'] != 0) {
                // 删除此评论
                $comm_server = new CommentSer();
                $data['audit_status'] = 4;
                $data['update_time'] = time();
                $data['is_recommend'] = 0;
                $data['id'] = $result->id;
                $comm_server->periodCommentAudit($data);
                return throwResponse($audit_result['data'], ErrorCode::PARAM_ERROR, $audit_result['error_msg']);
            }
        }
        return throwResponse($result);
    }

    /**
     * 用户商品评论列表
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function commentList(Request $request): \think\Response
    {
        $params = $request->get();
        $periods = new PeriodsSer(0);
        $params['uid'] = $request->header('vinehoo-uid', '');
        $result = $periods->commentList($params);
        return throwResponse($result);
    }

    public function getCommentUserStatus(Request $request): \think\Response
    {
        $params = $request->post();
        $periods = new PeriodsSer(0);
        $params['uid'] = $request->header('vinehoo-uid', '');
        $result = $periods->getCommentUserStatus($params);
        return throwResponse($result);
    }

    /**
     * 添加商品产品库存
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function createPeriodsProductInventory(Request $request): \think\Response
    {
        $params = $request->post();
        $product_id = '';
        $periods_type = 0;
        $period = 0;

//        $virtual_warehouse = \think\facade\Db::name('virtual_warehouse')->column('company_ids', 'erp_id');
        // 仅用于同步评论验证
        $is_use_comment = 0;
        $product_ids = $product_id_arr = $short_code_arr = $short_code_use = $short_code_all = $erp_id_arr = [];
        foreach ($params['list'] as $v) {
            $period = $v['period'];
            $periods_type = intval($v['periods_type']);
//            if(!empty($v['erp_id'])){
//                $erp_id_arr[] = $v['erp_id'];
//                $pppm_id = (new PeriodsSer($periods_type))->getOne((int)$period, 'payee_merchant_id');
//                $company_ids = explode(',', $virtual_warehouse[$v['erp_id']] ?? '');
//                if (!in_array(($pppm_id['payee_merchant_id'] ?? ''), $company_ids)) {
//                    return throwResponse([], ErrorCode::PARAM_ERROR, '收款公司 与 发货仓库不匹配!');
//                }
//            }
            if (!empty($v['is_use_comment']) && $v['is_use_comment'] == 1) {
                $is_use_comment = 1;
                $product_id_arr[] = $v['product_id'];
                $short_code_arr[] = $v['short_code'];
            } else {
                $short_code_use[] = $v['short_code'];
                $product_ids[] = $v['product_id'];
            }
            $short_code_all[] = $v['short_code'];
        }
        $periods = new PeriodsSer($periods_type);
        // 期数信息
        $periods_info = $periods->getOne((int)$period, 'id,onsale_status,import_type');

        foreach ($params['list'] as $val) {
            if ($periods_info['import_type'] == 1 && empty($val['bar_code'])) {
                return throwResponse([], ErrorCode::PARAM_ERROR, '地采没有条码不能售卖！');
            }
        }

//        if(in_array('034',$erp_id_arr)){
//            $periods_payee_merchant_id = $periods->getOne((int)$params['list'][0]['period'], 'payee_merchant_id');
//            if(($periods_payee_merchant_id['payee_merchant_id'] ?? '') == 5){
//                return throwResponse([], ErrorCode::PARAM_ERROR, '收款单位是【微醺】时，仓库不能绑定到【034】');
//            }
//        }


        if ($is_use_comment == 1) {
            $Package = new PackageSer($periods_type);
            $where_product_id = '';
            foreach ($product_id_arr as $v) {
                $where_product_id .= "or `associated_products` LIKE '%\"product_id\":{$v},%' ";
            }
            $where = "period_id = {$period} and is_hidden = 0 and (".trim($where_product_id,'or').")";
            // 查询是否存在可售套餐
            $exists = $Package->ConditionalQueryPackageExists($where);
            if (!empty($exists)) {
                return throwResponse([], ErrorCode::PARAM_ERROR, '本期数存在可售套餐，不可勾选仅用于同步评论');
            }
            // 获取期数所有简码已售数量
            $res = GetSaleBottleNums([["period" => $period,"period_type" => $periods_type]]);
            foreach ($short_code_arr as $v) {
                if (!empty($res[$period][$v])) {
                    return throwResponse([], ErrorCode::PARAM_ERROR, '本期数存在已支付订单，不可勾选仅用于同步评论');
                }
            }
        }


        foreach ($params['list'] as $val) {
            $validate = new PeriodsProductInventory();
            $validate_params = $validate->check($val);
            if (!$validate_params) {
                return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
            }
            $val['periods_type'] = $val['periods_type'] ?? 0;
            $periods = new PeriodsSer((int)$val['periods_type']);
            $val['created_time'] = time();
            $val['inventory_accum'] = $val['inventory'];
            if (isset($val['id']) && (int)$val['id'] < 1) {
                unset($val['id']);
            }
            
            if ($periods_info['onsale_status'] >= 2 && isset($val['id'])) {
                unset($val['inventory'], $val['inventory_accum']);
            }
            if (empty($val['custom_product_name'])) {
                $val['custom_product_name'] = $val['product_name'];
            }
            $periods->createPeriodsProductInventory($val);
            $product_id .= $val['product_id'] . ',';
            $periods_type = (int)$val['periods_type'];
            $period = $val['period'];
        }
        $short_code = implode(',', $short_code_use);

        // 查询产品信息
        $product_url = env('ITEM.WINE_WIKI_URL').'/wiki/v3/product/query';
        $res = curlRequest($product_url, [
            'short_code' => $short_code,
            'field'      => 'short_code,product_category,country_id,capacity,product_type,product_keywords_id',
        ], [], 'GET');
        $product_data = $res['data'] ?? [];

        // 更新商品绑定产品
        $period_update['product_id'] = $product_id;
        $product_category = $country = $capacity = $productcategoryname = $keywords = [];
        foreach ($product_data as $v) {
            !empty($v['product_type_name']) && $product_category[] = $v['product_type_name'];
            !empty($v['country_name']) && $country[] = $v['country_name'];
            !empty($v['capacity']) && $capacity[] = $v['capacity'];
            !empty($v['productcategoryname']) && $productcategoryname[] = $v['productcategoryname'];
            foreach ($v['product_keywords_id'] as $vv) {
                $keywords[] = $vv['name'];
            }
        }
        $period_update['product_category'] = implode(',', $product_category);
        $period_update['country'] = implode(',', $country);
        $period_update['capacity'] = implode(',', $capacity);
        $period_update['product_main_category'] = implode(',', $productcategoryname);
        $period_update['short_code'] = implode(',', $short_code_all);
        $period_update['product_keyword'] = implode(',', $keywords);
        // 白酒默认过滤
        if (strpos($period_update['product_category'], '白酒') !== false) {
            $period_update['is_user_filter'] = 1;
        }
        $periods->updateInfoById((int)$period, $period_update);

        $sync_data       = $params;
        $sync_data['id'] = intval($period);
        (new PeriodsPoolService())->syncPeriods($sync_data);

        return throwResponse(count($params));
    }

    /**
     * 删除商品产品库存
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function deletePeriodsProductInventory(Request $request): \think\Response
    {
        $params = $request->post();
        #数据验证
        $validate = DataValidate::rule([
            'period|期数'    => 'require|number|>:0',
            'periods_type|商品频道'    => 'require|number',
            'short_code|简码' => 'require|max:100',
        ]);
        if (!$validate->check($params)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $periods = new PeriodsSer(intval($params['periods_type']));

        $result = $periods->deletePeriodsProductInventory($params);
        if ($result !== true) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $result);
        }

        return throwResponse();
    }

    /**
     * 查询马甲、背心、小编列表
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function vestRecordList(Request $request): \think\Response
    {
        $params = $request->get();
        $period = $params['period'] ?? 0;
        $periods = new PeriodsSer(0);
        $result = $periods->vestRecordList(['period' => $period]);
        return throwResponse($result);
    }

    /**
     * 查询期数驳回备注
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getReviewLog(Request $request): \think\Response
    {
        $params = $request->get();
        $period = $params['period'] ?? 0;
        $periods = new PeriodsSer(0);
        $result = $periods->getReviewLog(['period' => $period]);
        return throwResponse($result);
    }

    /**
     * 酒款加入收藏
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function periodsCollection(Request $request): \think\Response
    {
        $params = $request->post();
        $params['user_id'] = $request->header('vinehoo-uid', '0');
        $params['created_time'] = time();
        $periods = new PeriodsSer(0);
        if ($params['periods_type'] == 5) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, '该商品无法收藏');
        }
        $result = $periods->periodsCollection($params);
        if ($result['status'] == false) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, $result['msg']);
        }
        return throwResponse($result['data']);
    }

    /**
     * 用户收藏列表
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function collectionList(Request $request): \think\Response
    {
        $user_id = (int)$request->header('vinehoo-uid', '0');
        $limit = (int)$request->get('limit', 10);
        if (!$user_id) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '未查询到用户');
        }
        $periods = new PeriodsSer(0);
        $result = $periods->collectionList($user_id, $limit);
        $list = $result->getCollection()->toArray() ?? [];
        foreach ($list as $k => $v) {
            if (empty($v['period_info'])) {
                unset($list[$k]);
            }
        }
        $list = array_values($list);
        $re['total'] = $result->total() ?? 0;
        $re['list'] = $list;
        return throwResponse($re);
    }

    /**
     * 批量删除用户收藏
     * @param Request $request
     * @return \think\Response
     */
    public function delCollection(Request $request): \think\Response
    {
        $user_id = (int)$request->header('vinehoo-uid', '0');
        $id = $request->post('id', '0');
        if (!$user_id) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '未查询到用户');
        }
        $periods = new PeriodsSer(0);
        $result = $periods->delCollection($user_id, $id);
        if (!$result) {
            return throwResponse(0);
        }
        return throwResponse(1);
    }

    /**
     * 检测用户是否收藏
     * @param Request $request
     * @return \think\Response
     */
    public function existsCollection(Request $request): \think\Response
    {
        $user_id = (int)$request->header('vinehoo-uid', '0');
        $period = (int)$request->get('period', 0);
        if (!$user_id || !$period) {
            return throwResponse(0);
        }
        $periods = new PeriodsSer(0);
        $result = $periods->existsCollection($user_id, $period);
        if (!$result) {
            return throwResponse(0);
        }
        return throwResponse(1);
    }

    /**
     * 商品库存列表
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function periodsInventoryList(Request $request): \think\Response
    {
        $params = $request->get();
        if (!isset($params['period']) || empty($params['period'])) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, "请选择期数");
        }
        $periods = new PeriodsSer(0);
        $list = $periods->periodsInventoryList((int)$params['period']);
        return throwResponse($list);
    }

    /**
     * 更新期数产品库存
     * @param Request $request
     * @return \think\Response
     */
    public function updateInventory(Request $request): \think\Response
    {
        $params = $request->post();
        $validate = new PeriodsProductUpInventory();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $periods = new PeriodsSer(0);
        $result = $periods->updateInventory((int)$params['id'], (int)$params['inventory']);
        return throwResponse($result);
    }

    /**
     * 生成期数 json 文件
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function createJson(Request $request): \think\Response
    {
        $params = $request->param();
        $periods = new PeriodsSer((int)$params['periods_type']);
        $periods->create_period_json((int)$params['period'], (int)$params['periods_type']);
        // 刷新 CDN
        PeriodsSer::CDNrefreshObject(intval($params['period']));
        return throwResponse([]);
    }

    /**
     * 检测 oss 是否存在于期数 json 文件
     * @param Request $request
     * @return \think\Response
     * @throws \OSS\Core\OssException
     */
    public function objectExist(Request $request): \think\Response
    {
        $period = $request->get('period');
        $periods = new PeriodsSer(0);
        $result = $periods->objectExist((int)$period);
        return throwResponse($result);
    }

    /**
     * 获取期数 json 文件
     * @param Request $request
     * @return \think\Response
     */
    public function getPeriodJson(Request $request): \think\Response
    {
        return throwResponse([], ErrorCode::PARAM_ERROR, '此接口已启用，请直接访问 OSS 期数缓存文件');
        $period = $request->get('period');
        $path = App::getRuntimePath() . '/periods/' . $period;
        if (!file_exists($path) || empty($period)) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '未查询到期数文件');
        }

        // 查询期数
//        $periods = new PeriodsSer((int)$request->get('periods_type'));
//        $onsale_status = $periods->getPeriodsValue((int)$period, 'onsale_status');
//        if ($onsale_status != 2) {
//            return throwResponse([], ErrorCode::EXEC_ERROR, '该期数未上架');
//        }

        $period_json = file_get_contents($path);
        $period_json = json_decode($period_json, true);
        return throwResponse($period_json);
    }

    /**
     * 更新期数采购
     * @param Request $request
     * @return \think\Response
     */
    public function updatePurchaseInfo(Request $request): \think\Response
    {
        $params = $request->post();
        #数据验证
        $validate = DataValidate::rule([
            'period|期数'    => 'require|number|>:0',
            'periods_type|商品频道'    => 'require|number|>=:0',
            'supplier_id|供应商ID' => 'require|number|>:0',
            'supplier|供应商' => 'require|max:255',
            'payee_merchant_id|收款商户ID' => 'require|number|in:1,2,5,10',
            'payee_merchant_name|收款商户名称' => 'require|max:255',
        ]);
        if (!$validate->check($params)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }

        // 秒发、跨境强制科技公司
        if (in_array(intval($params['periods_type']), [1, 2])) {
            $params['payee_merchant_id'] = 2;
            $params['payee_merchant_name'] = '佰酿云酒（重庆）科技有限公司';
        }

//        if (($params['payee_merchant_id'] ?? '') == 5) {
//            if (\think\facade\Db::name('periods_product_inventory')->where('period', $params['period'])->where('erp_id', 'in', ['034', '34'])->count()) {
//                return throwResponse([], ErrorCode::PARAM_ERROR, '收款单位是【微醺】时，仓库不能绑定到【034】');
//            }
//        }

        $validate = new Purchase();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }

        $periods = new PeriodsSer((int)$params['periods_type']);

        $result = $periods->updatePurchaseInfo($params);
        if ($result['status'] == false) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, $result['msg']);
        }

        $sync_data       = $params;
        $sync_data['id'] = intval($params['period']);
        (new PeriodsPoolService())->syncPeriods($sync_data);

        return throwResponse($result['data']);
    }

    /**
     * 更新期数收款商户
     * @param Request $request
     * @return \think\Response
     */
    public function updatePayeeMerchant(Request $request)
    {
        $params = $request->post();
        #数据验证
        $validate = DataValidate::rule([
            'period|期数'    => 'require|number|>:0',
            'periods_type|商品频道'    => 'require|number|in:0,1,2,3',
            'payee_merchant_id|收款商户ID' => 'require|number|in:1,2,5,10',
            'payee_merchant_name|收款商户名称' => 'require|max:255',
        ]);
        if (!$validate->check($params)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        // 秒发、跨境强制科技公司
        if (in_array(intval($params['periods_type']), [1, 2])) {
            $params['payee_merchant_id'] = 2;
            $params['payee_merchant_name'] = '佰酿云酒（重庆）科技有限公司';
        }

        $periods = new PeriodsSer((int)$params['periods_type']);
        $result = $periods->updatePayeeMerchant($params);

        if ($result['status'] == false) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, $result['msg']);
        }

        return throwResponse($result['data']);
    }

    /**
     * 系统到点自动上架，秒级自动任务调用此接口
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function systemPutShelf(Request $request): \think\Response
    {
        $period = (int)$request->post('period');
        $periods_type = $request->post('periods_type');
        $onsale_status = $request->post('onsale_status');
        $task_id = $request->post('task_id');
        $type = $request->post('type');
        $params = json_encode($request->param());
        // 更新执行状态
        $other_ser = new \app\service\Other();
        $other_ser->updateTaskStatus($task_id, $params);
        if (empty($period)) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '未传递期数');
        }

        //预约短信提醒
        if ($type == 11) {
            $uid = PeriodsReservation::where('period', $period)->column('uid');
            $uid = implode(',', $uid);
            // 获取用户手机号
            $user_url = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
            $user_url = $user_url . '?uid=' . $uid . '&field=telephone,telephone_encrypt,plaintext_telephone';
            $user_list = get_url($user_url);
            $user_list = get_interior_http_response($user_list);
            $user_list = $user_list['list'] ?? [];
            if (!empty($user_list)) {
                // 期数详细
                $period_ser = new PeriodsSer($periods_type);
                $period_info = $period_ser->getOne($period, 'title,price,sell_time');
                $user_phone = [];
                foreach ($user_list as $val) {
                    array_push($user_phone, $val['plaintext_telephone']);
                }
                //通知发送小程序订阅推送
                post_url(env("ITEM.USER_CACHE_URL") . '/wechat/miniSubscribeMessageTask', json_encode(['template_id' => 'HIp6uo4wHsFGFXWBqEJCKV4GH-X5C0i67X81LCuB2ow','data' => ['period' => $period,'title' => $period_info['title'],'price'=>$period_info['price'],'sell_time' => $period_info['sell_time']]]),['Content-Type: application/json;charset=utf-8']);
                // 给用户发送推送短信
                if (!empty($user_phone)) {
                    // 推送用户
                    $user_phone = implode(',', $user_phone);
                    //使用短链接
                    $longUrl = 'https://cloud1-9gkbx77e27bcf5b3-1310518333.tcloudbaseapp.com/goods-detail.html?id='.$period;
                    $resp = post_url(env("ITEM.SHORT_URL") . '/shorten', ['url' => $longUrl,'base_type' => 'vinehoo']);
                    $resp = json_decode($resp, true);
                    if ($resp['error_code'] != 0 || empty($resp['data'])) {
                        Log::info('预约发送短信短链接生成返回:'.json_encode($resp));
                        return throwResponse(true);
                    }
                    $shortUrl = $resp['data'];
                    // 推送内容
                    $title = mb_substr($period_info['title'], 0, 10, 'UTF-8');
                    $send_str = "您预约的({$title}...)距离开售仅剩 10 分钟，请前往抢购！".$shortUrl;
                    $post = ['telephone' => $user_phone, 'content' => $send_str,'type' => 1];
                    $send = post_url(env('ITEM.SMS_URL').'/sms/v3/group/sendSms', $post);
                    Log::info('预约发送短信: 请求:'.json_encode($post).'  返回:'.$send);
                }
            }
            return throwResponse(true);
        }

        // 拍卖提前推送通知任务
        if ($type == 6 || $type == 8) {
            $uid = PeriodsAuctionFollow::where('period', $period)->column('user_id');
            $uid = implode(',', $uid);
            // 获取用户手机号
            $user_url = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
            $user_url = $user_url . '?uid=' . $uid . '&field=telephone,telephone_encrypt,plaintext_telephone';
            $user_list = get_url($user_url);
            $user_list = get_interior_http_response($user_list);
            $user_list = $user_list['list'] ?? [];
            if (!empty($user_list)) {
                // 期数详细
                $period_ser = new PeriodsSer(11);
                $period_info = $period_ser->getOne($period, 'title, price');
                $user_phone = [];
                foreach ($user_list as $val) {
                    array_push($user_phone, $val['plaintext_telephone']);
                }
                // 给用户发送推送短信
                if (!empty($user_phone)) {
                    // 推送用户
                    $user_phone = implode(',', $user_phone);
                    // 推送内容
                    $send_str = '';
                    if ($type == 6) {
                        $send_str = "{$period_info['title']}以{$period_info['price']}元开拍，速来！查看详情http://h5.vinehoo.com，回T退订";
                    } elseif ($type == 8) {
                        $send_str = "{$period_info['title']}将于30分钟后结束，http://h5.vinehoo.com看出价数，回T退订";
                    }
                    // Todo.. 发送短信域名服务配置？发送内容模板？
                    $send = post_url(env('ITEM.SMS_URL').'/sms/v3/group/sendSms', ['telephone' => $user_phone, 'content' => $send_str]);
                }
            }
            return throwResponse(true);
        }

        // 秒发、兔头商品不自动下架
        if (in_array($periods_type, [1, 4, 5]) && $onsale_status == 3) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '秒发商品不自动下架');
        }

        $server_period = new PeriodsSer((int)$periods_type);
        $data['onsale_status'] = $onsale_status;
        if ($periods_type == 9) {
            $data['off_sell_time'] = time();
            $data['off_sell_type'] = 2;
        }


//        $result = $server_period->updateInfoById($period, ['onsale_status' => $onsale_status]);
        // 拍卖商品截拍后处理
        if ($periods_type == 11 && $onsale_status == 4 && $type == 5) {
            $auction_ser = new \app\service\Auction();
            $bid_info = $auction_ser->getAuctionNewBid($period);
            if ($bid_info) {
                $time = time();
                $user_auction['period'] = $period;
                $user_auction['user_id'] = $bid_info['uid'];
                $user_auction['created_time'] = $time;
                PeriodsUserAuction::create($user_auction);
                // 建立拍品用户支付自动任务
                $pay_exp_time = $time + 300;
                $server_period->task($period, $pay_exp_time, 7, 4);
                $data['final_auction_price'] = $bid_info['bid_price'];
                $url = env('ITEM.AUCTION_SERVER_URL'). '/auction/v3/notice?id='.$period.'&notice='.'end';
                get_url($url);
            } else {
                // 无人拍下，流拍状态
                $data['onsale_status'] = 5;
            }
        }

        if ($periods_type == 11) {
            if ($onsale_status == 2) {
                $url = env('ITEM.AUCTION_SERVER_URL'). '/auction/v3/notice?id='.$period.'&notice='.'start';
                get_url($url);
            } elseif ($onsale_status == 3) {
                // 拍卖删除拍卖 redis 记录
                $auction_ser = new Auction();
                $set_key = ["auction_{$params['period']}_start_time" => 0,
                            "auction_{$params['period']}_end_time" => 0,
                            "auction_{$params['period']}_bid_price" => 0,
                            "auction_{$params['period']}_min_bid_price" => 0,
                            "auction_{$params['period']}_delay_time" => 0,
                            "auction_{$params['period']}_strike_time" => 0,
                ];
                $auction_ser->delAuctionRedis($set_key);
                $url = env('ITEM.AUCTION_SERVER_URL'). '/auction/v3/notice?id='.$period.'&notice='.'end';
                get_url($url);
            }
        }

        // 拍卖支付自动任务处理
        if ($type == 7) {
            $user_auction = PeriodsUserAuction::where('period', $period)->field('pay_status, user_id')->find();
            if ($user_auction['pay_status'] != 2) {
                $es_ser = new \app\service\ElasticSearch();
                $order = $es_ser->getOrderByPeriod($period);
                if (empty($order)) {
                    $period_info = $server_period->getOne($period, 'title, sell_time, closing_auction_time');
                    if (!empty($period_info)) {
                        $period_info['violation_time'] = date('Y-m-d H:i:s', time());
                        $period_info['auction_time'] = $period_info['sell_time'] .'-'.$period_info['closing_auction_time'];
                        $period_info['remark'] = '竞拍商品拍下未查询到支付，扣除拍卖保证金';
                        $period_info['uid'] = $user_auction['user_id'];
                        // 未查询到拍品支付订单扣除保证金
                        $url = env('ITEM.ORDERS_URL').'/orders/v3/earnest/deduction';
                        post_url($url, $period_info);
                    }
                }
            }
            // 拍卖支付不处理状态
            unset($data['onsale_status']);
        }

        if ($periods_type < 5 && in_array($onsale_status, [1, 2])) {
            $period_info = $server_period->getOne($period, 'id, is_fail');
            if (empty($period_info['is_fail'])) {
                return throwResponse([], ErrorCode::PARAM_ERROR, '商品已失效');
            }
            // 验证产品关单卫检
            $resu = $server_period->vCustomsOrderHealthInspect($period);
            if ($resu !== true) {
                return throwResponse([], ErrorCode::PARAM_ERROR, $resu);
            }
            //验证是否存在库存
            $is_inv = $server_period->getPeriodsInventory($period, $periods_type);
            if (!$is_inv) {
                // 不存在库存直接下架
                $data['onsale_status'] = 3;
            }
        }

        // 更新状态
        $result = $server_period->updateInfoById($period, $data);

        // 生成 json 文件
        if ($result) {
            $server_period->create_period_json($period, (int)$periods_type);
        }

        return throwResponse($result);
    }

    /**
     * 拍品自动任务
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function systemAuctionPutShelf(Request $request): \think\Response
    {
        $period = (int)$request->post('period');
        $periods_type = $request->post('periods_type');
        $onsale_status = $request->post('onsale_status');
        $task_id = $request->post('task_id');
        $params = json_encode($request->param());
        // 更新执行状态
        $other_ser = new \app\service\Other();
        $other_ser->updateTaskStatus($task_id, $params);
        if (empty($period)) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '未传递期数');
        }

        // 秒发商品不自动下架
        if ($periods_type == 1 && $onsale_status == 3) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '秒发商品不自动下架');
        }

        $server_period = new PeriodsSer((int)$periods_type);

        $data['onsale_status'] = $onsale_status;
        if ($periods_type == 9) {
            $data['off_sell_time'] = time();
            $data['off_sell_type'] = 2;
        }
//        $result = $server_period->updateInfoById($period, ['onsale_status' => $onsale_status]);
        $result = $server_period->updateInfoById($period, $data);

        if ($result) {
            // 生成 json 文件
            $server_period->create_period_json($period, (int)$periods_type);
        }
        return throwResponse($result);
    }

    /**
     * 文案提交
     * @param Request $request
     * @return \think\Response
     */
    public function copywriterSubmit(Request $request)
    {
        $params = $request->post();
        if (!isset($params['period']) || !isset($params['periods_type'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择提交商品');
        }
        $params['periods_type'] = (int)$params['periods_type'] ?? 0;

        // 添加套餐编辑人
        $params['user_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['user_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['user_name']) {
            $params['user_name'] = base64_decode($params['user_name']);
        }

        $params['buyer_review_status'] = 2;
        $periods = new PeriodsSer($params['periods_type']);

        $result = $periods->review($params);

        return throwResponse($result);

    }

    /**
     * 期数点赞
     *
     * @param Request $request
     * @return \think\Response
     */
    public function praise(Request $request): \think\Response
    {
        $params = $request->post();
        if (!isset($params['period']) || !isset($params['periods_type'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择点赞商品');
        }
        $params['periods_type'] = (int)$params['periods_type'] ?? 0;

        // 添加套餐编辑人
        $params['user_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['user_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['user_name']) {
            $params['user_name'] = base64_decode($params['user_name']);
        }
        // 是否登录
        if (!$params['user_id'] || !$params['user_name']) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请登录');
        }
        $periods = new PeriodsSer($params['periods_type']);
        // 是否点赞
        $other_ser = new \app\service\Other();
        $is_praise = $other_ser->isPraise((int)$params['user_id'], (int)$params['period']);
        if ($is_praise == 1) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '您已点赞');
        }
        // 点赞当前账户和采购人不一致
//        $period_info = $periods->getOne((int)$params['period'], 'buyer_id');
//        if ($period_info['buyer_id'] != $params['user_id']) {
//            return throwResponse([], ErrorCode::EXEC_ERROR, '当前账户和采购人不一致');
//        }
        // 点赞操作
        $result = $periods->praise($params);
        if (!$result) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '点赞失败');
        }
        return throwResponse(true);
    }

    /**
     * 猜你喜欢，热门推荐列表
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function recommendList(Request $request): \think\Response
    {
        // 获取闪购
        $flash_ser = new PeriodsSer(0);
        $field = 'id, banner_img, title, brief, price, purchased, vest_purchased,limit_number,is_hidden_price';
        $flash = $flash_ser->recommendList(100, $field);
        if (!empty($flash)) {
            foreach ($flash as &$v) {
                $v['periods_type'] = 0;
            }
        }
        unset($v);
        // 获取秒发
        $second_ser = new PeriodsSer(1);
        $second = $second_ser->recommendList(100, $field);
        if (!empty($second)) {
            $second[0]['periods_type'] = 1;
        }
        shuffle($flash);
        array_splice($flash, 3);
        shuffle($second);
        array_splice($second, 1);
        $data = array_merge($flash, $second);

        // 请求端
        $client = $request->header('vinehoo-client');
        $img_url = '&x-oss-process=image/resize,w_288,h_180/auto-orient,1/quality,q_90/format,';

        foreach ($data as &$val) {
            foreach ($val['banner_img'] as &$v) {
                if ($client != 'vinehoo-pc') {
                    $v_arr = explode('.', $v);
                    $suffix = end($v_arr);
                    if (strpos($suffix, '?')) {
                        $suffix = current(explode('?', $suffix));
                    }
                    $v .= $img_url . $suffix;
                }
            }
        }
        return throwResponse($data);
    }

    /**
     * 文案主管审核
     * @param Request $request
     * @return \think\Response
     */
    public function copyWriterReview(Request $request): \think\Response
    {
        $params = $request->post();
        if (!isset($params['period']) || !isset($params['periods_type'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择审核商品');
        }
        $params['periods_type'] = (int)$params['periods_type'] ?? 0;

        // 添加套餐编辑人
        $params['user_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['user_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['user_name']) {
            $params['user_name'] = base64_decode($params['user_name']);
        }

        $periods = new PeriodsSer($params['periods_type']);

        $result = $periods->copyWriterReview($params);

        return throwResponse($result);

    }

    /**
     * 文案提交主管审核
     * @param Request $request
     * @return \think\Response
     */
    public function submitCopyWriter(Request $request): \think\Response
    {
        $params = $request->post();
        if (!isset($params['period']) || !isset($params['periods_type'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择审核商品');
        }
        $params['periods_type'] = (int)$params['periods_type'] ?? 0;

        // 添加套餐编辑人
        $params['user_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['user_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['user_name']) {
            $params['user_name'] = base64_decode($params['user_name']);
        }

        $periods = new PeriodsSer($params['periods_type']);

        $product = $periods->getOne($params['period'], 'product_id');
        if (!isset($product['product_id']) || empty($product['product_id'])) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '请绑定产品');
        }

        $result = $periods->submitCopyWriter($params);

        return throwResponse($result);

    }

    /**
     * 根据 id 获取期数详细
     * @param Request $request
     * @return \think\Response
     */
    public function getESPeriodInfoById(Request $request): \think\Response
    {
        $result = esGetOne((int)$request->get('period'), 'vinehoo.periods');
        if (!empty($result)) {
            $value = explode(",", $result['banner_img']);
            foreach ($value as &$v) {
                $v = env('ALIURL') . $v;
            }
            $result['banner_img'] = $v;
            unset($value, $v);
            if ($result['product_img'] != '') {
                $value = explode(",", $result['product_img']);
                foreach ($value as &$v) {
                    $v = env('ALIURL') . $v;
                }
                $result['product_img'] = $value[0];
                unset($value, $v);
            }
            if (isset($result['horizontal_img']) && $result['horizontal_img'] != '') {
                $value = explode(",", $result['horizontal_img']);
                foreach ($value as &$v) {
                    $v = env('ALIURL') . $v;
                }
                $result['horizontal_img'] = $v;
                unset($value, $v);
            }
            $Package = new PackageSer($result['periods_type']);
            $result['package'] = $Package->packageList(['period' => $result['id']]);

        }
        return throwResponse($result);
    }


    /**
     * 添加拼团数据
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function addGroup(Request $request): \think\Response
    {
        $params = $request->post();
        // 添加套餐编辑人
        $params['creator_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['creator_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['creator_name']) {
            $params['creator_name'] = base64_decode($params['creator_name']);
        }
        $params['created_time'] = time();
        if ($params['group_people'] < 2 || $params['group_people'] > 6) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '成团人数必须大于等于2且小于等于6');
        }
        $periods_type = $request->post('periods_type', 0);
        $periods = new PeriodsSer((int)$periods_type);
        // 查询期数
        $period_info = $periods->getOne((int)$params['period'], 'id,marketing_attribute');
        if (empty($period_info)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '未查询到商品');
        }
        // 验证参数
        $group_price_arr = json_decode($params['group_price'], true);
        foreach ($group_price_arr as &$value) {
            if ((float)$value['price'] <= 0) {
                return throwResponse([], ErrorCode::PARAM_ERROR, '拼团金额不能小于0');
            }
            $value['price'] = (float)$value['price'];
        }
        $params['group_price'] = json_encode($group_price_arr);
        // 查看是否已是拼团商品
        $group_find = $periods->getGroupInfo([], ['period' => $params['period']]);
        if (!isset($params['share_status'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '分享状态字段必传');
        }
        if (!empty($group_find)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '此商品已存在拼团');
        }
        // 添加拼团
        $result = $periods->addGroup($params);
        if ($result['status'] == false) {
            return throwResponse($result['data'], ErrorCode::PARAM_ERROR, $result['msg']);
        }
        $exists = strpos($period_info['marketing_attribute'], '1');
        // 商品不存在拼团属性，更新商品属性
        if ($exists === false) {
            $marketing_attribute = $period_info['marketing_attribute'] . ',1';
            $periods->updateField((int)$params['period'], 'marketing_attribute', $marketing_attribute);
        }
        return throwResponse($result['data']);
    }

    public function getShareInfo(Request $request): \think\Response
    {
        $param = $request->param();
        if (isset($param['period'])) {
            $info = \think\facade\Db::name('periods_group')->where('period', $param['period'])->order('id', 'desc')
                ->field('id,period,periods_type,group_people,share_image,sub_title,main_title,share_status,share_url')->find();
            if (!empty($info['share_image'])) $info['share_image'] = image_full_path($info['share_image']);
        } else {
            $info = [
                'id'           => null,
                'period'       => null,
                'periods_type' => null,
                'group_people' => null,
                'share_image'  => 'https://images.vinehoo.com/vinehoo/vos/marketing/2046/17313155496542yjF4kA3J_Xhhf8mEFN.jpg?w=490&h=400',
                'sub_title'    => '拼团中，还差1人即可拼成！',
                'main_title'   => '拼团中，还差1人即可拼成！',
                'share_status' => null,
                'share_url'    => null,
            ];
        }
        return throwResponse($info);
    }

    /**
     * 添加新人数据
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function addNewcomer(Request $request): \think\Response
    {
        $params = $request->post();
        // 添加套餐编辑人
        $params['creator_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['creator_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['creator_name']) {
            $params['creator_name'] = base64_decode($params['creator_name']);
        }
        $params['created_time'] = time();
        $periods_type = $request->post('periods_type', 0);
        $periods = new PeriodsSer((int)$periods_type);
        // 查询期数
        $period_info = $periods->getOne((int)$params['period'], 'id,marketing_attribute');
        if (empty($period_info)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '未查询到商品');
        }
        // 查看是否已是新人商品
        $newcomer_find = $periods->getNewcomerInfo([], ['period' => $params['period']]);
        if (!empty($newcomer_find)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '此商品已存在新人商品');
        }
        $result = $periods->addNewcomer($params);
        if ($result['status'] == false) {
            return throwResponse($result['data'], ErrorCode::PARAM_ERROR, $result['msg']);
        }
        $exists = strpos($period_info['marketing_attribute'], '2');
        // 商品不存在新人属性，更新商品属性
        if ($exists === false) {
            $marketing_attribute = $period_info['marketing_attribute'] . ',2';
            $periods->updateField((int)$params['period'], 'marketing_attribute', $marketing_attribute);
        }
        return throwResponse($result['data']);
    }

    /**
     * 更新拼团
     * @param Request $request
     * @return \think\Response
     */
    public function upGroup(Request $request): \think\Response
    {
        return throwResponse(0, ErrorCode::PARAM_ERROR, '绑定商品后禁止编辑拼团信息，请新建拼团活动');
        $params = $request->post();
        // 添加套餐编辑人
        $params['updated_time'] = time();
        $periods = new PeriodsSer(0);
        // 验证参数
        $group_price_arr = json_decode($params['group_price'], true);
        foreach ($group_price_arr as &$value) {
            if ((float)$value['price'] <= 0) {
                return throwResponse([], ErrorCode::PARAM_ERROR, '拼团金额不能小于0');
            }
            $value['price'] = (float)$value['price'];
        }
        $params['group_price'] = json_encode($group_price_arr);
        if (isset($params['group_people'])) {
            if ($params['group_people'] < 2 || $params['group_people'] > 6) {
                return throwResponse(null, ErrorCode::PARAM_ERROR, '成团人数必须大于等于2且小于等于6');
            }
        }
        $result = $periods->upGroup($params);
        return throwResponse($result);
    }

    /**
     * 更新新人
     * @param Request $request
     * @return \think\Response
     */
    public function upNewcomer(Request $request): \think\Response
    {
        $params = $request->post();
        // 添加套餐编辑人
        $params['updated_time'] = time();
        $group_price_arr = json_decode($params['newcomer_price'], true);
        foreach ($group_price_arr as &$value) {
            if ((float)$value['price'] <= 0) {
                return throwResponse([], ErrorCode::PARAM_ERROR, '拼团金额不能小于0');
            }
            $value['price'] = (float)$value['price'];
        }
        $params['newcomer_price'] = json_encode($group_price_arr);
        $periods_type = $request->post('periods_type', 0);
        $periods = new PeriodsSer((int)$periods_type);
        $result = $periods->upNewcomer($params);
        return throwResponse($result);
    }

    /**
     * 拼团列表
     * @param Request $request
     * @return \think\Response
     */
    public function getGroupList(Request $request): \think\Response
    {
        $params = $request->get();
        $periods_type = $request->get('periods_type', 0);
        $periods = new PeriodsSer((int)$periods_type);
//        $periods = new PeriodsSer(0);
        $list = $periods->getGroupList($params);
        // 分页
        $result['total'] = $list->total() ?? 0;
        $result['list'] = $list->getCollection() ?? [];
        foreach ($result['list'] as &$item) {
            $item['share_image'] = image_full_path($item['share_image']);
        }
        return throwResponse($result);
    }

    /**
     * 新人列表
     * @param Request $request
     * @return \think\Response
     */
    public function getNewcomerList(Request $request): \think\Response
    {
        $params = $request->get();
        $periods_type = $request->get('periods_type', 0);
        $periods = new PeriodsSer((int)$periods_type);
        $list = $periods->getNewcomerList($params);
        // 分页
        $result['total'] = $list->total() ?? 0;
        $result['list'] = $list->getCollection() ?? [];
        return throwResponse($result);
    }

    /**
     * 删除新人营销商品
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function delNewComer(Request $request): \think\Response
    {
        $params = $request->post();
        $id = (int)$params['id'];
        $periods_type = $request->post('periods_type', 0);
        $periods = new PeriodsSer((int)$periods_type);
        $result = $periods->delNewComer($id);
        return throwResponse($result);
    }

    /**
     * 删除拼团营销商品
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function delGroup(Request $request): \think\Response
    {
        $params = $request->post();
        $periods_type = $request->post('periods_type', 0);
        $id = (int)$params['id'];
        $periods = new PeriodsSer((int)$periods_type);
        $result = $periods->delGroup($id);
        return throwResponse($result);
    }

    /**
     * 获取拼团详细
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getGroupInfo(Request $request): \think\Response
    {
        $params = $request->get();
        $periods_type = $request->get('periods_type', 0);
        $periods = new PeriodsSer((int)$periods_type);
//        $periods = new PeriodsSer(0);
        $where = [];
        // 期数查询
        if (isset($params['period']) && !empty($params['period'])) {
            $where[] = ['period', '=', $params['period']];
        }
        // 拼团id查询
        if (isset($params['id']) && !empty($params['id'])) {
            $where[] = ['id', '=', $params['id']];
        }
        // 查询是否已存在拼团
        $group = $periods->getGroupInfo($params, $where);
        return throwResponse($group);
    }

    /**
     * 获取新人详细
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getNewcomerInfo(Request $request): \think\Response
    {
        $params = $request->get();
        $periods_type = $request->post('periods_type', 0);
        $periods = new PeriodsSer((int)$periods_type);
        $where = [];
        // 期数查询
        if (isset($params['period']) && !empty($params['period'])) {
            $where[] = ['period', '=', $params['period']];
        }
        // 新人id查询
        if (isset($params['id']) && !empty($params['id'])) {
            $where[] = ['id', '=', $params['id']];
        }
        $newcomer = $periods->getNewcomerInfo($params, $where);
        return throwResponse($newcomer);
    }

    /**
     * 期数上架获取期数详细钉钉消息消息推送
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function onSaleDingdingPush(Request $request)
    {
        $period = $request->get('period');
        $periods_ser = new PeriodsSer(0);
        $result = $periods_ser->onSaleDingdingPush((int)$period);
        return throwResponse($result);
    }

    /**
     * 期数自增浏览量
     * @param Request $request
     * @return \think\Response
     */
    public function incPageviews(Request $request): \think\Response
    {
        $params = $request->post();

        if (!isset($params['period']) || !isset($params['periods_type'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择审核商品');
        }

        $periods_ser = new PeriodsSer((int)$params['periods_type']);
        $result = $periods_ser->incPageviews($params);
        return throwResponse($result);
    }

    /**
     * 获取首页缓存
     * @param Request $request
     * @return \think\Response
     */
    public function indexListSort(Request $request): \think\Response
    {
        $page = $request->get('page', 1);
        if ($page <= 0) {
            $page = 0;
        }
        $limit = $request->get('limit', 10);
        if ($limit <= 0) {
            $limit = 10;
        }
        $offset = ($page - 1) * $limit;
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        //老排序方式，还保留了redis数据，需要还原时直接替换即可
        /*
        $redis->select(3);
        $key = 'index_periods_sort';
        $data = $redis->zRange($key, (int)$offset, (int)$offset + $limit - 1);
        //end*/

        //新排序方式
        $redis->select(15);
        $key = 'vm:product:list';
        $data = $redis->zRevRange($key, (int)$offset, (int)$offset + $limit - 1);
        //end

        $count = $redis->zCard($key);
//        $data = $redis->zRange($key, 0, -1);
        // 用户id
        $uid = (int)$request->header('vinehoo-uid');
        // 获取用户信息
        $user_info = [];
        if ($uid) {
            // 查询用户
            $user_url = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
            $user_url = $user_url . '?uid=' . $uid . '&field=exps,goods_restrict';
            $user_list = get_url($user_url);
            $user_list = get_interior_http_response($user_list);
            $user_list = $user_list['list'] ?? [];
            $user_info_d['exps'] = 0;
            $user_info_d['goods_restrict'] = 1;
            $user_info = $user_list[0] ?? $user_info_d;

        }

        $img_url = '&x-oss-process=image/resize,w_631,h_390/auto-orient,1/quality,q_90/format,';
        $img_url1 = '?x-oss-process=image/resize,w_631,h_390/auto-orient,1/quality,q_90/format,';
        $client = $request->header('vinehoo-client');
        
        // 查询满减活动标签
        $body = [];
        foreach ($data as &$dv) {
            $dv = json_decode($dv, true);
            if ($dv['is_support_reduction'] == 1) {
                $body[] = [
                    'period' => $dv['id'],
                    'period_type' => $dv['periods_type'],
                ];
            }
        }
        if (!empty($body)) {
            $url = env('ITEM.COMMODITIES_SERVICES').'/commodities_server/v3/marketing/GetFullReductionActivityLabel';
            $res = curlRequest($url, json_encode(['data' => $body]));
            $reduction_label = $res['data']['label'] ?? [];
        }
        

        // 商品处理
        foreach ($data as $key => &$val) {
            // 查询满减活动标签
            $val['activity_label'] = $reduction_label[$val['id']] ?? [];
            
            if ($client == 'vinehoo-pc' && !empty($val['is_deposit']) && $val['is_deposit'] == 1) {
                unset($data[$key]);
                continue;
            }
            
            // 用户过滤商品
            if ($val['is_user_filter'] == 1) {
                $user_info['exps'] = $user_info['exps'] ?? 0;
                $user_info['goods_restrict'] = $user_info['goods_restrict'] ?? 0;
                if ($user_info['exps'] < 200 || $user_info['goods_restrict'] == 1) {
                    //fix 商品管理 - 零级用户对白酒的不可见设定
                    unset($data[$key]);
                    continue;
                }
            }
            if (isset($val['quota_rule']) && $val['quota_rule'] != '') {
                $val['quota_rule'] = json_decode($val['quota_rule'], true);
            }
            // 图片新增域名
            if (isset($val['banner_img']) && $val['banner_img'] != '') {
                $val['banner_img'] = explode(",", $val['banner_img']);
                foreach ($val['banner_img'] as &$v) {
                    $v = env('ALIURL') . $v;
                }
                $val['banner_img'] = $val['banner_img'][0];
                if ($client != 'vinehoo-pc') {
                    $v_arr = explode('.', $val['banner_img']);
                    $suffix = end($v_arr);
                    if (strpos($suffix, '?')) {
                        $suffix = current(explode('?', $suffix));
                    }
                    if (strpos($val['banner_img'], '?')) {
                        $val['banner_img'] .= $img_url . $suffix;
                    } else {
                        $val['banner_img'] .= $img_url1 . $suffix;
                    }
                }
            }
            // 增加边框图路径
            if (isset($val['special_activity_data']) && !empty($val['special_activity_data'])) {
                if (!empty($val['special_activity_data']['list_back'])) {
                    $val['special_activity_data']['list_back'] = env('ALIURL') . $val['special_activity_data']['list_back'];
                }

                if (!empty($val['special_activity_data']['title_map'])) {
                    $val['special_activity_data']['title_map'] = env('ALIURL') . $val['special_activity_data']['title_map'];
                }
                
                if (!empty($val['special_activity_data']['new_title_map'])) {
                    $val['special_activity_data']['title_map'] = env('ALIURL') . $val['special_activity_data']['new_title_map'];
                }
            }

        }
        unset($key, $val);
        // 增加商品曝光率
        $periods_arr = [];
        foreach ($data as $val) {
            $periods_arr[$val['id']] = 1;
        }
        $redis->select(6);
        $date = date('Ymd', time());
        $redis->multi(); // 开启事务
        foreach ($periods_arr as $member => $score) {
            $redis->zIncrBy('vinehoo.periods.exposure.' . $date, $score, $member);
        }
        $redis->exec(); // 提交事务
        $result['list'] = array_values($data);
        $result['total'] = $count;

        return throwResponse($result);
    }

    public function v2SyncV3Periods(Request $request)
    {
        $params = $request->post();
        if ($params['table'] == 'wy_sales_goods') {
            $periods_list = [];
            if ($params['type'] == 'INSERT') {
                foreach ($params['data'] as $val) {
                    // 查询期数详细
                    $desc = Db::connect('v2mysql')->table('wy_sales_goods_description')
                        ->where('goods_id', $val['id'])->find();
                    // 查询期数题图详细
                    $image = Db::connect('v2mysql')->table('wy_sales_images')
                        ->where('goods_id', $val['id'])->select();
                    // 题图
                    $banner_img = '';
                    // 产品图
                    $product_img = '';
                    // 秒发竖图
                    $horizontal_img = '';
                    $periods_type = '';
                    if (!empty($image)) {
                        foreach ($image as $v) {
                            if ($v['type'] == 1) {
                                $banner_img = $v['big_img'];
                            }
                            if ($v['type'] == 2) {
                                $product_img .= $v['big_img'] . ',';
                            }
                            if ($v['type'] == 3) {
                                $horizontal_img = $v['big_img'];
                            }
                        }
                    }
                    // 所属频道
                    if ($params['sell_type'] == 5) {
                        // 闪购
                        $periods_type = 0;
                    } elseif ($params['sell_type'] == 20) {
                        // 秒发
                        $periods_type = 1;
                    } elseif ($params['sell_type'] == 9) {
                        // 跨境
                        $periods_type = 2;
                    } elseif ($params['sell_type'] == 22) {
                        // 尾货
                        $periods_type = 3;
                    } elseif ($params['sell_type'] == 5) {
                        // 兔头
                        $periods_type = 4;
                    }
                    // 审核状态
                    $p['id'] = $val['id'];
                    $p['title'] = $val['goods_name'];
                    $p['brief'] = $val['summary'];
                    $p['detail'] = $desc['description'] ?? '';
                    $p['banner_img'] = $banner_img;
                    $p['product_img'] = trim($product_img);
                    if ($periods_type == 1) {
                        $p['horizontal_img'] = $horizontal_img;
                    }
                    $p['creator_name'] = '文案添加人';
                    $p['creator_id'] = $desc['cpwriter_id'];
                    $p['sort'] = $val['fixed'];
                    $p['price'] = $val['price'];
                    $p['market_price'] = $val['market_price'];
                    $p['is_hidden_price'] = $val['is_show_price'];
                    $p['is_channel'] = $val['channel'];
                    $p['is_delete'] = $val['is_delete'];
                    $p['is_presell'] = $desc['is_pre_order'];
                    $p['is_supplier_delivery'] = $desc['on_behalf'];
                    $p['is_parcel_insurance'] = 0;
                    $p['is_support_reduction'] = 0;
                    $p['sellout_sold_out'] = 0;
                    $p['is_sold_out_lock'] = 0;
                    $p['is_hot'] = $val['is_hot'];
                    $p['is_cold_chain'] = $desc['option_express'] == 3 ? 1 : 0;
                    $p['predict_shipment_time'] = $desc['predict_time'];
                    $p['onsale_time'] = $val['stime'];
                    $p['sell_time'] = $val['stime'];
                    $p['sold_out_time'] = $val['etime'];
                    $p['onsale_status'] = 0;
                    $p['copywriting_review_status'] = 0;
                    $p['buyer_review_status'] = 0;
                    $p['onsale_review_status'] = 0;
                    $p['onsale_review_time'] = 0;
                    $p['onsale_verify_status'] = 0;
                    $p['import_type'] = $desc['import_type'];
                    $p['operation_id'] = $desc['audit_id'];
                    $p['operation_name'] = $desc['运营编辑'];
                    $p['operation_review_id'] = $desc['audit_id'];
                    $p['operation_review_name'] = '运营审核';
                    $p['purchased'] = 0;
                    $p['vest_purchased'] = 0;
                    $p['limit_number'] = 0;
                    $p['invariant_number'] = 0;
                    $p['critical_value'] = 0;
                    $p['incremental'] = 0;
                    $p['supplier'] = $val['supplier_name'];
                    $p['supplier_id'] = 0;
                    $p['buyer_id'] = $val['buyer_id'];
                    $p['buyer_id'] = $val['buyer_id'];

                    array_push($comment_list, $p);
                    unset($c);
                }
            }
        }
    }

    /**
     * v2 更新数据同步 v3
     * @param Request $request
     */
    public function v2SyncUpdateV3Periods(Request $request)
    {
        $params = $request->post();
        if ($params['type'] == 'UPDATE') {
            if ($params['table'] == 'wy_sales_goods') {
                foreach ($params['data'] as $val) {
                    $p['title'] = $val['goods_name'];
                    $p['brief'] = $val['summary'];
                    $p['price'] = $val['price'];
                    $p['market_price'] = $val['market_price'];
                    $p['purchased'] = $val['now_buy_nums'];
                    $p['vest_purchased'] = $val['virtual_nums'];
                    $p['onsale_time'] = $val['stime'];
                    $p['sell_time'] = $val['etime'];
                    $p['is_hot'] = $val['is_hot'];
                    $p['is_hot'] = $val['sup_full_reduction'];
                    $p['is_hidden_price'] = $val['is_show_price'];
                    $p['sort'] = $val['fixed'];
                    $p['is_delete'] = $val['is_delete'];
                    $p['is_channel'] = $val['channel'];
                    $p['is_user_filter'] = $val['specifyuser_hide'];
                    $periods_type = '';
                    // 更新期数
                    if ($val['sell_type'] == 5) {
                        // 闪购
                        $periods_type = 0;
                    } elseif ($val['sell_type'] == 20) {
                        // 秒发
                        $periods_type = 1;
                    } elseif ($val['sell_type'] == 9) {
                        // 跨境
                        $periods_type = 2;
                    } elseif ($val['sell_type'] == 22) {
                        // 尾货
                        $periods_type = 3;
                    } elseif ($val['sell_type'] == 5) {
                        // 兔头
                        $periods_type = 4;
                    }
                    if ($periods_type != '') {
                        $periods_ser = new PeriodsSer((int)$params['periods_type']);
                        $periods_ser->update($val['id'], $p);
                    }
                    unset($p);
                }
            } elseif ($params['table' == 'wy_sales_goods_description']) {
                foreach ($params['data'] as $val) {
                    $p['detail'] = $val['description'];
                    $p['predict_time'] = $val['predict_time'];
                    $p['is_presell'] = $val['is_pre_order'];
                    $p['praise_count'] = $val['digg_nums'];
                    $p['import_type'] = $val['import_type'];
                    $p['supplier'] = $val['supplier_name'];
                    $p['pageviews'] = $val['views'];
                    $p['is_supplier_delivery'] = $val['on_behalf'];
                    // 查询期数频道
                    $val['sell_type'] = Db::connect('v2mysql')->table('wy_sales_goods')
                        ->where('id', $val['goods_id'])->value('sell_type');
                    $periods_type = '';
                    // 更新期数
                    if ($val['sell_type'] == 5) {
                        // 闪购
                        $periods_type = 0;
                    } elseif ($val['sell_type'] == 20) {
                        // 秒发
                        $periods_type = 1;
                    } elseif ($val['sell_type'] == 9) {
                        // 跨境
                        $periods_type = 2;
                    } elseif ($val['sell_type'] == 22) {
                        // 尾货
                        $periods_type = 3;
                    } elseif ($val['sell_type'] == 5) {
                        // 兔头
                        $periods_type = 4;
                    }
                    if ($periods_type != '') {
                        $periods_ser = new PeriodsSer((int)$params['periods_type']);
                        $periods_ser->update($val['goods_id'], $p);
                    }
                    unset($p);
                }
            } elseif ($params['table'] == 'wy_sales_images') {
                // 题图
                $banner_img = '';
                // 产品图
                $product_img = '';
                // 秒发竖图
                $horizontal_img = '';
                // 查询期数频道
                $val['sell_type'] = Db::connect('v2mysql')->table('wy_sales_goods')
                    ->where('id', $params['data'][0]['goods_id'])->value('sell_type');
                $periods_type = '';
                // 更新期数
                if ($val['sell_type'] == 5) {
                    // 闪购
                    $periods_type = 0;
                } elseif ($val['sell_type'] == 20) {
                    // 秒发
                    $periods_type = 1;
                } elseif ($val['sell_type'] == 9) {
                    // 跨境
                    $periods_type = 2;
                } elseif ($val['sell_type'] == 22) {
                    // 尾货
                    $periods_type = 3;
                } elseif ($val['sell_type'] == 5) {
                    // 兔头
                    $periods_type = 4;
                }
                foreach ($params['data'] as $val) {
                    if ($val['type'] == 1) {
                        $banner_img = $val['big_img'];
                    }
                    if ($val['type'] == 2) {
                        $product_img .= $val['big_img'] . ',';
                    }
                    if ($val['type'] == 3) {
                        $horizontal_img = $val['big_img'];
                    }
                }
                $p = [];
                if ($banner_img != '') {
                    $p['banner_img'] = $banner_img;
                }
                if ($product_img != '') {
                    $p['product_img'] = $product_img;
                }
                if ($periods_type == 1 && $horizontal_img != '') {
                    $p['horizontal_img'] = $horizontal_img;
                }
                if ($periods_type != '') {
                    $periods_ser = new PeriodsSer((int)$params['periods_type']);
                    $periods_ser->update($params['data'][0]['goods_id'], $p);
                }
            } elseif ($params['table'] == 'wy_sales_goods_not_upshelf') {
                foreach ($params['data'] as $val) {
                    // 查询期数频道
                    $val['sell_type'] = Db::connect('v2mysql')->table('wy_sales_goods')
                        ->where('id', $val['goods_id'])->value('sell_type');
                    $periods_type = '';
                    // 更新期数
                    if ($val['sell_type'] == 5) {
                        // 闪购
                        $periods_type = 0;
                    } elseif ($val['sell_type'] == 20) {
                        // 秒发
                        $periods_type = 1;
                    } elseif ($val['sell_type'] == 9) {
                        // 跨境
                        $periods_type = 2;
                    } elseif ($val['sell_type'] == 22) {
                        // 尾货
                        $periods_type = 3;
                    } elseif ($val['sell_type'] == 5) {
                        // 兔头
                        $periods_type = 4;
                    }
                    $p['sellout_sold_out'] = $val['onshelf'];
                    if ($periods_type != '') {
                        $periods_ser = new PeriodsSer((int)$params['periods_type']);
                        $periods_ser->update($val['goods_id'], $p);
                    }
                }
            } elseif ($params['table'] == 'wy_sales_collocation') {
                foreach ($params['data'] as $val) {
                    // 查询期数频道
                    $val['sell_type'] = Db::connect('v2mysql')->table('wy_sales_goods')
                        ->where('id', $val['goods_id'])->value('sell_type');
                    $periods_type = '';
                    // 更新期数
                    if ($val['sell_type'] == 5) {
                        // 闪购
                        $periods_type = 0;
                    } elseif ($val['sell_type'] == 20) {
                        // 秒发
                        $periods_type = 1;
                    } elseif ($val['sell_type'] == 9) {
                        // 跨境
                        $periods_type = 2;
                    } elseif ($val['sell_type'] == 22) {
                        // 尾货
                        $periods_type = 3;
                    } elseif ($val['sell_type'] == 5) {
                        // 兔头
                        $periods_type = 4;
                    }
                    $p['package_name'] = $val['c_name'];
                    $p['price'] = $val['price'];
                    $p['market_price'] = $val['market_price'];
                    $p['is_hidden'] = $val['is_del'];
                    $p['rabbit'] = $val['rabbit'];
                    $p['is_original_package'] = $val['is_original_box'];
                    $p['coupons_id'] = $val['coupon_id'];
                    $p['id'] = $val['id'];
                    if ($periods_type != '') {
                        $package_ser = new \app\service\Package($periods_type);
                        $package_ser->update($p);
                    }

                    unset($p);
                }
            } elseif ($params['table'] == 'wy_sales_goods_for_auto_increment ') {
                foreach ($params['data'] as $val) {
                    // 查询期数频道
                    $val['sell_type'] = Db::connect('v2mysql')->table('wy_sales_goods')
                        ->where('id', $val['goods_id'])->value('sell_type');
                    $periods_type = '';
                    // 更新期数
                    if ($val['sell_type'] == 5) {
                        // 闪购
                        $periods_type = 0;
                    } elseif ($val['sell_type'] == 20) {
                        // 秒发
                        $periods_type = 1;
                    } elseif ($val['sell_type'] == 9) {
                        // 跨境
                        $periods_type = 2;
                    } elseif ($val['sell_type'] == 22) {
                        // 尾货
                        $periods_type = 3;
                    } elseif ($val['sell_type'] == 5) {
                        // 兔头
                        $periods_type = 4;
                    }
                    $p['limit_number'] = $val['init_stock'];
                    $p['incremental'] = $val['add_num'];
                    $p['critical_value'] = $val['near_num'];
                    if ($periods_type != '') {
                        $periods_ser = new PeriodsSer((int)$params['periods_type']);
                        $periods_ser->update($val['goods_id'], $p);
                    }
                }
            }
        }
    }

    /**
     * 商品转换率
     * @param Request $request
     */
    public function getAccessStatisticsCount(Request $request)
    {
        $params = $request->param();
        if (empty($params['start_time']) || empty($params['end_time'])) {//统计时间
            return serviceReturn(false, [], "查询时间范围不能为空");
        }
        $product = new \app\service\Products();
        // 转换率
        $result = $product->getAccessStatisticsCount($params);
        return throwResponse($result);
    }

    /**
     * 商家商品扣减库存上下架
     * @param Request $request
     * @return \think\Response
     */
    public function vmallInventorySaleStatus(Request $request): \think\Response
    {
        $params = $request->post();
        if (!isset($params['period']) || (int)$params['period'] <= 0) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '期数参数错误');
        }
        if (!isset($params['periods_type']) || (int)$params['periods_type'] <= 0) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '频道参数错误');
        }
        if (!isset($params['inventory'])) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '库存参数错误');
        }
        $period_ser = new PeriodsSer((int)$params['periods_type']);
        $result = $period_ser->vmallInventorySaleStatus($params);
        return throwResponse($result);
    }

    /**
     * 更新期数采购人
     * @param Request $request
     * @return \think\Response
     */
    public function updatePeriodsBuyer(Request $request): \think\Response
    {
        $params = $request->post();
        $period = 0;
        $periods_type = 0;
        $buyer_name = '';
        if ($params['errcode'] == 0) {
            if ($params['process_instance']) {
                if ($params['process_instance']['result'] == 'agree') {
                    foreach ($params['process_instance']['form_component_values'] as $val) {
                        if ($val['name'] == '期数') {
                            $period = $val['value'];
                        }
                        if ($val['name'] == '频道') {
                            $periods_type_str = $val['value'];
                            $periods_type_arr = explode('-', $periods_type_str);
                            $periods_type = (int)$periods_type_arr[0] ?? 0;
                        }
                        if ($val['name'] == '采购人修改为') {
                            $buyer_name = $val['value'];
                        }
                    }
                }
            }
        }

        $period_ser = new PeriodsSer($periods_type);
        $buyer_id = 0;
        // 获取采购人信息
        try {
            $url_header[] = 'vinehoo-client: tp6-commodities';
            $url_header[] = 'vinehoo-client-version: v3';
            $get_url = env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/conditionQueryList';
            $get_url .= '?limit=1&realname=' . $buyer_name;
            $re_status = get_url($get_url, $url_header);
            $re_status_data = json_decode($re_status, true);
            if (!empty($re_status_data['data']['list'])) {
                $buyer_id = (int)$re_status_data['data']['list'][0]['id'] ?? 0;
            }
        } catch (\Exception $e) {
            // 记录异常
            InterfaceCallLog::create([
                'function_name' => 'getSecondMerchants',
                'model' => 'mysql_batch_search',
                'method' => 'GET',
                'url' => $get_url,
                'params' => $buyer_name,
                'response' => $re_status,
                'remark' => '获取商品列表状态接口',
                'create_time' => date('Y-m-d H:i:s', time())
            ]);
        }
        // 更新采购人
        $data = [];
        if ($buyer_id > 0) {
            $data['buyer_id'] = $buyer_id;
            $data['buyer_name'] = $buyer_name;
            $result = $period_ser->updateInfoById((int)$period, $data);
            return throwResponse($result);
        }
        return throwResponse(0);
    }

    /**
     * 发送邮件建立自动任务
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function sendEmailToBuyer(Request $request): \think\Response
    {
        $params = $request->post();

        if (!isset($params['period']) || (int)$params['period'] < 1) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '期数错误');
        }

        if (!isset($params['periods_type'])) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '期数频道错误');
        }

        $per_ser = new PeriodsSer((int)$params['periods_type']);
        $period_info = $per_ser->getOne((int)$params['period'], 'id,title,predict_shipment_time,buyer_id, 
            buyer_name');
        if (empty($period_info)) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '未获取到期数信息');
        }
        $before_time = 0;
        if ($period_info['predict_shipment_time']) {
            $predict_shipment_time = strtotime($period_info['predict_shipment_time']);
            $before_time = strtotime("-1 week", $predict_shipment_time);
        }
        // 上午十点发送邮件
        $before_time = date("Y-m-d 10:00:00", $before_time);
        $before_time = strtotime($before_time);
        // 查看是否存在秒级任务
        $is_exists = PeriodsTaskLog::where([
            'period' => $params['period'],
            'type' => 4,
            'is_exec' => 0
        ])->value('task_id');
        // 发邮件时间在当前时间之后建立定时任务
        if ($before_time > time()) {
            $buyer_phone = 0;
            $buyer_email = '';
            // 获取采购人信息
            try {
                $url_header[] = 'vinehoo-client: tp6-commodities';
                $url_header[] = 'vinehoo-client-version: v3';
                $get_url = env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/conditionQueryList';
                $get_url .= '?limit=1&realname=' . $period_info['buyer_name'] . '&field=id,telephone';
                $re_status = get_url($get_url, $url_header);
                $re_status_data = json_decode($re_status, true);
                if (!empty($re_status_data['data']['list'])) {
                    $buyer_phone = (int)$re_status_data['data']['list'][0]['telephone'] ?? 0;
                }
            } catch (\Exception $e) {
                // 记录异常
                InterfaceCallLog::create([
                    'function_name' => 'sendEmailToBuyer',
                    'model' => 'AUTHORITY_URL',
                    'method' => 'GET',
                    'url' => $get_url,
                    'params' => $period_info['buyer_name'],
                    'response' => $re_status,
                    'remark' => '获取用户信息',
                    'create_time' => date('Y-m-d H:i:s', time())
                ]);
            }
            // 获取用户邮箱
            if ($buyer_phone > 0) {
                // 获取采购人信息
                try {
                    $url_header[] = 'vinehoo-client: tp6-commodities';
                    $url_header[] = 'vinehoo-client-version: v3';
                    $get_url = env('ITEM.DINGTALK_APPROVAL_URL') . '/dingtalk/v3/users/mobile/info';
                    $get_url .= '?mobile=' . $buyer_phone;
                    $re_status = get_url($get_url, $url_header);
                    $re_status_data = json_decode($re_status, true);
                    if (!empty($re_status_data['userinfo'])) {
                        $buyer_email = $re_status_data['userinfo']['email'] ?? 0;
                    }
                } catch (\Exception $e) {
                    // 记录异常
                    InterfaceCallLog::create([
                        'function_name' => 'sendEmailToBuyer',
                        'model' => 'DINGTALK_APPROVAL_URL',
                        'method' => 'GET',
                        'url' => $get_url,
                        'params' => $buyer_phone,
                        'response' => $re_status,
                        'remark' => '获取钉钉用户信息',
                        'create_time' => date('Y-m-d H:i:s', time())
                    ]);
                }
            }

            // 建立秒级任务发送邮件
            if ($buyer_email) {
                // 建立秒级任务地址
                $task_url = env('item.SLS_URL') . '/services/v3/task/add';
                // 更新秒级任务地址
                $up_task_url = env('item.SLS_URL') . '/services/v3/task/update';
                // 回调地址
                $re_url = env('item.COMMODITIES_URL') . '/commodities/v3/other/sendEmail';
                // 建立秒级任务
                $task_id = uuid();
                $task_data['task_trigger_time'] = $before_time;
                $task_data['task_url'] = $re_url;
                $task_data['task_data'] = (string)json_encode([
                        'type' => 0,
                        'toAddress' => $buyer_email,
                        'task_id' => $task_id,
                        'period' => $period_info['id'],
                        'periods_type' => (int)$params['periods_type']
                    ]
                );
                if ($is_exists) {
                    $task_data['old_task_id'] = $is_exists;
                    $task_data['new_task_id'] = $task_id;
                    $task_data = json_encode($task_data);
                    $url_header[] = 'vinehoo-client: tp6-commodities';
                    $url_header[] = 'vinehoo-client-version: v3';
                    $url_header[] = 'Content-Type: application/json;charset=utf-8';
                    $url_header[] = 'Content-Length: ' . strlen($task_data);
                    $t1 = post_url($up_task_url, $task_data, $url_header);
                } else {
                    $task_data['task_id'] = $task_id;
                    $task_data = json_encode($task_data);
                    $url_header[] = 'vinehoo-client: tp6-commodities';
                    $url_header[] = 'vinehoo-client-version: v3';
                    $url_header[] = 'Content-Type: application/json;charset=utf-8';
                    $url_header[] = 'Content-Length: ' . strlen($task_data);
                    $t1 = post_url($task_url, $task_data, $url_header);
                }
                $t1 = json_decode($t1, true);
                // 记录任务日志
                $task_log_data['task_id'] = $task_id;
                $task_log_data['period'] = $period_info['id'];
                $task_log_data['task_trigger_time'] = $before_time;
                $task_log_data['response'] = $t1['error_code'] ?? null;
                $task_log_data['remark'] = '期数：' . $period_info['id'] . ' 给采购发送邮件任务';
                $task_log_data['created_time'] = time();
                $task_log_data['type'] = 4;
                if ($is_exists) {
                    PeriodsTaskLog::where('task_id', $is_exists)->update(['task_id' => $task_id,
                                                                          'task_trigger_time' => $before_time]);
                } else {
                    PeriodsTaskLog::create($task_log_data);
                }
            }
        } else {
            // 已过发邮件时间
            if ($is_exists) {
                $up_task_url = env('item.SLS_URL') . '/services/v3/task/delete';
                $task_data['task_id	'] = $is_exists;
                $task_data = json_encode($task_data);
                $url_header[] = 'vinehoo-client: tp6-commodities';
                $url_header[] = 'vinehoo-client-version: v3';
                $url_header[] = 'Content-Type: application/json;charset=utf-8';
                $url_header[] = 'Content-Length: ' . strlen($task_data);
                $t1 = post_url($up_task_url, $task_data, $url_header);
                // 删除任务日志
                PeriodsTaskLog::where('task_id', $is_exists)->delete();
            }
        }

        return throwResponse(1);

    }


    /**
     * 方法描述：导入商品
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2022/12/12 11:01
     * @param Request $request
     * @return \think\Response
     */
    public function importByExcel(Request $request): \think\Response
    {
        $params = $request->post();

        //$params['file'] = $request->file('file');
        //上传文件验证
        if (!isset($params['file']) || empty($params['file'])) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '请上传文件');
        }
        $inputFileName = env('ALIURL') . $params['file'];
        if (!$inputFileName) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '缺少文件地址');
        }

        //拿到excel数据
        try {
            //$filename = $params['file']->getPathname();
            $filename = tempnam(sys_get_temp_dir(), "xlsx");
            $handle = fopen($filename, 'wb');
            fwrite($handle, file_get_contents($inputFileName));
            $spreadsheet = IOFactory::load($filename);
            $excel = $spreadsheet->getActiveSheet()->toArray(null, true, true, true);
        } catch (\Throwable $e) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, "获取文件数据失败，请稍后再试:" . $e->getMessage());
        }

        # 取消标题
        unset($excel[1]);
        # 删除处理字符前后的空格
        $excelData = [];
        foreach ($excel as $v) {
            $row = [];
            foreach ($v as $kk => $vv) {
                if (!empty($vv) && is_string($vv)) {
                    $vv = trim($vv);
                }
                $row[$kk] = $vv;
            }
            $excelData[] = $row;
        }


        //过滤空数据
        $excelData = array_filter($excelData, function (array $d) {
            return isset($d['T'], $d['F']) && !empty($d['T']);
        });

        if (count($excelData) === 0) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '无导入数据或格式不完整');
        }

        if (count($excelData) > 50) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '单次导入数据不能超过50条');
        }

        $sev = new PeriodsSer(99999);

        //闪购
        $flashData = [];
        //秒发
        $secondData = [];
        //跨境
        $crossData = [];
        //尾货
        $leftoverData = [];
        $defaultType = $params['type'] ?? '闪购';
        foreach ($excelData as $k => $v) {
            $s = $k . '行 ';

            /*//限制单次类型
            if($v['A'] !== $defaultType){
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '单次导入只能使用一种类型');
            }*/

            //验证进口类型
            $importTypeName = $sev->getImportTypeName();
            if (!in_array($v['H'], $importTypeName)) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '进口类型不存在');
            }
            //验证采购类型
            if (in_array($defaultType, ['闪购', '秒发', '尾货'])) {
                if ($v['H'] == $importTypeName[2]) {
                    return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '采购类型错误');
                }
            } else {
                if (in_array($v['H'], [$importTypeName[0], $importTypeName[1]])) {
                    return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '采购类型错误');
                }
            }
            $v['H'] = array_search($v['H'], $importTypeName);

            if (empty($v['T'])) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '期数不能为空');
            }

            //验证是否代发
            if (!in_array($v['Y'], ['否', '是'])) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '是否代发不匹配');
            }
            $v['Y'] = $v['Y'] == '否' ? 0 : 1;

            //验证是否预售
            if (!in_array($v['Z'], ['否', '是'])) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '是否预售不匹配');
            }
            $v['Z'] = $v['Z'] == '否' ? 0 : 1;

            //验证收款商户
            $merchants = payeeMerchantList();
            if (!in_array($v['F'], array_keys($merchants))) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '收款商户不匹配');
            }
            $v['payee_merchant_id'] = $merchants[$v['F']];

            //验证类型
            switch ($defaultType) {
                case "闪购":
                    if (isset($flashData[$v['T']])) {
                        return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '期数重复');
                    }
                    $flashData[$v['T']] = $v;
                    break;
                case "秒发":
                    if (isset($secondData[$v['T']])) {
                        return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '期数重复');
                    }
                    $secondData[$v['T']] = $v;
                    break;
                case "跨境":
                    if (isset($crossData[$v['T']])) {
                        return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '期数重复');
                    }
                    $crossData[$v['T']] = $v;
                    break;
                case "尾货":
                    if (isset($leftoverData[$v['T']])) {
                        return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '期数重复');
                    }
                    $leftoverData[$v['T']] = $v;
                    break;
                default:
                    return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '类型不存在');
            }
            //验证产品简码不能重复
            if (count(explode(',', (string)$v['S'])) !== count(array_unique(explode(',', (string)$v['S'])))) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '简码不能重复');
            }
        }

        //获取当前操作人
        $userId = $request->header('vinehoo-uid');
        $userName = $request->header('vinehoo-vos-name', '');
        if ($userName) {
            $userName = base64_decode($userName);
        }
        $rst = $sev->importByExcel($userId, $userName, $flashData, $secondData, $crossData, $leftoverData);
        if (is_string($rst)) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, $rst);
        }

        return throwResponse();
    }


    /**
     * 跨境导入商品
     * @param Request $request
     * @return \think\Response
     */
    public function importCrossByExcel(Request $request): \think\Response
    {
        $params = $request->post();

        //$params['file'] = $request->file('file');
        //上传文件验证
        if (!isset($params['file']) || empty($params['file'])) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '请上传文件');
        }
        $inputFileName = env('ALIURL') . $params['file'];
        if (!$inputFileName) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '缺少文件地址');
        }

        //拿到excel数据
        try {
            //$filename = $params['file']->getPathname();
            $filename = tempnam(sys_get_temp_dir(), "xlsx");
            $handle = fopen($filename, 'wb');
            fwrite($handle, file_get_contents($inputFileName));
            $spreadsheet = IOFactory::load($filename);
            $excelData = $spreadsheet->getActiveSheet()->toArray(null, true, true, true);
        } catch (\Throwable $e) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, "获取文件数据失败，请稍后再试:" . $e->getMessage());
        }

        # 取消标题
        unset($excelData[1]);


        //过滤空数据
        $excelData = array_filter($excelData, function (array $d) {
            return isset($d['T'], $d['F']) && !empty($d['T']);
        });

        if (count($excelData) === 0) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '无导入数据或格式不完整');
        }

        if (count($excelData) > 50) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '单次导入数据不能超过50条');
        }

        $sev = new PeriodsSer(99999);

        //闪购
        $flashData = [];
        //秒发
        $secondData = [];
        //跨境
        $crossData = [];
        //尾货
        $leftoverData = [];
        $defaultType = $params['type'] ?? '闪购';
        foreach ($excelData as $k => $v) {
            $s = $k . '行 ';

            /*//限制单次类型
            if($v['A'] !== $defaultType){
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '单次导入只能使用一种类型');
            }*/
            // 数据验证必填
            if ($v['A'] == '') {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '上架时间必填');
            }
            if ($v['E'] == '') {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '发货仓库必填');
            }
            if ($v['M'] == '') {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '成本必填');
            }
            if ($v['N'] == '') {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '价格必填');
            }
            if ($v['R'] == '') {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '库存必填');
            }
            if ($v['S'] == '') {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '简码必填');
            }
            if ($v['T'] == '') {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '期数必填');
            }
            //验证进口类型
            $importTypeName = $sev->getImportTypeName();
            if (!in_array($v['H'], $importTypeName)) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '进口类型不存在');
            }
            //验证采购类型
            if (in_array($defaultType, ['闪购', '秒发', '尾货'])) {
                if ($v['H'] == $importTypeName[2]) {
                    return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '采购类型错误');
                }
            } else {
                if (in_array($v['H'], [$importTypeName[0], $importTypeName[1]])) {
                    return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '采购类型错误');
                }
            }
            $v['H'] = array_search($v['H'], $importTypeName);

            if (empty($v['T'])) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '期数不能为空');
            }

            //验证是否代发
            if (!in_array($v['Y'], ['否', '是'])) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '是否代发不匹配');
            }
            $v['Y'] = $v['Y'] == '否' ? 0 : 1;

            //验证是否预售
            if (!in_array($v['Z'], ['否', '是'])) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '是否预售不匹配');
            }
            $v['Z'] = $v['Z'] == '否' ? 0 : 1;

            //验证收款商户
            $merchants = payeeMerchantList();
            if (!in_array($v['F'], array_keys($merchants))) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '收款商户不匹配');
            }
            $v['payee_merchant_id'] = $merchants[$v['F']];

            //验证类型
            switch ($defaultType) {
                case "闪购":
                    if (isset($flashData[$v['T']])) {
                        return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '期数重复');
                    }
                    $flashData[$v['T']] = $v;
                    break;
                case "秒发":
                    if (isset($secondData[$v['T']])) {
                        return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '期数重复');
                    }
                    $secondData[$v['T']] = $v;
                    break;
                case "跨境":
                    if (isset($crossData[$v['T']])) {
                        return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '期数重复');
                    }
                    $crossData[$v['T']] = $v;
                    break;
                case "尾货":
                    if (isset($leftoverData[$v['T']])) {
                        return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '期数重复');
                    }
                    $leftoverData[$v['T']] = $v;
                    break;
                default:
                    return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '类型不存在');
            }
            //验证产品简码不能重复
            if (count(explode(',', (string)$v['S'])) !== count(array_unique(explode(',', (string)$v['S'])))) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, $s . '简码不能重复');
            }
        }

        //获取当前操作人
        $userId = $request->header('vinehoo-uid');
        $userName = $request->header('vinehoo-vos-name', '');
        if ($userName) {
            $userName = base64_decode($userName);
        }
        $rst = $sev->importCrossByExcel($userId, $userName, $flashData, $secondData, $crossData, $leftoverData);
        if (is_string($rst)) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, $rst);
        }

        return throwResponse();
    }

    /**
     * 获取浏览量
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function getPageviewsByHours(Request $request): \think\Response
    {
        $params = $request->get();
        if (empty($params['et'])) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '时间参数错误');
        }
        if (empty($params['st'])) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '时间参数错误');
        }
        if (strtotime($params['st']) > strtotime($params['et'])) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '时间区间错误');
        }
        $periods = new PeriodsSer(0);
        $re = $periods->getPageviewsByHours($params);
        $result['total'] = $re->total() ?? 0;
        $result['list'] = $re->getCollection()->toArray() ?? [];
        return throwResponse($result);
    }

    /**
     * 按天获取浏览量
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function getPageviewsByDays(Request $request): \think\Response
    {
        $params = $request->get();
        if (empty($params['et'])) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '时间参数错误');
        }
        if (empty($params['st'])) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '时间参数错误');
        }
        $periods = new PeriodsSer(0);
        $re = $periods->getPageviewsByDays($params);
//        $result['total'] = $re->total() ?? 0;
//        $result['list'] = $re->getCollection()->toArray() ?? [];
        return throwResponse($re);
    }

    /**
     * 统计期数小时浏览量
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function getPeriodPageviewsByHours(Request $request): \think\Response
    {
        $params = $request->get();
        if (empty($params['et'])) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '时间参数错误');
        }
        if (empty($params['st'])) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '时间参数错误');
        }
        $periods = new PeriodsSer(0);
        $re = $periods->getPeriodPageviewsByHours($params);
        $result['total'] = $re->total() ?? 0;
        $result['list'] = $re->getCollection()->toArray() ?? [];
        return throwResponse($result);
    }

    /**
     * 更新标签
     * @param Request $request
     * @return \think\Response
     */
    public function updateLabel(Request $request): \think\Response
    {
        $params = $request->post();
        if (empty($params['id'])) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '期数id错误');
        }
        if (empty($params['label'])) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '请选择标签');
        }
        if (!isset($params['periods_type']) || $params['periods_type'] === '') {
            return throwResponse([], ErrorCode::EXEC_ERROR, '请选择频道');
        }
        $periods = new PeriodsSer((int)$params['periods_type']);
        $re = $periods->updateLabel($params);
        return throwResponse($re);
    }

    /**
     * 批量更新标签
     * @param Request $request
     * @return \think\Response
     */
    public function batchUpdateLabel(Request $request): \think\Response
    {
        $params = $request->post();
        if (empty($params['id'])) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '期数id错误');
        }
        if (empty($params['label']) && empty($params['del_label'])) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '追加标签和删除标签不能都为空。');
        }
        $params['label'] = $params['label'] ?? '';
        $params['del_label'] = $params['del_label'] ?? '';
        
        try  {
            MDb::startTrans();
            $ids = explode(',', $params['id']);
            $periods_type_map = MDb::name('periods_product_inventory')
                ->whereIn('period', $ids)
                ->group('period')
                ->column('period,periods_type', 'period');

            foreach ($ids as $id) {
                $periods_type = strval($periods_type_map[$id]['periods_type'] ?? '');
                if (strlen($periods_type) == 0) {
                    throw new \Exception("【{$id}】期数未添加产品，获取类型失败。");
                }
                if (!in_array($periods_type, [0, 1, 2, 3])) {
                    throw new \Exception("【{$id}】期数不能添加标签，仅闪购、秒发、跨境、尾款可添加标签，请检查。");
                }
                $periods = new PeriodsSer(intval($periods_type));
                $periods_info = $periods->getOne(intval($id), 'id,label');
                if (empty($periods_info)) {
                    throw new \Exception("【{$id}】期数不存在");
                }
                $label = explode(',', $periods_info['label'] ?? '');
                $label = array_values(array_unique(array_merge($label, explode(',', $params['label']))));
                
                // 删除标签
                if (!empty($params['del_label'])) {
                    $del_label = explode(',', $params['del_label']);
                    $label = array_values(array_diff($label, $del_label));
                }
                
                $periods->updateLabel([
                    'id' => $id,
                    'label' => implode(',', $label),
                ]);
            }
            #提交事务
            MDb::commit();
        } catch (\Exception $e) {
            #回滚事务
            MDb::rollback();
            Log::error('batchUpdateLabelException：' . $e->getMessage() . '' . $e->getLine());
            return throwResponse([], ErrorCode::EXEC_ERROR, $e->getMessage());
        }

        return throwResponse([]);
    }

    /**
     * 查询期数关单卫检状态
     * @param Request $request
     * @return \think\Response
     */
    public function getCustomsOrderHealthInspect(Request $request): \think\Response
    {
        $params = $request->param();
        if (empty($params['id'])) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '期数id错误');
        }
        if (!isset($params['periods_type']) || $params['periods_type'] === '') {
            return throwResponse([], ErrorCode::EXEC_ERROR, '请选择频道');
        }

        $periods = new PeriodsSer((int)$params['periods_type']);
        $re = $periods->getCustomsOrderHealthInspect($params['id']);
        if (!is_array($re)) {
            return throwResponse([], ErrorCode::EXEC_ERROR, $re);
        }

        return throwResponse($re);
    }

    /**
     * 查询期数渠道加密ID
     * @param Request $request
     * @return \think\Response
     */
    public function getChannelEncryptId(Request $request): \think\Response
    {
        $params = $request->param();
        if (empty($params['id'])) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '期数id错误');
        }
        
        $info = Es::name('periods')
            ->field('id,is_channel')
            ->where([
                ['_id', '=', $params['id']]
            ])
            ->find();
        if (empty($info)) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '期数信息查询失败');
        }
        $re = [
            'encrypt_id' => strval($params['id']),
            'is_channel' => $info['is_channel'] ?? 0,
        ];

        if (!empty($info['is_channel']) && intval($info['is_channel']) === 1) {
            $re['encrypt_id'] = getChannelEncryptId($params['id']);
        }

        return throwResponse($re);
    }

    /**
     * 验证期数渠道加密ID
     * @param Request $request
     * @return \think\Response
     */
    public function verifyChannelEncryptId(Request $request): \think\Response
    {
        $params = $request->param();
        $result = ['result' => false];
        if (empty($params['id'])) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '期数id错误');
        }

        $info = Es::name('periods')
            ->field('id,is_channel')
            ->where([
                ['_id', '=', $params['id']]
            ])
            ->find();

        if (empty($info)) {
            return throwResponse($result);
        }

        if (!empty($info['is_channel']) && intval($info['is_channel']) === 1 && $params['id'] > 122230) {
            if (empty($params['encrypt_id'])) {
                return throwResponse($result);
            }
            $decrypt = CryptionDeals(['orig_data' => [strval($params['encrypt_id'])]]);
            if (empty($decrypt[$params['encrypt_id']])) {
                return throwResponse($result);
            }
            $de_data = json_decode($decrypt[$params['encrypt_id']], true);
            if (empty($de_data['id']) || $de_data['id'] != $params['id']) {
                return throwResponse($result);
            }
        }

        return throwResponse(['result' => true]);

    }

    /**
     * 批量查询期数毛利率
     * @param Request $request
     * @return \think\Response
     */
    public function BatchQueryPeriodGrossProfitMargin(Request $request): \think\Response
    {
        $params = $request->param();
        if (empty($params['period']) || is_array($params['period']) === false) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '期数id错误');
        }
        $periods = new PeriodsSer(0);
        $re = $periods->BatchQueryPeriodGrossProfitMargin ($params['period']);
        return throwResponse($re);
    }

    /**
     * 保存期数操作记录
     * @param Request $request
     * @return \think\Response
     */
    public function SaveOperationRecord(Request $request): \think\Response
    {
        $params = $request->param();
        #数据验证
        $validate = DataValidate::rule([
            'route_url|route_url' => 'require',
            'request_body|request_body' => 'require',
            'uid|操作人ID' => 'require|number|>:0',
            'vos_name|操作人' => 'require|max:255',
        ]);
        if (!$validate->check($params)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $periods = new PeriodsSer(0);
        $re = $periods->SaveOperationRecord($params);
        if ($re !== true) {
            return throwResponse([], ErrorCode::EXEC_ERROR, $re);
        }
        return throwResponse();
    }

    /**
     * 操作记录列表
     * @param Request $request
     * @return \think\Response
     */
    public function OperationRecordList(Request $request): \think\Response
    {
        $params = $request->param();
        #数据验证
        $validate = DataValidate::rule([
            'period|期数' => 'require|number|>:0',
            'change_item|变更项' => 'max:100',
            'change_content|变更内容' => 'max:255',
        ]);
        if (!$validate->check($params)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $periods = new PeriodsSer(0);
        $re = $periods->OperationRecordList($params);
        return throwResponse($re);
    }

    /**
     * 方法描述：预约
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2024/8/8 15:46
     * @param Request $request
     * @return \think\Response
     */
    public function reservation(Request $request): \think\Response
    {
        $params = $request->post();
        $params['uid'] = $request->header('vinehoo-uid', '0');
        $params['create_time'] = time();
        $params['periods_type'] = intval($params['periods_type'] ?? 0);

        if($params['uid'] <= 0 || !isset($params['periods_type'],$params['period'])){
            return throwResponse();
        }

        $periods = new PeriodsSer($params['periods_type']);
        if (!in_array($params['periods_type'],[0,1,2,3])) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, '该商品无法预约');
        }

        $result = $periods->reservation($params);

        if ($result !== '') {
            return throwResponse(null, ErrorCode::EXEC_ERROR, $result);
        }
        return throwResponse();
    }

    /**
     * 方法描述：返回用户是否预约过
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2024/8/8 16:15
     * @param Request $request
     * @return \think\Response
     */
    public function isReservation(Request $request): \think\Response
    {
        $params = $request->post();
        $params['uid'] = $request->header('vinehoo-uid', '0');
        $params['create_time'] = time();

        if($params['uid'] <= 0 || !isset($params['period'])){
            return throwResponse();
        }

        $periods = new PeriodsSer(0);

        return throwResponse(['is_reservation'=>$periods->isReservation($params)]);
    }

    /**
     * 方法描述：预约列表
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2024/8/8 16:58
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function reservationList(Request $request): \think\Response
    {
        $params = $request->get();
        $limit = $request->get('limit', '10');
        $where = function ($query) use ($params) {
            if (isset($params['period']) && $params['period'] > 0) {
                $query->where('period', $params['period']);
            }
        };
        $re = PeriodsReservation::where($where)->order('id', 'desc')->paginate([
            'list_rows' => $limit,
            'var_page' => 'page',
        ]);
        $result['total'] = $re->total() ?? 0;
        $result['list'] = $re->getCollection()->toArray() ?? [];
        return throwResponse($result);
    }

    /**
     * 方法描述：我的预约列表
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2024/9/24 14:07
     * @param Request $request
     * @return \think\Response
     */
    public function myReservation(Request $request): \think\Response
    {
        $uid = $request->header('vinehoo-uid', '0');

        if ($uid <= 0) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, '非法操作');
        }

        //获取用户最新100条预约记录
        $periods = PeriodsReservation::where(['uid' => $uid])->order('id', 'desc')->limit(100)->column('period');
        $result['list'] = Es::name('periods')
            ->where([
                ['id', 'in', array_values($periods)],
                ['is_delete', '=', 0],//没删除的
                ['is_channel', '=', 0],//不是渠道
                ['onsale_status', 'in', [1, 2, 3, 4]],//1待售中，2：在售中，3：已下架，4：已售罄
            ])
            ->field('id,banner_img,periods_type,title,brief,price,onsale_status,sell_time,limit_number,purchased,vest_purchased,is_hidden_price')
            ->order(['sell_time' => 'asc'])->select()->toArray();

        //排序
        usort($result['list'], function ($a, $b) {
            // 状态优先级
            $status_priority = [
                1 => 1, // 待售中
                2 => 1, // 在售中
                3 => 2, // 已下架
                4 => 2  // 已售罄
            ];

            // 比较状态优先级
            if ($status_priority[$a['onsale_status']] != $status_priority[$b['onsale_status']]) {
                return $status_priority[$a['onsale_status']] - $status_priority[$b['onsale_status']];
            }

            // 状态相同时，按sell_time升序排序
            return strtotime($a['sell_time']) - strtotime($b['sell_time']);
        });

        foreach ($result['list'] as &$v) {
            $v['banner_img_str'] = env('ALIURL') . $v['banner_img'];
            $v['purchased'] = $v['purchased'] + $v['vest_purchased'];
            unset($v['vest_purchased']);
            $v = [
                'id' => $v['id'],
                'periods_type' => $v['periods_type'],
                'period_info' => $v,
            ];
            unset($v['period_info']['id'],$v['period_info']['periods_type'],$v['period_info']['banner_img']);
        }
        return throwResponse($result);
    }


    /**
     * 方法描述：用户取消预约
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2024/9/24 14:27
     * @param Request $request
     * @return \think\Response
     */
    public function cancelReservation(Request $request): \think\Response
    {
        $params = $request->post();
        $params['uid'] = intval($request->header('vinehoo-uid', '0'));

        if($params['uid'] <= 0 || !isset($params['periods']) || !is_array($params['periods'])){
            return throwResponse(null, ErrorCode::EXEC_ERROR, '非法操作');
        }

        $periods = new PeriodsSer(0);
        $periods->cancelReservation($params['uid'],$params['periods']);

        return throwResponse();
    }

    /**
     * 查询未推送萌牙订单
     * @param Request $request
     * @return \think\Response
     */
    public function getunpushorder(Request $request): \think\Response
    {
        $params = $request->param();
        #数据验证
        $validate = DataValidate::rule([
            'period|期数' => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $periods = new PeriodsSer(0);
        $re = $periods->getunpushorder($params);

        return throwResponse($re);
    }

    /**
     * 更新代发期数发货时间
     * @param Request $request
     * @return \think\Response
     */
    public function updateDfDeliveryTime(Request $request): \think\Response
    {
        $params = $request->param();
        for ($i = 0; $i <= 3; $i++) {
            (new PeriodsSer($i))->updateDfDeliveryTime();
        }

        return throwResponse();
    }

    /**
     * 【闪购、秒发、食品】仓扣减库存后如果库存为0或者【库存不足】推送萌牙失败，需要包含此简码的在售的闪购非渠道期数修改发货时间
     * @param Request $request
     * @return \think\Response
     */
    public function stockUpdateDeliveryTime(Request $request): \think\Response
    {
        $params = $request->param();
        #数据验证
        $validate = DataValidate::rule([
            'fictitious_id|虚拟仓' => 'require',
            'short_code|简码' => 'require|array',
        ]);
        if (!$validate->check($params)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $result = (new PeriodsSer(0))->stockUpdateDeliveryTime($params);

        if ($result !== true) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $result);
        }

        return throwResponse();
    }

    /**
     * 取消在售期数渠道标识
     * @param Request $request
     * @return \think\Response
     */
    public function cancelChannel(Request $request): \think\Response
    {
        $params = $request->param();
        #数据验证
        $validate = DataValidate::rule([
            'period|期数' => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $period_info = Es::name('periods')->where([
            ['_id', '=', $params['period']],
        ])->field('id,periods_type')->find();
        if (empty($period_info)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '期数信息不存在');
        }
        $periods_type = $period_info['periods_type'];
        $periods = new PeriodsSer($periods_type);
        $result = $periods->cancelChannel($params['period']);
        if ($result !== true) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $result);
        }

        return throwResponse();
    }

    /**
     * 期数查询同时在售的期数
     * @param Request $request
     * @return \think\Response
     */
    public function getSamePeriods(Request $request): \think\Response
    {
        $params = $request->param();
        #数据验证
        $validate = DataValidate::rule([
            'period|期数' => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $period_info = Es::name('periods')->where([
            ['_id', '=', $params['period']],
        ])->field('id,periods_type')->find();
        if (empty($period_info)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '期数不存在');
        }
        $periods_type = $period_info['periods_type'];
        $periods = new PeriodsSer($periods_type);
        $re = $periods->VerifySimultaneousSales($period_info['id'], 0, 0, [], 3);
        if ($re === true) {
            $re = [];
        }

        return throwResponse(['list' => $re]);
    }

    /**
     * 展示文案通过记录
     * @param Request $request
     * @return \think\Response
     */
    public function showingText(Request $request): \think\Response
    {
        $params = $request->param();
        #数据验证
        $validate = DataValidate::rule([
            'start_time|开始时间' => 'require|dateFormat:Y-m-d',
            'end_time|结束时间' => 'require|dateFormat:Y-m-d',
        ]);
        if (!$validate->check($params)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $start_time = $params['start_time'] . ' 00:00:00';
        $end_time = $params['end_time'] . ' 23:59:59';

        $review = MDb::name('periods_status_change_record')->where([
            ['type', '=', 1],
            ['status_type', '=', 3],
            ['created_time', 'BETWEEN', [$start_time, $end_time]],
        ])
        ->group('operator_id,period')
        ->column('operator_id,operator_name,period,periods_type');
        $period_map = [];
        $total = count($review);
        foreach ($review as $k => $v) {
            $period_ids[] = $v['period'];
            if (count($period_ids) >= 1000 || ($k + 1) == $total) {
                $period_info = Es::name('periods')->where([
                    ['_id', 'in', $period_ids],
                ])->field('id,onsale_verify_status,is_manual_create')
                ->select()->toArray();
                foreach ($period_info as $vv) {
                    if (empty($vv['is_manual_create']) || $vv['is_manual_create'] != 1) {
                        continue;
                    }
                    $period_map[$vv['id']] = $vv;
                }
            }
        }


        $list = [];
        foreach ($review as $v) {
            $period_info = $period_map[$v['period']] ?? [];
            if (empty($period_info)) {
                continue;
            }
            $info = $list[$v['operator_id']] ?? [];
            if (empty($info)) {
                $info = [
                    'operator_name' => $v['operator_name'],
                    'total_submit' => 0,
                    'onsale_nums' => 0,
                    'not_onsale_nums' => 0,
                    'not_onsale_ratio' => 0,
                ];
            }
            $info['total_submit']++;
            if (!empty($period_info['onsale_verify_status']) && $period_info['onsale_verify_status'] == 1) {
                $info['onsale_nums']++;
            } else {
                $info['not_onsale_nums']++;
            }

            if ($info['not_onsale_nums'] <= 0) {
                $info['not_onsale_ratio'] = 0;
            }
            if ($info['not_onsale_nums'] > 0) {
                $not_onsale_ratio = bcdiv(strval($info['not_onsale_nums']), strval($info['total_submit']), 4);
                $info['not_onsale_ratio'] = floatval(bcmul(strval($not_onsale_ratio), strval(100), 2));
            }

            $list[$v['operator_id']] = $info;
        }
        $list = array_values($list);
        
        return throwResponse(['list' => $list]);
    }
}
