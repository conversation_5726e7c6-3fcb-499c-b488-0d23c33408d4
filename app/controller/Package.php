<?php
declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\model\InterfaceCallLog;
use app\model\PeriodsRabbit;
use app\model\PeriodsRabbitCoupon;
use app\model\PeriodsSecondMerchants;
use app\model\PeriodsSecondMerchantsSet;
use app\service\es\Es;
use app\service\Package as PeriodsPackage;
use app\Request;
use app\validate\Package as valPackage;
use app\validate\PackageInventory;
use think\facade\Db;
use think\facade\Log;
use think\Validate;


class Package extends BaseController
{

    /**
     * 添加商品套餐
     *
     * @param Request $request
     * @return false|string|\think\Response
     * @throws \Exception
     */
    public function create(Request $request)
    {
        // 参数
        $params = $origin_params = $request->post();

        $period_id = (int)$params[0]['period_id'];
        $periods_type = (int)$params[0]['periods_type'];
        $is_second = (($params[0]['periods_type'] ?? null) == 1); //是否秒发频道
        // 添加商品套餐
        $package = new PeriodsPackage($periods_type);
        $result = 0;
        // 验证设置某一个套餐设置了套餐限量，全部需要设置
        $is_set    = 0;
        $is_no_set = 1;
        // 对比次数，防止全部隐藏时无法对比
        $contrast_count = 0;
        $product_ids = [];
        if (count($params) > 1) {
            foreach ($params as &$validate) {
                $validate['inventory'] = $validate['inventory'] ?? 0;
                // 只判断非隐藏套餐
                if ($validate['is_hidden'] == 1) {
                    continue;
                }
                
                $validate['unlimited'] = (int)$validate['unlimited'];
                if ($validate['unlimited'] > 0) {
                    $is_set = 1;
                } else {
                    $is_no_set = 0;
                }
                $validate['inventory_accum'] = $validate['inventory'];
                $contrast_count++;
            }
            if ($contrast_count > 1 && ($is_set != $is_no_set)) {
                return throwResponse([], ErrorCode::EXEC_ERROR, '套餐限量设置不一致');
            }
            
        }

        // 检测套餐数据格式
        $package_prices = [];
        $is_deposit = false;
        $display_price = false;
        $product_id_all = [];
        foreach ($params as &$value) {
            $validate['inventory'] = $validate['inventory'] ?? 0;
            $value['is_custom_package'] = $value['is_custom_package'] ?? 0;
            // 取出未隐藏套餐价格
            if ($value['is_hidden'] == 0) {
                array_push($package_prices, $value['price']);
                if ($value['is_display_price'] ?? 0 == 1) {
                    if ($display_price === false) {
                        $display_price = $value['price'];
                    } else {
                        throw new \Exception("只允许一个优先展示价!");
                    }
                }
            }
            //判断是否订金套餐
            if (!empty($value['is_deposit']) && intval($value['is_deposit']) === 1) {
                if (!isset($value['deposit_coupon_threshold']) || empty($value['deposit_coupon_value'])) {
                    return throwResponse([], ErrorCode::EXEC_ERROR, '优惠券满减金额不能为空');
                }
                if (empty($value['deposit_price'])) {
                    return throwResponse([], ErrorCode::EXEC_ERROR, '订金金额不能为空');
                }
                if (empty($value['deposit_coupon_threshold'])) {
                    $value['deposit_coupon_threshold'] = 0;
                }
                if (empty($value['deposit_coupon_value'])) {
                    $value['deposit_coupon_value'] = 0;
                }
                if ($value['deposit_coupon_threshold'] < $value['deposit_coupon_value']) {
                    return throwResponse([], ErrorCode::EXEC_ERROR, '优惠券面额必须小于满减金额');
                }
                $is_deposit = true;
            }

            $associated_products = json_decode($value['associated_products'], true);
            if (empty($associated_products)) {
                return throwResponse([], ErrorCode::EXEC_ERROR, '套餐数据格式有误');
            }
            $custom_product_count = 1;
            $product_id_arr = [];
            foreach ($associated_products as &$val) {
                if ($value['is_mystery_box'] != 1 && $value['is_custom_package'] != 1) {
                    $val['product_id'] = (int)$val['product_id'];
                    if ($val['product_id'] <= 0) {

                        return throwResponse([], ErrorCode::EXEC_ERROR, '套餐产品设置有误');
                    }
                }

                // 非赠品
                if (isset($val['isGift']) && intval($val['isGift']) === 0) {
                    if (is_array($val['product_id'])) {
                        $product_ids = array_merge($product_ids, $val['product_id']);
                    } else {
                        $product_ids[] = $val['product_id'];
                    }
                }
                
                $val['nums'] = (int)$val['nums'];
                $custom_product_count = $val['nums'];
                if ($val['nums'] <= 0) {
                    return throwResponse([], ErrorCode::EXEC_ERROR, '套餐产品数量设置有误');
                }
                if (is_array($val['product_id'])) {
                    $product_id_arr = array_merge($product_id_arr, $val['product_id']);
                } else {
                    $product_id_arr[] = $val['product_id'];
                }
                $product_id_all = array_merge($product_id_all, $product_id_arr);
            }

            // 自选套餐
            if (!empty($value['is_custom_package']) && $value['is_custom_package'] == 1) {
                $value['custom_product_count'] = $custom_product_count;

                if (!empty($value['is_mystery_box']) && $value['is_mystery_box'] == 1) {
                    return throwResponse([], ErrorCode::EXEC_ERROR, '盲盒不能设置自选套餐。');
                }
                if (empty($value['custom_product_count']) || $value['custom_product_count'] == 0) {
                    return throwResponse([], ErrorCode::EXEC_ERROR, '自选套餐的自选产品数必须大于0。');
                }
                if ($value['custom_product_count'] >= count($product_id_arr)) {
                    return throwResponse([], ErrorCode::EXEC_ERROR, '自选套餐的自选产品数必须小于套餐产品数量。');
                }
            }

            if (empty($value['is_hidden']) && !empty($value['periods_type']) && $value['periods_type'] == 2 && !empty($value['period_id'])) {
                $period_info = (new \app\service\Periods(intval($value['periods_type'])))->getModel()
                    ->where('id', $value['period_id'])
                    ->find();

                if ($period_info['is_channel'] == 0 && ($period_info['onsale_status'] >= 2)) {
                    $short_codes = Db::connect('wiki')->name('products')
                        ->where('id', 'in', array_column($associated_products, 'product_id'))
                        ->column('short_code');

                    $sales_period = Es::name(Es::PERIODS)->where([
                        ['short_code', 'in', $short_codes],
                        ['periods_type', 'in', [2]],
                        ['is_channel', '==', 0],
                        ['onsale_status', 'in', [1, 2]],
                        ['id', '<>', $value['period_id']],
                    ])->find();
                    if ($sales_period) {
                        return throwResponse([], ErrorCode::EXEC_ERROR, '相同的跨境商品不能同时在售!');
                    }
                }
            }

            // 检测成功转回 json
            $value['associated_products'] = (string)json_encode($associated_products);
        }
        unset($value, $val);

        // 仅用于同步评论验证
        if (!empty($product_id_all)) {
            $exists = Db::name('periods_product_inventory')->where([
                ['period' , '=', $period_id],
                ['product_id' , 'in', $product_id_all],
                ['is_use_comment', '=', 1]
            ])->value('id');
            if (!empty($exists)) {
                return throwResponse([], ErrorCode::PARAM_ERROR, '可售套餐的存货已设置仅用于评论，请先取消');
            }
        }

        $err    = '';
        $period = new \app\service\Periods((int)$params[0]['periods_type']);
        // 查询当前期数运营审核状态
        $period_info = $period->getOne($params[0]['period_id'], 'id,onsale_review_status,onsale_status,sell_time,sold_out_time,operation_review_id,is_channel');
        if (empty($period_info)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '未获取到期数：' . $params[0]['period_id']);
        }

        // 非渠道销售不能存在同产品同时售卖，判断依据是【商品售卖区间】开始时间-下架时间。不能存在相同存货
        if (
            isset($period_info['is_channel']) && intval($period_info['is_channel']) === 0 && 
            isset($period_info['sell_time']) && 
            isset($period_info['sold_out_time']) && 
            !empty($product_ids)
        ) {
            $res = $period->VerifySimultaneousSales($params[0]['period_id'], strtotime($period_info['sell_time']), strtotime($period_info['sold_out_time']), $product_ids);
            if ($res !== true) {
                return throwResponse(null, ErrorCode::EXEC_ERROR, $res);
            }
        }

        // 已上架期数验证产品关单卫检
        if (in_array($period_info['onsale_status'], [1, 2])) {
            $resu = $period->vCustomsOrderHealthInspect($params[0]['period_id'], 1, $params);
            if ($resu !== true) {
                return throwResponse(null, ErrorCode::PARAM_ERROR, $resu);
            }
        }
        
        // 更新 es 套餐价格
        if (!empty($package_prices)) {
            sort($package_prices);
            $package_prices_str = implode('/', $package_prices);
            $es_ser             = new \app\service\ElasticSearch();
            $es_ser->updatePeriodsById($period_id, ['package_prices' => $package_prices_str]);
        }
        $package_validate = new valPackage();
        
        $customPackage = [];
        // 验证是否存在新增套餐，解决只有一个套餐隐藏新增同时出现时，套餐无法隐藏问题
        // 添加套餐
        foreach ($params as $cv) {
            if (!isset($cv['id']) || empty($cv['id'])) {
                // 获取商品套餐id
                $cv['id'] = $this->getGeneratorID(2);
                if (!$cv['id']) {
                    return throwResponse([], ErrorCode::EXEC_ERROR, '生成商品套餐失败');
                }
                $cv['created_time']    = time();
                $cv['inventory_accum'] = $cv['inventory'] ?? 0;
                // 参数验证
                $validate_params = $package_validate->check($cv);
                if (!$validate_params) {
                    return throwResponse([], ErrorCode::PARAM_ERROR, $package_validate->getError());
                }
                unset($validate_params);

                // 如果运营已审核产品，直接上架
                if ($period_info['onsale_review_status'] == 3) {
                    $cv['is_onsale'] = 1;
                    if (!empty($cv['is_custom_package']) && $cv['is_custom_package'] == 1) {
                        $customPackage[] = $cv;
                    }
                }

                $result = $package->create($cv);
                if (!$result['status']) {
                    $err = $result['msg'];
                }
            }
        }
        if ($err) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, $err);
        } else {
            $result = $result['data'] ?? $result;

            // 创建自选套餐子套餐
            if (!empty($customPackage)) {
                $package->createCustomPackage($customPackage);
            }
        }
        // 更新套餐
        foreach ($params as $v) {
            // 如果传入 id 更新套餐是否隐藏属性
            if (isset($v['id']) && $v['id'] != '') {
//                if ($period_info['onsale_status'] == 2) {
//                    return throwResponse([], ErrorCode::PARAM_ERROR, '商品在售中，禁止更新套餐');
//                }
                if (isset($v['package_name']) && strlen($v['package_name']) > 150) {
                    return throwResponse([], ErrorCode::PARAM_ERROR, '套餐名称长度不能超过 50 字符');
                }
                // 如果运营已审核产品，只更新隐藏套餐
                if ($period_info['onsale_review_status'] == 3) {
                    $pac_result = $package->update([
                        'id'        => $v['id'],
                        'is_hidden' => $v['is_hidden'],
                        'unlimited' => $v['unlimited'],
                        'display_price' => $v['is_display_price'] ?? 0,
                        'is_onsale' => 1
                    ]);
                } else {
                    // 如果未审核更新全部套餐属性
                    $pac_result = $package->update($v);
                }
                if ($pac_result['status'] == true) {
                    $result = $pac_result['data'];
                } else {
                    $err = $pac_result['msg'];
                }
            }
        }

        // 添加套餐编辑人
        $params['creator_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['creator_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['creator_name']) {
            $params['creator_name'] = base64_decode($params['creator_name']);
        }

        $up_data = [
            'operation_id' => $params['creator_id'],
            'operation_name' => $params['creator_name']
        ];
        #设置为订金期数
        if ($is_deposit) {
            $up_data['is_deposit_period'] = 1;
        }
        #设置为订金优先展示价
        if ($display_price !== false) {
            $up_data['price'] = $display_price;
        }
        $period->updateInfoById((int)$period_id, $up_data);

        if ($err) {
            return throwResponse($result, ErrorCode::EXEC_ERROR, $err);
        }

        if ($is_deposit) {
            #生成订金优惠券
            $res_err = $period->generateDepositCoupon($period_id, $periods_type, $period_info);
            if ($res_err !== true) {
                return throwResponse([], ErrorCode::EXEC_ERROR, $res_err);
            }
        }
        

        if ($is_second) {
            //是秒发频道
            //查询原套餐信息
            $origin_packages = Es::name(Es::PERIODS_PACKAGE)->where([['period_id', '==', $period_id],])->select()->toArray();
            $origin_packages = array_column($origin_packages, null, 'id');

            $up_hidden_packages = $up_new_packages = [];
            foreach ($origin_params as $package_param) {
                $id = $package_param['id'] ?? null;
                if ($id) {
                    //已存在的套餐 判断有无更新数据
                    $origin_package = $origin_packages[$id];
                    if (($package_param['is_hidden'] == 1) && ($origin_package['is_hidden'] != $package_param['is_hidden'])) {
                        //有隐藏套餐
                        $up_hidden_packages[] = $package_param;
                    }
                } else {
                    //新增的套餐
                    $up_new_packages[] = $package_param;
                }
            }

            if (!empty($up_hidden_packages)) {
                //同步隐藏状态到商家秒发套餐
                $hidden_pg_ids = PeriodsSecondMerchantsSet::where('join_vh_package_id', 'in', array_column($up_hidden_packages, 'id'))->column('id,period_id');
                PeriodsSecondMerchantsSet::where('id', 'in', array_column($hidden_pg_ids, 'id'))->update(['is_hidden' => 1]); //更新全部的隐藏套餐状态

                //判断期数是否所有套餐被隐藏 如果全部套餐被隐藏, 则下架
                $m_period_ids             = array_unique(array_column($hidden_pg_ids, 'period_id'));
                $vendibility_m_period_ids = PeriodsSecondMerchantsSet::where('period_id', 'in', $m_period_ids)->where('is_hidden', 0)->column('period_id'); //还拥有其他可售套餐的期数ID
                $offline_m_period_ids     = array_diff($m_period_ids, $vendibility_m_period_ids); //需要下架的期数
                $up_data                  = [
                    'onsale_status'   => 3,
                    'off_sell_remark' => '无可使用套餐',
                    'off_sell_type'   => 4,
                    'off_sell_time'   => time(),
                ];
                (new \app\service\SecondMerchants())->batchSoldOut([
                    "periods" => $offline_m_period_ids,
                    "data"    => $up_data,
                ]); //批量下架
            }

            if (!empty($up_new_packages)) {
                //判断是编辑套餐 还是 新建套餐
                $period_info = Es::name(Es::PERIODS)->where([['id', '==', $period_id]])->find();
                $mids        = array_unique(PeriodsSecondMerchants::where('join_period_id', 'in', $period_id)->column('supplier_id'));

                if (!empty($mids)) {
                    //通知下游企业套餐的变化
                    if (empty($up_hidden_packages)) {
                        //新增套餐
                        $content = "商品 " . $period_info['title'] . " 有新的套餐 " . $up_new_packages[0]['package_name'];
                    } else {
                        //编辑套餐
                        $content = "商品 " . $period_info['title'] . " 的套餐 " . $up_new_packages[0]['package_name'] . ' 金额或优惠发生变化';
                    }
                    $msgtype   = 'text';
                    $send_data = compact('msgtype', 'mids', 'content');
                    $res       = \Curl::batchRemind($send_data);
                }
            }
        }

        return throwResponse($result);

    }

    /**
     * 更新商品套餐
     *
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function update(Request $request)
    {
        // 参数
        $params = $request->post();

        // 验证套餐 id
        $update_validate = $this->updateValidate($params);
        if (!empty($update_validate)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $update_validate);
        }

        $package = new PeriodsPackage((int)$params['periods_type']);

        // 更新套餐信息
        $params['update_time'] = time();
        $result = $package->update($params);

        if (!$result['status']) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, '更新套餐失败');
        }

        return throwResponse($result['data']);

    }


    /**
     * 验证套餐库存是否充足
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function validateInventory(Request $request): \think\Response
    {
        $params = $request->get();
        // 参数验证
        $validate = new PackageInventory();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $package = new PeriodsPackage((int)$params['periods_type']);
        $result = $package->validateInventory($params);
        if (!$result['status']) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, $result['msg']);
        }

        return throwResponse([]);
    }

    /**
     * 返回库存数据
     * @param Request $request
     * @return \think\Response
     */
    public function sendBackInventory(Request $request): \think\Response
    {
        $params = $request->post();
        // 参数验证
        $validate = new PackageInventory();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $package = new PeriodsPackage((int)$params['periods_type']);
        $result = $package->sendBackInventory($params);
        if (!$result['status']) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, $result['msg']);
        }

        return throwResponse([]);
    }

    /**
     * 更新套餐参数验证
     *
     * @param array $params
     * @return string
     */
    protected function updateValidate(array $params): string
    {
        // 提示信息
        $message = '';

        // 商品 id 验证
        if (!isset($params['id']) || empty($params['id'])) {
            $message .= '请选择套餐';
        }

        return $message;
    }

    /**
     * 根据期数查询期数套餐列表
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function packageList(Request $request)
    {
        $params = $request->get();
        $package = new PeriodsPackage((int)$params['periods_type']);
        $result = $package->packageList($params);
        if ($request->get('product')) {
            foreach ($result as &$v) {
                $v['associated_products'] = json_decode($v['associated_products'], true);
                // 字符串id转数组
                $product_id = [];
                foreach ($v['associated_products'] as $pv) {
                    $product_id[] = $pv['product_id'];
                }
                $product_url = env('ITEM.WINE_WIKI_URL') . '/wiki/v3/product/fieldsdatarr';
                $product_info = post_url($product_url, ['ids' => $product_id,
                    'fields' => 'id,cn_product_name,en_product_name,short_code,bar_code'
                ]);
                $product_list = [];
                if (!is_null(json_decode($product_info))) {
                    $result_pro = json_decode($product_info, true);
                    if ($result_pro['error_code'] == 0) {
                        $product_list = $result_pro['data']['list'];
                    }
                }
                $v['product'] = $product_list;
            }
        }
        return throwResponse($result);
    }

    /**
     * 根据期数查询套餐+套餐下包含产品列表
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function packageProductList(Request $request)
    {
        $params = $request->get();
        $params['uid'] = $request->header('vinehoo-uid', '');
        $package = new PeriodsPackage((int)$params['periods_type']);
        $result = $package->packageProductList($params);
        return throwResponse($result);
    }


    /**
     * 商品套餐售罄更新
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function examinationPackageInventory(Request $request): \think\Response
    {
        $params = $request->post();
        // 表名
        if (!isset($params['table']) || empty($params['table'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请输入表名');
        }
        // 期数
        if (!isset($params['period']) || empty($params['period'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请输入期数');
        }

        // 兔头优惠券库存售空下架
        if ($params['table'] == 'vh_periods_rabbit_coupon') {
            // 期数
            $periods = new \app\service\Periods(5);
            // 期数详细
            $period_info = $periods->getOne(
                (int)$params['period'],
                'onsale_status, onsale_verify_status, sold_out_time, inventory'
            );
            // 未进行二次上架确认不做任何操作
            if (empty($period_info['onsale_verify_status']) || $period_info['onsale_verify_status'] != 1) {
                return throwResponse([]);
                exit();
            }
            if ($period_info['inventory'] <= 0) {
                $onsale_status = 3;
            }
            if ($period_info['inventory'] > 0 && $period_info['onsale_verify_status'] == 1
                && strtotime($period_info['sold_out_time']) > time()) {
                $onsale_status = 2;
            }
            $result = $periods->updateField((int)$params['period'], 'onsale_status', (string)$onsale_status);
            return throwResponse($result);
        }

        $package_name = ['vh_periods_cross_set' => 2, 'vh_periods_flash_set' => 0, 'vh_periods_leftover_set' => 3,
            'vh_periods_rabbit_set' => 4, 'vh_periods_second_set' => 1];
        // 频道
        $periods_type = 0;
        if ($params['table'] == 'vh_periods_product_inventory') {
            $periods_type = intval($params['periods_type']);
            $params['period'] = intval($params['period']);

            try {
                $url  = env('ITEM.COMMODITIES_SERVICES') . '/commodities_server/v3/marketing/AutomaticallyAddPeriod';
                $data = json_encode([
                    'period'      => intval($params['period']),
                    'period_type' => intval($params['periods_type']),
                    'card'        => [],
                    'column'      => [],
                    'title'       => '',
                    'inventory'   => 1,
                ]);
                curlRequest($url, $data);
            } catch (\Exception $e) {
                Log::write('AutomaticallyAddPeriod ERROR: ' . json_encode($params . '  ') . $e->getMessage());
            }

            //秒发库存扣减库存后小于6瓶后需要企微通知陈泓州
            if ($periods_type == 1) {
                (new PeriodsPackage($periods_type))->sendSecondInventoryMessage($params['period']);
            }

        } else {
            $periods_type = $package_name[$params['table']];
        }
        // 期数
        $periods = new \app\service\Periods($periods_type);
        $field = 'id, onsale_status, onsale_verify_status, sold_out_time,sellout_sold_out,is_sold_out_lock, limit_number,purchased, vest_purchased, incremental, critical_value, sell_time, is_channel';
        if ($periods_type == 0) {
            $field .= ',is_deposit,is_seckill';
        }
        // 期数详细
        $period_info = $periods->getOne(
            (int)$params['period'],
            $field
        );
        // 未进行二次上架确认不做任何操作
        if (empty($period_info['onsale_verify_status']) || $period_info['onsale_verify_status'] != 1) {
            return throwResponse([]);
            exit();
        }

        $package = new PeriodsPackage($periods_type);
        // 套餐列表
        $package_list = $package->packageList(['period' => $params['period']]);
        // 是否还存在套餐库存 0 不存在，1 存在
        $inventory = 0;
        // 是否不限量 0 限量，1 不限量
        $unlimited = 0;
        if (!empty($package_list)) {
            // 检查套餐库存
            foreach ($package_list as $val) {
                if ($val['unlimited'] == 0 && $val['inventory'] > 0) {
                    $inventory = 1;
                }
                $unlimited = $val['unlimited'];
            }
            // 不限量
            if ($unlimited == 1) {
                $unlimited = 1;
            }
        }
        unset($val);

        if (empty($period_info)) {
            return throwResponse(null, 0, '未查询到期数');
        }

        // 更新商品状态 套餐类型判断
        $onsale_status = $period_info['onsale_status'];
        // 更新期数状态已售罄: 商品无库存并且售完不下架,并且套餐限量
        if ($inventory == 0 && $period_info['sellout_sold_out'] == 0 && $unlimited == 0) {
            $onsale_status = 4;
        }

        // 更新商品状态在售中：存在库存并且已确认过上架，并未到下架时间
        if ($inventory == 1 && $period_info['onsale_verify_status'] == 1 &&
            strtotime($period_info['sold_out_time']) > time() && empty($period_info['is_deposit'])) {
            $onsale_status = 2;
        }
        // 更新状态已下架：没有库存并且售完下架，并且套餐限量
        if ($inventory == 0 && $period_info['sellout_sold_out'] == 1 && $unlimited == 0) {
            $onsale_status = 3;
        }
        
        $other_ser = new \app\service\Other();
        // 套餐无库存并且套餐不限量，检查产品库存，套餐产品库存不足的写入以下数组
        $package_inventory = [];
        // 套餐id
        $package_id_arr = [];
        // 跨境满足库存低于或等于3的期数自动勾选跨境尾货，并进入首页的“跨境尾货”板块
        $is_leftover = 0;
        if ($inventory == 0 && $unlimited == 1) {

            // 验证每个套餐
            foreach ($package_list as $val) {
                array_push($package_id_arr, $val['id']);
                $product_arr = json_decode($val['associated_products'], true);
                // 验证套餐下每个产品库存
                foreach ($product_arr as $v) {
                    // 盲盒不参与验证
                    if (is_array($v['product_id'])) {
                        continue;
                    }
                    $product = $other_ser->getPackageProductInfo((int)$val['period_id'],
                        (int)$v['product_id'], 'inventory');
                    // 产品库存不足
                    if ($product['inventory'] < $v['nums']) {
                        array_push($package_inventory, $val['id']);
                    }
                    if ($product['inventory'] <= 2) {
                        $is_leftover = 1;
                    }
                }
            }

            unset($val, $v);
            $package_inventory = array_values(array_unique($package_inventory));
            $package_id_arr = array_values(array_unique($package_id_arr));
            // 所有套餐都存在库存不足的产品，判断商品售罄还是下架
            if ($package_inventory == $package_id_arr) {
                $onsale_status = 3;
                // 售罄状态售完不下架，并且未到下架时间
                if ($period_info['sellout_sold_out'] == 0 && strtotime($period_info['sold_out_time']) > time()) {
                    $onsale_status = 4;
                }
                // 更新状态已下架：没有库存并且售完下架
                if ($period_info['sellout_sold_out'] == 1) {
                    $onsale_status = 3;
                }
            }
            // 更新商品状态在售中：存在库存并且已确认过上架，并未到下架时间
            if ($package_inventory != $package_id_arr && $period_info['onsale_verify_status'] == 1
                && strtotime($period_info['sold_out_time']) > time() && empty($period_info['is_deposit'])) {
                $onsale_status = 2;
            }
        }

        // 下架锁定后不允许上架
        if ($onsale_status == 1 || $onsale_status == 2) {
            // 下架后锁定不做任何操作
            if ($period_info['is_sold_out_lock'] == 1) {
                // 下架后锁定不做任何操作
                return throwResponse([]);
                exit();
            }
        }

        // 有必要才更新
//        if ($onsale_status != $period_info['onsale_status']) {
        // 如果增加库存使其上架，判断库存增量值
        if ($onsale_status == 2) {
            // 验证产品相同售卖区间
            $resu = $periods->VerifySimultaneousSales($params['period'],0,0,[],0);
            if ($resu !== true) {
                return throwResponse([]);
                exit();
            }

            // 取出套餐份数
            $min_inv = [];
            // 是否盲盒
            $is_box = 0;
            // 是否剩余库存1
            $is_one = false;
            foreach ($package_list as $value) {
                $product_arr = json_decode($value['associated_products'], true);
                // 验证套餐下每个产品库存
                foreach ($product_arr as $v) {
                    // 盲盒不参与验证
                    if (is_array($v['product_id'])) {
                        $is_box = 1;
                        continue;
                    }
                    $product = $other_ser->getPackageProductInfo((int)$value['period_id'],
                        (int)$v['product_id'], 'inventory');
                    // 产品库存不足
                    if ($product['inventory'] >= $v['nums']) {
                        // 计算最大可卖份数
                        $min_inv[] = floor($product['inventory'] / $v['nums']);
                    }
                    if (intval($product['inventory']) == 1) {
                        $is_one = true;
                    }
                }
            }
            // 最小可用套餐，非盲盒
            if ($is_box == 0) {
                if (empty($min_inv)) {
                    $min_i = 0;
                    $onsale_status = 4;
                } else {
                    $min_i = min($min_inv);
                }
                // 增量值
                $o_n = $min_i;
                // 前端已购数量（真实已购 + 马甲）
                $pur = $period_info['purchased'] + $period_info['vest_purchased'];
                // 如果存在最大套餐，并且已购份数大于等于初始值，增加初始值
//                if ($min_i && $pur >= $period_info['limit_number']) {
                if ((int)$min_i > $period_info['incremental']) {
                    $o_n = $period_info['incremental'];
                    // 计算差值
                    $y = ($pur + $o_n) - $period_info['limit_number'];
                    // 触发增量
                    if ($y <= $period_info['critical_value']) {
                        // 库存允许增量增加
                        if ($o_n + $period_info['incremental'] <= $min_i) {
                            $o_n += $period_info['incremental'];
                        }
                    }
                }
                if ($pur >= $period_info['limit_number'] && empty($period_info['is_seckill'])) {
                    $periods->incField((int)$params['period'], 'limit_number', (int)$o_n);
                    $period_info['limit_number'] = intval($period_info['limit_number'] + $o_n);
                }
                $interval = intval($period_info['limit_number'] - $pur);
                // 当真实库存只剩1瓶时，限量值与已购值之差为1
                if (!empty($is_one) && $interval != 1 && empty($period_info['is_seckill'])) {
                    $vest_purchased = intval(($period_info['limit_number'] - 1) - $period_info['purchased']);
                    $periods->updateInfo((int)$params['period'], ['vest_purchased' => $vest_purchased]);
                }
//                }
                // 刷新 CDN
                $periods::CDNrefreshObject((int)$params['period']);
            }
        } elseif (($onsale_status == 3 || $onsale_status == 4) && empty($period_info['is_seckill'])) {
            // 库存售罄下架填平已购
            // 期数更新后详细
            $period_new_info = $periods->getOne(
                (int)$params['period'],
                'onsale_status, onsale_verify_status, sold_out_time,sellout_sold_out,is_sold_out_lock, limit_number,
            purchased, vest_purchased, incremental, critical_value'
            );
            $new_purch = (int)$period_new_info['purchased'] + (int)$period_new_info['vest_purchased'];
            if ($new_purch < $period_new_info['limit_number']) {
                $inc_vest_purchased = $period_new_info['limit_number'] - $new_purch;
                $periods->incField((int)$params['period'], 'vest_purchased', (int)$inc_vest_purchased);
            } elseif ($new_purch > $period_new_info['limit_number']) {
                $inc_limit_number = $new_purch - $period_new_info['limit_number'];
                $periods->incField((int)$params['period'], 'limit_number', (int)$inc_limit_number);
            }
        }
        
        // 非渠道销售不能存在同产品同时售卖，判断依据是【商品售卖区间】开始时间-下架时间。不能存在相同存货
        if (empty($period_info['is_channel']) && $period_info['onsale_status'] != 2 && $onsale_status == 2) {
            $res = $periods->VerifySimultaneousSales($period_info['id'], strtotime($period_info['sell_time']), strtotime($period_info['sold_out_time']));
            if ($res !== true) {
                return throwResponse([], ErrorCode::PARAM_ERROR, $res);
            }
        }

        $update = ['onsale_status' => $onsale_status];

        //跨境满足库存低于或等于3的期数自动勾选跨境尾货，并进入首页的“跨境尾货”板块
        if ($periods_type == 2 && $onsale_status == 2 ) {
            $update['is_leftover'] = $is_leftover;
            // 进入首页的“跨境尾货”板块
            // $exist = Db::table('vh_marketing.vh_special_activity_goods')
            //     ->where('periods',$params['period'])
            //     ->value('id');
            // if (empty($exist)) {
            //     $url = env('ITEM.ACTIVITIES_MANAGEMENT_URL').'/activity/v3/goods/create';
            //     $body = json_encode([
            //         "periods" => intval($params['period']),
            //         "goods_short_name" => "",
            //         "product_introduction" => "",
            //         "activity_id" => 23,
            //         "label_id" => 83,
            //         "status" => 2,
            //         "sort" => 0,
            //     ]);
            //     curlRequest($url, $body);
            // }
        }

        // 更新期数状态
        $result = $periods->updateInfoById((int)$params['period'], $update, 0);
        return throwResponse($result);
//        }
    }


    /**
     * 查询套餐包含可用库存
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPeriodsPackageInventory(Request $request): \think\Response
    {
        $params = $request->get();
        if (!isset($params['period']) || $params['period'] == '') {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择期数', [], 403);
        }
        if (!isset($params['periods_type']) || $params['periods_type'] == '') {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择期数频道', [], 403);
        }
        // 获取期数信息
        $startT = microtime(true);

        $periods_ser = new \app\service\Periods((int)$params['periods_type']);
        $period = $periods_ser->getOne((int)$params['period'], 'id,purchased,vest_purchased,limit_number, 
        onsale_status, onsale_time, sell_time, sold_out_time, is_supplier_delivery, predict_shipment_time, is_hidden_price,is_support_coupon, 
        is_support_reduction, buyer_id');

        $execution_time = (microtime(true) - $startT);
        if ($execution_time > 0.5) {
            //file_put_contents(app()->getRuntimePath() . '/log/db_console.log', sprintf("时间:%s,请求方法:%s,耗时:%s秒\n", date('Y-m-d H:i:s'), 'getOne', $execution_time), FILE_APPEND);
        }

        if (isset($period['predict_shipment_time']) && !empty($period['predict_shipment_time'])) {
            $predict_shipment_time = strtotime($period['predict_shipment_time']);
            #代发期数
            if ($period['is_supplier_delivery'] == 1) {
                $predict_shipment_time = SupplierShipPredictShipmentTimeLogic($predict_shipment_time);
            }
            $period['shipment_time'] = date('Y-m-d', $predict_shipment_time);
            $period['predict_shipment_time'] = date('Y-m-d H:i:s', $predict_shipment_time);
        }

        if (empty($period)) {
            return throwResponse([]);
        }
        $period['purchased'] = $period['purchased'] + $period['vest_purchased'];

        // 获取采购信息
        $buyer_info = Db::table('vh_authority.vh_admins')
            ->where('id', $period['buyer_id'])
            ->field('image,introduction,display_name')
            ->find();
        $period['purchasing_said_image'] = image_full_path($buyer_info['image'] ?? '');
        $period['purchasing_said_name'] = $buyer_info['display_name'] ?? '';
        $period['purchasing_said_desc'] = $buyer_info['introduction'] ?? '';


        unset($period['vest_purchased']);
        // 获取套餐信息
        $package_ser = new PeriodsPackage((int)$params['periods_type']);
        $package = $package_ser->packageInventory((int)$params['period']);


        $execution_time = (microtime(true) - $startT);
        if ($execution_time > 0.5) {
            //file_put_contents(app()->getRuntimePath() . '/log/db_console.log', sprintf("时间:%s,请求方法:%s,耗时:%s秒\n", date('Y-m-d H:i:s'), 'packageInventory', $execution_time), FILE_APPEND);
        }

        $price = $package[0]['price'] ?? 0;
        // 查询拼团套餐价格
        if (isset($params['is_group']) && $params['is_group'] == 1) {
            $group_package = $periods_ser->getGroupInfo([], ['period' => $params['period']]);
            if (!empty($group_package)) {
                $group_package_info = json_decode($group_package['group_price'], true);
                // 写入拼团价格
                foreach ($package as &$val) {
                    foreach ($group_package_info as &$v) {
                        if ($val['id'] == $v['package_id']) {
                            $val['group_price'] = $v['price'];
                        }
                    }
                }
                $price = $package[0]['group_price'] ?? 0;
                unset($val, $v);
            }
        }
        $uid = $request->header('vinehoo-uid', '');
        // 查询新人套餐价格
        if (isset($params['is_new_comer']) && $params['is_new_comer'] != '') {
            $group_package = $periods_ser->getNewcomerInfo([], ['period' => $params['period']]);
            if (!empty($group_package)) {
                $group_package_info = json_decode($group_package['newcomer_price'], true);
                // 写入拼团价格
                foreach ($package as &$val) {
                    foreach ($group_package_info as &$v) {
                        if ($val['id'] == $v['package_id']) {
                            $val['newcomer_price'] = $v['price'];
                        }
                    }
                }
                $price = $package[0]['newcomer_price'] ?? $package[0]['price'];
                unset($val, $v);
            }
        }

        //临时关闭142183 这一期获取满减信息
        if((int)$params['period'] != 142183){
            // 获取最优满减金额
            $calculate_url = env('ITEM.MARKET_URL') . '/marketing/v3/reduction/calculate';
            $cud = [['period' => $params['period'], 'period_type' => $params['periods_type'], 'price' => $price]];
            $re_cud = post_url($calculate_url, $cud);
            $calculate = get_interior_http_response($re_cud);
            if ($calculate > 0 && $period['is_support_reduction'] == 1) {
                $price = $price - $calculate;
            }
        }

        $coupon = [];
        // 获取优惠券

        //临时关闭142183 这一期获取优惠卷信息
        if((int)$params['period'] != 142183){
            $coupon_url = env('ITEM.COUPON_URL') . '/coupon/v3/couponissue/maxCouponIssue';
            $c_d['uid'] = $uid ?? '';
            $c_d['periods'] = $params['period'];
            $c_d['periods_type'] = 1;
            $c_d['price'] = $price;
            $c_d['coupon_package_id'] = 17;
            $coupon_list = post_url($coupon_url, $c_d);
            $coupon = get_interior_http_response($coupon_list);
            // 最大优惠券减免金额
            if (!empty($coupon) && $period['is_support_coupon'] == 1) {
                if ($price < $coupon['coupon_face_value']) {
                    $price = 0.01;
                } else {
                    $price = bcsub(strval($price), strval($coupon['coupon_face_value']), 2);
                }
            }
        }

        // 兔头优惠券
        $rabbit_ser = new \app\service\RabbitCoupon();
        $rabbit_coupon = $rabbit_ser->getRabbitCoupon();
        $coupon = $rabbit_coupon;
        $period['rabbit_coupon'] = $coupon;
        $period['price'] = round(floatval($price), 2);
        $period['coupon_package_id'] = 17;
        $period['price_discount'] = 0;
        if ($price > 0 && $package[0]['market_price'] > $price) {
            $period['price_discount'] = round(floatval($price) / floatval($package[0]['market_price']), 2) * 10;
            $period['price_discount'] = (float)number_format($period['price_discount'], 1);
            if ($period['price_discount'] >= 100) {
                $period['price_discount'] = 0;
            }
        }

        $execution_time = (microtime(true) - $startT);
        if ($execution_time > 0.5) {
            //file_put_contents(app()->getRuntimePath() . '/log/db_console.log', sprintf("时间:%s,请求方法:%s,耗时:%s秒\n", date('Y-m-d H:i:s'), 'getRabbitCoupon', $execution_time), FILE_APPEND);
        }

        foreach ($package as &$value) {
            $value['market_price'] = strval(floatval($value['market_price']));
            $value['price'] = strval(floatval($value['price']));
            // 限购可购 计算最大购买数量
            $purchase_limit = $period['limit_number'] - $period['purchased'];
            if ($purchase_limit < $value['inventory']) {
                $value['inventory'] = $purchase_limit;
            }
            // 当真实库存只剩1瓶时，限量值与已购值之差为1
            // if (intval($value['inventory']) == 1 && $purchase_limit > $value['inventory']) {
            //     $period['purchased'] = $period['limit_number'] - 1;
            // }
        }

        // 获取活动套餐价格
        $package = $package_ser->getSpecialActivityPackage($package, $params);

        $execution_time = (microtime(true) - $startT);
        if ($execution_time > 0.5) {
            //file_put_contents(app()->getRuntimePath() . '/log/db_console.log', sprintf("时间:%s,请求方法:%s,耗时:%s秒\n", date('Y-m-d H:i:s'), 'getSpecialActivityPackage', $execution_time), FILE_APPEND);
        }

        $period['packageList'] = addChildPackage($package);
        $event = $request->header('source-event', '');
        //压测时不添加浏览量和分享
        if((int)$params['period'] != 142183) {
            // 增加浏览量
            $periods_ser->incPageviews($params);
            // 增加分享浏览量
            $source_user = $request->header('source-user', '');
            if ($event == 'share' && $source_user) {
                $shar_data['access_id'] = $request->header('vinehoo-uid', 'nologged');
                $shar_data['share_id'] = $source_user;
                $shar_data['share_name'] = '';
                // 获取中台用户名称
                $url = env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info?admin_id=' . $source_user . '';
                $get_admin = get_url($url);
                if ($get_admin) {
                    $re = json_decode($get_admin, true);
                    if (!empty($re['data'])) {
                        $shar_data['share_name'] = $re['data'][$source_user]['realname'];
                    }
                }
                $shar_data['period'] = $params['period'];
                $shar_data['created_time'] = time();
                $periods_ser->createShareRecord($shar_data);
            }
        }
        return throwResponse($period);
    }

    /**
     * 秒发详情
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPeriodsSecondPackageInventory(Request $request): \think\Response
    {
        $params = $request->get();
        if (!isset($params['period']) || $params['period'] == '') {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择期数', [], 403);
        }
        if (!isset($params['periods_type']) || $params['periods_type'] == '') {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择期数频道', [], 403);
        }
        // 获取期数信息
        $periods_ser = new \app\service\Periods((int)$params['periods_type']);
        $period = $periods_ser->getOne((int)$params['period'], 'id,purchased,vest_purchased,limit_number, 
        onsale_status, onsale_time, sell_time, sold_out_time, predict_shipment_time, is_hidden_price,is_support_coupon, 
        is_support_reduction,buyer_id');
        if (isset($period['predict_shipment_time']) && !empty($period['predict_shipment_time'])) {
            $period['shipment_time'] = date('Y-m-d', strtotime($period['predict_shipment_time']));
        }

        if (empty($period)) {
            return throwResponse([
                'coupon_package_id' => 17
            ]);
        }
        $period['purchased'] = $period['purchased'] + $period['vest_purchased'];

        // 获取采购信息
        $buyer_info = Db::table('vh_authority.vh_admins')
            ->where('id', $period['buyer_id'])
            ->field('image,introduction,display_name')
            ->find();
        $period['purchasing_said_image'] = image_full_path($buyer_info['image'] ?? '');
        $period['purchasing_said_name'] = $buyer_info['display_name'] ?? '';
        $period['purchasing_said_desc'] = $buyer_info['introduction'] ?? '';


        unset($period['vest_purchased']);
        // 获取套餐信息
        $package_ser = new PeriodsPackage((int)$params['periods_type']);
        $package = $package_ser->packageInventory((int)$params['period']);
        $price = $package[0]['price'] ?? 0;
        // 查询拼团套餐价格
        if (isset($params['is_group']) && $params['is_group'] == 1) {
            $group_package = $periods_ser->getGroupInfo([], ['period' => $params['period']]);
            if (!empty($group_package)) {
                $group_package_info = json_decode($group_package['group_price'], true);
                // 写入拼团价格
                foreach ($package as &$val) {
                    foreach ($group_package_info as &$v) {
                        if ($val['id'] == $v['package_id']) {
                            $val['group_price'] = $v['price'];
                        }
                    }
                }
                $price = $package[0]['group_price'] ?? 0;
                unset($val, $v);
            }
        }
        // 查询是否新
        $uid = $request->header('vinehoo-uid', '');
//        if ($uid) {
//            $user_url = env('ITEM.USER_URL') . '/user/v3/profile/getSpecifiedData?field=is_new_user';
//            $user_filed = get_url($user_url, ['vinehoo-uid' => 'uid']);
//            $user_filed = get_interior_http_response($user_filed);
//            $params['is_new_comer'] = $user_filed['is_new_user'];
//        }
        // 查询新人套餐价格
        if (isset($params['is_new_comer']) && (int)$params['is_new_comer'] > 0) {
            $group_package = $periods_ser->getNewcomerInfo([], ['period' => $params['period']]);
            if (!empty($group_package)) {
                $group_package_info = json_decode($group_package['newcomer_price'], true);
                // 写入拼团价格
                foreach ($package as &$val) {
                    foreach ($group_package_info as &$v) {
                        if ($val['id'] == $v['package_id']) {
                            $val['newcomer_price'] = $v['price'];
                        }
                    }
                }
                $price = $package[0]['newcomer_price'] ?? $package[0]['price'];
                unset($val, $v);
            }
        }
        // 获取最优满减金额
        $calculate_url = env('ITEM.MARKET_URL') . '/marketing/v3/reduction/calculate';
        $cud = [['period' => $params['period'], 'period_type' => 1, 'price' => $price]];
        $re_cud = post_url($calculate_url, $cud);
        $calculate = get_interior_http_response($re_cud);
        if ($calculate > 0 && $period['is_support_reduction'] == 1) {
            $price = $price - $calculate;
        }
        $coupon = [];
        // 获取优惠券
        $coupon_url = env('ITEM.COUPON_URL') . '/coupon/v3/couponissue/maxCouponIssue';
        $c_d['uid'] = $uid ?? '';
        $c_d['periods'] = $params['period'];
        $c_d['periods_type'] = 1;
        $c_d['price'] = $price;
        $c_d['coupon_package_id'] = 17;
        $coupon_list = post_url($coupon_url, $c_d);
        $coupon = get_interior_http_response($coupon_list);
        // 最大优惠券减免金额
        if (!empty($coupon) && $period['is_support_coupon'] == 1) {
            if ($price < $coupon['coupon_face_value']) {
                $price = 0.01;
            } else {
                $price = bcsub(strval($price), strval($coupon['coupon_face_value']), 2);
            }
        }
        // 兔头优惠券
        $rabbit_ser = new \app\service\RabbitCoupon();
        $rabbit_coupon = $rabbit_ser->getRabbitCoupon();
        $coupon = $rabbit_coupon;
        $period['rabbit_coupon'] = $coupon;
        $period['price'] = round($price, 2);
        $period['coupon_package_id'] = 17;
        $period['price_discount'] = 0;
        if ($price > 0 && $package[0]['market_price'] > $price) {
            $period['price_discount'] = round($price / $package[0]['market_price'], 2) * 10;
            $period['price_discount'] = (float)number_format($period['price_discount'], 1);
            if ($period['price_discount'] >= 100) {
                $period['price_discount'] = 0;
            }
        }
        foreach ($package as &$value) {
            $value['market_price'] = strval(floatval($value['market_price']));
            $value['price'] = strval(floatval($value['price']));
            // 限购可购 计算最大购买数量
            $purchase_limit = $period['limit_number'] - $period['purchased'];
            if ($purchase_limit < $value['inventory']) {
                $value['inventory'] = $purchase_limit;
            }
        }
        // 获取活动套餐价格
        $package = $package_ser->getSpecialActivityPackage($package, $params);
        
        // 移除隐藏套餐，自选套餐新增子套餐
        $period['packageList'] = addChildPackage($package);

        // 增加浏览量
        $periods_ser->incPageviews($params);
        // 增加分享浏览量
        $event = $request->header('source-event', '');
        $source_user = $request->header('source-user', '');
        if ($event == 'share' && $source_user) {
            $shar_data['access_id'] = $request->header('vinehoo-uid', 'nologged');
            $shar_data['share_id'] = $source_user;
            $shar_data['share_name'] = '';
            // 获取中台用户名称
            $url = env('ITEM.AUTHORITY_URL').'/authority/v3/admin/info?admin_id='.$source_user.'';
            $get_admin = get_url($url);
            if ($get_admin) {
                $re = json_decode($get_admin, true);
                if (!empty($re['data'])) {
                    $shar_data['share_name'] = $re['data'][$source_user]['realname'];
                }
            }
            $shar_data['period'] = $params['period'];
            $shar_data['created_time'] = time();
            $periods_ser->createShareRecord($shar_data);
        }
        return throwResponse($period);
    }

    /**
     * 查询商品套餐包含可用库存
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getVmallPeriodsPackageInventory(Request $request): \think\Response
    {
        $params = $request->get();
        if (!isset($params['period']) || $params['period'] == '') {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择期数');
        }
        if (!isset($params['periods_type']) || $params['periods_type'] == '') {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择期数频道');
        }
        // 获取期数信息
        $periods_ser = new \app\service\Periods((int)$params['periods_type']);
        $period = $periods_ser->getOne((int)$params['period'], 'id,purchased,vest_purchased,is_support_coupon,limit_number,join_period_id, 
        onsale_status, onsale_time, sell_time, sold_out_time, predict_shipment_time, is_hidden_price');
        if (isset($period['predict_shipment_time']) && !empty($period['predict_shipment_time'])) {
            $period['shipment_time'] = date('Y-m-d', strtotime($period['predict_shipment_time']));
        }

        if (empty($period)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '未查询到期数信息');
        }

        if (empty($period['join_period_id'])) {
            $period['purchased'] = $period['purchased'] + $period['vest_purchased'];
        } else {
            $vh_period           = Es::name(Es::PERIODS)->where([
                ['id', '==', $period['join_period_id']]
            ])->field('purchased,vest_purchased')->find();
            $period['purchased'] = $vh_period['purchased'] + $vh_period['vest_purchased'];
        }
        unset($period['vest_purchased']);
        // 获取套餐信息
        $package_ser = new PeriodsPackage((int)$params['periods_type']);
        $package = $package_ser->vmallPackageInventory((int)$params['period']);
        // 获取商家套餐库存
        $package_arr['good_id'] = $params['period'];
        $package_item = [];
        foreach ($package as $value) {
            array_push($package_item, ['period' => $params['period'], 'package_id' => $value['id']]);
        }
        unset($value);
        $package_arr['items_info'] = $package_item;
        try {
            $url_header[] = 'vinehoo-client: tp6-commodities';
            $url_header[] = 'vinehoo-client-version: v3';
            $url_header[] = 'Content-Type: application/json;charset=utf-8';
            $vmall_url = env('ITEM.VMALL_URL') . '/vmall/v3/common/getperiodshopstock';
            $package_arr_json = json_encode($package_arr);
            $re_vmall = post_url($vmall_url, $package_arr_json, $url_header);
            $re_vmall_data = json_decode($re_vmall, true);
            foreach ($package as &$v) {
                foreach ($re_vmall_data['data'] as $k => $vv) {
                    if ($v['id'] == $vv['package_id']) {
                        $v['inventory'] = $vv['periodstock'];
                    }
                }
            }
            unset($v, $vv, $k);
        } catch (\Exception $e) {
            // 记录异常
            InterfaceCallLog::create([
                'function_name' => 'getSecondMerchants',
                'model' => 'vmall',
                'method' => 'GET',
                'url' => $vmall_url,
                'params' => $package_arr_json,
                'response' => $re_vmall,
                'remark' => '获取商家秒发总库存接口',
                'create_time' => date('Y-m-d H:i:s', time())
            ]);
        }
        // 查询拼团套餐价格
        if (isset($params['is_group']) && $params['is_group'] == 1) {
            $group_package = $periods_ser->getGroupInfo([], ['period' => $params['period']]);
            if (!empty($group_package)) {
                $group_package_info = json_decode($group_package['group_price'], true);
                // 写入拼团价格
                foreach ($package as &$val) {
                    foreach ($group_package_info as &$v) {
                        if ($val['id'] == $v['package_id']) {
                            $val['group_price'] = $v['price'];
                        }
                    }
                }
                unset($val, $v);
            }
        }
        // 查询新人套餐价格
        if (isset($params['is_new_comer']) && $params['is_new_comer'] != '') {
            $group_package = $periods_ser->getNewcomerInfo([], ['period' => $params['period']]);
            if (!empty($group_package)) {
                $group_package_info = json_decode($group_package['newcomer_price'], true);
                // 写入拼团价格
                foreach ($package as &$val) {
                    foreach ($group_package_info as &$v) {
                        if ($val['id'] == $v['package_id']) {
                            $val['newcomer_price'] = $v['price'];
                        }
                    }
                }
                unset($val, $v);
            }
        }
//        foreach ($package as &$value) {
//            // 限购可购 计算最大购买数量
//            $purchase_limit = $period['limit_number'] - $period['purchased'];
//            if ($purchase_limit < $value['inventory']) {
//                $value['inventory'] = $purchase_limit;
//            }
//        }

        $period['coupon_package_id'] = 17;
        $period['packageList'] = $package;
        // 增加浏览量
        $periods_ser->incPageviews($params);
        return throwResponse($period);
    }

    /**
     * 指定期数、套餐返回套餐相信，包含套餐产品剩余库存
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPeriodsPackageProductInventory(Request $request): \think\Response
    {
        $params = $request->post();
        $data = [];
        foreach ($params as &$val) {
            $package_ser = new PeriodsPackage((int)$val['periods_type']);
            $package = $package_ser->getPeriodsPackageProductInventory((int)$val['period'], (int)$val['package_id']);
//            $val['package_info'] = $package;
            if (isset($package[0]) && !empty($package[0])) {
                // 查询期数
                $periods_ser = new \app\service\Periods((int)$val['periods_type']);
                $period_info = $periods_ser->getOne((int)$val['period'], 'purchased,vest_purchased,limit_number');
                if (!empty($period_info)) {
                    $package[0]['purchased'] = $period_info['purchased'] + $period_info['vest_purchased'];
                    $package[0]['limit_number'] = $period_info['limit_number'];
                }
                $data[$package[0]['id']] = $package[0];
            }
        }
        return throwResponse($data);
    }

    /**
     * 获取单个套餐期数
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPeriodsPackageOne(Request $request): \think\Response
    {
        $params = $request->get();
        if (!isset($params['period']) || $params['period'] == '') {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择期数');
        }
        if (!isset($params['package_id']) || $params['package_id'] == '') {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择期数');
        }
        if (!isset($params['periods_type']) || $params['periods_type'] == '') {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择期数频道');
        }
        // 获取套餐信息
        $package_ser = new PeriodsPackage((int)$params['periods_type']);
        $package = $package_ser->getPackageOne($params);
        return throwResponse($package);
    }

    /**
     * 获取剩余订货量及预计发货时间
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPeriodsRemainingOrderQuantity(Request $request): \think\Response
    {
        $params = $request->get();
        if (!isset($params['period']) || $params['period'] == '') {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择期数');
        }
        if (!isset($params['periods_type']) || $params['periods_type'] == '') {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择期数频道');
        }
        // 获取套餐信息
        $package_ser = new PeriodsPackage((int)$params['periods_type']);
        $result = $package_ser->getPeriodsRemainingOrderQuantity($params);
        return throwResponse($result);
    }

    /**
     * 获取专题活动期数套餐价格
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getActivityPeriodsPrice(Request $request)
    {
        $time = time();
        $result = ['package' => (object)[]];
        $params = $request->param();
        if (empty($params['uid']) || empty($params['period']) || empty($params['package_id'])) {
            return throwResponse($result);
        }
        $user = getUserInfoByUids($params['uid'], 'activity_periods');
        $activity_periods = $user[0]['activity_periods'] ?? '';
        if (empty($activity_periods)) {
            return throwResponse($result);
        }

        // 解析活动期数
        $res = analysisActivityPeriods($activity_periods);
        $aid = $res['aid'];
        $periods = $res['periods'];
        if ($periods != $params['period']) {
            return throwResponse($result);
        }

        // 查询活动套餐价格
        $package = Db::table("vh_marketing.vh_special_activity")
            ->alias('a')
            ->leftJoin("vh_marketing.vh_special_activity_package pkg",'pkg.activity_id=a.id')
            ->where([
                ['a.id','=',$aid],
                ['a.start_at','<=',$time],
                ['a.end_at','>',$time],
                ['pkg.periods','=',$periods],
                ['pkg.package_id','=',$params['package_id']],
            ])
            ->field('pkg.*')
            ->findOrEmpty();

        if (!empty($package)) {
            $package['activity_periods'] = strval($activity_periods);
            $package['price'] = floatval($package['price']);
            $result['package'] = $package;
        }

        return throwResponse($result);
    }

    /**
     * 跨境套餐根据SKU拆分为不同的子套餐
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function split(Request $request): \think\Response
    {
        $params = $request->get();
        if (empty($params['package_id']) || !is_numeric($params['package_id'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择套餐ID');
        }
        // 获取套餐信息
        $package_ser = new PeriodsPackage(2);
        $result = $package_ser->split($params['package_id']);
        if (!is_array($result)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $result);
        }
        return throwResponse($result);
    }

    /**
     * 删除套餐
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function del(Request $request): \think\Response
    {
        $params = $request->param();
        if (!isset($params['period_id']) || $params['period_id'] == '') {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择期数');
        }
        if (!isset($params['periods_type']) || !is_numeric($params['periods_type'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择期数频道');
        }
        if (empty($params['package_id']) || !is_numeric($params['package_id'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择套餐ID');
        }
        // 获取套餐信息
        $package_ser = new PeriodsPackage((int)$params['periods_type']);
        $result = $package_ser->del($params);

        if ($result !== true) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $result);
        }
        return throwResponse();
    }


    /**
     * 自选专题活动创建自选套餐
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function createCustomActivityPackage(Request $request): \think\Response
    {
        $params = $request->param();
        if (!isset($params['id']) || $params['id'] == '') {
            return throwResponse([], ErrorCode::PARAM_ERROR, '活动ID不能为空');
        }
        if (!isset($params['custom_quantity']) || $params['custom_quantity'] == '') {
            return throwResponse([], ErrorCode::PARAM_ERROR, '活动自选数量不能为空');
        }
        $package_ser = new PeriodsPackage(0);
        $result = $package_ser->createCustomActivityPackage($params);

        if (!is_array($result)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $result);
        }

        return throwResponse($result);
    }

    /**
     * 根据期数获取支持自选套餐
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getCustomActivityPackage(Request $request): \think\Response
    {
        $params = $request->param();
        if (!isset($params['period_id']) || $params['period_id'] == '') {
            return throwResponse([], ErrorCode::PARAM_ERROR, '期数不能为空');
        }

        $period = Es::name('periods')->where([
            ['id', '=', $params['period_id']],
        ])->field('id,periods_type')->find();
        $period_info = (new \app\service\Periods(intval($period['periods_type'] ?? 0)))->getOne(intval($params['period_id']), 'id,title,detail,onsale_status,sort,product_img');

        $package_list = Es::name('periods_set')->where([
            ['period_id', '=', $params['period_id']],
            ['is_hidden', '=', 0],
        ])->select()->toArray();

        $product_info = Db::name('periods_product_inventory')
            ->where('period', $params['period_id'])
            ->where('is_use_comment', 0)
            ->column('product_id,product_name,en_product_name', 'product_id');

        $list = [];
        foreach ($package_list as $v) {
            $associated_products = json_decode($v['associated_products'], true);
            if (count($associated_products) > 1 || !empty($v['is_hidden_package']) || !empty($v['is_mystery_box']) || !empty($v['is_custom_package'])) {
                continue;
            }
            foreach ($associated_products as $ap) {
                if (empty($ap['product_id']) || is_array($ap['product_id']) || $ap['nums'] > 1) {
                    continue 2;
                }
            }
            $product_id = $associated_products[0]['product_id'] ?? 0;
            $v['product_name'] = $product_info[$product_id]['product_name'] ?? '';
            $v['en_product_name'] = $product_info[$product_id]['en_product_name'] ?? '';
            $list[] = $v;
        }

        return throwResponse([
            'period_info' => $period_info,
            'list' => $list
        ]);
    }

}