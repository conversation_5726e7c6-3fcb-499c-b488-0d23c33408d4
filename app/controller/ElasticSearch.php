<?php

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\Request;
use Elasticsearch\ClientBuilder;
use think\App;
use think\facade\Db;

class ElasticSearch extends BaseController
{

    private $hosts = [];

    public function __construct()
    {
        $this->hosts[] = [
            'host' => env('ES.HOST','127.0.0.1'),
            'port' => env('ES.PORT',9200),
            'user' => env('ES.USER','root'),
            'pass' => env('ES.PASS','vinehoo666')
        ];
    }

    /**
     * 获取期数操作员
     */
    public function getPeriodOperator(Request $request): \think\Response
    {
        $param = $request->get();
        // 获取筛选项字段
        $fields = ['id', 'creator_name','buyer_name', 'operation_name', 'operation_review_name'];
        $esWhere['bool']['must'][] = ['match' => ['id' => $param['period']]];

        $params = [
            'index' => 'vinehoo.periods',
            'type' => '_doc',
            '_source' => $fields,
            'body' => [
                'query' => $esWhere,
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($this->hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);
        $list = [];
        if (isset($data['list'][0])) {
            $list = $data['list'][0];
        }
        return throwResponse($list);
    }

    /**
     * 根据期数查询套餐
     * @param Request $request
     * @return \think\Response
     */
    public function getPeriodPackages(Request $request): \think\Response
    {
        $period = (int)$request->get('period', 0);
        $esWhere['bool']['must'][] = ['match' => ['period_id' => $period]];
        if (empty($period)) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '请输入期数');
        }
        $params = [
            'index' => 'vinehoo.periods_set',
            'type' => '_doc',
            'body' => [
                'query' => $esWhere,
            ],
            'size' => 10000,
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($this->hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);
        return throwResponse($data['list']);
    }

    /**
     * 查询 es 商品详情
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPeriodInfo(Request $request): \think\Response
    {
        $param = $request->get();
        if (!isset($param['period']) || empty($param['period'])) {
            return throwResponse('参数错误');
        }
        // 获取筛选项字段
        $fields = ['id', 'title', 'brief', 'periods_type', 'product_id', 'banner_img', 'product_img'];
        $esWhere['bool']['must'][] = ['match' => ['id' => $param['period']]];
        $params = [
            'index' => 'vinehoo.periods',
            'type' => '_doc',
            '_source' => $fields,
            'body' => [
                'query' => $esWhere,
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($this->hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);
        if (!empty($data['list'])) {
            // 查询套餐
            $package_ser = new \app\service\Package((int)$data['list'][0]['periods_type']);
            $data['list'][0]['package'] = $package_ser->packageProductList(['period' => $param['period']]);
            // 查询产品
            $product_ser = new \app\service\Products();
            $product_list = $product_ser->getListById($data['list'][0]['product_id']);
            $data['list'][0]['product_list'] = $product_list;
            //查询产品详细
            $periods_ser = new \app\service\Periods((int)$data['list'][0]['periods_type']);
            $period_info = $periods_ser->getOne((int)$param['period'], 'detail');
            $data['list'][0]['detail'] = $period_info['detail'] ?? '';
            // 图片域名
            if ($data['list'][0]['banner_img']) {
                $data['list'][0]['banner_img'] = env('ALIURL').$data['list'][0]['banner_img'];
            }
            if ($data['list'][0]['product_img']) {
                $data['list'][0]['product_img'] = explode(",", $data['list'][0]['product_img']);
                foreach ($data['list'][0]['product_img'] as &$v) {
                    $v = env('ALIURL').$v;
                }
                unset($v);
            }

        }

        $period_info = $data['list'][0] ?? [];
        return throwResponse($period_info);
    }

    /**
     * 根据期数 id 获取期数
     * @param Request $request
     * @return \think\Response
     */
    public function getPeriodList(Request $request) {
        $param = $request->get();
        $esWhere['bool']['must'] = [];
        if (isset($param['period_ids']) && !empty($param['period_ids'])) {
            $ids = explode(',', $param['period_ids']);
            $esWhere['bool']['must'][] = ['terms' => ['id' => $ids]];
        }
        if (isset($param['onsale_status']) && !empty($param['onsale_status'])) {
            $status_ids = explode(',', $param['onsale_status']);
            $esWhere['bool']['must'][] = ['terms' => ['onsale_status' => $status_ids]];
        }
        $params = [
            'index' => 'vinehoo.periods',
            'type' => '_doc',
            '_source' => [],
            'body' => [
                'query' => $esWhere,
                'size' => 1000
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($this->hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);
        if (!empty($data['list'])) {
            foreach ($data['list'] as &$v) {
                if ($v['banner_img']) {
                    $v['banner_img'] =  env('ALIURL').$v['banner_img'];
                }
            }
            unset($v);
        }
        return throwResponse($data['list']);
    }

    /**
     * 根据套餐 id 返回套餐产品名称及数量
     * @param Request $request
     * @return \think\Response
     */
    public function getOrderPackage(Request $request): \think\Response
    {
        $package_id = (int) $request->get('package_id', 0);
        $num = (int) $request->get('num', 1);
        $main_order_no = $request->get('main_order_no', '');
        $esWhere['bool']['must'] = [];
        $esWhere['bool']['must'][] = ['match' => ['id' => $package_id]];
        $params = [
            'index' => 'vinehoo.periods_set',
            'type' => '_doc',
            '_source' => [],
            'body' => [
                'query' => $esWhere,
                'size' => 1000
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($this->hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);
        $list = $data['list'];
        $order_product = [];
        if (!empty($list)) {
            // 盲盒
            if (!empty($main_order_no) && !empty($list[0]['is_mystery_box'])) {
                $order_mystery_box = Db::table('vh_orders.vh_order_mystery_box_log')
                    ->where('main_order_no', $main_order_no)
                    ->where('package_id', $package_id)
                    ->value('product_info');
                $product_json = json_decode($order_mystery_box ?? '[]', true);

            } else {// 非盲盒
                $product_json = json_decode($list[0]['associated_products'], true);
            }
            
            $product_id = [];
            foreach ($product_json as $val) {
                array_push($product_id, $val['product_id']);
            }
            unset($val);
            $product_url = env('ITEM.WINE_WIKI_URL').'/wiki/v3/product/fieldsdatarr';
            $field  = 'id,cn_product_name,en_product_name,short_code,country_id,product_type,capacity,
                product_keywords_id';
            $product_info = post_url($product_url, ['ids' => $product_id, 'fields' => $field]);
            if (!is_null(json_decode($product_info))) {
                $result_pro = json_decode($product_info, true);
                if ($result_pro['error_code'] == 0) {
                    // 产品数据
                    $product_list = $result_pro['data']['list'];
                    foreach ($product_list as $val) {
                        foreach ($product_json as $v) {
                            if ($val['id'] == $v['product_id']) {
                                array_push($order_product, ['cn_product_name' => $val['cn_product_name'],
                                    'en_product_name'=>$val['en_product_name'],
                                    'short_code'=>$val['short_code'] ?? '',
                                    'nums' => $v['nums'] * $num
                                ]);
                            }
                        }
                    }
                }
            }
        }
        return throwResponse($order_product);
    }

    /**
     * 根据套餐 id 获取套餐
     * @param Request $request
     * @return \think\Response
     */
    public function getPackageList(Request $request) {
        $param = $request->get();
        $esWhere['bool']['must'] = [];
        if (isset($param['package_ids']) && !empty($param['package_ids'])) {
            $ids = explode(',', $param['package_ids']);
            $esWhere['bool']['must'][] = ['terms' => ['id' => $ids]];
        }
        $params = [
            'index' => 'vinehoo.periods_set',
            'type' => '_doc',
            '_source' => [],
            'body' => [
                'query' => $esWhere,
                'size' => 1000
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($this->hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);
        return throwResponse($data['list']);
    }

    /**
     * 根据期数获取订单
     * @param Request $request
     * @return \think\Response
     */
    public function getOrderByPeriod(Request $request): \think\Response
    {
        $param = $request->get();
        $es_ser = new \app\service\ElasticSearch();
        $result = $es_ser->getOrderByPeriod((int)$param['period']);
        return throwResponse($result);
    }

}