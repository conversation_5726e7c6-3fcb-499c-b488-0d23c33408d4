<?php
declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\validate\BannedWord as BannedWordVal;
use app\service\BannedWord as BannedWordSer;

class BannedWord extends BaseController
{

    /**
     * 添加违禁词
     *
     * @param Request $request
     * @return \think\Response
     */
    public function create(Request $request)
    {
        // 参数
        $params = $request->post();
        // 参数验证
        $validate = new BannedWordVal();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $params['created_time'] = time();
        // 添加违禁词
        $banned_word = new BannedWordSer();
        $result = $banned_word->create($params);

        if (!$result) {
            return throwResponse($result, ErrorCode::EXEC_ERROR, '添加违禁词失败');
        }

        return throwResponse($result);

    }

    /**
     * 删除违禁词
     *
     * @param Request $request
     * @return \think\Response
     */
    public function delete(Request $request)
    {
        // 参数
        $params = $request->post();
        // 参数验证
        $del_validate = $this->updateValidate($params);
        if (!empty($del_validate)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $del_validate);
        }
        // 删除违禁词
        $banned_word = new BannedWordSer();
        $result = $banned_word->delete( (int) $params['id']);

        if (!$result) {
            return throwResponse($result, ErrorCode::EXEC_ERROR, '添加违禁词失败');
        }

        return throwResponse($result);

    }

    /**
     * 违禁词列表
     *
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list()
    {
        $banned_word = new BannedWordSer();
        $result = $banned_word->list();
        return throwResponse($result);
    }

    /**
     * 删除违禁词参数验证
     *
     * @param array $params
     * @return string
     */
    protected function updateValidate(array $params): string
    {
        // 提示信息
        $message = '';

        // 违禁词 id 验证
        if (!isset($params['id']) || empty($params['id'])) {
            $message .= '请选择违禁词';
        }

        return $message;
    }

}