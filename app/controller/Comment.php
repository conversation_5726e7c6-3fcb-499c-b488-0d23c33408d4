<?php
declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\model\PeriodsComment;
use app\model\PeriodsProductInventory;
use app\model\StatisticsPeriod;
use app\Request;
use app\service\Comment as CommentSer;
use think\facade\Db;
use think\Validate;
use app\validate\Comment as CommentV;

class Comment extends BaseController
{

    /**
     * 获取期数评论
     * @param Request $request
     * @return \think\Response
     */
    public function getComment(Request $request): \think\Response
    {
        $params = $request->get();
        $comm_server = new CommentSer();
        $list = $comm_server->getComment($params);
        $result['total'] = $list->total() ?? 0;
        $result['list'] = $list->getCollection() ?? [];
        return throwResponse($result);
    }

    /**
     * 根据简码获取期数评论
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPeriodsByShortCode(Request $request): \think\Response
    {
        $params = $request->get();
        $comm_server = new CommentSer();
        $list = $comm_server->getPeriodsByShortCode($params);
        return throwResponse($list);
    }

    /**
     * 根据期数，产品简码查询评论
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getCommentByPeriodAndShortCode(Request $request): \think\Response
    {
        $params = $request->get();
        $comm_server = new CommentSer();
        $list = $comm_server->getCommentByPeriodAndShortCode($params);
        return throwResponse($list);
    }

    /**
     * 更新评论
     * @param Request $request
     * @return \think\Response
     */
    public function upComment(Request $request): \think\Response
    {
        $params = $request->post();
        $comm_server = new CommentSer();
        $result = $comm_server->upPeriodComment($params);
        return throwResponse($result);
    }

    /**
     * 审核商品评论
     * @param Request $request
     * @return \think\Response
     */
    public function periodCommentAudit(Request $request): \think\Response
    {
        $params = $request->post();
        if (!isset($params['id'])) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '请选择评论');
        }
        if (!isset($params['audit_status'])) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '请选择审核状态');
        }
        $comm_server = new CommentSer();
        $data['audit_status'] = $params['audit_status'];
        $data['update_time'] = time();
        if ($params['audit_status'] == 4) {
            $data['is_recommend'] = 0;
            $data['audit_status'] = 0;
        } elseif ($params['audit_status'] == 1) {
            $data['is_recommend'] = 1;
        }
        $data['id'] = $params['id'];
        $result = $comm_server->periodCommentAudit($data);
        return throwResponse($result);

    }


    /**
     * 查询评论用户信息
     * @param Request $request
     * @return \think\Response
     */
    public function getUserCommentInfo(Request $request): \think\Response
    {
        $params = $request->get();
        $comm_server = new CommentSer();
        $list = $comm_server->getUserCommentInfo($params);
        return throwResponse($list);
    }


    /**
     * 后台评论管理添加评论
     * @param Request $request
     * @return \think\Response
     */
    public function createComment(Request $request): \think\Response
    {
        $params = $request->post();
        // 验证基本输入
        $validate = new CommentV();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $params['created_time'] = time();
        if (!isset($params['audit_status']) || $params['audit_status'] != '0') {
            $params['audit_status'] = empty($params['automatic_task']) ? 1 : 0;
        }
        if (!isset($params['is_recommend']) || $params['is_recommend'] != '0') {
            $params['is_recommend'] = 1;
        }
        $comm_server = new CommentSer();
        $result = $comm_server->createComment($params);
        return throwResponse((int)$result);
    }


    /**
     * 评论自动任务回调
     * @param Request $request
     * @return \think\Response
     */
    public function automaticTaskComment(Request $request): \think\Response
    {
        $id = (int)$request->post('comment');
        $audit_status = $request->post('audit_status');
        $task_id = $request->post('task_id');
        if (empty($id)) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '未传递期数');
        }

        // 更新任务状态
        $other_ser = new \app\service\Other();
        $other_ser->updateTaskStatus($task_id);

        // 更新评论状态
        $comm_server = new CommentSer();
        $data['id'] = $id;
        $data['audit_status'] = $audit_status;
        $result = $comm_server->upPeriodComment($data);
        return throwResponse($result);

    }

    /**
     * 用户评论列表
     * @param Request $request
     * @return \think\Response
     */
    public function getCommentList(Request $request): \think\Response
    {
        $params = $request->get();
        $comm_server = new CommentSer();
        $result = $comm_server->getCommentList($params);
        return throwResponse($result);
    }

    /**
     * 评论添加喜欢
     * @param Request $request
     * @return \think\Response
     */
    public function likeComment(Request $request): \think\Response
    {
        $params = $request->post();
        $comm_server = new CommentSer();
        $uid = $request->header('vinehoo-uid', '0');
        $data['period'] = $params['period'] ?? 0;
        // 商品频道
        $periods_type = PeriodsProductInventory::where('period', $data['period'])->value('periods_type');
        $period = $data['period'];
        if (!empty($periods_type) && $periods_type == 9) {
            $period_id = Db::name('periods_second_merchants')
                ->where('id', $period)
                ->value('join_period_id');
            if (!empty($period_id)) {
                //关联秒发期数
                $data['period'] = $period_id;
            }
        }

        $data['uid'] = $uid;
        $data['comment_id'] = $params['comment_id'] ?? 0;
        $data['created_time'] = time();
        $data['action'] = $params['action'] ?? 0;
        $result = $comm_server->likeComment($data);
        $params['comment_nickname'] = '';
        // 自己点赞不推送消息
        if ($params['comment_uid'] != $uid && intval($data['action']) === 0) {
            // 是否推送
            $is_push = 1;
            if ($params['comment_uid'] > 0) {
                $user_url = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
                $user_url = $user_url . '?uid=' . $params['comment_uid'] . ',' . $uid .
                    '&field=uid,user_level,avatar_image,nickname,certified_info, is_pushconbtn';
                $user_list = get_url($user_url);
                $user_list = get_interior_http_response($user_list);
                $user_list = $user_list['list'] ?? [];
                $params['comment_nickname'] = $user_list[0]['nickname'] ?? '';
                $params['nickname'] = $user_list[1]['nickname'] ?? '';
                $is_push = $user_list[0]['is_pushconbtn'] ?? '';
            }
            // 点赞消息推送
            $url = env('ITEM.APPPUSH_URL') . '/apppush/v3/push/single';
            if ($data['comment_id'] == $data['uid']) {
                $is_push = 0;
            }
            $push_data = [
                'is_push' => $is_push,
                'uid' => $params['comment_uid'],
                'title' => '评论点赞',
                'content' => $params['nickname'] . '点赞了您的评论',
                'data_type' => 14,
                'custom_param' => ['id' => $period],
                'data' => [
                    'content' => getsre($params['content'], 100),
                    'nickname' => $params['nickname'],
                    'type_title' => '你的评论',
                    'type' => 2
                ],
                'label' => 'GoodsDetail',
            ];
            $push_result = post_url($url, $push_data);
            $push_result = json_decode($push_result, true);
            if ($push_result['error_code'] != 0) {
                return throwResponse($push_result['data'], ErrorCode::PARAM_ERROR, $push_result['error_msg']);
            }
        }
        return throwResponse($result);
    }

    /**
     * v2 评论数据变通同步 v3 评论
     * @param Request $request
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function v2SyncV3Comment(Request $request)
    {
        $params = $request->post();
        $comm_server = new CommentSer();
        // 添加评论或添加回复
        if ($params['type'] == 'INSERT') {
            $comment_list = [];
            // 添加评论
            if ($params['table'] == 'wy_comment') {
                foreach ($params['data'] as $val) {
                    $c['id'] = $val['comment_id'];
                    $c['uid'] = $val['uid'];
                    $c['uname'] = $val['nickname'];
                    $c['period'] = $val['product_id'];
                    $c['content'] = $val['content'];
                    $c['likenums'] = $val['zanum'];
                    $c['audit_status'] = $val['audit_status'];
                    $c['is_show'] = $val['is_show'];
                    $c['is_recommend'] = $val['is_better'];
                    $c['created_time'] = $val['add_time'];
                    $c['vest_is_buy'] = $val['is_buy'];
                    $c['first_id'] = 0;
                    $c['pid'] = 0;
                    $c['p_uid'] = 0;
                    array_push($comment_list, $c);
                    unset($c);
                }
            } elseif ($params['table'] == 'wy_comment_reply') {
                foreach ($params['data'] as $val) {
                    // 查询关联期数
                    $period = Db::connect('v2mysql')->table('wy_comment')
                        ->where('comment_id', $val['comment_id'])
                        ->value('product_id');
                    $c['uid'] = $val['uid'];
                    $c['uname'] = $val['nickname'];
                    $c['period'] = $period;
                    $c['content'] = $val['content'];
                    $c['likenums'] = $val['zanum'];
                    $c['audit_status'] = $val['audit_status'];
                    $c['is_show'] = $val['is_show'];
                    $c['is_recommend'] = $val['is_better'];
                    $c['created_time'] = $val['add_time'];
                    $c['vest_is_buy'] = $val['is_buy'];
                    $c['first_id'] = $val['comment_id'];
                    $c['pid'] = $val['comment_id'];
                    $c['p_uid'] = $val['replyuser_id'];
                    array_push($comment_list, $c);
                    unset($c);
                }
            }
            if (!empty($comment_list)) {
                $comm_server->createCommentAll($comment_list);
            }
        } elseif ($params['type'] == 'UPDATE') {
            // 添加评论
            if ($params['table'] == 'wy_comment') {
                foreach ($params['data'] as $val) {
                    $c['id'] = $val['id'];
                    $c['uid'] = $val['uid'];
                    $c['uname'] = $val['nickname'];
                    $c['period'] = $val['product_id'];
                    $c['content'] = $val['content'];
                    $c['likenums'] = $val['zanum'];
                    $c['audit_status'] = $val['audit_status'];
                    $c['is_show'] = $val['is_show'];
                    $c['is_recommend'] = $val['is_better'];
                    $c['created_time'] = $val['add_time'];
                    $c['vest_is_buy'] = $val['is_buy'];
                    $c['first_id'] = 0;
                    $c['pid'] = 0;
                    $c['p_uid'] = 0;
                    $comm_server->upPeriodComment($c);
                    unset($c);
                }
            } elseif ($params['table'] == 'wy_comment_reply') {
                foreach ($params['data'] as $val) {
                    $c['id'] = $val['id'];
                    $c['uid'] = $val['uid'];
                    $c['uname'] = $val['nickname'];
                    $c['period'] = $val['product_id'];
                    $c['content'] = $val['content'];
                    $c['likenums'] = $val['zanum'];
                    $c['audit_status'] = $val['audit_status'];
                    $c['is_show'] = $val['is_show'];
                    $c['is_recommend'] = $val['is_better'];
                    $c['created_time'] = $val['add_time'];
                    $c['vest_is_buy'] = $val['is_buy'];
                    $c['first_id'] = 0;
                    $c['pid'] = 0;
                    $c['p_uid'] = 0;
                    $comm_server->upPeriodCommentByReplyId($c);
                    unset($c);
                }
            }
        }
    }

    /**
     * 更新评论总数
     * @param Request $request
     * @return \think\Response
     */
    public function updateCommentCount(Request $request): \think\Response
    {
        $period = (int)$request->post('period', 0);
        if (empty($period)) {
            return throwResponse($period, ErrorCode::PARAM_ERROR, '期数错误');
        }

        // 查询统计统计期数是否存在
        $sss = StatisticsPeriod::where('period', $period)->value('id');
        if (!$sss) {
            // 创建统计
            StatisticsPeriod::create(['period' => $period, 'periods_type' => 0]);
        }
        // 当期总评论数
        $c = PeriodsComment::where(['period' =>$period, 'is_recommend' => 1, 'audit_status' => 1])->column('uid');

        $cc = count($c);
        // 小编评论数
        $xb = 0;
        if (!empty($c)) {
            $c_str = implode(',', $c);
            $c_url = env('ITEM.USER_URL').'/user/v3/vestuser/uidQuery?uid='. $c_str;
            $user_url = get_url($c_url);
            $user_list = get_interior_http_response($user_url);
            $xb = $user_list['count'] ?? 0;
        }
        // 总评论数
        $is_show_count = 0;
//        $c_c = PeriodsProductInventory::where(['b.period' =>$period, 'b.is_recommend' => 1, 'b.audit_status' => 1])
//            ->alias('a')
//            ->join('periods_comment b', 'a.period = b.period')
//            ->field('a.short_code, a.period')
//            ->group('a.short_code')
//            ->select()
//            ->map(function ($item) use (&$is_show_count) {
//                $is_show_count += PeriodsComment::where(['b.short_code' => $item->short_code, 'is_show' => 1])
//                    ->alias('a')
//                    ->join('periods_product_inventory b', 'a.period = b.period')
//                    ->count();
//                // 产品已购瓶数
//                return $item;
//            })->toArray();
        $product_id = PeriodsProductInventory::where('period', $period)->column('product_id');
        $periods_id = PeriodsProductInventory::whereIn('product_id', $product_id)->column('period');
        $is_show_count = PeriodsComment::alias('a')
            ->leftJoin('periods_product_inventory b', 'a.period = b.period')
            ->where([
                ['a.period', '=', $period],
                ['a.pid', '=', 0],
                ['a.audit_status', '=', 1],
                ['a.is_recommend', '=', 1],
//                ['a.is_show', '=', 1],
            ])
            ->whereOr([[['a.is_show', '=', 1], ['a.period', 'in', $periods_id], ['a.audit_status', '=', 1],
                ['a.is_recommend', '=', 1]]])
            ->field('a.id, a.uid, a.first_id, a.pid, a.p_uid, a.period, a.content, a.likenums, a.audit_status, 
            a.emoji_image, a.created_time, a.hot_num, a.is_recommend,a.is_show')
            ->order(['hot_num' => 'desc', 'created_time' => 'desc'])
            ->group('a.id')
            ->count();
        // 本期等于 本期评论-小编评论
        $cc = $cc - $xb;
        $result = StatisticsPeriod::where('period', $period)->update([
            'notes_user_this' => $cc,
            'notes_admin_this' => $xb,
            'notes_all' => $is_show_count
        ]);
        return throwResponse($result);

    }

}