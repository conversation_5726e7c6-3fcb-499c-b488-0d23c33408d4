<?php

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\service\InventoryService;
use app\Request;

class Inventory extends BaseController
{
    /**
     * 库存调整
     * <AUTHOR>
     * @Date 2023/12/14
     * @param Request $request
     * @return \think\Response
     */
    public function update(Request $request): \think\Response
    {
        $params = $request->param();
        $params['user_id'] = $request->header('vinehoo-uid');
        $service = new InventoryService();
        $re = $service->update($params);
        if ($re !== true) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $re);
        }
        return throwResponse();
    }

    /**
     * 库存调整审批回调
     * <AUTHOR>
     * @Date 2023/12/14
     * @param Request $request
     * @return \think\Response
     */
    public function callbackUpdate(Request $request): \think\Response
    {
        $params = $request->param();
        $service = new InventoryService();
        $re = $service->callbackUpdate($params);
        if ($re !== true) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $re);
        }
        return throwResponse();
    }

    /**
     * 查询修改库存提示
     * <AUTHOR>
     * @Date 2023/12/14
     * @param Request $request
     * @return \think\Response
     */
    public function queryPrompt(Request $request): \think\Response
    {
        $params       = $request->get();
        $labelService = new InventoryService();
        $result       = $labelService->queryPrompt($params);
        return throwResponse($result);
    }
}