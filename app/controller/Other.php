<?php
declare(strict_types=1);

namespace app\controller;


use app\BaseController;
use app\ErrorCode;
use app\model\PeriodsGroup;
use app\model\PeriodsInventoryAlert;
use app\model\PeriodsProductInventory;
use app\model\PeriodsTaskLog;
use app\model\V2WyCareas;
use app\model\V2WySalesGoodsBase;
use app\Request;
use app\service\AliSendEmail;
use app\service\Other as ServiceOther;
use app\validate\Fictitious;
use app\validate\GroupOrderInventory;
use think\facade\Db;

class Other extends BaseController {

    /**
     * 获取可筛选商品国家、类型、关键词
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function filterList() {
        $ser = new ServiceOther();
        $list = $ser->filterList();
        return throwResponse(['list' => $list]);
    }


    /**
     * 获取仓库列表
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function fictitiousList(Request $request) {
        $ser = new ServiceOther();
        $params = $request->get();
        $list = $ser->fictitiousList($params);
        return throwResponse($list);
    }

    /**
     * 添加仓库
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function fictitiousAdd(Request $request) {
        $params = $request->post();
        $validate = new Fictitious();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $ser = new ServiceOther();
        $list = $ser->fictitiousAdd($params);
        if (!$list['status']) {
            return throwResponse([], ErrorCode::EXEC_ERROR, $list['msg']);
        }
        return throwResponse($list['data']);
    }


    /**
     * 同步萌芽数据
     * @return array|\think\Response
     */
    public function syncFictitious() {
        // 获取实体仓库
        $q['store_code'] = 'xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa';
        $sync_my = post_url(env("ITEM.DISTRIBUTE_URL") . '/query/warehouse/allList', $q);
        $sync_my = json_decode($sync_my, true);
        if ($sync_my['errorCode'] != '0') {
            return serviceReturn(false, $q, '未获取到同步数据'. $sync_my['msg']);
        }
        $ser = new ServiceOther();
        // 是否正常返回数据
        if (!empty($sync_my['data'])) {
            foreach ($sync_my['data'] as $val) {
                // 查看实体仓是否存在，不存在则添加实体仓
                $w['fictitious_id'] = $val['id'] ?? null;
                $w['is_my'] = 1;
                $w['fictitious_name'] = $val['store_name'];
                $w['fictitious_pid'] = 0;
                $w['store_code'] = $val['store_code'];
                $w['channel_types'] = '';
                $w['created_time'] = time();
                $r = $ser->syncFictitious($w);
                unset($w);
                // 查询虚拟仓
                if ($val['store_code']) {
                    $fq['store_code'] = $val['store_code'];
                    $sync_f = post_url(env("ITEM.DISTRIBUTE_URL") . '/query/getFictitious', $q);
                    $sync_f = json_decode($sync_f, true);
                    if ($sync_f['errorCode'] != '0') {
                        echo $sync_f['msg'];
                    }
                    // 添加虚拟仓
                    if (!empty($sync_f['data'])) {
                        foreach ($sync_f['data'] as $v)  {
                            $f['fictitious_id'] = $v['fictitious_id'] ?? null;
                            $f['is_my'] = 1;
                            $f['fictitious_name'] = $v['fictitious_name'];
                            $f['fictitious_pid'] = $val['id'] ?? null;;
                            $f['store_code'] = '';
                            $f['channel_types'] = '';
                            $f['created_time'] = time();
                            $fr = $ser->syncFictitious($f);
                            if ($fr['status'] == false) {
                                continue;
                            }
                            unset($f);
                        }
                    }
                }
            }
        }
        return throwResponse(true);
    }

    /**
     * 修改仓库
     * @param Request $request
     * @return \think\Response
     */
    public function fictitiousUp(Request $request) {
        $ser = new ServiceOther();
        $params = $request->post();
        $validate = new Fictitious();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        if (!isset($params['id']) || empty($params['id'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择仓库');
        }
        $list = $ser->fictitiousUp((int)$params['id'], $params);
        return throwResponse($list);
    }

    /**
     * 操作期数状态操作列表
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function periodsStatusChangeRecord(Request $request) {
        $period = (int)$request->get('period', '');
        if (!$period) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '期数参数错误');
        }
        $ser = new ServiceOther();
        $list = $ser->periodsStatusChangeRecord($period);
        return throwResponse($list);
    }

    /**
     * 添加拼团库存数据
     * @param Request $request
     * @return \think\Response
     */
    public function createGroupOrderInventory(Request $request) {
        $ser = new ServiceOther();
        $params = $request->post();
        $validate = new GroupOrderInventory();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $result = $ser->createGroupOrderInventory($params);
        if (!$result) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '添加失败');
        }
        return throwResponse($result);
    }

    /**
     * 复制期数
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function copyPeriod(Request $request) {
        $ser = new ServiceOther();
        $params = $request->get();
        // 添加套餐编辑人
        $params['user_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['user_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['user_name']) {
            $params['user_name'] = base64_decode($params['user_name']);
        }

        $params['gid'] = $this->getGeneratorID(1);
        if (!$params['gid']) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '生成期数失败');
        }

        $result = $ser->copyPeriod($params);
        if (!$result['status']) {
            return throwResponse([], ErrorCode::EXEC_ERROR, $result['msg']);
        }
        return throwResponse($result['data']);


    }

    public function syncLeftover(Request $request) {
        $ser    = new ServiceOther();
        $params = $request->param();
        // 添加套餐编辑人
        $params['user_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['user_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['user_name']) {
            $params['user_name'] = base64_decode($params['user_name']);
        }

        $params['gid'] = $this->getGeneratorID(1);
        if (!$params['gid']) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '生成期数失败');
        }

        $params['periods_type'] = 0;
        $params['to_periods'] = 3;

        $result = $ser->syncLeftover($params);
        if (!$result['status']) {
            return throwResponse([], ErrorCode::EXEC_ERROR, $result['msg']);
        }
        return throwResponse($result['data']);


    }

    /**
     * 用户商品相关个人信息
     * @param Request $request
     * @return \think\Response
     */
    public function getUserPeriodsInfo(Request $request) {
        $ser = new ServiceOther();
        $user_id = (int)$request->header('vinehoo-uid', '0');
        // 获取用户收藏数
        $collection_count = $ser->getUserCollectionCount($user_id);
        // 用户足迹数 TODO
        $footprint_count = 0;
        $data['collect_nums'] = $collection_count;
        $data['footprint_nums'] = $footprint_count;
        return throwResponse($data);
    }

    /**
     * 查询套裁商品详细
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPackageProductInfo(Request $request): \think\Response
    {
        $ser = new ServiceOther();
        $period = (int)$request->get('period');
        $product_id = (int)$request->get('product_id');
        $field = (string)$request->get('field');
        if (!isset($period) || empty($period)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '期数必传');
        }
        if (!isset($product_id) || empty($product_id)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '产品必传');
        }
        if (strlen($field) > 40) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '查询字段过长');
        }
        $result = $ser->getPackageProductInfo($period, $product_id, $field);
        return throwResponse($result);
    }


    /**
     * 更新套餐绑定产品信息
     * @param Request $request
     * @return \think\Response
     */
    public function updatePackageProductInfo(Request $request): \think\Response
    {
        $ser = new ServiceOther();
        $id = (int) $request->post('id');
        $params = $request->post();
        if (!$id) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '产品必选');
        }
        $data = [];
        if (isset($params['costprice'])) {
            $data['costprice'] = $params['costprice'];
        }
        $result = $ser->updatePackageProductInfo($id, $data);
        return throwResponse($result);

    }

    /**
     * 磐石更新数据
     */
    public function updatePeriodsProductInfo(Request $request): \think\Response
    {
        $product_id = $request->post('id', 0);
        if ($product_id < 1) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '参数错误');
        }
       if ($request->post('created_time') == $request->post('update_time')) {
           return throwResponse([], ErrorCode::PARAM_ERROR, '添加无需更新');
       }
        $periods = PeriodsProductInventory::where('product_id', $product_id)
            ->field('id,period,periods_type')
            ->select()->toArray();
        foreach ($periods as $val) {
            $periods_ser = new \app\service\Periods($val['periods_type']);
            $periods_product = $periods_ser->getOne($val['period'], 'product_id');
            if (!empty($periods_product)) {
                $p_id = explode(',', $periods_product['product_id']);
                $product_url = env('ITEM.WINE_WIKI_URL').'/wiki/v3/product/fieldsdatarr';
                $field  = 'id,cn_product_name,en_product_name,short_code,country_id,product_type,capacity,
                product_keywords_id';
                $product_info = post_url($product_url, ['ids' => $p_id, 'fields' => $field]);
                if (!is_null(json_decode($product_info))) {
                    $result_pro = json_decode($product_info, true);
                    if ($result_pro['error_code'] == 0) {
                        // 产品数据
                        $product_list = $result_pro['data']['list'];
                        // 更新数据
                        $product_category = '';
                        $product_main_category = '';
                        $product_keyword = '';
                        $country = '';
                        $capacity = '';
                        foreach ($product_list as $v) {
                            $product_category .= $v['product_type_name']. ',';
                            $product_main_category .= $v['product_category_name']. ',';
                            $country .= $v['country_name']. ',';
                            $capacity .= $v['capacity']. ',';
                            foreach ($v['product_keywords_id'] as $v1) {
                                $product_keyword .= $v1['name']. ',';
                            }
                        }
                        $up_period['product_category'] = trim($product_category, ',');
                        $up_period['product_main_category'] = trim($product_main_category, ',');
                        $up_period['product_keyword'] = trim($product_keyword, ',');
                        $up_period['country'] = trim($country, ',');
                        $up_period['capacity'] = trim($capacity, ',');
                        $periods_ser->updateInfoById((int)$val['period'], $up_period);
                    } else {
                        return  throwResponse(null, ErrorCode::EXEC_ERROR, $result_pro['error_msg']);
                    }
                }
            }
        }

        return throwResponse([]);
    }

    /**
     * 更新期数产品库存
     * @param Request $request
     * @return \think\Response
     */
    public function updatePeriodsProductInventory(Request $request): \think\Response
    {
        $params = $request->post();
        $period = $params['period'] ?? 0;
        if ($period == 0) {
            return  throwResponse(null, ErrorCode::EXEC_ERROR, '请选择期数');
        }
        $product = $params['product_id'] ?? 0;
        if ($product == 0) {
            return  throwResponse(null, ErrorCode::EXEC_ERROR, '请选择产品');
        }
        $data = [];
        if (isset($params['costprice']) && !empty($params['costprice'])) {
            $data['costprice'] = (float) $params['costprice'];
        }
        $ser = new ServiceOther();
        if (!empty($data)) {
            $result = $ser->updatePeriodsProductInventory($period, $product, $data);
            return throwResponse($result);
        }
        return throwResponse(0);
    }

    /**
     * 更新期数产品信息(根据期数)
     * @param Request $request
     * @return \think\Response
     */
    public function updatePeriodsProductByPeriod(Request $request): \think\Response
    {
        $params = $request->post();
        $period = $params['period'] ?? 0;
        if ($period == 0) {
            return  throwResponse(null, ErrorCode::EXEC_ERROR, '请选择期数');
        }
        // 操作信息
        $params['creator_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['creator_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['creator_name']) {
            $params['creator_name'] = base64_decode($params['creator_name']);
        }
        $ser = new ServiceOther();
        if (!empty($params)) {
            $result = $ser->updatePeriodsProductByPeriod($period, $params);
            if ($result !== true) {
                return throwResponse([], ErrorCode::EXEC_ERROR, $result);
            }
        }
        return throwResponse();
    }

    /**
     * 获取期数产品信息列表
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPeriodsProductInventory(Request $request): \think\Response
    {
        $params = $request->get();
        $period = $params['period'] ?? 0;
        $ser = new ServiceOther();
        $result = $ser->getPeriodsProductInventory($period);
        return throwResponse($result);
    }


    /**
     * 发送邮件
     */
    public function sendEmail(Request $request): \think\Response
    {
        $params = $request->post();
        // type = 0 给采购发邮件
        $type = (int) $params['type'] ?? 0;
        $toAddress = $params['toAddress'] ?? '';
        $period = $params['period'] ?? 0;
        $task_id = $params['task_id'] ?? '';
        $periods_type = $params['periods_type'] ?? '';
        // 更新任务状态已执行
        if ($task_id) {
            PeriodsTaskLog::where('task_id', $task_id)->update(['is_exec' => 1]);
        }
        $period_ser = new \app\service\Periods($periods_type);
        $period_info = $period_ser->getOne($period,'id, title, predict_shipment_time');
        if (empty($period_info)) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '未获取到期数');
        }
        $data = [];
        switch ($type) {
            case 0:
                $data = [
                    "accountName" => "<EMAIL>",
                    "addressType" => 1,
                    "replyAddress" => "<EMAIL>",
                    "replyAddressAlias" => "酒云网",
                    "fromAlias" => "酒云网",
                    "textBody" => '期数: '.$period_info['id']. '  名称：'. $period_info['title'] . '预计发货时间：'.
                        $period_info['predict_shipment_time'] . '，请及时订货。',
                    "htmlBody" => '期数: '.$period_info['id']. '  名称：'. $period_info['title'] . '预计发货时间：'.
                        $period_info['predict_shipment_time'] . '，请及时订货。',
                    "subject" => "预售商品订货通知",
                    "toAddress" => $toAddress,
                    "replyToAddress" => "true"
                ];
                break;
        }
        $send_email_ser = new AliSendEmail();
//        $send_data = [
//            "accountName" => "<EMAIL>",
//            "addressType" => 1,
//            "replyAddress" => "<EMAIL>",
//            "replyAddressAlias" => "酒云网",
//            "fromAlias" => "酒云网",
//            "textBody" => "期数【商品名称】预计发货时间：XXXX ，请及时订货",
//            "htmlBody" => "期数【商品名称】预计发货时间：XXXX ，请及时订货",
//            "subject" => "预售商品订货通知",
//            "toAddress" => "<EMAIL>",
//            "replyToAddress" => "true"
//        ];
        $send_email_ser::main($data);
        return throwResponse(1);
    }

    /**
     * 拼团价格
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPeriodsGroupPrice(Request $request): \think\Response
    {
        $result = PeriodsGroup::whereIn('period', $request->get('periods'))->select();
        return throwResponse($result);
    }

    /**
     * 库存预警运维钉钉群推送
     * @param Request $request
     * @return mixed|\think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function pushInventoryAlert(Request $request) {
        $params = $request->post();
        $inventory_alert = PeriodsInventoryAlert::where([
            'period' => $params['period'],
            'product_id' => $params['product_id']
        ])->find();
        if ($inventory_alert || $params['inventory'] > 3 || $params['inventory_accum'] < 6) {
            return throwResponse(0);
        }
        // 查询期数名称
        $periods_ser = new \app\service\Periods((int)$params['periods_type']);
        $period_info = $periods_ser->getOne((int) $params['period'], 'title');
        // 查询产品名称
        $p_id = [$params['product_id']];
        $p_n = '';
        $p_en = '';
        $product_url = env('ITEM.WINE_WIKI_URL').'/wiki/v3/product/fieldsdatarr';
        $field  = 'id,cn_product_name,en_product_name,short_code,country_id,product_type,capacity,
                product_keywords_id';
        $product_info = post_url($product_url, ['ids' => $p_id, 'fields' => $field]);
        if (!is_null(json_decode($product_info))) {
            $result_pro = json_decode($product_info, true);
            if ($result_pro['error_code'] == 0) {
                // 产品数据
                $product_list = $result_pro['data']['list'];
                if (!empty($product_list)) {
                    $p_n = $product_list[0]['cn_product_name'];
                    $p_en = $product_list[0]['en_product_name'];
                }
            }
        }
        $period_title = $period_info['title'];
        // 推送 markdown 数据
        $str = "## **期数产品库存预警通知** 
- 期数：{$params['period']}
- 期数标题：{$period_title}
- 简码：{$params['short_code']}
- 产品中文名称：{$p_n}
- 产品英文名称：{$p_en}
- 剩余库存：{$params['inventory']}
        ";

        // markdown 详情
        $markdown['title'] = '期数产品库存预警通知';
        $markdown['text'] = $str;
        $markdown_str = base64_encode(json_encode($markdown));

        // -- 开始推送
        // 推送地址
        $queue_url = env('item.QUEUE_URL');
        // 请求头
        $url_header[] = 'vinehoo-client: tp6-commodities';
        $url_header[] = 'Content-Type: application/json;charset=utf-8';
        // 钉钉请求参数
//        $test_access_token = 'd44318c20312dd432ca3313431b2d1a7608203b9b01bdead09752a4f73ad66c9';
        $d_json['access_token'] = 'd44318c20312dd432ca3313431b2d1a7608203b9b01bdead09752a4f73ad66c9';
        $d_json['type'] = 'markdown';
        $d_json['at'] = '';
        $d_json['content'] = $markdown_str;

        $d_str = base64_encode(json_encode($d_json));
        // 推送请求参数
        $push_data['exchange_name'] = 'dingtalk';
        $push_data['routing_key'] = 'dingtalk_sender';
        $push_data['data'] = $d_str;
        $p_data = json_encode($push_data);
        $result = post_url($queue_url, $p_data, $url_header);
        // 添加推送记录
        $inventory_alert_add = PeriodsInventoryAlert::create([
            'period' => $params['period'],
            'product_id' => $params['product_id'],
            'short_code' => $params['short_code'],
            'created_time' => time()
        ]);
//        return json_decode($result, true);
        return throwResponse($result);
    }

    /**
     * 删除库存预警钉钉推送记录
     */
    public function delInventoryAlert(Request $request): \think\Response
    {
        $params = $request->post();
        if (!isset($params['action']) || $params['action'] != 'inc') {
            return throwResponse(0);
        }
        $inventory_alert = PeriodsInventoryAlert::where([
            'period' => $params['period_id'],
            'product_id' => $params['product_id']
        ])->delete();
        return throwResponse($inventory_alert);
    }

    /**
     * 获取 v2 数据
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getV2GoodsBase(Request $request): \think\Response
    {
        $period = $request->get('period');
        $info_list = V2WySalesGoodsBase::where('goods_id', $period)->select();
        foreach ($info_list as &$val) {
            $wine = V2WyCareas::where('cid', $val['wine_id'])->field('carea_name,carea_ename')->find();
            $val['wine_name'] = '';
            $val['wine_ename'] = '';
            if ($wine) {
                $val['wine_name'] = $wine['carea_name'] ?? '';
                $val['wine_ename'] = $wine['carea_ename'] ?? '';
            }
//            $area = V2WyCareas::where('cid', $val['area_id'])->field('carea_name,carea_ename')->find();
//            $val['area_name'] = '';
//            $val['area_ename'] = '';
//            if ($area) {
//                $val['area_name'] = $wine['carea_name'] ?? '';
//                $val['area_ename'] = $wine['carea_ename'] ?? '';
//            }
            $country = V2WyCareas::where('cid', $val['country_id'])->field('carea_name,carea_ename')->find();
            $val['country_name'] = '';
            $val['country_ename'] = '';
            if ($country) {
                $val['country_name'] = $country['carea_name'] ?? '';
                $val['country_ename'] = $country['carea_ename'] ?? '';
            }
            $val['keywords'] = '';
            $val['type_name'] = '';
        }
        return throwResponse($info_list);
    }

    /**
     * 添加暂存模板
     * @param Request $request
     * @return \think\Response
     */
    public function addTsTemplate(Request $request): \think\Response
    {
        $params = $request->post();
        $ser = new ServiceOther();
        $params['start_time'] = strtotime($params['start_time']);
        $params['end_time'] = strtotime($params['end_time']);
        if (!empty($params['ts_longtime']) && strlen($params['ts_longtime']) > 0) {
            $params['ts_longtime'] = strtotime($params['ts_longtime']);
        }
        $params['created_time'] = time();
        $result = $ser->addTsTemplate($params);
        return throwResponse($result);
    }

    /**
     * 更新暂存
     * @param Request $request
     * @return \think\Response
     */
    public function upTsTemplate(Request $request): \think\Response
    {
        $params = $request->post();
        $ser = new ServiceOther();
        $params['start_time'] = strtotime($params['start_time']);
        $params['end_time'] = strtotime($params['end_time']);
        !empty($params['ts_longtime']) && $params['ts_longtime'] = strtotime($params['ts_longtime']);
        $result = $ser->upTsTemplate($params);
        return throwResponse($result);
    }

    /**
     * 暂存列表
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function tsTemplateList(Request $request): \think\Response
    {
        $params = $request->get();
        $ser = new ServiceOther();
        $result = $ser->getTsTemplateList();
        return throwResponse($result);
    }

    /**
     * 开启/关闭暂存模板
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function enabledTsTemplate(Request $request): \think\Response
    {
        $params = $request->post();
        $ser = new ServiceOther();
        $result = $ser->enabledTsTemplate($params);
        if ($result['status'] == false) {
            return throwResponse($result['data'], ErrorCode::PARAM_ERROR, $result['msg']);
        }
        return throwResponse($result['msg']);
    }

    /**
     * 更新变动供应商
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateSupplier(): \think\Response
    {
        $ser = new ServiceOther();
        $result = $ser->updateSupplier();
        return throwResponse($result);
    }


    /**
     * 增加期数曝光率
     * @param Request $request
     * @return \think\Response
     * @throws \RedisException
     */
    public function exposureRate(Request $request): \think\Response
    {
        $periods_arr = $request->post('periods', '');
        if ($periods_arr) {
//            $periods_arr = array_flip($periods_arr);
            $redis = new \Redis();
            $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
            $redis->auth(env('cache.PASSWORD'));
            $redis->select(6);
            $date = date('Ymd', time());
            $redis->multi(); // 开启事务
            foreach ($periods_arr as $value) {
                $redis->zIncrBy('vinehoo.periods.exposure.' . $date, 1, $value);
            }
            $redis->exec(); // 提交事务
        }
        return throwResponse(null);
    }

    public function getIndexSort() {
        $flash = Db::table('vh_periods_flash')->field('id')
            ->select()->map(function ($item) {
                $item['periods_type'] = 0;
                return $item;
            })->toArray();
        $second = Db::table('vh_periods_second')->field('id')
            ->select()->map(function ($item) {
                $item['periods_type'] = 1;
                return $item;
            })->toArray();
        $cross = Db::table('vh_periods_cross')->field('id')
            ->select()->map(function ($item) {
                $item['periods_type'] = 2;
                return $item;
            })->toArray();

        // 跨境秒发插入初始位置
        $cInit = 5;
        // 尾货插入初始位置
        $sInit = 9;
        // 插入次数
        $count = 1;
        $all = [];
        // 规则 5 个闪购后面跟一个跨境，，9 个闪购后跟一个秒发
        foreach ($flash as $key => $v) {
            if ($key == $cInit) {
                if (!empty($cross)) {
                    array_push($all, array_shift($cross));
                }
                $cInit = $count * 9 + 5;
            }
            if ($key == $sInit) {
                if (!empty($second)) {
                    array_push($all, array_shift($second));
                }
                $sInit = $count * 9 + 9;
                $count++;
            }
            array_push($all, $v);
        }

        print_r($all);
        exit();
    }

}