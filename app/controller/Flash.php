<?php
declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\ElasticSearchConnection;
use app\ErrorCode;
use app\model\InterfaceCallLog;
use app\model\PeriodsAuctionFollow;
use app\model\PeriodsCross;
use app\model\PeriodsLeftover;
use app\model\PeriodsSecond;
use app\model\StatisticsPeriod;
use app\service\elasticsearch\ElasticSearchService;
use app\service\Flash as PeriodsFlash;
use app\service\Package;
use app\service\Products;
use app\service\Periods;
use app\Request;
use app\service\v3\PeriodsPoolService;
use app\validate\Buyer;
use app\validate\Copywriter;
use app\validate\Operations;
use app\service\Other;
use Elasticsearch\ClientBuilder;
use think\facade\Db;

class Flash extends BaseController
{

    /**
     * 添加闪购商品文案
     *
     * @param Request $request
     * @return false|string|\think\Response
     */
    public function create(Request $request)
    {
        // 参数
        $params = $request->post();
        // 获取闪购id
        $params['id'] = $this->getGeneratorID(1);
        if (!$params['id']) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '生成闪购文案失败');
        }
        // 操作信息
        $params['creator_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['creator_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['creator_name']) {
            $params['creator_name'] = base64_decode($params['creator_name']);
        }
        // 参数验证
        $validate = new Copywriter();
        $params['creator_id'] = $request->header('vinehoo-uid', '0');
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $params['created_time'] = time();
        $periods_ser = new Periods(0);
        // 获取参数配置默认值
        $params = $periods_ser->get_params_default_value($params);
        
        // 获取商品产品评分饮用建议
        $product_list = [];
        // 产品简码
        $params['short_code']  = '';
        $params['product_main_category']= '';
//        if ($params['product_id'] != '') {
//            $params['product_id'] = trim($params['product_id'], ',');
//            $product_ids = explode(',', $params['product_id']);
//            // 查询产品信息
//            $product_url = env('ITEM.WINE_WIKI_URL').'/wiki/v3/product/fieldsdatarr';
//            $field  = 'id, short_code, tasting_notes, score, prize, drinking_suggestion, product_category';
//            $product_info = post_url($product_url, ['ids' => $product_ids, 'fields' => $field]);
//            if (!is_null($product_info)) {
//                $result_pro = json_decode($product_info, true);
//                if ($result_pro['error_code'] == 0) {
//                    $product_list = $result_pro['data']['list'];
//                } else {
//                    return  throwResponse(null, ErrorCode::EXEC_ERROR, $result_pro['error_msg']);
//                }
//            }
//            if (!empty($product_list)) {
//                foreach ($product_list as &$value) {
//                    $value['period'] = $params['id'];
//                    $value['product_id'] = $value['id'];
//                    $value['created_time'] = time();
//                    $params['short_code'] .= $value['short_code']. ',';
//                    $params['product_main_category'] .= $value['product_category_name']. ',';
//                    unset($value['id'], $value['product_category_name'], $value['product_category']);
//                }
//                unset($value);
//            }
//        }
        if ($params['short_code'] != '') {
            $params['short_code'] = trim($params['short_code'], ',');
        }
        if ($params['product_main_category'] != '') {
            $params['product_main_category'] = trim($params['product_main_category'], ',');
        }
        
        // 手动创建
        $params['is_manual_create'] = 1;
        // 添加闪购商品
        $flash = new PeriodsFlash();
        $result = $flash->create($params);
        if (!$result) {
            return throwResponse($result, ErrorCode::EXEC_ERROR, '产品闪购添加失败');
        }
        // 添加商品产品评分饮用建议
        if (!empty($params['product_info'])) {
            $other_ser = new Other();
            $other_ser->createProductInfo($result, $params['product_info']);
        }
        return throwResponse($result);

    }


    /**
     * 获取 ES 商品列表
     * @return \think\Response
     */
    public function esList(Request $request)
    {
        $from = (int)$request->get('page', 1);
        $size = (int)$request->get('limit', 15);

        $where = [];
        $match_phrase = [];
        $range = [];

        $sort = [['id' => 'desc'], ['onsale_time' => 'desc']];

        // 未删除
        $where[] = ['is_delete' => 0];

        // 频道
        if ($request->get('periods_type') != '') {
            $where[] = ['periods_type' => $request->get('periods_type')];
        }

        // 商品标题
        if ($request->get('title')) {
            $where[] = ['title' => ['query' => $request->get('title'), 'operator' => 'and']];
//            $match_phrase[] = ['title' => $request->get('title')];
        }

        // 期数
        if ($request->get('periods')) {
            $where[] = ['id' => (int)$request->get('periods')];
        }

        // 期数批量查询
        $therms = [];
        if ($request->get('periods_in')) {
            $periods = explode(",", $request->get('periods_in'));
            $therms[] = ['id' => $periods];
        }

        // 文案审核状态
        if ($request->get('buyer_review_status') != '') {
            $where[] = ['buyer_review_status' => $request->get('buyer_review_status')];
        }

        // 文案制作人操作人查询
        if ($request->get('creator_id') != '') {
            $where[] = ['creator_id' => $request->get('creator_id')];
        }

//        // 文案审核列表不包含已提交
        if ($request->get('review')) {
            $range[] = ['buyer_review_status' => ['gt' => 1]];
        }

        // 上架状态
        if ($request->get('onsale_status') != '') {
            // 下架状态查询，已下架，已售罄
            if ($request->get('onsale_status') == 3) {
                $therms[] = ['onsale_status' => [3, 4, 5]];
            } else {
                $where[] = ['onsale_status' => $request->get('onsale_status')];
            }
        }

        if ($request->get('multi_onsale_status')) {
            $therms[] = ['onsale_status' => explode(',', $request->get('multi_onsale_status'))];
        }

        // 售卖模式
        if ($request->get('sales_model')) {
            $sales_model = explode(',', $request->get('sales_model'));
            if (in_array('1', $sales_model)) {
                $where[] = ['is_supplier_delivery' => 1];//代发
            }
            if (in_array('2', $sales_model)) {
                $where[] = ['is_deposit_period' => 1];//定金
            }
            if (in_array('3', $sales_model)) {
                $where[] = ['is_presell' => 1];//预售
            }
        }

        // 上架状态包含待售中和已上架
        if ($request->get('onsale_status_t')) {
            $therms[] = ['onsale_status' => [1, 2]];
            unset($sort);
            $sort = [['onsale_time' => 'desc'], ['id' => 'desc']];
        }

        // 频道包含兔头和兔头优惠券
        if ($request->get('rabbit')) {
            $therms[] = ['periods_type' => [4, 5]];
        }

        // 运营审核状态
        if ($request->get('onsale_review_status') != '') {
            $where[] = ['onsale_review_status' => $request->get('onsale_review_status')];
        }

        // 商品列表查询审核通过的
        if ($request->get('periods_list')) {
            $range[] = ['onsale_review_status' => ['gte' => 1]];
        }

        // 商品主类别
        if ($request->get('product_category')) {
            $therms[] = ['product_main_category' => [$request->get('product_category')]];
        }

        // 商品类型
        if ($request->get('product_type')) {
            $category = [$request->get('product_type')];
            $product_type = Db::table('vh_wiki.vh_product_type')
                ->where('name', $request->get('product_type'))
                ->field('id,fid')
                ->findOrEmpty();
            // 查询子类型
            if (!empty($product_type) && intval($product_type['fid']) === 0) {
                $sub_category = Db::table('vh_wiki.vh_product_type')
                    ->where('fid', $product_type['id'])
                    ->column('name');
                $category = array_merge($category,$sub_category);
            }

            $therms[] = ['product_category' => $category];
        }

        // 国家
        if ($request->get('country') != '') {
            $therms[] = ['country' => [$request->get('country')]];
        }


        // 采购类型
        if ($request->get('import_type') != '') {
            $where[] = ['import_type' => $request->get('import_type')];
        }

        // 采购人
        if ($request->get('buyer_id') != '') {
            $where[] = ['buyer_id' => $request->get('buyer_id')];
        }

        // 文案人
        if ($request->get('creator_name') != '') {
            $where[] = ['creator_name' => $request->get('creator_name')];
        }

        // 采购状态查询
        if ($request->get('buyer_review_status') != '') {
            $where[] = ['buyer_review_status' => $request->get('buyer_review_status')];
        }

        // 上架时间
        if ($request->get('onsale_time_start')) {
            $range[] = ['onsale_time' => [
                'gte' => $request->get('onsale_time_start'),
                'lt' => $request->get('onsale_time_end')]
            ];
        }

        // 下架时间
        if ($request->get('sold_out_time_start')) {
            $range[] = ['sold_out_time' => [
                'gte' => $request->get('sold_out_time_start'),
                'lt' => $request->get('sold_out_time_end')]
            ];
        }

        // 发货时间
        if ($request->get('predict_shipment_time_start')) {
            $range[] = ['predict_shipment_time' => [
                'gte' => $request->get('predict_shipment_time_start'),
                'lt' => $request->get('predict_shipment_time_end')]
            ];
        }

        // 文案主管审核状态
        if ($request->get('copywriting_review_status') != '') {
            $where[] = ['copywriting_review_status' => $request->get('copywriting_review_status')];
        }

        // 简码搜索
        if ($request->get('short_code')  != '') {
            $therms[] = ['short_code' => [$request->get('short_code')]];
        }

        // 自营供应商搜索，商家搜索
        if ($request->get('supplier_id') != '') {
            $where[] = ['supplier_id' => $request->get('supplier_id')];
            $therms[] = ['periods_type' => [9, 10]];

        }

        // 供应商搜索
        if ($request->get('seller_id') != '') {
            $where[] = ['supplier_id' => $request->get('seller_id')];

        }

        // 商家产品渠道搜索
        if ($request->get('product_channel') != '') {
            $where[] = ['product_channel' => $request->get('product_channel')];
        }

        // 是否渠道
        if ($request->get('is_channel') != '') {
            $where[] = ['is_channel' => $request->get('is_channel')];
        }

        // 频道批量搜索
        if ($request->get('periods_type_arr') != '') {
            $periods_arr  = explode(",", $request->get('periods_type_arr'));
            $therms[] = ['periods_type' => $periods_arr];
        }

        // 是否失效
        if ($request->get('is_fail') != '') {
            $where[] = ['is_fail' => $request->get('is_fail')];

        }
        // $is_fail = $request->get('is_fail');
        // if (isset($is_fail) && strlen($is_fail) > 0) {
        //     $where[] = ['is_fail' => $is_fail];
        // } else {
        //     $where[] = ['is_fail' => 1];
        // }

        // 前端小于已售
        $must = [];
        if (!empty($request->get('is_front_lt_saled'))) {
            $must[] = [
                'script' => [
                    'script' => [
                        'source' => "doc['vest_purchased'].value + doc['purchased'].value < doc['saled_count'].value",
                        'lang' => 'painless'
                    ]
                ]
            ];
        }

        // 标签
        if (!empty($request->get('label_id'))) {
            $label_ids = explode(',', $request->get('label_id'));
            $must_should = [];
            foreach ($label_ids as $v) {
                $must_should[] = [
                    'term' => [
                        'label.keyword' => $v
                    ]
                ];
                $must_should[] = [
                    'wildcard' => [
                        'label.keyword' => "$v,*",
                    ]
                ];
                $must_should[] = [
                    'wildcard' => [
                        'label.keyword' => "*,$v,*",
                    ]
                ];
                $must_should[] = [
                    'wildcard' => [
                        'label.keyword' => "*,$v",
                    ]
                ];
            }
            $must[] = [
                'bool' => [
                    'should' => $must_should
                ]
            ];
        }

        // 排序
        if ($request->get('sort_rule')) {
            $sort_rule = json_decode($request->get('sort_rule'), true);
            
            // 获取排序字段
            $sort_field = array_key_first($sort_rule);
            
            // 根据不同字段设置排序规则
            switch ($sort_field) {
                case 'conversion_rate':
                    $sort = [[
                        '_script' => [
                            'type' => 'number',
                            'script' => [
                                'lang' => 'painless',
                                // 优化计算逻辑,避免重复判断
                                'source' => "doc['pageviews'].size() == 0 || doc['pageviews'].value == 0 ? 0 : (doc['purchased_person'].value * 100.0) / doc['pageviews'].value"
                            ],
                            'order' => $sort_rule['conversion_rate'] 
                        ]
                    ]];
                    break;
                    
                default:
                    $sort = [$sort_rule];
                    break;
            }
        }
        $must_not = [];
        // 不包含商家商品
        if ($request->get('not_merchant_second') != '') {
            $must_not = ['term' => ['periods_type' => 9]];
        }
        $es = new ElasticSearchService();
        $label_ser = new \app\service\Label();

        $params = [
            'index' => ['periods'],
            'must' => $must,
            'match' => $where,
            'match_phrase' => $match_phrase,
            'must_not' => $must_not,
            'terms' => $therms,
            'should' => $should ?? [],
            'range' => $range,
            'page' => $from,
            'limit' => $size,
            'sort' => $sort,
        ];
        $data = $es->getDocumentList($params);

        $other_ser = new \app\service\Other();
        $params['user_id'] = (int)$request->header('vinehoo-uid');
        // 统计数据
        $statistics_period_id = [];
        // 遍历列表取出限购数量
        if (!empty($data['data'])) {
            $time = time();
            $merchants_period_id = $endata = $enauth = $period_ids = [];
            //秒杀马甲数计算
            $seckill_periods = $seckill_vest =[];
            foreach ($data['data'] as $v) {
                $period_ids[] = $v['id'];
                if (!empty($v['is_channel']) && intval($v['is_channel']) === 1) {
                    $enauth[] = $v['id'] . '_' . $time;
                }
                if (!empty($v['is_seckill']) && intval($v['is_seckill']) === 1) {
                    array_push($seckill_periods,$v['id']);
                }
            }
            // 查询期数预约
            $periods_reservation = Db::name('periods_reservation')
                ->whereIn('period', $period_ids)
                ->group('period')
                ->column('period,count(1) as count', 'period');
                
            // 查询商品产品库存
            $periods_product_inventory = Db::name('periods_product_inventory')
                ->where('is_use_comment',0)
                ->whereIn('period', $period_ids)
                ->column('period,warehouse');
            $period_warehouse = [];
            foreach ($periods_product_inventory as $v){
                $period_warehouse[$v['period']] = $v['warehouse'];
            }

            if(!empty($seckill_periods)){
                $seckill_vest  = Db::name('seckill_vest')
                    ->field('period,sum(num) as num')
                    ->whereIn('period',$seckill_periods)
                    ->whereIn('status',[1,2])
                    ->group("period")
                    ->select()->toArray();
                $seckill_vest = array_column($seckill_vest,"num","period");
            }

            // 渠道数据加密
            if (!empty($enauth)) {
                $en_auth = CryptionDeals(['orig_data' => $enauth], 'E');
            }
            
            foreach ($data['data'] as $v) {
                #商家秒发期数
                if (intval($v['periods_type']) === 9) {
                    $merchants_period_id[] = $v['id'];
                }
                if (!empty($v['is_channel']) && intval($v['is_channel']) === 1) {
                    $endata[] = json_encode([
                        'id' => $v['id'],
                        'auth' => $en_auth[$v['id'] . '_' . $time] ?? '',
                    ]);
                }
            }

            #查询商家秒发套餐
            $merchants_set_row = [];
            if (!empty($merchants_period_id)) {
                $merchants_set = Db::name('periods_second_merchants_set')
                    ->field('period_id,price')
                    ->whereIn('period_id',$merchants_period_id)
                    ->where('is_hidden',0)
                    ->select()->toArray();
                foreach ($merchants_set as $v) {
                    $merchants_set_row[$v['period_id']]['package_prices'][] = $v['price'];
                }
            }
            
            // 渠道id数据加密
            if (!empty($endata)) {
                $en_data = CryptionDeals(['orig_data' => $endata], 'E');
            }

            // 获取期数曝光方式
            $exposure_map = (new \app\service\Marketing)->GetExposureMethod($period_ids);

            foreach ($data['data'] as &$val) {
                // 曝光方式
                $val['exposure_method'] = $exposure_map[$val['id']] ?? [];

                // 预约人数
                $val['reservation_number'] = $periods_reservation[$val['id']]['count'] ?? 0;
                // 仓库
                $val['warehouse'] = $period_warehouse[$val['id']] ?? '';
                // 限购可购 计算最大购买数量
                // $purchase_limit = $val['limit_number'] - intval($val['vest_purchased'] + $val['purchased']);
                // // 当真实库存只剩1瓶时，限量值与已购值之差为1
                // if (intval($val['inventory']) == 1 && $purchase_limit > $val['inventory']) {
                //     $val['vest_purchased'] = intval($val['limit_number'] - 1) - $val['purchased'];
                //     $val['vest_purchased'] = $val['vest_purchased'] < 0 ? 0 : $val['vest_purchased'];
                // }
                
                $enkay = json_encode([
                    'id' => $val['id'],
                    'auth' => $en_auth[$val['id'] . '_' . $time] ?? '',
                ]);
                if (!empty($en_data[$enkay])) {
                    $val['encrypt_id'] = $en_data[$enkay];
                }

                $val['is_deposit_period'] = $val['is_deposit_period'] ?? 0;
                #商家秒发期数售价
                if (intval($val['periods_type']) === 9) {
                    $val['package_prices'] = '0';
                    if (!empty($merchants_set_row[$val['id']]['package_prices'])) {
                        $val['package_prices'] = implode('/', $merchants_set_row[$val['id']]['package_prices']);
                    }
                }

                if (isset($val['quota_rule']) && !empty($val['quota_rule'])) {
                    $val['quota_number'] = json_decode($val['quota_rule'], true)['quota_number'] ?? 0;
                }
                // 查询是否点赞
                $val['is_praise'] = $other_ser->isPraise(intval($params['user_id']), intval($val['id']));
                // 图片新增域名
                $val['banner_img'] = explode(",", $val['banner_img']);
                foreach ($val['banner_img'] as &$v) {
                    $v = env('ALIURL').$v;
                }
                unset($v);
                $val['product_img_arr'] = [];
                if ($val['product_img'])
                {
                    $val['product_img_arr'] = explode(",", $val['product_img']);
                    foreach ($val['product_img_arr'] as &$v) {
                        $v = env('ALIURL').$v;
                    }
                    unset($v);
                }

                // 视频封面图增加域名
                if (isset($val['video_cover']) && $val['video_cover'] != '') {
                    $val['video_cover'] = env('ALIURL').$val['video_cover'];
                }
                array_push($statistics_period_id, $val['id']);
                // 拍卖统计数据
                if ($request->get('periods_type') == '11') {
                    // 当前报价时间次数
                    $val['bid_price'] = 0;
                    $val['bid_time'] = '';
                    $auction_ser = new \app\service\Auction();
                    $bid_info = $auction_ser->getAuctionNewBid((int)$val['id']);
                    if (!empty($bid_info)) {
                        $val['bid_price'] = $bid_info['bid_price'];
                        $val['bid_time'] = date('Y-m-d H:i:s', $bid_info['create_time']);
                    }
                    $val['bid_count'] = $auction_ser->getAuctionBidList((int)$val['id'])['count'];
                    // 关注
                    $val['follow'] = PeriodsAuctionFollow::where('period', $val['id'])->count();
                }
                // 标签查询
                $val['label_arr'] = [];
                if (!empty($val['label'])) {
                    $val['label_arr'] = $label_ser->getLabelNameByIds($val['label']);
                }

                if (!empty($val['is_seckill']) && intval($val['is_seckill']) === 1 && isset($seckill_vest[$val['id']])) {
                    $val["vest_count"] = intval($seckill_vest[$val['id']]);
                }
            }
            unset($val);
        }
        $ids = '';
        foreach ($data['data'] as &$val) {
            // 转化率（‰）
            $val['rate'] = 0;
            if (!empty($val['pageviews']) && !empty($val['purchased_person'])) {
                $val['rate'] = intval(($val['purchased_person'] * 1000) / $val['pageviews']);
            }

            $ids .= $val['id'] .',';
            if (empty($val['payee_merchant_name'])) {
                $val['payee_merchant_name'] = "";
                // 0-闪购 1-秒发 2-跨境 3-尾货 4-兔头实物 5-酒会(酒历) 6-课程 7-三方 8-线下
                if (in_array($val['periods_type'], [1, 2, 9])) {
                    $val['payee_merchant_name'] = "佰酿云酒（重庆）科技有限公司";//秒发、跨境、商家秒发：科技
                }
                if (in_array($val['periods_type'], [4])) {//兔头：云酒
                    $val['payee_merchant_name'] = "重庆云酒佰酿电子商务有限公司";
                }
            }
        }
        unset($val);
        // 赋值统计数据
        $statistics_arr = StatisticsPeriod::whereIn('period', $statistics_period_id)->select();
        if (!empty($statistics_arr)) {
            foreach ($data['data'] as &$val) {
                foreach ($statistics_arr as $v) {
                    if ($val['id'] == $v['period']) {
                        // $val['rate'] = $v['rate'] ?? 0;
                        $val['periods_comment_count'] = $v['notes_user_this'] ?? 0;
                        $val['virtual_comment_count'] = $v['notes_admin_this'] ?? 0;
                        $val['product_comment_count'] = $v['notes_all'] ?? 0;
                        $val['conversion_rate_score'] = $v['product_rating'] ?? 0;
                        $val['rate_score'] = $v['conversion_rate_score'] ?? 0;
                        $val['page_views_score'] = $v['page_views_score'] ?? 0;
                        $val['sales_score'] = $v['sales_score'] ?? 0;
                        $val['profit_rate'] = $v['profit_rate'] ?? 0;
                        $val['profit_value'] = $v['profit_value'] ?? 0;
                        $val['order_quantity'] = $v['order_quantity'] ?? 0;
                        $val['total_order_amount'] = $v['total_order_amount'] ?? 0;
                        $val['total_order_cost'] = $v['total_order_cost'] ?? 0;
                        $val['sale_orders'] = $v['sale_orders'] ?? 0;
                        $val['sale_bottle'] = $v['sale_bottle'] ?? 0;
                    }
                }
            }
            unset($val, $v);
        }
        // 获取商品列表状态
        try {
            $url_header[] = 'vinehoo-client: tp6-commodities';
            $url_header[] = 'vinehoo-client-version: v3';
            $get_url = env('ITEM.MYSQL_BATCH_SEARCH'). '/services/v3/batchsearch/goods/status';
            $ids = trim($ids, ',');
            $get_url .= '?pids=' .$ids;
            $re_status = get_url($get_url, $url_header);
            if (!empty($re_status)) {
                $re_status_data = json_decode($re_status, true);
                if (!empty($re_status_data['data'])) {
                    foreach ($data['data'] as &$val) {
                        foreach ($re_status_data['data'] as $v) {
                            if ($val['id'] == array_key_first($v)) {
                                $val['buyer_review_status'] = $v[$val['id']]['buyer_review_status'];
                                $val['copywriting_review_status'] = $v[$val['id']]['copywriting_review_status'];
                                $val['onsale_review_status'] = $v[$val['id']]['onsale_review_status'];
                            }
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            // 记录异常
            InterfaceCallLog::create([
                'function_name' => 'getSecondMerchants',
                'model' => 'mysql_batch_search',
                'method' => 'GET',
                'url' => $get_url,
                'params' => $ids,
                'response' => 400,
                'remark' => '获取商品列表状态接口',
                'create_time' => date('Y-m-d H:i:s', time())
            ]);
        }
        $totalNum = $data['total']['value'];

        $result['list'] = array_values($data['data']);
        $result['total'] = $totalNum;
        return throwResponse($result);

    }

    /**
     * 二维数组排序
     * @param $arr
     * @param $keys
     * @param string $type
     * @return array
     */
    function arraySort($arr, $keys, $type = 'asc') {
        $keysvalue = $new_array = array();

        //方式1【推荐】
        $keysvalue= array_column($arr,$keys,NULL);//将目标字段单独提取出来生成一个一维数组
        //方式2
        foreach ($arr as $k => $v){ //遍历生成
            $keysvalue[$k] = $v[$keys];
        }

        $type == 'asc' ? asort($keysvalue) : arsort($keysvalue);//排序键名不变
        reset($keysvalue);
        foreach ($keysvalue as $k => $v) {
            $new_array[$k] = $arr[$k];
        }
        return $new_array;
    }

    /**
     * 获取 ES 商品列表
     * @return \think\Response
     */
    public function esFrontEndList(Request $request)
    {
        $from = (int)$request->get('page', 0);
        $size = (int)$request->get('limit', 15);

        if ($from > 0) {
            $from = ($from - 1) * $size;
        } else {
            $from = 0;
        }

        // 默认查询所有
        $esWhere['bool']['must'] = [];
        $param = $request->get();
        // 未删除
        $esWhere['bool']['must'][] = ['match' => ['is_delete' => 0]];

        // 频道
        if (isset($param['periods_type'])  && $param['periods_type'] != '') {
            $esWhere['bool']['must'][] = ['match' => ['periods_type' => $param['periods_type']]];
            // 非渠道商品
            if ($param['periods_type'] < 5) {
                $esWhere['bool']['must'][] = ['match' => ['is_channel' => 0]];
            }
        } else {
            // 非渠道商品
            $esWhere['bool']['must'][] = ['match' => ['is_channel' => 0]];
        }
        
        // 商品标题
        if (!empty($param['title'])) {
            $esWhere['bool']['must'][] = ['match_phrase' => ['title' => $param['title']]];
        }
        // 期数
        if (!empty($param['periods'])) {
            $periods = explode(',', $param['periods']);
            $esWhere['bool']['must'][] = ['terms' => ['id' => $periods]];
        }

        // 采购ID
        if (!empty($param['buyer_id'])) {
            $esWhere['bool']['must'][] = ['match' => ['buyer_id' => $param['buyer_id']]];
            $esWhere['bool']['must'][] = ['exists' => ['field' => 'purchasing_said']];
            $esWhere['bool']['must'][] = ['wildcard' => ['purchasing_said' => '*']];
        }

        $periods_type = $param['periods_type'] ?? 0;

        // 是否频道下尾货
        if (isset($param['is_leftover']) && $param['is_leftover']) {
            $esWhere['bool']['must'][] = ['match' => ['is_leftover' => $param['is_leftover']]];
        }


        // 默认查询，待售中，在售中，售完不下架
//        $esWhere['bool']['must'][] = ['match' => ['onsale_status' => 2]];
        $esWhere['bool']['should'][] = ['match' => ['onsale_status' => 1]];
        $esWhere['bool']['should'][] = ['match' => ['onsale_status' => 2]];
        $esWhere['bool']['should'][] = ['match' => ['sellout_sold_out' => 1]];
        $esWhere['bool']['minimum_should_match'] = 1;

        // 排序
        $sort = [['sort' => 'desc'], ['onsale_time' => 'desc']];
        // 指定字段
        $field = [];//['id', 'country', 'title', 'brief','banner_img', 'horizontal_img', 'sort', 'second_ids', 'limit_number', 'purchased', 'vest_purchased', 'price', 'market_price','periods_type', 'product_img', 'is_hidden_price', 'onsale_status', 'rabbit', 'inventory'];

        $params = [
            'index' => 'vinehoo.periods',
            'type' => '_doc',
            'body' => [
                'query' => $esWhere,
                '_source' => $field,
                'sort' => $sort,
                'size' => $size,
                'from' => $from
            ]
        ];
        // es 配置
        $hosts = [
            [
                'host' => env('ES.HOST','127.0.0.1'),
                'port' => env('ES.PORT',9200),
                'user' => env('ES.USER','root'),
                'pass' => env('ES.PASS','vinehoo666')
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);

        // 请求端
        $client = $request->header('vinehoo-client');

        $img_url = '&x-oss-process=image/resize,w_300,h_187/auto-orient,1/quality,q_90/format,';
        // 遍历列表取出限购数量
        if (!empty($data['list'])) {
            foreach ($data['list'] as &$val) {
                if (isset($val['quota_rule']) && !empty($val['quota_rule'])) {
                    $val['quota_number'] = json_decode($val['quota_rule'], true)['quota_number'] ?? 0;
                }
                // 题图
                if (isset($val['banner_img']) && $val['banner_img'] != '') {
                    $val['banner_img'] = explode(",", $val['banner_img']);
                    foreach ($val['banner_img'] as &$v) {
                        $v = env('ALIURL').$v;
                        if ($client != 'vinehoo-pc'  && $periods_type == 4) {
                            $v_arr = explode('.', $v);
                            $suffix = end($v_arr);
                            if (strpos($suffix, '?')) {
                                $suffix = current(explode('?', $suffix));
                            }
                            $v .= $img_url. $suffix;
                        }
                    }
                    $val['banner_img'] = $val['banner_img'][0] ?? '';
                    unset($v);
                }
                // 产品图
                if (isset($val['product_img']) && $val['product_img'] != '') {
                    $val['product_img'] = explode(",", $val['product_img']);
                    foreach ($val['product_img'] as &$v) {
                        $v = env('ALIURL').$v;
                        if ($client != 'vinehoo-pc' && $periods_type == 4) {
                            $v_arr = explode('.', $v);
                            $suffix = end($v_arr);
                            if (strpos($suffix, '?')) {
                                $suffix = current(explode('?', $suffix));
                            }
                            $v .= $img_url. $suffix;
                        }
                    }
                    unset($v);
                }
                // 秒发竖图
                if (isset($val['horizontal_img']) && $val['horizontal_img'] != '') {
                    $val['horizontal_img'] = explode(",", $val['horizontal_img']);
                    foreach ($val['horizontal_img'] as &$v) {
                        $v = env('ALIURL').$v;
                    }
                    $val['horizontal_img'] = $val['horizontal_img'][0] ?? '';
                    unset($v);
                }
                // 视频封面图增加域名
                if (isset($val['video_cover']) && $val['video_cover'] != '') {
                    $val['video_cover'] = env('ALIURL').$val['video_cover'];
                }
                // 已购
                if (isset($val['purchased']) && $val['purchased'] != '') {
                    $val['purchased'] = $val['purchased'] + $val['vest_purchased'];
                }
            }
        }
        if ($request->get('rand_sort', 0) == 1) shuffle($data['list']);
        $result['list'] = $data['list'];
        $result['total'] = $data['total'];
        return throwResponse($result);

    }

    /**
     * 活动专题页-热门推荐
     * @return \think\Response
     */
    public function esHotRecommendations(Request $request)
    {
        $from = 0;
        $size = 20;

        // 默认查询所有
        $esWhere['bool']['must'] = [];
        $param = $request->get();
        // 未删除
        $esWhere['bool']['must'][] = ['match' => ['is_delete' => 0]];
        // 非渠道商品
        $esWhere['bool']['must'][] = ['match' => ['is_channel' => 0]];
        $esWhere['bool']['must'][] = ['terms' => ['periods_type' => [0,1]]];

        // 默认查询，待售中，在售中，售完不下架
//        $esWhere['bool']['must'][] = ['match' => ['onsale_status' => 2]];
        $esWhere['bool']['should'][] = ['match' => ['onsale_status' => 2]];
        $esWhere['bool']['should'][] = ['match' => ['sellout_sold_out' => 1]];
        $esWhere['bool']['minimum_should_match'] = 1;

        // 排序
        $sort = [['purchased' => 'desc'], ['onsale_time' => 'desc']];
        // 指定字段
        $field = ['id', 'country', 'title', 'brief','banner_img', 'horizontal_img', 'sort', 'second_ids', 'limit_number',
            'purchased', 'vest_purchased', 'price', 'market_price','periods_type', 'product_img', 'is_hidden_price',
            'onsale_status'];

        $params = [
            'index' => 'vinehoo.periods',
            'type' => '_doc',
            'body' => [
                'query' => $esWhere,
                '_source' => $field,
                'sort' => $sort,
                'size' => $size,
                'from' => $from
            ]
        ];
        // es 配置
        $hosts = [
            [
                'host' => env('ES.HOST','127.0.0.1'),
                'port' => env('ES.PORT',9200),
                'user' => env('ES.USER','root'),
                'pass' => env('ES.PASS','vinehoo666')
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);

        // 遍历列表取出限购数量
        if (!empty($data['list'])) {
            foreach ($data['list'] as &$val) {
                if (isset($val['quota_rule']) && !empty($val['quota_rule'])) {
                    $val['quota_number'] = json_decode($val['quota_rule'], true)['quota_number'] ?? 0;
                }
                // 题图
                if (isset($val['banner_img']) && $val['banner_img'] != '') {
                    $val['banner_img'] = explode(",", $val['banner_img']);
                    foreach ($val['banner_img'] as &$v) {
                        $v = env('ALIURL').$v;
                    }
                    $val['banner_img'] = $val['banner_img'][0] ?? '';
                    unset($v);
                }
                // 产品图
                if (isset($val['product_img']) && $val['product_img'] != '') {
                    $val['product_img'] = explode(",", $val['product_img']);
                    foreach ($val['product_img'] as &$v) {
                        $v = env('ALIURL').$v;
                    }
                    unset($v);
                }
                // 秒发竖图
                if (isset($val['horizontal_img']) && $val['horizontal_img'] != '') {
                    $val['horizontal_img'] = explode(",", $val['horizontal_img']);
                    foreach ($val['horizontal_img'] as &$v) {
                        $v = env('ALIURL').$v;
                    }
                    $val['horizontal_img'] = $val['horizontal_img'][0] ?? '';
                    unset($v);
                }
                // 视频封面图增加域名
                if (isset($val['video_cover']) && $val['video_cover'] != '') {
                    $val['video_cover'] = env('ALIURL').$val['video_cover'];
                }

                // 已购
                if (isset($val['purchased']) && $val['purchased'] != '') {
                    $val['purchased'] = $val['purchased'] + $val['vest_purchased'];
                }

            }
        }
        $result['list'] = $data['list'];
//        $result['total'] = $data['total'];
        return throwResponse($result);

    }

    // 取出所有数据
    public function esPeriods()
    {
        // 闪购列表
        $f_data = \app\model\PeriodsFlash::field('id')->order('id', 'desc')->select()
            ->map(function ($item) {
                $item->periods_type = 0;
                return $item;
            })->toArray();
        // 跨境列表
        $c_data = PeriodsCross::field('id')->order('id', 'desc')->select()->map(function ($item) {
            $item->periods_type = 2;
            return $item;
        })->toArray();
        // 秒发列表
        $s_data = PeriodsSecond::field('id')->order('id', 'desc')->select()->map(function ($item) {
            $item->periods_type = 1;
            return $item;
        })->toArray();
        // 尾货列表
        $l_data = PeriodsLeftover::field('id')->order('id', 'desc')->select()->map(function ($item) {
            $item->periods_type = 3;
            return $item;
        })->toArray();
        // 跨境插入初始位置
        $c_init = 4;
        // 秒发插入初始位置
        $s_init = 5;
        // 尾货插入初始位置
        $l_init = 8;
        // 跨境取值位
        $c_a = 0;
        // 秒发取值位
        $s_a = 0;
        // 尾货取值位
        $l_a = 0;
        // 插入次数
        $count = 1;
        // 00002010003|00002010003|00002010003 展示规则
        $new_list = [];
        foreach ($f_data as $k => $v) {
            // 插入跨境
            if ($k == $c_init) {
                $new_list[] = $c_data[$c_a];
                $c_init = $count * 8 + 4;
                ++$c_a;
            }
            // 插入秒发
            if ($k == $s_init) {
                $new_list[] = $s_data[$s_a];
                $s_init = $count * 8 + 5;
                ++$s_a;
            }
            // 插入尾货
            if ($k == $l_init) {
                $new_list[] = $l_data[$l_a];
                $l_init = $count * 8 + 8;
                ++$l_a;
                ++$count;
            }
            $new_list[] = $v;
        }
        print_r($new_list);
        exit();
    }


    public function esUpdate()
    {
        try {
            $es = new ElasticSearchConnection();
            $params = [
                'index' => 'vinehoo.periods',
//                'from' => $from,
//                'size' => $size,
                'id' => 80065,
                'body' => [
                    'doc' => [
                        'title' => '澳大利亚进口红酒'
                    ]
                ]
            ];
//            $result =  $es->connection()->count($params);
            $result = $es->connection()->update($params);
            return throwResponse($result);
        } catch (Elasticsearch\Common\Exceptions\TransportException $e) {
            return throwResponse([], ErrorCode::EXEC_ERROR, $e->getMessage());
        }
    }

    /**
     * 添加编辑采购信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateBuyerInfo(Request $request)
    {
        // 参数
        $params = $request->post();
        // 参数验证
        $validate = new Buyer();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }

        $flash = new PeriodsFlash();

        // 添加采购信息
        $result = $flash->update($params);

        if (!$result['status']) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, '采购信息添加失败');
        }

        return throwResponse($result['data']);

    }

    /**
     * 添加编辑运营信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update(Request $request)
    {
        // 参数
        $params = $request->post();
        // 参数验证
        $validate = new Operations();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }

        // 验证商品 id
        $update_validate = $this->updateValidate($params);
        if (!empty($update_validate)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $update_validate);
        }

        $flash = new PeriodsFlash();
        // 验证绑定产品
        $period_info = $flash->getOne((int)$params['id'], 'product_id, buyer_review_status, supplier_id, payee_merchant_id');
        if (empty($period_info)) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, '未查询到期数信息');
        }
        if (!empty($params['onsale_time'])) {
            if (empty($period_info['supplier_id'])) {
                return throwResponse(null, ErrorCode::EXEC_ERROR, '供应商不能为空');
            }
            if (empty($period_info['payee_merchant_id'])) {
                return throwResponse(null, ErrorCode::EXEC_ERROR, '收款商户不能为空');
            }
        }
        $old_product_id = explode(',', $period_info['product_id']);
        $product_id = explode(',', $params['product_id']);
        $is_del_product = 0;
        foreach ($old_product_id as $v) {
            if (empty($v)) {
                continue;
            }
            if (!in_array($v, $product_id)) {
                $is_del_product = 1;
            }
        }
        if ($is_del_product == 1 && $period_info['buyer_review_status'] == 3) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, '采购审核后不允许删除绑定产品');
        }
        // 添加运营信息 时间戳转换
        if (isset($params['predict_shipment_time'])) {
            $params['predict_shipment_time'] = strtotime($params['predict_shipment_time']);
        }
        if (isset($params['onsale_time'])) {
            $params['onsale_time'] = strtotime($params['onsale_time']);
        }
        if (isset($params['sell_time'])) {
            $params['sell_time'] = strtotime($params['sell_time']);
        }
        if (isset($params['sold_out_time'])) {
            $params['sold_out_time'] = strtotime($params['sold_out_time']);
        }

        // 限购规则转 json 字符串
        if (isset($params['quota_rule']) && !empty($params['quota_rule'])) {
            $params['quota_rule'] = json_encode($params['quota_rule']);
        }

        // 如果前端返回图片域名，则删除域名
        if (isset($params['banner_img']) && $params['banner_img'] != '') {
            $params['banner_img'] = str_replace(env('ALIURL'),"", $params['banner_img']);
        }
        if (isset($params['product_img']) && $params['product_img'] != '') {
            $params['product_img'] = str_replace(env('ALIURL'),"", $params['product_img']);
        }
        if (isset($params['video_cover']) && $params['video_cover'] != '') {
            $params['video_cover'] = str_replace(env('ALIURL'),"", $params['video_cover']);
        }


        // 查询产品简码，大类
        if ($params['product_id'] != '') {
            $other_ser = new Other();
            $product_info = $other_ser->getProductList($params);
            $params['short_code'] = $product_info['short_code'];
            $params['product_main_category'] = $product_info['product_main_category'];
        }
        $result = $flash->update($params);

        if (!$result['status']) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, '运营信息添加失败');
        }

        (new PeriodsPoolService())->syncPeriods($params);

        // 添加商品产品评分饮用建议
        if (!empty($params['product_info'])) {
            $other_ser = new Other();
            $other_ser->createProductInfo((int)$params['id'], $params['product_info']);
        }
        $periods_ser = new Periods(0);
        // 更新 json 文件
        $res = $periods_ser->create_period_json((int)$params['id'], 0,0,-1);
        if ($res !== true) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, $res);
        }
        // 更新 CDN
        $periods_ser::CDNrefreshObject((int)$params['id']);
        return throwResponse($result['data']);

    }

    /**
     * 商品详细
     *
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(Request $request)
    {
        // 参数
        $params = $request->get();
        $params['id'] = (int)$params['id'];
        // 验证商品 id
        $update_validate = $this->updateValidate($params);
        if (!empty($update_validate)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $update_validate);
        }

        $flash = new PeriodsFlash();

        // 详细信息
        $result = $flash->getOne($params['id'], '*');
        if (empty($result)) {
            return throwResponse($result, ErrorCode::PARAM_ERROR, '期数不存在');
        }
        // 套餐信息
        $package_ser = new Package(0);
        $where['period_id'] = $params['id'];
        $result['package'] = $package_ser->getPackage($where);
        // 产品信息
        $products_ser = new Products();
        $result['product_list'] = $products_ser->getListById($result['product_id'], $params['id']);
        // 是否存在上架记录
        $periods = new Periods(0);
        $result['is_onsale_record'] = 0;
        $onsale_record = $periods->getOnSaleRecord($params['id'], 0);
        if ($onsale_record) {
            $result['is_onsale_record'] = 1;
        }
        $label_ser = new \app\service\Label();
        // 标签查询
        $result['label_arr'] = [];
        if (!empty($result['label'])) {
            $result['label_arr'] = $label_ser->getLabelNameByIds($result['label']);
        }

        return throwResponse($result);

    }

    /**
     * 更新产品参数验证
     *
     * @param array $params
     * @return string
     */
    protected function updateValidate(array $params): string
    {
        // 提示信息
        $message = '';

        // 商品 id 验证
        if (!isset($params['id']) || empty($params['id'])) {
            $message .= '请选择商品';
        }

        return $message;
    }

    /**
     * 闪购字段查询
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getFlashFieldAll(Request $request): \think\Response
    {
        $params = $request->get();
        $flash = new PeriodsFlash();
        $result = $flash->getFlashFieldAll($params);
        return throwResponse($result);
    }

}
