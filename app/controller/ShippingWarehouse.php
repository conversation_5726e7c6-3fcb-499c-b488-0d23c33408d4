<?php
declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\service\ShippingWarehouse as ShippingWarehouseSer;
use app\Request;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;
use think\Exception;

/**
 * 发货仓管理
 * Class ShippingWarehouse
 * @package app\controller
 */
class ShippingWarehouse extends BaseController {

    /**
     * 添加实体仓库
     * @param Request $request
     * @return \think\Response
     */
    public function createPhysicalWarehouse(Request $request): \think\Response
    {
        $params = $request->post();
        $service = new ShippingWarehouseSer();
        $type = $params['type'] ?? 1;
        // 查询仓库是否存在
        if ($type == 1) {
            $exists = $service->getPhysicalWarehouseInfo([
                'coding' => $params['coding'],
                'type' => 1
            ]);
            if ($exists) {
                return throwResponse(null, ErrorCode::PARAM_ERROR, '该仓库编码已存在');
            }
        }
        $params['created_time'] = time();
        $result = $service->createPhysicalWarehouse($params);
        return throwResponse($result);
    }


    /**
     * 添加虚拟仓库
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function createVirtualWarehouse(Request $request): \think\Response
    {
        $params = $request->post();
        $service = new ShippingWarehouseSer();
        $exists = $service->getVirtualWarehouseInfo([
            'virtual_id' => $params['virtual_id'],
        ]);
        if ($exists) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '虚拟仓id已存在');
        }
        if (isset($params['channel_types']) && $params['channel_types'] != '') {
            if (is_array($params['channel_types'])) {
                $params['channel_types'] = implode(',', $params['channel_types']);
            }
        }
        if(empty($params['company_ids'])){
            throw new Exception("所属公司不能为空");
        }
        if (isset($params['company_ids']) && $params['company_ids'] != '') {
            if (is_array($params['company_ids'])) {
                $params['company_ids'] = implode(',', $params['company_ids']);
            }
        }
        $params['is_supplier_delivery'] = 0;
        if (!empty($params['virtual_name']) && strpos_str($params['virtual_name'], '代发') !== false) {
            $params['is_supplier_delivery'] = 1;
        }
        $params['created_time'] = time();
        $result = $service->createVirtualWarehouse($params);
        return throwResponse($result);
    }

    /**
     * 实体仓详细
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPhysicalWarehouseInfo(Request $request): \think\Response
    {
        $id = $request->get('id');
        if (!(int)$id) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '参数错误');
        }
        $service = new ShippingWarehouseSer();
        $warehouse = $service->getPhysicalWarehouseInfo(['id' => $id]);
        return throwResponse($warehouse);
    }

    /**
     * 实体仓详细
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getVirtualWarehouseInfo(Request $request): \think\Response
    {
        $id = $request->get('id');
        if (!(int)$id) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '参数错误');
        }
        $service = new ShippingWarehouseSer();
        $warehouse = $service->getVirtualWarehouseInfo(['id' => $id]);
        if ($warehouse) {
            // 获取实体仓名称
            $physical = $service->getPhysicalWarehouseInfo(['id' => $warehouse['physical_id']], 'name');
            if ($physical) {
                $warehouse['physical_name'] = $physical['name'];
            }
            if ($warehouse['channel_types'] !== null) {
                $channel_type = explode(",", $warehouse['channel_types']);
                $val = [];
                $type = ['0' => '闪购', '1' => '秒发', '2' => '跨境', '3' => '尾货', '4' => '兔头商城'];
                foreach ($channel_type as $v) {
                    array_push($val, $type[$v]);
                }
                $warehouse['channel_types_name'] = implode("、", $val);
            }
        }
        return throwResponse($warehouse);
    }

    /**
     * 更新实体仓库
     * @param Request $request
     * @return \think\Response
     */
    public function upPhysicalWarehouse(Request $request): \think\Response
    {
        $id = $request->post('id');
        if (!(int)$id) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '请选择要更新的实体仓库');
        }
        $params = $request->post();
        $service = new ShippingWarehouseSer();
        $result = $service->upPhysicalWarehouse($params);
        return throwResponse($result);
    }

    /**
     * 更新虚拟仓库
     * @param Request $request
     * @return \think\Response
     */
    public function upVirtualWarehouse(Request $request): \think\Response
    {
        $id = $request->post('id');
        if (!(int)$id) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '请选择要更新的虚拟仓库');
        }
        $params = $request->post();
        if (isset($params['channel_types']) && $params['channel_types'] != '') {
            if (is_array($params['channel_types'])) {
                $params['channel_types'] = implode(',', $params['channel_types']);
            }
        }
        if(empty($params['company_ids'])){
            throw new Exception("所属公司不能为空");
        }
        if (isset($params['company_ids']) && $params['company_ids'] != '') {
            if (is_array($params['company_ids'])) {
                $params['company_ids'] = implode(',', $params['company_ids']);
            }
        }
        $params['is_supplier_delivery'] = 0;
        if (!empty($params['virtual_name']) && strpos_str($params['virtual_name'], '代发') !== false) {
            $params['is_supplier_delivery'] = 1;
        }
        $service = new ShippingWarehouseSer();
        $result = $service->upVirtualWarehouse($params);
        return throwResponse($result);
    }

    /**
     * 获取实体仓列表
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function getPhysicalWarehouseList(Request $request): \think\Response
    {
        $params = $request->get();
        $limit = 15;
        if (isset($params['limit']) && !empty($params['limit'])) {
            $limit = (int)$params['limit'];
        }
        $service = new ShippingWarehouseSer();
        $result = $service->getPhysicalWarehouseList($params, $limit);
        return throwResponse($result);
    }

    /**
     * 获取虚拟仓列表
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function getVirtualWarehouseList(Request $request): \think\Response
    {
        $params = $request->get();
        $limit = 15;
        if (isset($params['limit']) && !empty($params['limit'])) {
            $limit = (int)$params['limit'];
        }
        $service = new ShippingWarehouseSer();
        $result = $service->getVirtualWarehouseList($params, $limit);
        return throwResponse($result);
    }

    public function corpList(Request $request): \think\Response
    {
        return throwResponse([
            [
                'label' => '重庆云酒佰酿电子商务有限公司',
                'value' => 1,
            ],
            [
                'label' => '佰酿云酒（重庆）科技有限公司',
                'value' => 2,
            ],
            [
                'label' => '渝中区微醺酒业商行',
                'value' => 5,
            ],
            [
                'label' => '海南一花一世界科技有限公司',
                'value' => 10,
            ]
        ]);
    }

    /**
     * 查询所有实体仓下面的绑定了频道的虚拟仓需
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPhysicalAndVirtualList(): \think\Response
    {
        $service = new ShippingWarehouseSer();
        $result = $service->getPhysicalAndVirtualList();
        return throwResponse($result);
    }

    /**
     * 查询产品选择虚拟仓
     * @param Request $request
     * @return \think\Response
     */
    public function getVirtualListByChannelType(Request $request): \think\Response
    {
        $params = $request->get();
        $service = new ShippingWarehouseSer();
        $result = $service->getVirtualListByChannelType($params);
        return throwResponse($result);
    }

    /**
     * 根据 erp_id（编码）查询虚拟仓列表
     * @param Request $request
     * @return \think\Response
     */
    public function getVirtualByVirtualId(Request $request): \think\Response
    {
        $params = $request->get();
        $service = new ShippingWarehouseSer();
        $result = $service->getVirtualByVirtualId($params);
        return throwResponse($result);
    }


    /**
     * 添加临时库存
     */
    public function addTempWarehouse(Request $request): \think\Response
    {
        $params = $request->post();
        $service = new ShippingWarehouseSer();
        $params['created_time'] = time();
        $result = $service->addTempWarehouse($params);
        if ($result['status'] == false) {
            return throwResponse(0, ErrorCode::EXEC_ERROR, $result['msg']);
        }
        return throwResponse($result);
    }

    /**
     * 更新库存
     * @param Request $request
     * @return \think\Response
     */
    public function updateTempWarehouseInventory(Request $request): \think\Response
    {
        $params = $request->post();
        $service = new ShippingWarehouseSer();
        $params['updated_time'] = time();
        $result = $service->updateTempWarehouseInventory($params);
        if (!$result) {
            return throwResponse($result, ErrorCode::EXEC_ERROR, '更新库存失败');
        }
        return throwResponse($result);
    }

    /**
     * 获取临时仓库库存信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function getTempWarehouse(Request $request): \think\Response
    {
        $params = $request->get();
        $service = new ShippingWarehouseSer();
        $result = $service->getTempWarehouse($params);
        return throwResponse($result);
    }

    /**
     * 导入临时仓库数据
     */
    public function importWarehouse(Request $request): \think\Response
    {
        $params = $request->post();
        //$params['file'] = $request->file('file');
        //上传文件验证
        if (!isset($params['file']) || empty($params['file'])) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '请上传文件');
        }
        $inputFileName = env('ALIURL') . $params['file'];
//        $inputFileName = 'https://images.wineyun.com/vinehoo/goods-images/20230106/1.xlsx';
        if (!$inputFileName) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '缺少文件地址');
        }

        //拿到excel数据
        try {
            //$filename = $params['file']->getPathname();
            $filename = tempnam(sys_get_temp_dir(), "xlsx");
            $handle = fopen($filename, 'wb');
            fwrite($handle, file_get_contents($inputFileName));
            $spreadsheet = IOFactory::load($filename);
            $excelData = $spreadsheet->getActiveSheet()->toArray(null, true, true, true);
        } catch (\Throwable $e) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, "获取文件数据失败，请稍后再试:" . $e->getMessage());
        }
        if (count($excelData) > 100) {
            return throwResponse(false, ErrorCode::EXEC_ERROR, '导入数据请勿超过 100 条');
        }
        if (!empty($excelData)) {
            $service = new ShippingWarehouseSer();
            $result = $service->importWarehouse($excelData);
            return throwResponse($result);
        }

        return throwResponse(false, ErrorCode::EXEC_ERROR, '未导入数据');

    }

    /**
     * 获取代发仓列表
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function getDfWarehouseList(Request $request): \think\Response
    {
        $params = $request->get();
        $service = new ShippingWarehouseSer();
        $result = $service->getDfWarehouseList($params);
        return throwResponse($result);
    }

}