<?php
declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\elasticsearch\ElasticSearchService;
use app\validate\Products as valProduct;
use app\validate\ProductMinority;
use app\service\Products as serProduct;
use think\Validate;

class Products extends BaseController
{
    /**
     * 添加产品
     *
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function create(Request $request)
    {
        // 参数
        $params = $request->post();
        // 生成条码
        if (empty($params['bar_code'])) {
            $params['bar_code'] = $this->generateBarCode($params);
        }

        // 参数验证
        if ($params['product_category'] == 1) {
            // 酒类验证
            $validate = new valProduct();
        } else {
            // 产品基础字段验证
            $validate = new ProductMinority();
        }
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $params['created_time'] = time();
        // 添加产品
        $product = new serProduct();
        $result = $product->create($params);

        if (!$result['status']) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, $result['msg']);
        }

        return throwResponse($result['data']);
    }


    /**
     * 更新产品
     *
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update(Request $request)
    {
        // 参数
        $params = $request->post();

        // 参数验证
        if ($params['product_category'] == 1) {
            // 酒类验证
            $validate = new valProduct();
        } else {
            // 产品基础字段验证
            $validate = new ProductMinority();
        }
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }

        // 更新产品必选项验证
        $update_validate = $this->updateValidate($params);
        if (!empty($update_validate)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $update_validate);
        }
        $params['update_time'] = time();

        $product = new serProduct();

        // 更新产品
        $result = $product->update($params);

        if (!$result['status']) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, $result['msg']);
        }

        return throwResponse($result['data']);
    }

    /**
     * 更新产品参数验证
     * @param array $params
     * @return string
     */
    protected function updateValidate(array $params): string
    {
        // 提示信息
        $message = '';

        // 产品 id 验证
        if (!isset($params['id']) || empty($params['id']) ||
            !is_int($params['id'])) {
            $message .= '请选择更新数据';
        }
        // 更新操作人 id 验证
        if (!isset($params['operator_id']) || empty($params['operator_id'])) {
            $message .= '请选择更新人';
        }

        return $message;
    }


    /**
     * 获取产品单位列表
     *
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getUnit()
    {
        $product = new serProduct();
        // 更新产品
        $result = $product->getUnit();
        return throwResponse($result);
    }

    /**
     * 产品类别列表
     *
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getCategory()
    {
        $product = new serProduct();
        // 更新产品
        $result = $product->getCategory();
        return throwResponse($result);
    }

    /**
     * 根据父 id 获取类型列表
     *
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getTypeByFID(Request $request)
    {
        $param = $request->get();
        $param['id'] = $param['id'] ?? 0;
        $product = new serProduct();
        $result = $product->getTypeByFID((int)$param['id'] ?? 0);
        return throwResponse($result);
    }

    /**
     * 生成产品条码
     *
     * @param $param
     * @return string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function generateBarCode($param)
    {

        $result = ['bar_code' => ''];

        #年份取后两位
        $years = '00';
        $param['grape_picking_years'] = intval($param['grape_picking_years'] ?? null);
        if (!empty($param['grape_picking_years'])) {
            $years = mb_substr((string)$param['grape_picking_years'], -2);
        }

        #容量取前3位
        $capacity = '000';
        $param['capacity'] = floatval($param['capacity']);
        if (!empty($param['capacity'])) {
            $capacity = mb_substr(str_replace('.', '', $param['capacity']), 0, 3);
            $capacity = str_pad($capacity, 3, "0", STR_PAD_LEFT);
        }

        $code = $param['product_type_code'] . $capacity . $years;

        $product = new serProduct();
        $odCode = $product->getBarcode();

        for ($i = 1; $i <= 9999; $i++) {
            #条码限制13位
            $max = 13 - strlen($code);
            $bar_code = $max > 0 ? $code . str_pad((string)$i, $max, "0", STR_PAD_LEFT) : $code;
            if (!in_array($bar_code, $odCode)) {
                $result['bar_code'] = $bar_code;
                break;
            }
        }

        return $result['bar_code'];
    }

    /**
     * 根据产品 id 获取产品信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getProductInfoById(Request $request)
    {
        $id = $request->get('id', '');
        if (!$id) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '请确认产品id');
        }
        $field = $request->get('field', '');
        $product = new serProduct();
        $info = $product->getOne((int)$id, $field);
        return throwResponse($info);
    }

    /**
     * 根据条件和字段返回商品详细信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getProductByShortCode(Request $request)
    {
        $param = $request->get();
        // 查询条件
        $where = [];
        // 查询字段
        $field = $param['field'] ?? '*';
        // 简码, 条码 查询
        if (isset($param['short_code']) && !empty($param['short_code'])) {
            $where[] = ['short_code|bar_code', 'like', $param['short_code'] . '%'];
        }

        $product = new serProduct();
        $result = $product->getProductInfo($where, $field);

        return throwResponse($result);

    }

    /** 获取仓库
     * @param Request $request
     * @return \think\Response
     */
    public function warehouse(Request $request): \think\Response
    {
        # 010046701091
        $params = $request->get();
        if (!isset($params['bra_code']) || empty($params['bra_code'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择简码');
        }
        $product = new serProduct();
        $result = $product->warehouse($params['bra_code'], $params['payee_merchant_id'] ?? null);

        return throwResponse($result);
    }

    /**
     * 商品属性
     * @param Request $request
     * @return \think\Response
     */
    public function getGoodsProperty(Request $request): \think\Response
    {
        $param = $request->get();
        if (empty($param['type_id'])) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '商品类型主编号不能为空');
        }
        $product = new serProduct();

        $res = $product->getGoodsProperty($param['type_id']);
        foreach ($res as $v) {
            $result['list'] = $v;
        }

        // 所有产品类别
        $result['category'] = $product->getCategory();
//        return $result??[];
        return throwResponse($result);

    }

    /**
     * 获取 ES 产品列表
     * @return \think\Response
     */
    public function esList(Request $request)
    {
        $from = (int)$request->get('page', 0);
        $size = (int)$request->get('limit', 15);

        $where = [];

        // 中文名称
        if ($request->get('cn_product_name')) {
            $where[] = ['cn_product_name' => $request->get('cn_product_name')];
        }
        // 英文名称
        if ($request->get('en_product_name')) {
            $where[] = ['en_product_name' => $request->get('en_product_name')];
        }

        // 条码
        if ($request->get('bar_code')) {
            $where[] = ['bar_code' => (int)$request->get('bar_code')];
        }

        // 简码
        if ($request->get('short_code')) {
            $where[] = ['short_code' => $request->get('short_code')];
        }

        $sort = [['created_time' => 'desc']];

        $es = new ElasticSearchService();
        $params = [
            'index' => ['products'],
            'match' => $where,
            'page'  => $from,
            'limit' => $size,
            'sort'  => $sort,
        ];

        $data = $es->getDocumentList($params);
        $totalNum = $data['total']['value'];
        $result['list'] = $data['data'];
        $result['total'] = $totalNum;

        return throwResponse($result);

    }

}
