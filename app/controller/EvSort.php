<?php

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\es\Es;
use think\facade\Db;
use think\facade\Log;
use think\Response;
use Throwable;


/**
 * 数据表：
 * CREATE TABLE `vh_product_sort_by_ev` (
 * `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
 * `uid` int(10) unsigned NOT NULL COMMENT '运营人员uid',
 * `uname` varchar(255) NOT NULL COMMENT '运营人员名称',
 * `period` int(10) unsigned NOT NULL COMMENT '商品id',
 * `sort` tinyint(3) unsigned NOT NULL COMMENT '这个时间段的排序值',
 * `start_time` int(10) unsigned NOT NULL COMMENT '排序开始时间（时间戳）',
 * `end_time` int(10) unsigned NOT NULL COMMENT '排序解释时间（时间戳）',
 * `duration` tinyint(5) unsigned NOT NULL COMMENT '时长（分钟）',
 * `limit` tinyint(5) unsigned NOT NULL COMMENT '本次排序需要的额度值',
 * PRIMARY KEY (`id`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='曝光排序表';
 *
 * CREATE TABLE `vh_product_sort_by_ev_config` (
 * `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
 * `duration` tinyint(5) unsigned NOT NULL COMMENT '时长（分钟）',
 * `rule` json NOT NULL COMMENT '示例{1:100,2:50}',
 * PRIMARY KEY (`id`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='曝光排序配置';
 *
 * CREATE TABLE `vh_product_sort_by_ev_user` (
 * `uid` int(10) unsigned NOT NULL,
 * `limit` int(8) unsigned NOT NULL DEFAULT '0',
 * PRIMARY KEY (`uid`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='曝光排序运营人员管理表';
 *
 * CREATE TABLE `vh_product_sort_by_ev_user_record` (
 * `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
 * `uid` int(10) unsigned NOT NULL COMMENT '用户id',
 * `uname` varchar(255) NOT NULL COMMENT '用户名',
 * `type` tinyint(1) unsigned NOT NULL COMMENT '类型（1新增，2减少）',
 * `limit` tinyint(5) unsigned NOT NULL COMMENT '额度',
 * `describe` varchar(255) NOT NULL COMMENT '说明',
 * `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
 * PRIMARY KEY (`id`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='曝光排序运营人员消费记录表';
 */
class EvSort extends BaseController
{

    /**
     * 方法描述：新增排序
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2025/4/3 15:02
     * @param Request $request
     * @return Response
     */
    public function addSort(Request $request): Response
    {
        //运营的uid
        $uid = request()->header('vinehoo-uid', 0);
        $uname = request()->header('vinehoo-vos-name', '');
        if ($uname) {
            $uname = base64_decode($uname);
        }
        $period = $request->param('period', 0);
        $startTime = $request->param('start_time', 0);
        $endTime = $request->param('end_time', 0);
        $sort = $request->param('sort', 0);
        $type = $request->param('type', 1);
        //$configId = $request->param('config_id', 0);

        if ($startTime === 0 || $endTime === 0 || $startTime >= $endTime || $sort === 0 || $period === 0 || !in_array($type, [1, 2])) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '参数有误');
        }

        try {
            /*//验证配置id
            $config = Db::name('product_sort_by_ev_config')->where('id', $configId)->find();
            if (empty($config)) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, '配置id无效');
            }*/
            //本次需要扣除的额度
            //示例：{"1": 10,"2": 8}
            /*$rule = json_decode($config['rule'], true);
            if (!isset($rule[$sort])) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, '排序值无效');
            }
            $limit = $rule[$sort];

            //验证用户余额
            $userLimit = Db::name('product_sort_by_ev_user')->where('uid', $uid)->value('limit', 0);
            if ($userLimit < $limit) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, '余额不足');
            }*/
            $limit = 0;

            //验证是否有已存在的商品
            $ct = Db::name('product_sort_by_ev')->where('period', $period)->where('end_time', '>=', time())->count();
            if ($ct > 0) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, '当前商品已有在投记录，请取消后再添加');
            }

            //验证投放位置是否被使用
            $ct = Db::name('product_sort_by_ev')->where('sort', $sort)->where('end_time', '>=', time())->count();
            if ($ct > 0) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, '当前投放位置已被使用，请重新选择');
            }

            //拿到期数当前es信息
            $esInfo = Es::name('periods')->where([
                ['id', '=', $period],
            ])->field('id,pageviews,purchased_person')->find();
            if (empty($esInfo)) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, '期数信息查询失败');
            }
            //计算转化率
            $rate = $esInfo['purchased_person'] > 0 ? intval(($esInfo['purchased_person'] * 1000) / $esInfo['pageviews']) : 0;

            //验证分值
            $start_score = $this->getScoreByRank($sort);
            if ($start_score < 1) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, '当前投放排名分值为0，请重新选择排名');
            }
            $start_score = intval($start_score);//舍去小数
            $end_score = intval($this->getScoreByPeriod($period));
            $before_rank = $this->getRankByPeriod($period);

            // 计算分钟
            $duration = intval(($endTime - $startTime) / 60);


            // 开始事务
            Db::startTrans();

            // 添加排序记录
            $insertData = [
                'uid' => $uid,
                'uname' => $uname,
                'period' => $period,
                'sort' => $sort,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'duration' => $duration,
                'limit' => $limit,
                'start_score' => $start_score,
                'end_score' => $end_score,
                'before_rank' => $before_rank,
                'type' => $type,
                'start_page_view' => $esInfo['pageviews'],
                'start_purchased_person' => $esInfo['purchased_person'],
                'start_rate' => $rate,
            ];

            $sortId = Db::name('product_sort_by_ev')->insertGetId($insertData);
            if (!$sortId) {
                Db::rollback();
                return throwResponse(0, ErrorCode::PARAM_ERROR, '添加排序失败');
            }

            /*// 扣除用户额度
            $result = Db::name('product_sort_by_ev_user')
                ->where('uid', $uid)
                ->dec('limit', $limit)
                ->update();
            if (!$result) {
                Db::rollback();
                return throwResponse(0, ErrorCode::PARAM_ERROR, '扣除额度失败');
            }*/

            // 添加消费记录
            $recordData = [
                'uid' => $uid,
                'uname' => $uname,
                'type' => 2, // 2表示减少
                'limit' => $limit,
                'describe' => "为商品期数{$period}设置排序{$sort}，时长{$duration}分钟",
                'create_time' => time()
            ];

            $recordId = Db::name('product_sort_by_ev_user_record')->insertGetId($recordData);
            if (!$recordId) {
                Db::rollback();
                return throwResponse(0, ErrorCode::PARAM_ERROR, '记录消费失败');
            }

            // 提交事务
            Db::commit();

            return throwResponse();

        } catch (Throwable $e) {
            Db::rollback();
            return throwResponse(0, ErrorCode::PARAM_ERROR, '服务异常，请稍后再试：' . $e->getMessage());
        }
    }

    public function updateSort(Request $request)
    {

    }

    /**
     * 方法描述：取消排序
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2025/4/3 16:30
     * @param Request $request
     * @return Response
     */
    public function cancelSort(Request $request): Response
    {
        $id = $request->param('id', 0);
        $uid = request()->header('vinehoo-uid', 0);
        $uname = request()->header('vinehoo-vos-name', '');
        if ($uname) {
            $uname = base64_decode($uname);
        }

        if ($id === 0) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '参数有误');
        }

        try {
            // 查询排序记录
            $sortInfo = Db::name('product_sort_by_ev')->where('id', $id)->find();
            if (empty($sortInfo)) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, '排序记录不存在');
            }

            /*// 验证是否是设置排序的人
            if ($sortInfo['uid'] != $uid) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, '只有设置排序的人才能取消');
            }*/

            // 验证排序是否已经结束
            if ($sortInfo['end_time'] <= time()) {
                return throwResponse(0, ErrorCode::PARAM_ERROR, '排序已结束，无法取消');
            }

            // 计算已使用时间和剩余时间比例
            $usedTime = time() - $sortInfo['start_time'];
            $totalTime = $sortInfo['duration'] * 60;
            $remainingRatio = max(0, ($totalTime - $usedTime) / $totalTime);

            // 计算应返还的额度
            $refundLimit = round($sortInfo['limit'] * $remainingRatio);

            // 开始事务
            Db::startTrans();

            // 更新排序记录的结束时间为当前时间
            $result = Db::name('product_sort_by_ev')
                ->where('id', $id)
                ->update(['end_time' => time()]);
            if (!$result) {
                Db::rollback();
                return throwResponse(0, ErrorCode::PARAM_ERROR, '取消排序失败');
            }

            // 如果有额度需要返还
            if ($refundLimit > 0) {
                // 返还用户额度
                $result = Db::name('product_sort_by_ev_user')
                    ->where('uid', $uid)
                    ->inc('limit', $refundLimit)
                    ->update();
                if (!$result) {
                    Db::rollback();
                    return throwResponse(0, ErrorCode::PARAM_ERROR, '返还额度失败');
                }

                // 添加返还记录
                $recordData = [
                    'uid' => $uid,
                    'uname' => $uname,
                    'type' => 1, // 1表示新增
                    'limit' => $refundLimit,
                    'describe' => "取消商品期数{$sortInfo['period']}的排序，返还剩余额度",
                    'create_time' => time()
                ];

                $recordId = Db::name('product_sort_by_ev_user_record')->insertGetId($recordData);
                if (!$recordId) {
                    Db::rollback();
                    return throwResponse(0, ErrorCode::PARAM_ERROR, '记录返还失败');
                }
            }

            // 提交事务
            Db::commit();

            return throwResponse();

        } catch (Throwable $e) {
            Db::rollback();
            return throwResponse(0, ErrorCode::PARAM_ERROR, '服务异常，请稍后再试：' . $e->getMessage());
        }
    }

    /**
     * 方法描述：投流列表
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2025/4/3 17:00
     * @param Request $request
     * @return Response
     */
    public function sortList(Request $request): Response
    {
        $status = $request->param('status', 0); // 0:所有商品 1:在投商品 2:投放结束
        $page = $request->param('page', 1);
        $pageSize = $request->param('limit', 10);

        try {
            $query = Db::name('product_sort_by_ev');

            // 如果是在投商品，筛选结束时间大于当前时间的记录
            if ($status == 1) {
                $query = $query->where('end_time', '>', time());
            } elseif ($status == 2) {
                $query = $query->where('end_time', '<=', time());
            }

            // 获取总数
            $total = $query->count();

            // 获取列表数据
            $list = $query->order('sort asc')
                ->page($page, $pageSize)
                ->select()
                ->toArray();

            // 收集所有商品ID
            $periodIds = array_column($list, 'period');

            // 获取Redis信息
            $redisInfo = $this->getProductRedisInfo($periodIds);
            $scores = $redisInfo['scores'];
            $ranks = $redisInfo['ranks'];

            // 获取ES商品信息
            $esById = [];
            if (!empty($periodIds)) {
                $esResult = Es::name('periods')
                    ->field('id,title,brief,banner_img,price,purchased,vest_purchased,limit_number,saled_count,pageviews,purchased_person')
                    ->where([
                        ['_id', 'in', $periodIds]
                    ])
                    ->select()
                    ->toArray();

                foreach ($esResult as $v) {
                    $v['banner_img'] = env('ALIURL') . $v['banner_img'];
                    //$v['purchased'] = $v['purchased'] + $v['vest_purchased'];
                    //unset($v['vest_purchased']);
                    $v['purchased'] = $v['saled_count'];
                    //计算当前转化率
                    $v['rate'] = intval(($v['purchased_person'] * 1000) / $v['pageviews']);
                    $esById[$v['id']] = $v;
                }
            }

            // 处理列表数据，添加状态字段、分值和商品信息
            $now = time();
            foreach ($list as &$item) {
                // 添加状态
                if ($item['end_time'] <= $now) {
                    $item['status'] = '已结束';
                } else if ($item['start_time'] <= $now) {
                    $item['status'] = '进行中';
                } else {
                    $item['status'] = '未开始';
                }

                // 格式化时间
                $item['start_time'] = date('Y-m-d H:i:s', $item['start_time']);
                $item['end_time'] = date('Y-m-d H:i:s', $item['end_time']);

                // 添加分值
                $item['score'] = $scores[$item['period']] ?? 0;
                //添加排名
                $item['rank'] = $ranks[$item['period']] ?? '';

                // 添加商品信息
                if (isset($esById[$item['period']])) {
                    unset($esById[$item['period']]['id']);
                    $item = array_merge($item, $esById[$item['period']]);
                }

                //计算增量数据
                $item['increment_page_view'] = $esById[$item['period']]['pageviews'] - $item['start_page_view'];
                $item['increment_purchased_person'] = $esById[$item['period']]['purchased_person'] - $item['start_purchased_person'];
                $item['increment_rate'] = $item['increment_purchased_person'] > 0 ? intval(($item['increment_purchased_person'] * 1000) / $item['increment_page_view']) : 0;

                /**
                 * 增加建议，名词说明：自然转化率：（开始时转化率：start_rate ），整体转换率：（当前转化率：rate），增量转化率：（增加转换 increment_rate）
                 * 增量 >= 自然 并且 整体 >= 自然 提示：🟢 加大投放 投流引入优质流量，转化效果超出预期
                 * 增量 >= 自然 并且 整体 < 自然 提示：🟡 优化页面 投流新客转化良好，但整体转化下降，建议优化商品详情页
                 * 增量 < 自然 并且 整体 >= 自然 提示：🔵 观察效果 投流流量不如自然流量精准，但因其量大，总销量仍在增长。
                 * 增量 < 自然 并且 整体 < 自然 提示：🔴 暂停优化 流量不精准，还可能干扰了正常用户下单，建议立即暂停投放优化
                 * 已投放时间120分钟以上 并且 增量<千分之1 提示：❌ 停止投放 吸引用户点击但完全不具有购买意向，需要找原因调整
                 */
                if ($item['increment_rate'] >= $item['start_rate'] && $item['rate'] >= $item['start_rate']) {
                    $item['suggest'] = '🟢 加大投放 投流引入优质流量，转化效果超出预期';
                } else if ($item['increment_rate'] >= $item['start_rate'] && $item['rate'] < $item['start_rate']) {
                    $item['suggest'] = '🟡 优化页面 投流新客转化良好，但整体转化下降，建议优化商品详情页';
                } else if ($item['increment_rate'] < $item['start_rate'] && $item['rate'] >= $item['start_rate']) {
                    $item['suggest'] = '🔵 观察效果 投流流量不如自然流量精准，但因其量大，总销量仍在增长。';
                } else if ($item['increment_rate'] < $item['start_rate'] && $item['rate'] < $item['start_rate']) {
                    $item['suggest'] = '🔴 暂停优化 流量不精准，还可能干扰了正常用户下单，建议立即暂停投放优化';
                } else if ($item['increment_purchased_person'] <= 1 && $item['start_time'] <= ($now - 7200)) {
                    $item['suggest'] = '❌ 停止投放 吸引用户点击但完全不具有购买意向，需要找原因调整';
                } else {
                    $item['suggest'] = '';
                }
            }

            $result = [
                'list' => $list,
                'total' => $total,
            ];

            return throwResponse($result);

        } catch (Throwable $e) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '获取列表失败，请稍后再试：' . $e->getMessage());
        }
    }

    /**
     * 格式化剩余时间
     * @param int $seconds 剩余秒数
     * @return string 格式化后的时间
     */
    private function formatRemainingTime(int $seconds): string
    {
        if ($seconds <= 0) {
            return '0秒';
        }

        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;

        $result = '';
        if ($hours > 0) {
            $result .= $hours . '小时';
        }
        if ($minutes > 0) {
            $result .= $minutes . '分钟';
        }
        if ($secs > 0 && $hours == 0) { // 只有在小时为0时才显示秒
            $result .= $secs . '秒';
        }

        return $result;
    }

    /**
     * 方法描述：获取用户额度
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2025/4/3 17:30
     * @param Request $request
     * @return Response
     */
    public function getUserLimit(Request $request): Response
    {
        $uid = request()->header('vinehoo-uid', 0);

        try {
            // 查询用户额度
            $userLimit = Db::name('product_sort_by_ev_user')->where('uid', $uid)->value('limit', 0);

            return throwResponse(['limit' => $userLimit]);

        } catch (Throwable $e) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '获取用户额度失败，请稍后再试');
        }
    }

    /**
     * 方法描述：获取用户消费记录
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2025/4/3 17:40
     * @param Request $request
     * @return Response
     */
    public function getUserLimitRecord(Request $request): Response
    {
        $uid = request()->header('vinehoo-uid', 0);
        $page = $request->param('page', 1);
        $pageSize = $request->param('limit', 10);
        $status = $request->param('status', 0);

        $query = Db::name('product_sort_by_ev_user_record');

        if ($status == 1) {
            $query = $query->where('uid', $uid);
        }

        try {
            // 获取总数
            $total = $query->count();

            // 获取列表数据
            $list = $query
                ->order('id desc')
                ->page($page, $pageSize)
                ->select()
                ->toArray();

            // 处理列表数据
            foreach ($list as &$item) {
                $item['type_text'] = $item['type'] == 1 ? '增加' : '减少';
                $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            }

            $result = [
                'list' => $list,
                'total' => $total,
            ];

            return throwResponse($result);

        } catch (Throwable $e) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '获取消费记录失败，请稍后再试');
        }
    }

    /**
     * 方法描述：获取规则配置
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2025/4/3 15:11
     * @param Request $request
     * @return Response
     */
    public function config(Request $request): Response
    {
        try {
            $list = Db::name('product_sort_by_ev_config')->order('duration asc')->select()->map(function ($item) {
                $item['rule'] = json_decode($item['rule'], true);
                return $item;
            });
            return throwResponse(['list' => $list]);
        } catch (Throwable $e) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '获取配置失败，请稍后');
        }
    }

    /**
     * 方法描述：获取当前生效的排序商品ID列表
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2025/4/3 18:00
     * @param Request $request
     * @return Response
     */
    public function getActiveSortIds(Request $request): Response
    {
        try {
            // 获取当前时间戳
            $now = time();

            // 查询当前生效的排序记录，并按sort值升序排序
            $productIds = Db::name('product_sort_by_ev')
                ->where('start_time', '<=', $now)
                ->where('end_time', '>', $now)
                ->order('sort asc')
                ->column('sort,start_time,start_score,end_score,duration,type,before_rank', 'period');

            $list = [];
            foreach ($productIds as $period => $v) {
                if ($v['type'] == 1) {
                    $list[$period] = ['type' => $v['type'], 'val' => $v['sort']];
                } else {
                    //滑动方式也改为固定排名方式，只是固定位置是实时计算出来的
                    $list[$period] = ['type' => /*$v['type']*/
                        1, 'val' => $this->calculateCurrentRank($v['start_time'], $v['sort'], $v['before_rank'], $v['duration'])];
                }
            }
            return throwResponse($list);

        } catch (Throwable $e) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '获取排序商品列表失败，请稍后再试');
        }
    }

    /**
     * 方法描述：重置所有用户余额为1000
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2025/4/3 18:30
     * @param Request $request
     * @return Response
     */
    public function resetUserLimits(Request $request): Response
    {
        try {
            // 获取所有用户当前余额
            $users = Db::name('product_sort_by_ev_user')->select()->toArray();
            // 更新所有用户余额为1000
            Db::name('product_sort_by_ev_user')->where('uid', '<', 999999)->update(['limit' => 1000]);

            // 记录每个用户的余额变动
            $recordData = [];
            foreach ($users as $user) {
                $diffLimit = 1000 - $user['limit']; // 计算差值
                if ($diffLimit != 0) { // 只记录有变动的用户
                    $recordData[] = [
                        'uid' => $user['uid'],
                        'uname' => Db::name('product_sort_by_ev')->where('uid', $user['uid'])->value('uname', ''), // 获取用户名
                        'type' => ($diffLimit > 0) ? 1 : 2, // 1增加 2减少
                        'limit' => abs($diffLimit), // 取绝对值
                        'describe' => '系统重置余额到1000',
                        'create_time' => time()
                    ];
                }
            }
            if (count($recordData) > 0) {
                Db::name('product_sort_by_ev_user_record')->insertAll($recordData);
            }
            return throwResponse();

        } catch (Throwable $e) {
            return throwResponse(0, ErrorCode::PARAM_ERROR, '重置用户余额失败，请稍后再试：' . $e->getMessage());
        }
    }

    /**
     * 批量获取商品Redis信息
     * @param array $productIds 商品ID数组
     * @return array [
     *     'scores' => [商品ID => 分值],
     *     'details' => [商品ID => 商品详情]
     *     'ranks' => [商品ID => 当前排名（从1开始）]
     * ]
     */
    private function getProductRedisInfo(array $productIds): array
    {
        if (empty($productIds)) {
            return [
                'scores' => [],
                'details' => [],
                'ranks' => []
            ];
        }

        try {
            $redis = new \Redis();
            $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
            $redis->auth(env('cache.PASSWORD'));
            $redis->select(15);

            // 使用pipeline获取分值、详情和排名
            $pipeline = $redis->multi(\Redis::PIPELINE);

            // 获取分值
            foreach ($productIds as $id) {
                $pipeline->zScore('vm:product:scores', $id);
            }

            // 获取详情
            foreach ($productIds as $id) {
                $pipeline->get("vm:product:details:{$id}");
            }

            // 获取排名（zRevRank结果+1转换为从1开始的排名）
            foreach ($productIds as $id) {
                $pipeline->zRevRank('vm:product:scores', $id);
            }

            $results = $pipeline->exec();

            // 处理分值结果
            $scores = [];
            $totalIds = count($productIds);
            for ($i = 0; $i < $totalIds; $i++) {
                if ($results[$i] !== false) {
                    $scores[$productIds[$i]] = $results[$i];
                }
            }

            // 处理详情结果
            $details = [];
            for ($i = $totalIds; $i < 2 * $totalIds; $i++) {
                $index = $i - $totalIds;
                $raw = $results[$i];
                if ($raw !== false && $raw !== null) {
                    $detail = json_decode($raw, true);
                    if ($detail) {
                        $details[$productIds[$index]] = $detail;
                    }
                }
            }

            // 处理排名结果（ZRANK返回的索引+1）
            $ranks = [];
            for ($i = 2 * $totalIds; $i < 3 * $totalIds; $i++) {
                $index = $i - 2 * $totalIds;
                $rank = $results[$i];
                if ($rank !== false && $rank !== null) {
                    $ranks[$productIds[$index]] = $rank + 1; // 转换为从1开始的排名
                }
            }

            return [
                'scores' => $scores,
                'details' => $details,
                'ranks' => $ranks
            ];
        } catch (Throwable $e) {
            Log::error('获取商品Redis信息失败: ' . $e->getMessage());
            return [
                'scores' => [],
                'details' => [],
                'ranks' => []
            ];
        }
    }

    /**
     * 方法描述：获取指定排名的分值
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2025/7/23 14:38
     * @param int $rank
     * @return float
     */
    private function getScoreByRank(int $rank): ?float
    {
        if ($rank < 1) {
            return 0;
        }

        try {
            $redis = new \Redis();
            $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
            $redis->auth(env('cache.PASSWORD'));
            $redis->select(15);

            // 获取指定排名的成员和分值
            // Redis的排名是从0开始的，所以传入的rank需要减1
            $result = $redis->zRevRange('vm:product:scores', $rank - 1, $rank - 1, true);

            if (empty($result)) {
                return 0;
            }

            // 返回第一个元素的分值
            return reset($result);

        } catch (Throwable $e) {
            Log::error('获取排名分值失败: ' . $e->getMessage());
            return 0;
        }
    }


    /**
     * 方法描述：获取期数的分值
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2025/7/29 11:31
     * @param int $period
     * @return float|null
     */
    private function getScoreByPeriod(int $period): ?float
    {
        try {
            $redis = new \Redis();
            $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
            $redis->auth(env('cache.PASSWORD'));
            $redis->select(15);

            $info = $redis->get('vm:product:details:' . $period);

            if (empty($info)) {
                return 0;
            }

            return json_decode($info, true)['score'] ?? 0;

        } catch (Throwable $e) {
            Log::error('获取期数排名分值失败: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 方法描述：获取期数的排名
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2025/7/30 16:58
     * @param int $period
     * @return int
     */
    public function getRankByPeriod(int $period): int
    {

        try {
            $redis = new \Redis();
            $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
            $redis->auth(env('cache.PASSWORD'));
            $redis->select(15);

            // 获取期数在有序集合中的排名（降序，分值高的排名靠前）
            $rank = $redis->zRevRank('vm:product:scores', $period);

            if ($rank === false) {
                return 0;
            }

            // Redis排名从0开始，转换为从1开始
            return $rank + 1;

        } catch (Throwable $e) {
            return 0;
        }
    }

    /**
     * 方法描述：线性平滑计算当前排名（支持正向递减或反向递增）
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2025/8/28 10:03
     * @param int $startTime 开始时间
     * @param int $startRank 开始排名
     * @param int $endRank 结束排名
     * @param int $durationMinutes 持续时间
     * @return int
     */
    private function calculateCurrentRank(
        int $startTime,
        int $startRank,
        int $endRank,
        int $durationMinutes
    ): int
    {
        $currentTime = time();
        // 如果当前时间小于开始时间，直接返回开始排名
        if ($startTime > $currentTime) {
            return $startRank;
        }

        $elapsedTime = $currentTime - $startTime;
        $totalTime = $durationMinutes * 60;

        // 如果已经超过持续时间，直接返回结束排名
        if ($elapsedTime >= $totalTime) {
            return $endRank;
        }

        // 计算进度比例（0到1之间）
        $progress = $elapsedTime / $totalTime;

        // 线性计算当前排名
        $currentRank = $startRank + ($endRank - $startRank) * $progress;

        // 返回整数排名
        return (int)$currentRank;
    }

    /**
     * 获取商品分页列表
     */
    public function getProductsByPage(Request $request): Response
    {
        $page = max(1, (int)$request->get('page', 1));
        $size = max(1, (int)$request->get('limit', 10));
        $start = ($page - 1) * $size;
        $end = $start + $size - 1;

        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        /*$redis->connect("r-8vbyf9qn03iwmy7w5npd.redis.zhangbei.rds.aliyuncs.com", 6379);
        $redis->auth("NRDSYa5e6EWZuJ3d");*/
        $redis->select(15);

        // 获取总数和ID列表
        $pipeline = $redis->multi(\Redis::PIPELINE);
        $pipeline->zCard('vm:product:scores');
        $pipeline->zRevRange('vm:product:scores', $start, $end, true);
        $results = $pipeline->exec();

        $total = $results[0];
        $idsWithScores = $results[1];

        if (empty($idsWithScores)) {
            return throwResponse(['list' => [], 'total' => $total]);
        }

        $productIDs = array_keys($idsWithScores);


        // 获取Redis信息
        $redisInfo = $this->getProductRedisInfo($productIDs);

        $labels = Db::name('recommend_label')
            ->where('is_delete', 0)
            ->where('type', 2)
            ->column('name', 'id');

        // 获取ES信息
        $esById = [];
        if (count($productIDs) > 0) {
            //拿到投放类型type,1固定位置，2滑动位置
            $sortTypes = Db::name('product_sort_by_ev')->where('end_time', '>=', time())->column('type', 'period');
            $esResult = Es::name('periods')
                ->field('id,periods_type,title,brief,banner_img,price,purchased,vest_purchased,limit_number,label')
                ->where([
                    ['_id', 'in', $productIDs]
                ])
                ->select()->toArray();
            foreach ($esResult as $v) {
                //处理标签
                $v['label'] = array_values(array_intersect_key($labels, array_flip(explode(',', $v['label']))));
                $v['banner_img'] = env('ALIURL') . $v['banner_img'];
                $v['purchased'] = $v['purchased'] + $v['vest_purchased'];
                $v['sortType'] = isset($sortTypes[$v['id']]) ? ($sortTypes[$v['id']] == 1 ? '固定位置' : '滑动位置') : '';
                unset($v['vest_purchased']);
                $esById[$v['id']] = $v;
            }
        }

        $columnNames = [
            'score' => '总分',
            'exposure' => '曝光量',
            'exposure_score' => '曝光量得分',
            'click' => '浏览量',
            'click_score' => '浏览量得分',
            'order' => '订单量',
            'order_score' => '订单量得分',
            'comment' => '评论量',
            'comment_score' => '评论量得分',
            'base_score' => '基础得分',
            'manual' => '投流排名',
            'manual_score' => '投流排名得分',
            'is_new' => '是否新品',
            'decay_factor' => '衰减系数',
            'decay_score' => '衰减得分',
            'sales_time' => '上架时间',
        ];

        $products = [];
        foreach ($productIDs as $i => $id) {
            if (isset($redisInfo['details'][$id])) {
                $p = $esById[$id];
                $p['score'] = $idsWithScores[$id];
                $p['scoreDetails'] = [];
                foreach ($redisInfo['details'][$id] as $c => $v) {
                    if ($c == 'id' || $c == 'score') continue;
                    $p['scoreDetails'][] = ['label' => $columnNames[$c] ?? $c, 'value' => is_bool($v) ? $v === true ? '是' : '否' : $v];
                }
                $products[] = $p;
            }
        }

        return throwResponse(['list' => $products, 'total' => $total]);
    }

    /**
     * 方法描述：获取权重配置
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2025/4/25 10:19
     * @param Request $request
     * @return Response
     */
    public function weightConfig(Request $request): Response
    {
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(15);
        return throwResponse(json_decode($redis->get("vm:cf"), true));
    }

    /**
     * 方法描述：修改权重配置
     * User：LiGenHui
     * Email：<EMAIL>
     * DateTime:2025/4/25 10:24
     * @param Request $request
     * @return Response
     */
    public function updateWeightConfig(Request $request): Response
    {
        if (!empty($request->param('cf'))) {
            $config = $request->param('cf');
            // 验证 time_weight 时间连续性
            if (isset($config['time_weight'])) {
                $timeWeights = $config['time_weight'];
                $lastEndHour = 0;
                $errors = [];

                // 按开始时间排序
                usort($timeWeights, function ($a, $b) {
                    return $a['start_hour'] <=> $b['start_hour'];
                });

                foreach ($timeWeights as $index => $timeWeight) {
                    // 检查必填字段
                    if (!isset($timeWeight['start_hour']) || !isset($timeWeight['end_hour'])) {
                        $errors[] = "第 {$index} 个时间段缺少 start_hour 或 end_hour";
                        continue;
                    }

                    // 检查时间范围有效性
                    if ($timeWeight['start_hour'] >= $timeWeight['end_hour']) {
                        $errors[] = "第 {$index} 个时间段结束时间必须大于开始时间";
                    }

                    // 检查时间连续性
                    if ($timeWeight['start_hour'] != $lastEndHour) {
                        $errors[] = "时间段不连续，在 {$lastEndHour} 点和 {$timeWeight['start_hour']} 点之间有间隔";
                    }

                    $lastEndHour = $timeWeight['end_hour'];
                }

                if (!empty($errors)) {
                    return throwResponse([], ErrorCode::PARAM_ERROR, '时间权重配置错误: ' . implode('; ', $errors));
                }
            }
            $redis = new \Redis();
            $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
            $redis->auth(env('cache.PASSWORD'));
            $redis->select(15);
            $redis->set("vm:cf", json_encode($request->param("cf"), JSON_UNESCAPED_UNICODE));
        }
        return throwResponse();
    }

}