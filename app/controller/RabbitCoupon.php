<?php
declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\service\RabbitCoupon as PeriodsRabbitCoupon;
use app\service\Package;
use app\service\Products;
use app\service\Periods;
use app\Request;
use app\validate\Buyer;
use app\validate\Copywriter;
use app\validate\Operations;


class RabbitCoupon extends BaseController
{

    /**
     * 添加兔头商品文案
     *
     * @param Request $request
     * @return false|string|\think\Response
     */
    public function create(Request $request)
    {
        // 参数
        $params = $request->param();
        // 获取兔头id
        $params['id'] = $this->getGeneratorID(1);
        if (!$params['id']) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '生成兔头优惠券文案失败');
        }
        // 操作信息
        $params['creator_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['creator_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['creator_name']) {
            $params['creator_name'] = base64_decode($params['creator_name']);
        }
        // 参数验证
//        $validate = new Copywriter();
//        $params['creator_id'] = $request->header('vinehoo-uid', '0');
//        $validate_params = $validate->check($params);
//        if (!$validate_params) {
//            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
//        }
        // 添加运营信息 时间戳转换
        if (isset($params['predict_shipment_time'])) {
            $params['predict_shipment_time'] = strtotime($params['predict_shipment_time']);
        }
        if (isset($params['onsale_time'])) {
            $params['onsale_time'] = strtotime($params['onsale_time']);
        }
        if (isset($params['sell_time'])) {
            $params['sell_time'] = strtotime($params['sell_time']);
        }
        if (isset($params['sold_out_time'])) {
            $params['sold_out_time'] = strtotime($params['sold_out_time']);
        }

        // 限购规则转 json 字符串
        if (isset($params['quota_rule']) && !empty($params['quota_rule'])) {
            $params['quota_rule'] = json_encode($params['quota_rule']);
        }
        // 验证兔头参数
        // 验证商品 id
        $update_validate = $this->rabbitCouponValidate($params);

        if (!empty($update_validate)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $update_validate);
        }
        //
        $params['onsale_review_status'] = 3;
        $params['created_time'] = time();
        // 不变初始值
        if (isset($params['limit_number']) && !empty($params['limit_number'])) {
            $params['invariant_number'] = $params['limit_number'];
        }
        // 添加兔头商品
        $rabbit_coupon = new PeriodsRabbitCoupon();
        $result = $rabbit_coupon->create($params);

        if (!$result) {
            return throwResponse($result, ErrorCode::EXEC_ERROR, '产品兔头添加失败');
        }
        return throwResponse($result);

    }

    /**
     * 添加编辑采购信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateBuyerInfo(Request $request)
    {
        // 参数
        $params = $request->post();
        // 参数验证
        $validate = new Buyer();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }

        $rabbit_coupon = new PeriodsRabbitCoupon();

        // 添加运营信息 时间戳转换
        if (isset($params['predict_shipment_time'])) {
            $params['predict_shipment_time'] = strtotime($params['predict_shipment_time']);
        }
        if (isset($params['onsale_time'])) {
            $params['onsale_time'] = strtotime($params['onsale_time']);
        }
        if (isset($params['sell_time'])) {
            $params['sell_time'] = strtotime($params['sell_time']);
        }
        if (isset($params['sold_out_time'])) {
            $params['sold_out_time'] = strtotime($params['sold_out_time']);
        }

        // 限购规则转 json 字符串
        if (isset($params['quota_rule']) && !empty($params['quota_rule'])) {
            $params['quota_rule'] = json_encode($params['quota_rule']);
        }

        $result = $rabbit_coupon->update($params);

        if (!$result['status']) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, '采购信息添加失败');
        }

        return throwResponse($result['data']);

    }

    /**
     * 添加编辑运营信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update(Request $request)
    {
        // 参数
        $params = $request->post();
        // 参数验证
        $validate = new Operations();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }

        // 验证商品 id
        $update_validate = $this->updateValidate($params);
        if (!empty($update_validate)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $update_validate);
        }

        $rabbit_coupon = new PeriodsRabbitCoupon();

        // 添加运营信息 时间戳转换
        if (isset($params['predict_shipment_time'])) {
            $params['predict_shipment_time'] = strtotime($params['predict_shipment_time']);
        }
        if (isset($params['onsale_time'])) {
            $params['onsale_time'] = strtotime($params['onsale_time']);
        }
        if (isset($params['sell_time'])) {
            $params['sell_time'] = strtotime($params['sell_time']);
        }
        if (isset($params['sold_out_time'])) {
            $params['sold_out_time'] = strtotime($params['sold_out_time']);
        }

        // 限购规则转 json 字符串
        if (isset($params['quota_rule']) && !empty($params['quota_rule'])) {
            $params['quota_rule'] = json_encode($params['quota_rule']);
        }
        $period_info = $rabbit_coupon->getOne($params['id'], 'onsale_verify_status');
        // 二次确认上架后不变初始值
        if ($period_info['onsale_verify_status'] == 1) {
            unset($params['limit_number'], $params['invariant_number']);
        }
        $result = $rabbit_coupon->update($params);

        if (!$result['status']) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, '运营信息添加失败');
        }

        return throwResponse($result['data']);

    }

    /**
     * 商品详细
     *
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(Request $request)
    {
        // 参数
        $params = $request->get();
        $params['id'] = (int)$params['id'];
        // 验证商品 id
        $update_validate = $this->updateValidate($params);
        if (!empty($update_validate)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $update_validate);
        }

        $rabbit_coupon = new PeriodsRabbitCoupon();

        // 详细信息
        $result = $rabbit_coupon->getOne($params['id'], '*');
        if (empty($result)) {
            return throwResponse($result, ErrorCode::PARAM_ERROR, '期数不存在');
        }
        // 优惠券详细
        $coupon_url = env('ITEM.COUPON_URL').'/coupon/v3/coupon/detail'. '?coupon_id='. $result['coupon_id'];
        $coupon_info = get_url($coupon_url);
        $coupon_info = get_interior_http_response($coupon_info);
        $result['coupon_info'] = $coupon_info;

        return throwResponse($result);

    }

    /**
     * 更新产品参数验证
     *
     * @param array $params
     * @return string
     */
    protected function updateValidate(array $params): string
    {
        // 提示信息
        $message = '';

        // 商品 id 验证
        if (!isset($params['id']) || empty($params['id'])) {
            $message .= '请选择商品';
        }

        return $message;
    }

    /**
     * 验证兔头商品
     * @param array $params
     * @return string
     */
    protected function rabbitCouponValidate(array $params): string
    {
        // 提示信息
        $message = '';

        // 商品兔头信息验证
        if (!isset($params['price']) || empty($params['price'])) {
            $message .= '请输入优惠券价格 ';
        }

        if (!isset($params['rabbit_price']) || empty($params['rabbit_price'])) {
            $message .= '请输入兔头价格 ';
        }

        if (!isset($params['coupon_id']) || empty($params['coupon_id'])) {
            $message .= '请选择优惠券id ';
        }

        return $message;
    }

    /**
     * @param Request $request
     * @return \think\Response
     */
    public function rabbitExchange(Request $request)
    {
        $params =$request->param();
        $params['uid']=$request->header('vinehoo-uid');
        if (empty($params['uid'])) {
            return throwResponse([],ErrorCode::PARAM_ERROR,"uid:用户id不是有效值");
        }
        if (!isset($params['id']) || empty($params['id'])) {
            return throwResponse([],ErrorCode::PARAM_ERROR,"兔头优惠券id必传");
        }
        try {
            $result = (new PeriodsRabbitCoupon())->rabbitExchange($params);
        } catch (\Exception $e) {
            $code = $e->getCode()==0?10001:$e->getCode();
            return throwResponse([], $code, $e->getMessage());
        }

        return throwResponse($result, 0, '操作成功');
    }

}
