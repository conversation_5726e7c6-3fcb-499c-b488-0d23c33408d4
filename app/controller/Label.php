<?php


namespace app\controller;


use app\BaseController;
use app\ErrorCode;
use app\Request;
use think\facade\Validate;
use app\service\Label as LabelService;

class Label extends BaseController
{
    /**
     * Description:推荐标签列表
     * Author: zrc
     * Date: 2023/4/24
     * Time: 16:06
     * @param Request $request
     * @return \think\Response
     */
    public function labelList(Request $request)
    {
        $params   = $request->param();
        $validate = Validate::rule([
            'page|页码'    => 'require|number|>:0',
            'limit|每页条数' => 'require|number|>:0'
        ]);
        if (!$validate->check($params)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $labelService = new LabelService();
        $result       = $labelService->getLabelList($params);
        if ($result['status'] == false) {
            return throwResponse([], ErrorCode::EXEC_ERROR, $result['msg']);
        }
        return throwResponse($result['data']);
    }

    /**
     * Description:新增推荐标签
     * Author: zrc
     * Date: 2023/4/24
     * Time: 16:46
     * @param Request $request
     * @return \think\Response
     */
    public function labelAdd(Request $request)
    {
        $params             = $request->param();
        $params['operator'] = $request->header('vinehoo-uid');
        $validate           = Validate::rule([
            'operator|后台用户ID' => 'require|number|>:0',
            'name|标签名称'       => 'require|max:100',
            'type|标签类型'       => 'require|in:1,2'
        ]);
        if (!$validate->check($params)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $labelService = new LabelService();
        $result       = $labelService->labelAddData($params);
        if ($result['status'] == false) {
            return throwResponse([], ErrorCode::EXEC_ERROR, $result['msg']);
        }
        return throwResponse($result['data']);
    }

    /**
     * Description:修改推荐标签
     * Author: zrc
     * Date: 2023/4/24
     * Time: 16:59
     * @param Request $request
     * @return \think\Response
     */
    public function labelEdit(Request $request)
    {
        $params             = $request->param();
        $params['operator'] = $request->header('vinehoo-uid');
        $validate           = Validate::rule([
            'operator|后台用户ID' => 'require|number|>:0',
            'id|自增ID'         => 'require|number|>:0'
        ]);
        if (!$validate->check($params)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $labelService = new LabelService();
        $result       = $labelService->labelEditData($params);
        if ($result['status'] == false) {
            return throwResponse([], ErrorCode::EXEC_ERROR, $result['msg']);
        }
        return throwResponse($result['data']);
    }

    /**
     * 查询标签名称
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getLabelByName(Request $request): \think\Response
    {
        $params       = $request->get();
        $labelService = new LabelService();
        $result       = $labelService->getLabelByName($params);
        return throwResponse($result);
    }
}