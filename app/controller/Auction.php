<?php
declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\service\Auction as PeriodsAuction;
use app\service\Package;
use app\service\Products;
use app\service\Periods;
use app\Request;
use app\validate\AuctionBid;
use app\validate\Buyer;
use app\validate\Copywriter;
use app\validate\Operations;
use app\service\Other;


class Auction extends BaseController
{

    /**
     * 添加闪购商品文案
     *
     * @param Request $request
     * @return false|string|\think\Response
     */
    public function create(Request $request)
    {
        // 参数
        $params = $request->post();
        // 获取闪购id
        $params['id'] = $this->getGeneratorID(1);
        if (!$params['id']) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '生成闪购文案失败');
        }
        // 操作信息
        $params['creator_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['creator_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['creator_name']) {
            $params['creator_name'] = base64_decode($params['creator_name']);
        }
        // 参数验证
        $validate = new Copywriter();
        $params['creator_id'] = $request->header('vinehoo-uid', '0');
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $params['created_time'] = time();
        // 获取商品产品评分饮用建议
        $product_list = [];
        // 产品简码
        $params['short_code']  = '';
        $params['product_main_category']= '';

        if ($params['short_code'] != '') {
            $params['short_code'] = trim($params['short_code'], ',');
        }
        if ($params['product_main_category'] != '') {
            $params['product_main_category'] = trim($params['product_main_category'], ',');
        }
        // 添加闪购商品
        $auction = new PeriodsAuction();
        $result = $auction->create($params);
        if (!$result) {
            return throwResponse($result, ErrorCode::EXEC_ERROR, '产品闪购添加失败');
        }
        // 添加商品产品评分饮用建议
        if (!empty($params['product_info'])) {
            $other_ser = new Other();
            $other_ser->createProductInfo($result, $params['product_info']);
        }
        return throwResponse($result);

    }


    /**
     * 添加编辑采购信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateBuyerInfo(Request $request)
    {
        // 参数
        $params = $request->post();
        // 参数验证
        $validate = new Buyer();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }

        $auction = new PeriodsAuction();

        // 添加采购信息
        $result = $auction->update($params);

        if (!$result['status']) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, '采购信息添加失败');
        }

        return throwResponse($result['data']);

    }

    /**
     * 添加编辑运营信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update(Request $request)
    {
        // 参数
        $params = $request->post();
        // 参数验证
        $validate = new Operations();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }

        // 验证商品 id
        $update_validate = $this->updateValidate($params);
        if (!empty($update_validate)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $update_validate);
        }

        $auction = new PeriodsAuction();
        // 验证绑定产品
        $period_info = $auction->getOne((int)$params['id'], 'product_id, buyer_review_status');
        if (empty($period_info)) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, '未查询到期数信息');
        }
        $old_product_id = explode(',', $period_info['product_id']);
        $product_id = explode(',', $params['product_id']);
        $is_del_product = 0;
        foreach ($old_product_id as $v) {
            if (empty($v)) {
                continue;
            }
            if (!in_array($v, $product_id)) {
                $is_del_product = 1;
            }
        }
        if ($is_del_product == 1 && $period_info['buyer_review_status'] == 3) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, '采购审核后不允许删除绑定产品');
        }
        // 添加运营信息 时间戳转换
        if (isset($params['predict_shipment_time'])) {
            $params['predict_shipment_time'] = strtotime($params['predict_shipment_time']);
        }
        if (isset($params['onsale_time'])) {
            $params['onsale_time'] = strtotime($params['onsale_time']);
        }
        if (isset($params['sell_time'])) {
            $params['sell_time'] = strtotime($params['sell_time']);
        }
        if (isset($params['sold_out_time'])) {
            $params['sold_out_time'] = strtotime($params['sold_out_time']);
        }

        // 限购规则转 json 字符串
        if (isset($params['quota_rule']) && !empty($params['quota_rule'])) {
            $params['quota_rule'] = json_encode($params['quota_rule']);
        }

        // 如果前端返回图片域名，则删除域名
        if (isset($params['banner_img']) && $params['banner_img'] != '') {
            $params['banner_img'] = str_replace(env('ALIURL'),"", $params['banner_img']);
        }
        if (isset($params['product_img']) && $params['product_img'] != '') {
            $params['product_img'] = str_replace(env('ALIURL'),"", $params['product_img']);
        }
        if (isset($params['video_cover']) && $params['video_cover'] != '') {
            $params['video_cover'] = str_replace(env('ALIURL'),"", $params['video_cover']);
        }


        // 查询产品简码，大类
        if ($params['product_id'] != '') {
            $other_ser = new Other();
            $product_info = $other_ser->getProductList($params);
            $params['short_code'] = $product_info['short_code'];
            $params['product_main_category'] = $product_info['product_main_category'];
        }
        $result = $auction->update($params);

        if (!$result['status']) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, '运营信息添加失败');
        }

        // 添加商品产品评分饮用建议
        if (!empty($params['product_info'])) {
            $other_ser = new Other();
            $other_ser->createProductInfo((int)$params['id'], $params['product_info']);
        }
        $periods_ser = new Periods(0);
        // 更新 json 文件
        $periods_ser->create_period_json((int)$params['id'], 0,0,-1);
        // 更新 CDN
        $periods_ser::CDNrefreshObject((int)$params['id']);
        return throwResponse($result['data']);

    }

    /**
     * 商品详细
     *
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(Request $request)
    {
        // 参数
        $params = $request->get();
        $params['id'] = (int)$params['id'];
        // 验证商品 id
        $update_validate = $this->updateValidate($params);
        if (!empty($update_validate)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $update_validate);
        }

        $auction = new PeriodsAuction();

        // 详细信息
        $result = $auction->getOne($params['id'], '*');
        if (empty($result)) {
            return throwResponse($result, ErrorCode::PARAM_ERROR, '期数不存在');
        }
        // 套餐信息
        $package_ser = new Package(11);
        $where['period_id'] = $params['id'];
        $result['package'] = $package_ser->getPackage($where);
        // 产品信息
        $products_ser = new Products();
        $result['product_list'] = $products_ser->getListById($result['product_id'], $params['id']);
        // 是否存在上架记录
        $periods = new Periods(0);
        $result['is_onsale_record'] = 0;
        $onsale_record = $periods->getOnSaleRecord($params['id'], 0);
        if ($onsale_record) {
            $result['is_onsale_record'] = 1;
        }


        return throwResponse($result);

    }

    /**
     * 拍卖期数设置
     * @param Request $request
     * @return \think\Response|void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function auctionSet(Request $request) {
        $params = $request->post();
        // 参数验证
        $params['periods_type'] = (int)$params['periods_type'] ?? 0;
        $period = (int)$params['period'] ?? 0;
        if (!isset($params['express_id']) || ((int)$params['express_id']) < 1) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '快递方式必选');
        }

        // 时间戳转换
        if (isset($params['onsale_time'])) {
            $params['onsale_time'] = strtotime($params['onsale_time']);
        }
        if (isset($params['sold_out_time'])) {
            $params['sold_out_time'] = strtotime($params['sold_out_time']);
        }
        if (isset($params['sell_time'])) {
            $params['sell_time'] = strtotime($params['sell_time']);
        }
        if (isset($params['predict_shipment_time'])) {
            $params['predict_shipment_time'] = strtotime($params['predict_shipment_time']);
        }
        if (isset($params['closing_auction_time'])) {
            $params['closing_auction_time'] = strtotime($params['closing_auction_time']);
        }


        $auction = new PeriodsAuction();
        $params['id'] = $period;
        $result = $auction->auctionSet($params);

        return throwResponse($result);
    }

    /**
     * 添加用户出价记录
     * @param Request $request
     * @return \think\Response
     */
    public function createDidRecord(Request $request): \think\Response
    {
        // 参数
        $params = $request->post();
        // 参数验证
        $validate = new AuctionBid();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }

        $auction = new PeriodsAuction();

        // 添加采购信息
        $result = $auction->createDidRecord($params);
        return throwResponse($result);
    }

    /**
     * 查询出价记录
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getBidRecord(Request $request): \think\Response
    {
        $params = $request->get();
        $auction = new PeriodsAuction();

        $result = $auction->getBidRecord($params);
        return throwResponse($result);
    }

    /**
     * 更新产品参数验证
     *
     * @param array $params
     * @return string
     */
    protected function updateValidate(array $params): string
    {
        // 提示信息
        $message = '';

        // 商品 id 验证
        if (!isset($params['id']) || empty($params['id'])) {
            $message .= '请选择商品';
        }

        return $message;
    }


    /**
     * 添加拍卖关注
     * @param Request $request
     * @return \think\Response
     */
    public function createAuctionFollow(Request $request): \think\Response
    {
        $params = $request->post();
        $auction = new PeriodsAuction();
        $data['period'] = $params['period'] ?? 0;
        $data['user_id'] = $params['uid'] ?? 0;
        $data['created_time'] = time();
        if (!$data['period'] || !$data['user_id']) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '获取用户或拍卖商品失败');
        }
        $result = $auction->createAuctionFollow($data);
        return throwResponse($result);
    }

    /**
     * 更新加价，延时
     * @param Request $request
     * @return \think\Response
     */
    public function priceTimeInc(Request $request): \think\Response
    {
        $params = $request->post();
        $data['bid_price'] = $params['bid_price'] ?? 0;
        $data['delay_time'] = $params['delay_time'] ?? 0;
        $data['id'] = $params['id'] ?? 0;
        if ($data['id'] <= 0) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '拍卖信息错误');
        }
        $data['id'] = (int)$data['id'];
        $data['bid_price'] = (int)$data['bid_price'];
        $data['delay_time'] = (int)$data['delay_time'];
        $periods_ser = new Periods(11);
        // 加价
        if ($data['bid_price'] > 0) {
            $periods_ser->incField($data['id'], 'final_auction_price',  $data['bid_price']);
        }
        // 加时
        if ($data['delay_time'] > 0) {
            $inc_res = $periods_ser->incField($data['id'], 'sold_out_time',  $data['delay_time']);
            $inc_res = $periods_ser->incField($data['id'], 'closing_auction_time',  $data['delay_time']);
            if ($inc_res) {
                $period_info = $periods_ser->getOne($data['id'], 'sold_out_time, closing_auction_time');
                // 更新下架任务
                $periods_ser->task($data['id'], strtotime($period_info['sold_out_time']), 2, 3);
                // 更新截拍任务
                $periods_ser->task($data['id'], strtotime($period_info['closing_auction_time']), 5, 4);
            }
        }
        return throwResponse(true);
    }


    /**
     * 拍卖首页商品
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getAuctionIndex(Request $request): \think\Response
    {
        $params = $request->get();
        $auction = new PeriodsAuction();
        $list = $auction->getAuctionIndex();
        return throwResponse($list);
    }

    /**
     * 删除关注商品
     * @param Request $request
     * @return \think\Response
     */
    public function delAuctionFollow(Request $request): \think\Response
    {
        $params = $request->post();
        $period = $params['period'] ?? 0;
        $uid = $params['uid'] ?? 0;
        if ((int)$period <= 0) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择拍卖商品');
        }
        if ((int)$uid <= 0) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '获取用户信息失败');
        }
        $auction = new PeriodsAuction();
        $result = $auction->delAuctionFollow((int)$period, (int)$uid);
        return throwResponse($result);
    }

    /**
     * 获取用户关注列表
     * @param Request $request
     * @return \think\Response
     */
    public function getMyAuctionFollow(Request $request): \think\Response
    {
        $params = $request->get();
        $uid = $params['uid'] ?? 0;
        if ((int)$uid <= 0) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择用户');
        }
        $auction = new PeriodsAuction();
        $result = $auction->getMyAuctionFollow((int)$uid);
        return throwResponse($result);
    }

    /**
     * 获取用户已拍下待下单列表
     * @param Request $request
     * @return \think\Response
     */
    public function getUserAuction(Request $request): \think\Response
    {
        $params = $request->get();
        $uid = $params['uid'] ?? 0;
        if ((int)$uid <= 0) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择用户');
        }
        $auction = new PeriodsAuction();
        $result = $auction->getUserAuction((int)$uid);
        return throwResponse($result);
    }

    /**
     * 获取前端拍卖商品详情
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getIndexAuctionDetail(Request $request): \think\Response
    {
        $params = $request->get();
        $period = $params['period'] ?? 0;
        if ((int)$period <= 0) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '请选择期数');
        }
        $auction = new PeriodsAuction();
        $uid = $params['uid'] ?? 0;
        $result = $auction->getIndexAuctionDetail((int)$period, (int)$uid);
        return throwResponse($result);

    }

    /**
     * 更新用户拍下支付状态
     * @param Request $request
     * @return \think\Response
     */
    public function updateUserAuction(Request $request): \think\Response
    {
        $params = $request->post();
        $period = $params['period'] ?? 0;
        $uid = $params['uid'] ?? 0;
        $pay_status = $params['pay_status'] ?? 0;
        if ((int)$period <= 0 || (int)$uid <= 0 || (int)$pay_status <= 0) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '参数错误');
        }
        $auction = new PeriodsAuction();
        $result = $auction->updateUserAuction((int)$period, (int)$uid, (int)$pay_status);
        return throwResponse($result);
    }

    /**
     * 获取用户竞拍中期数
     * @param Request $request
     * @return \think\Response
     */
    public function getUserAuctionNow(Request $request): \think\Response
    {
        $params = $request->get();
        $uid = $params['uid'] ?? 0;
        if ((int)$uid <= 0) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '参数错误');
        }
        $auction = new PeriodsAuction();
        $result = $auction->getUserAuctionNow((int)$uid);
        return throwResponse($result);
    }

}
