<?php

namespace app\controller;

use app\BaseController;
use app\Request;
use app\service\UserPortraitLogsService;


/**
 * 用户画像标签
 * Class UserPortraitLogs
 * @package app\controller
 */
class UserPortraitLogs extends BaseController
{

    protected $service = null;

    public function initialize()
    {
        $this->service = new UserPortraitLogsService;
    }

    public function listByUserPortrait(Request $request)
    {
        $params = $request->param();
        $data   = $this->service->listByUserPortrait($params);
        return throwResponse($data);
    }

}