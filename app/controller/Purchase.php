<?php

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\ElasticSearchConnection;
use app\ErrorCode;
use app\Request;
use app\service\es\Es;
use Elasticsearch\ClientBuilder;
use think\facade\Db;
use think\facade\Validate;

class Purchase extends BaseController
{

    // es 配置
    protected function esHost(): array
    {
        return [[
            'host' => env('ES.HOST', '127.0.0.1'),
            'port' => env('ES.PORT', 9200),
            'user' => env('ES.USER', 'root'),
            'pass' => env('ES.PASS', 'vinehoo666')
        ]];
    }

    /**
     * 采购首页期数
     * @param Request $request
     * @return \think\Response
     */
    public function getPeriodsList(Request $request): \think\Response
    {
        $param = $request->get();
        $from = (int)$request->get('page', 0);
        $size = (int)$request->get('limit', 15);
        if ($from > 0) {
            $from = ($from - 1) * $size;
        } else {
            $from = 0;
        }
        $esWhere['bool']['must'] = [];
        $esWhere['bool']['must'][] = ['match' => ['onsale_verify_status' => 1]];
        $esWhere['bool']['must'][] = [
            'range' => [
                'sell_time' => [
                    'gt' => "2023-01-29 23:59:59"
                ]
            ]
        ];
        // 频道搜索
        if (isset($param['periods_type']) && $param['periods_type'] != '') {
            $esWhere['bool']['must'][] = ['match' => ['periods_type' => $param['periods_type']]];
        } else {
            $esWhere['bool']['must'][] = ['terms' => ['periods_type' => [0, 1, 2, 3]]];
        }
        // 移除
        $esWhere['bool']['must_not'][] = ['match' => ['is_purchase_delete' => 1]];
        // 期数查询
        if (isset($param['period_ids']) && !empty($param['period_ids'])) {
            $ids = explode(',', $param['period_ids']);
            $esWhere['bool']['must'][] = ['terms' => ['id' => $ids]];
        }
        // 是否下单
        if (isset($param['place_order']) && strlen($param['place_order']) > 0) {
            $origin_period_where_ids = Db::name('purchase_orderno')->group('period')->column('period');
            $period_where_ids        = [];
            foreach ($origin_period_where_ids as $origin_period_where_id) {
                $origin_period_arr = explode(',', strval($origin_period_where_id));
                foreach ($origin_period_arr as $op_id) {
                    $period_where_ids[] = intval($op_id);
                }
            }
            if ($param['place_order'] == 1) {
                $esWhere['bool']['must'][] = ['terms' => ['id' => $period_where_ids]];
            } elseif ($param['place_order'] == 0) {
                $esWhere['bool']['must_not'][] = ['terms' => ['id' => $period_where_ids]];
            }
        }
        // 上架时间
        if ($request->get('onsale_time_start')) {
            $esWhere['bool']['must'][] = [
                'range' => [
                    'onsale_time' => [
                        'gte' => $request->get('onsale_time_start'),
                        'lt' => $request->get('onsale_time_end')
                    ]
                ]
            ];
        }
        // 开售时间
        if ($request->get('sell_time_start')) {
            $esWhere['bool']['must'][] = [
                'range' => [
                    'sell_time' => [
                        'gte' => $request->get('sell_time_start'),
                        'lt' => $request->get('sell_time_end')
                    ]
                ]
            ];
        }
        //预计采购时间
        if ($request->get('estimate_purchase_start')) {
            $esWhere['bool']['must'][] = [
                'range' => [
                    'estimate_purchase' => [
                        'gte' => $request->get('estimate_purchase_start'),
                        'lt' => $request->get('estimate_purchase_end')
                    ]
                ]
            ];
        }
        // 是否订货
        if ($request->get('is_inventory_order') != '') {
            if ($request->get('is_inventory_order') == 1) {
                $esWhere['bool']['must'][] = ['match' => ['is_inventory_order' => 1]];
            } elseif ($request->get('is_inventory_order') == 0) {
                $esWhere['bool']['must_not'][] = ['match' => ['is_inventory_order' => 1]];
            }
        }
        // 发货时间
        if ($request->get('shipment_start')) {
            $esWhere['bool']['must'][] = [
                'range' => [
                    'predict_shipment_time' => [
                        'gte' => $request->get('shipment_start'),
                        'lt' => $request->get('shipment_end')
                    ]
                ]
            ];
        }
        // 采购
        if ($request->get('import_type') != '') {
            $esWhere['bool']['must'][] = ['match' => ['import_type' => $request->get('import_type')]];
        }
        // 供应商
        if ($request->get('supplier') != '') {
//            $esWhere['bool']['must'][] = ['match' => ['supplier' => $request->get('supplier')]];
            $esWhere['bool']['must'][] = ['wildcard' => ['supplier' => '*'.$request->get('supplier').'*']];
        }

        // 收款公司
        if (isset($param['payee_merchant_id']) && $param['payee_merchant_id'] != '') {
            $esWhere['bool']['must'][] = ['match' => ['payee_merchant_id' => $param['payee_merchant_id']]];
        }
        // 已售
        if (isset($param['is_sold']) && intval($param['is_sold']) === 1) {
            $esWhere['bool']['must'][] = [
                'range' => [
                    'saled_count' => [
                        'gt' => 0,
                    ]
                ]
            ];
        }

        // 销售类型
        if (isset($param['sales_type']) && $param['sales_type'] !== '') {
            switch ($param['sales_type']) {
                case 0:// 代发
                    $esWhere['bool']['must'][] = ['match' => ['is_supplier_delivery' => 1]];
                    break;
                case 1:// 预售
                    $esWhere['bool']['must'][] = ['match' => ['is_presell' => 1]];
                    break;
                case 2:// 定金
                    $esWhere['bool']['must'][] = ['match' => ['is_deposit_period' => 1]];
                    break;
            }
        }

        // 预计采购时间
        if ($request->get('estimate_purchase')) {
            $esWhere['bool']['must'][] = [
                'range' => [
                    'estimate_purchase' => [
                        'gte' => $request->get('estimate_purchase_start'),
                        'lt' => $request->get('estimate_purchase_end')
                    ]
                ]
            ];
        }

        $sort = ['onsale_time' => 'asc'];
        if ($request->get('sort') != '') {
            $sort = json_decode($request->get('sort'), true);
        }

        $fields = ['id', 'title', 'brief', 'periods_type', 'onsale_status', 'product_id', 'short_code', 'is_postpone',
            'is_supplier_delivery', 'predict_shipment_time', 'onsale_time', 'sell_time', 'sold_out_time', 'import_type',
            'onsale_verify_status', 'supplier', 'creator_name', 'buyer_name', 'operation_name', 'operation_review_name',
            'estimate_purchase,payee_merchant_id,payee_merchant_name',
            'buyer_id', 'supplier_id',
            ];
        $all_short_codes = [];

//        $fields = ['*'];
        $params = [
            'index' => 'vinehoo.periods',
            'type' => '_doc',
            '_source' => $fields,
            'body' => [
                'query' => $esWhere,
                'from' => $from,
                'size' => $size,
                'sort' => [$sort]
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($this->esHost())->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);
        if (!empty($data['list'])) {
            $req_data = [];
            $period_arr = [];
            $service = new \app\service\Purchase();
            foreach ($data['list'] as &$val) {
                $val['payee_merchant'] = $val['payee_merchant_id'] ?? 0;
                $val['payee_merchant_name'] = $val['payee_merchant_name'] ?? '';
                if (isset($val['periods_type']) && in_array($val['periods_type'], [1, 2]) && $val['payee_merchant'] == 0){
                    // 秒发和跨境 如果没有收款公司字段 指定为2-科技公司
                    $val['payee_merchant'] = 2;
                    $val['payee_merchant_name'] = '佰酿云酒（重庆）科技有限公司';
                }
                array_push($req_data, ['period' => $val['id'], 'period_type' => $val['periods_type']]);
                array_push($period_arr, $val['id']);
                $val['short_code_saled'] = [];
                $val['rec_order'] = [];
                // 获取最新采购单
                $val['orderno'] = $service->getPurchaseNewOrdernoField((int)$val['id'], 'orderno') ?? '';
                if (!isset($val['estimate_purchase'])) {
                    $val['estimate_purchase'] = date('Y-m-d H:i:s', strtotime("+2 days", strtotime($val['onsale_time'])));
                }
            }
            // 期数库存订货列表
            $re = $service->getProductInventoryList($period_arr, 'period,short_code,order,inventory,costprice,warehouse,erp_id,warehouse_id');
            if (!empty($re)) {
                $short_code = array_values(array_unique(array_column($re,'short_code')));
                $products = Db::table("vh_wiki.vh_products")
                    ->whereIn('short_code',$short_code)
                    ->column('short_code,grape_picking_years','short_code');
            }

            // 未发货订单
            $unship_order_data = Es::name(Es::ORDERS)
                ->where([
                    ['period', 'in', $period_arr],
                    ['sub_order_status', '=', 1],
                    ['push_wms_status', 'in', [0, 2]],
                ])
                ->field('id,sub_order_no,period,predict_time')
                ->select()->toArray();
            // 预计发货时间最近订单
            $last_delivery_time = [];
            foreach ($unship_order_data as $v) {
                if (!isset($last_delivery_time[$v['period']])) {
                    $last_delivery_time[$v['period']] = $v['predict_time'];
                } else {
                    if (strtotime($last_delivery_time[$v['period']]) > strtotime($v['predict_time'])) {
                        $last_delivery_time[$v['period']] = $v['predict_time'];
                    }
                }
            }
            

//            $re = $service->getProductInventoryList($period_arr, 'period,short_code,order,inventory');
            unset($val);
            foreach ($data['list'] as &$val) {
                foreach ($re as $v) {
                    if ($val['id'] == $v['period']) {
                        $v['grape_picking_years'] = $products[$v['short_code']]['grape_picking_years'] ?? '';
                        $all_short_codes[] = $v['short_code'];
                        $val['inventory_order'][] = $v;
                    }
                }
            }
             $short_code_saled = $this->getSalesNums($period_arr); //short_code_saled
            // 获取简码已售
            $url = env('ITEM.USER_CACHE_URL') . '/commodities/GetSaleBottleNums';
            $req_data = json_encode($req_data);
            $url_header[] = 'Content-Type: application/json;charset=utf-8';
            $url_req = post_url($url, $req_data, $url_header);
            if ($url_req) {
                $url_req = json_decode($url_req, true);
                // 赋值已售库存
                if ($url_req['error_code'] == 0) {
                    foreach ($data['list'] as &$val) {
                        foreach ($url_req['data'] as $k => $v) {
                            if ($k == $val['id']) {
                                $val['short_code_saled'] = $v;
                                $val['rec_order'] = [];
                            }
                        }
                    }
                    unset($val, $v);
                    // 计算建议订货数量 （已售数-订货量）*2
                    $rec_order = [];
                    foreach ($url_req['data'] as $key => &$val) {
                        foreach ($re as &$v) {
                            if ($key == $v['period']) {
                                if (isset($val[$v['short_code']])) {
//                                    if ($val[$v['short_code']] > 0 && $v['order'] > 0 && $val[$v['short_code']] > $v['order']) {
                                        $rec_num = ($val[$v['short_code']] - $v['order']) * 2;
                                        $rec_order[] = [
                                            'period' => $key,
                                            'short_code' => $v['short_code'],
                                            'rec_num' => $rec_num
                                        ];
//                                    }
                                }
                            }
                        }
                    }
                    unset($val, $v);
                    // 赋值建议订货数量
                    if (!empty($rec_order)) {
                        foreach ($data['list'] as &$val) {
                            foreach ($rec_order as $v) {
                                if ($v['period'] == $val['id']) {
                                    $val['rec_order'][$v['short_code']] = $v['rec_num'];
                                }
                            }
                        }
                    }
                }
            }
        }

//        $buyer_names = array_column($data['list'], 'buyer_name');
        $suppliers   = array_column($data['list'], 'supplier');

//        $buyer_infos = Db::connect('supplychain')->name('staff')->alias('u')
//            ->leftJoin('department d', 'u.dept_id = d.id')
//            ->where('u.realname', 'in', $buyer_names)
//            ->column('u.id,u.realname,u.staff_code,d.dept_code,u.realname');

        $supplier_codes = Db::connect('supplychain')->name('partner_entity')->alias('pe')
            ->leftJoin('department d', 'pe.purchase_department_id = d.id')
            ->leftJoin('staff u', 'pe.purchase_maintainer_id = u.id')
            ->where('pe.name', 'in', $suppliers)
            ->column('u.id,u.realname,u.staff_code,d.dept_code,d.name as dept_name,pe.code as pe_code,memo,pe.name as pe_name', 'pe.name');

        $all_short_codes = array_values(array_unique($all_short_codes));
//        $wiki_short_codes = Db::connect('wiki')->name('products')->alias('p')
//            ->join('product_unit u', 'u.id=p.product_unit')
//            ->where('short_code','in',$all_short_codes)
//            ->field('p.short_code,p.invoice_name,p.capacity,p.en_product_name,u.name as unit,p.tax_rate')
//            ->select()->toArray();

        $wiki_short_codes = Es::name(Es::PRODUCTS)->where([
            ['short_code','in',$all_short_codes]
        ])
            ->field('short_code,invoice_name,capacity,en_product_name,product_unit_name as unit,tax_rate')
            ->select()->toArray();

        $wiki_short_codes = array_column($wiki_short_codes,null, 'short_code');

        foreach ($data['list'] as &$val) {
            $var_inventory_order = $val['inventory_order'] ?? [];
            foreach ($var_inventory_order as $kk => $vv){
                $var_inventory_order[$kk] =  array_merge($vv, $wiki_short_codes[$vv['short_code']] ?? []);
            }
            $val['inventory_order'] = $var_inventory_order;
            $val['supplier_code']  = $supplier_codes[$val['supplier']]['pe_code'] ?? '';
            $val['supplier_remark']  = $supplier_codes[$val['supplier']]['memo'] ?? '';
            $val['buyer_code']     = $supplier_codes[$val['supplier']]['staff_code'] ?? '';
            $val['supplier_buyer_name']     = $supplier_codes[$val['supplier']]['realname'] ?? '';
            $val['buyer_dep_code'] = $supplier_codes[$val['supplier']]['dept_code'] ?? '';
            $val['buyer_dep_name'] = $supplier_codes[$val['supplier']]['dept_name'] ?? '';
            $val['short_code_saled'] = $short_code_saled[$val['id']] ?? ($val['short_code_saled'] ?? []);
            
            //未发货订单的最近一次发货时间
            $val['last_delivery_time'] = $last_delivery_time[$val['id']] ?? '';
            
        }

        return throwResponse($data);
    }

    public function getSalesNums($period)
    {
        $orders = Es::name(Es::ORDERS)->where([
            ['period', 'in', $period],
            ['sub_order_status', 'in', [1, 2, 3]],
        ])
            ->field('main_order_no,sub_order_no,period,package_id,payment_amount,order_qty')
            ->select()->toArray();

        //盲盒数据
        $order_mystery_box = Db::connect('orders')->name('order_mystery_box_log')->column('main_order_no,package_id,product_info', 'main_order_no');
        $mboxs             = [];
        foreach ($order_mystery_box as $box) {
            $mboxs["{$box['main_order_no']}_{$box['package_id']}"] = json_decode($box['product_info'], true);
        }

        $period_group = $package_ids = [];
        foreach ($orders as $order) {
            $period_group[$order['period']][$order['package_id']][] = $order;
            $package_ids[]                                          = $order['package_id'];
        }

        $package_ids = array_values(array_unique($package_ids));
        $packages    = Es::name(Es::PERIODS_PACKAGE)->where([
            ['id', 'in', $package_ids]
        ])
            ->field('id,period_id,package_name,price,is_mystery_box,associated_products')
            ->select()->toArray();

        $pids = [];
        foreach ($packages as &$package) {
            $associated_products = json_decode($package['associated_products'], true);
            foreach ($associated_products as $a_product) {
                if (is_array($a_product['product_id'])) {
                    foreach ($a_product['product_id'] as $apid) {
                        $pids[] = $apid;
                    }
                } else {
                    $pids[] = $a_product['product_id'];
                }
            }
            $package['associated_products'] = $associated_products;
        }
        $pids = array_values(array_unique($pids));

        $wiki_products = Es::name(Es::PRODUCTS)->where([
            ['id', 'in', $pids]
        ])->field('id,short_code')->select()->toArray();

        $packages      = array_column($packages, null, 'id');
        $wiki_products = array_column($wiki_products, 'short_code', 'id');

        $package_info = [];
        $period_info  = [];
        foreach ($orders as $order) {
            if(strpos($order['sub_order_no'],'VHD') !== false) continue;
            $pkg = $packages[$order['package_id']];
            if ($pkg['is_mystery_box'] == 1) {
                $product_items = $mboxs["{$order['main_order_no']}_{$order['package_id']}"] ?? [];
            } else {
                $product_items = $pkg['associated_products'];
            }

            foreach ($product_items as $p_i) {
                $pi_num     = $p_i['nums'] * $order['order_qty'];
                $short_code = $wiki_products[$p_i['product_id']];

                $package_info[$order['period']][$order['package_id']][$short_code] = ($package_info[$order['period']][$order['package_id']][$short_code] ?? 0) + $pi_num;
                $period_info[$order['period']][$short_code]                        = ($period_info[$order['period']][$short_code] ?? 0) + $pi_num;
            }
        }


        $resData = [];
        #查询退货退款工单
        $return_goods_refunds = Db::connect('work')->name('return_goods_refunds')
            ->whereIn('period', $period)
            ->column('gd_id,return_product_info,return_package_id,period', 'gd_id');
        $gd_id                = array_merge(array_column($return_goods_refunds, 'gd_id'));
        #查询仅退款工单
        $refunds = Db::connect('work')->name('refunds')
            ->whereIn('period', $period)
            ->column('gd_id,period', 'gd_id');
        $gd_id   = array_merge($gd_id, array_column($refunds, 'gd_id'));
        #查询主工单信息
        $result = [];
        if (!empty($gd_id)) {
            $work_order = Db::connect('work')->name('work_order')
                ->whereIn('id', $gd_id)
                ->where("gd_status", "in", [4,7])// gd_status
                ->field('id,work_order_type,order_no,order_type')
                ->select()->toArray();
            foreach ($work_order as $v) {
                #仅退款
                if ($v['work_order_type'] == 4) {
                    $sub = $refunds[$v['id']];

                    #退货退款
                } else {
                    $sub = $return_goods_refunds[$v['id']];
                }
                $result[] = [
                    'work_order_type'     => $v['work_order_type'],
                    'order_no'            => $v['order_no'],
                    'order_type'          => $v['order_type'],
                    'return_product_info' => $sub['return_product_info'] ?? '',
                    'return_package_id'   => $sub['return_package_id'] ?? 0,
                    'period'              => $sub['period'],
                ];
            }

            $ret_nums = [];

            foreach ($result as $k => $v) {
                $products = json_decode($v['return_product_info'], true) ?? [];
                if ($v['work_order_type'] == 4) {
                    $resData['only_return_money_orders'][] = $v['order_no'];
                } else {
                    $v['return_product_info']       = $products;
                    $resData['return_all_orders'][] = $v;
                    foreach ($products as $product) {
                        $ret_nums[$v['period']][$v['return_package_id']][$product['short_code']] = ($ret_nums[$v['period']][$v['return_package_id']][$product['short_code']] ?? 0) + $product['nums'];

                        $period_info[$v['period']][$product['short_code']]                           = ($period_info[$v['period']][$product['short_code']] ?? 0) - $product['nums'];
                        $package_info[$v['period']][$v['return_package_id']][$product['short_code']] = ($package_info[$v['period']][$v['return_package_id']][$product['short_code']] ?? 0) - $product['nums'];

                        $key           = $v['period'] . '_' . $v['return_package_id'] . '_' . $product['short_code'];
                        $resData[$key] = ($resData[$key] ?? 0) + $product['nums'];
                    }
                }
            }
        }

        return $period_info;
    }



    /**
     * 更新期数订货
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\DataNotFoundException
     */
    public function updateInventoryOrder(Request $request): \think\Response
    {
        $params = $request->post();
        if (!isset($params[0]['period']) || !isset($params[0]['periods_type'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '参数错误');
        }
        // 操作信息
        $operator = $request->header('vinehoo-uid') ?? 0;
        // 获取当前操作人
        $operator_name = $request->header('vinehoo-vos-name', '');
        if ($operator_name) {
            $operator_name = base64_decode($operator_name);
        }
        $service = new \app\service\Purchase();
        $re = $service->updateInventoryOrder($params, (int)$operator, (string)$operator_name);
        if ($re['status'] == false) {
            return throwResponse([], ErrorCode::EXEC_ERROR, $re['msg']);
        }
        return throwResponse($re['data']);
    }

    /**
     * 期数列表
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\DataNotFoundException
     */
    public function getInventoryOrderList(Request $request): \think\Response
    {
        $params = $request->get();
        $period = (int)$params['period'] ?? 0;
        $service = new \app\service\Purchase();
        $re = $service->getInventoryOrderList($period);
        return throwResponse($re);
    }

    /**
     * 获取产品历史成本价
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\DataNotFoundException
     */
    public function getProductHistoricalCostprice(Request $request)
    {
        $params = $request->get();
        #数据验证
        $validate = Validate::rule([
            'period|期数'    => 'require|number|>:0',
            'product_id|产品ID'    => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $service = new \app\service\Purchase();
        $re = $service->getProductHistoricalCostprice($params);
        return throwResponse($re);
    }

    /**
     * 移除
     * @param Request $request
     * @return \think\Response
     */
    public function delRecord(Request $request): \think\Response
    {
        $period = $request->post('period') ?? 0;
        if ($period <= 0) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '期数错误');
        }
        // 移除
        try {
            $id = explode(',', strval($period));
            $result = Es::name('periods')->where([['_id','in',$id]])->save(['is_purchase_delete' => 1]);
            // $es = new ElasticSearchConnection();
            // $params = [
            //     'index' => 'vinehoo.periods',
            //     'id' => $period,
            //     'body' => [
            //         'doc' => [
            //             'is_purchase_delete' => 1
            //         ]
            //     ]
            // ];
            // $result = $es->connection()->update($params);
            return throwResponse($result);
        } catch (Elasticsearch\Common\Exceptions\TransportException $e) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '移除失败');
        }
    }

    /**
     * 添加采购单
     * @param Request $request
     * @return \think\Response
     */
    public function addPurchaseOrderno(Request $request): \think\Response
    {
        $params = $request->post();
        // 操作信息
        $params['operator'] = $request->header('vinehoo-uid') ?? 0;
        // 获取当前操作人
        $params['operator_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['operator_name']) {
            $params['operator_name'] = base64_decode($params['operator_name']);
        }
        $params['created_time'] = time();
        $service = new \app\service\Purchase();
        $re = $service->addPurchaseOrderno($params);
        return throwResponse($re);
    }

    /**
     * 采购单列表
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function getPurchaseOrdernoList(Request $request): \think\Response
    {
        $params = $request->get();
        $service = new \app\service\Purchase();
        $re = $service->getPurchaseOrdernoList($params);
        $result['total'] = $re->total() ?? 0;
        $result['list'] = $re->getCollection() ?? [];

        $short_code = [];
        foreach ($result['list'] as $v) {
            if (!empty($v['items'])) {
                $short_code = array_merge($short_code, array_values(array_unique(array_column($v['items']->toArray(), 'short_code'))));
            }
        }
        $products = Db::table("vh_wiki.vh_products")
            ->whereIn('short_code',$short_code)
            ->column('short_code,grape_picking_years', 'short_code');

        foreach ($result['list'] as &$item){
            if (empty($item['created_name'])) {
                $item['created_name'] = $item['operator_name'];
            }
            if (!empty($item['items'])) {
                foreach ($item['items'] as $k=>$v) {
                    $item['items'][$k]['grape_picking_years'] = $products[$v['short_code']]['grape_picking_years'] ?? '';
                }
            }
        }
        return throwResponse($result);
    }

    /**
     * 删除采购单
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function delPurchaseOrderno(Request $request): \think\Response
    {
        $params = $request->post();
        $id = (int)$params['id'] ?? 0;
        $service = new \app\service\Purchase();
        $re = $service->delPurchaseOrderno($id);
        return throwResponse($re);
    }

    /**
     * 更新预计采购时间
     * @param Request $request
     * @return \think\Response
     */
    public function updateEstimatePurchase(Request $request): \think\Response
    {
        $period = strval($request->post('period'));
        $estimate_purchase = $request->post('estimate_purchase') ?? '';
        if (empty($period)) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '期数错误');
        }
        if (empty($estimate_purchase)) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '预计采购时间错误');
        }
        $operator = $request->header('vinehoo-uid') ?? 0;
        //判断当前登录用户是否有权限
        $role_id = Db::table('vh_authority.vh_admins_roles')
            ->where([
                ['admin_id', '=', $operator],
                ['role_id', '=', '104']
            ])
            ->value('role_id');
        if (empty($role_id)) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '没有权限');
        }
        // 更新预计采购时间
        try {
            $period_s = explode(',', $period);
            $result = Es::name('periods')
                ->where([
                    ['_id', 'in', $period_s]
                ])
                ->save(['estimate_purchase' => $estimate_purchase]);
                
            return throwResponse($result);
        } catch (Elasticsearch\Common\Exceptions\TransportException $e) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '更新失败');
        }
    }

    /**
     * 添加到采购执行看板
     * @param Request $request
     * @return \think\Response
     */
    public function addRecord(Request $request): \think\Response
    {
        $period = (int)$request->post('period') ?? 0;
        if ($period <= 0) {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '期数错误');
        }
        // 移除
        try {
            $es = new ElasticSearchConnection();
            $params = [
                'index' => 'vinehoo.periods',
                'id' => $period,
                'body' => [
                    'doc' => [
                        'is_purchase_delete' => 0
                    ]
                ]
            ];
            $result = $es->connection()->update($params);
            return throwResponse($result);
        } catch (Elasticsearch\Common\Exceptions\TransportException $e) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '移除失败');
        }
    }

    /**
     * 收款公司列表
     * <AUTHOR>
     * @Date 2023/12/14
     * @param Request $request
     * @return \think\Response
     */
    public function payeeMerchantList(Request $request): \think\Response
    {
        $list = payeeMerchantList();
        $result = [];
        foreach ($list as $k => $v) {
            $result[] = [
                'id'   => $v,
                'name' => $k,
            ];
        }
        return throwResponse($result);
    }
}