<?php

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\Request;
use app\service\Auction as PeriodsAuction;
use app\service\Other;
use app\service\UserPortraitService;
use app\validate\Copywriter;


/**
 * 用户画像
 * Class UserPortrait
 * @package app\controller
 */
class UserPortrait extends BaseController
{
    protected $service = null;

    public function initialize()
    {
        $this->service = new UserPortraitService;
    }

    public function add(Request $request)
    {
        $params = $request->param();
        $data   = $this->service->add($params);
        return throwResponse($data);
    }

    public function list(Request $request)
    {
        $params = $request->param();
        $data   = $this->service->list($params);
        return throwResponse($data);
    }

    public function activeUserPortrait(Request $request)
    {
        $params        = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        $data          = $this->service->activeUserPortrait($params);
        return throwResponse($data);
    }

    public function postUserPortrait(Request $request)
    {
        $params        = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        $data          = $this->service->postUserPortrait($params);
        return throwResponse($data);
    }

    //反馈
    public function feedback(Request $request)
    {
        $params        = $request->param();
        $params['uid'] = $request->header('vinehoo-uid');
        $data          = $this->service->feedback($params);
        return throwResponse($data);
    }


    //批量反馈
    public function batchFeedback(Request $request)
    {
        $params = $request->param();
        $uid    = $request->header('vinehoo-uid');
        $items  = $params['items'];
        foreach ($items as &$item) {
            $item['uid'] = $uid;
        }
        $params['items'] = $items;

        $data = $this->service->batchFeedback($params);
        return throwResponse($data);
    }



}