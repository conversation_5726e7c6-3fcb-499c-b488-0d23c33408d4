<?php

namespace app\controller;

use app\BaseController;
use app\model\PeriodsShareRecord;
use app\Request;
use think\facade\Db;

class Statistics extends BaseController
{
    /**
     * 访问销售量统计
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getShareSta(Request $request): \think\Response
    {
        $where = [];
        $o_w   = [];
        if ($request->get('uid')) {
            $where[] = ['share_id', 'in', $request->get('uid')];
        }
        if ($request->get('ts')) {
            $where[] = ['created_time', '>=', strtotime($request->get('ts'))];
            $where[] = ['created_time', '<=', strtotime($request->get('te'))];
            $o_w[]   = ['created_time', '>=', strtotime($request->get('ts'))];
            $o_w[]   = ['created_time', '<=', strtotime($request->get('te'))];
        }
        $list = PeriodsShareRecord::field('share_id, share_name, count(id) as share_count')
            ->where($where)
            ->group('share_id')
            ->select()
            ->toArray();
//        if (empty($list)) {
//            return throwResponse([]);
//        }
        // 分享人id
        $share_id_arr = array_column($list, 'share_id');
        $o_w[] = ['source_user', 'in', $share_id_arr];
        // 查询分享内订单
        $order = Db::connect('orders')->table('vh_share_order_log')
            ->field('source_user, sub_order_no, order_type')
            ->where($o_w)
            ->select()
            ->toArray();
        if (empty($order)) {
            foreach ($list as &$val) {
                $val['order_count'] = 0;
                $val['order_sum'] = 0;
            }
            //            return throwResponse($list);
        } else {
            // 组装分享用户订单
            $share_u = [];
            foreach($order as $val) {
                switch ($val['order_type']) {
                    case '0':
                        $share_u[$val['source_user']]['flash'][] = $val['sub_order_no'];
                        break;
                    case '1':
                        $share_u[$val['source_user']]['second'][] = $val['sub_order_no'];
                        break;
                    case '2':
                        $share_u[$val['source_user']]['cross'][] = $val['sub_order_no'];
                        break;
                    case '3':
                        $share_u[$val['source_user']]['leftover'][] = $val['sub_order_no'];
                        break;
                }
            }
            unset($val);
            // 统计查询分享用户金额
            foreach ($list as &$val) {
                $val['order_count'] = 0;
                $val['order_sum'] = 0;
                foreach ($share_u as $k => $v) {
                    if ($val['share_id'] == $k) {
                        // 闪购总订单数量，金额
                        if (!empty($v['flash'])) {
                            $order = Db::connect('orders')->table('vh_flash_order')
                                ->field('COUNT(id) as order_count, SUM(payment_amount) as order_sum')
                                ->where([
                                    ['sub_order_status', 'in', '1,2,3'],
                                    ['sub_order_no', 'in', $v['flash']]
                                ])
                                ->find();
                            $val['order_count'] += $order['order_count'];
                            $val['order_sum'] += $order['order_sum'];
                        }
                        // 秒发总订单数量，金额
                        if (!empty($v['second'])) {
                            $order = Db::connect('orders')->table('vh_second_order')
                                ->field('COUNT(id) as order_count, SUM(payment_amount) as order_sum')
                                ->where([
                                    ['sub_order_status', 'in', '1,2,3'],
                                    ['sub_order_no', 'in', $v['second']]
                                ])
                                ->find();
                            $val['order_count'] += $order['order_count'];
                            $val['order_sum'] += $order['order_sum'];
                        }
                        // 跨境总订单数量，金额
                        if (!empty($v['cross'])) {
                            $order = Db::connect('orders')->table('vh_cross_order')
                                ->field('COUNT(id) as order_count, SUM(payment_amount) as order_sum')
                                ->where([
                                    ['sub_order_status', 'in', '1,2,3'],
                                    ['sub_order_no', 'in', $v['cross']]
                                ])
                                ->find();
                            $val['order_count'] += $order['order_count'];
                            $val['order_sum'] += $order['order_sum'];
                        }
                        // 尾货总订单数量，金额
                        if (!empty($v['leftover'])) {
                            $order = Db::connect('orders')->table('vh_tail_order')
                                ->field('COUNT(id) as order_count, SUM(payment_amount) as order_sum')
                                ->where([
                                    ['sub_order_status', 'in', '1,2,3'],
                                    ['sub_order_no', 'in', $v['leftover']]
                                ])
                                ->find();
                            $val['order_count'] += $order['order_count'];
                            $val['order_sum'] += $order['order_sum'];
                        }
                    }
                }
            }
        }
        unset($val);
        $s['sc'] = 0;
        $s['oc'] = 0;
        foreach ($list as $val) {
            $s['sc'] += $val['share_count'];
            $s['oc'] += $val['order_count'];
        }
        $re['list'] = $list;
        $re['sta'] = $s;
        return throwResponse($re);
    }

    /**
     * 分享订单详细列表
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getShareDetailList(Request $request): \think\Response
    {
        $v_uid = $request->header('vinehoo-uid');
        // 分页
        $limit = $request->get('limit', 15);
        $o_w   = [];
        if ($request->get('uid')) {
            $o_w[] = ['source_user', 'in', $request->get('uid')];
        }
        if ($request->get('user_id')) {
            $o_w[] = ['uid', '=', $request->get('user_id')];
        }
        if ($request->get('period')) {
            $o_w[] = ['period', '=', $request->get('period')];
        }
        if ($request->get('ts')) {
            $o_w[]   = ['created_time', '>=', strtotime($request->get('ts'))];
            $o_w[]   = ['created_time', '<=', strtotime($request->get('te'))];
        }
        // 除开售前主管（梅林丽）可直接看到所有人的客户信息，则其他人都无权访问只能看见自己的信息。
        if (!empty($v_uid) && $v_uid != 210) {
            $o_w[] = ['source_user', '=', $v_uid];
        } else if (empty($v_uid)) {
            $o_w[] = ['id', '=', 0];
        }
        
        // 查询分享内订单明细
        $order_l = Db::connect('orders')->table('vh_share_order_log')
            ->field('uid, period, sub_order_no, order_type')
            ->where($o_w)
            ->paginate([
                'list_rows'=> $limit,
                'var_page' => 'page',
            ])->map(function ($item) {
                $item['payment_amount'] = 0;
                $order = 0;
                switch ($item['order_type']) {
                    case '0':
                        $order = Db::connect('orders')->table('vh_flash_order')
                            ->where([
                                ['sub_order_status', 'in', '1,2,3'],
                                ['sub_order_no', '=', $item['sub_order_no']]
                            ])
                            ->value('payment_amount');
                        break;
                    case '1':
                        $order = Db::connect('orders')->table('vh_second_order')
                            ->where([
                                ['sub_order_status', 'in', '1,2,3'],
                                ['sub_order_no', '=', $item['sub_order_no']]
                            ])
                            ->value('payment_amount');
                        break;
                    case '2':
                        $order = Db::connect('orders')->table('vh_cross_order')
                            ->where([
                                ['sub_order_status', 'in', '1,2,3'],
                                ['sub_order_no', '=', $item['sub_order_no']]
                            ])
                            ->value('payment_amount');
                        break;
                    case '3':
                        $order = Db::connect('orders')->table('vh_tail_order')
                            ->where([
                                ['sub_order_status', 'in', '1,2,3'],
                                ['sub_order_no', '=', $item['sub_order_no']]
                            ])
                            ->value('payment_amount');
                        break;
                }
                $item['payment_amount'] += $order;
                return $item;
            });

        // 计算订单总数,以及总销量
        $order_c = Db::connect('orders')->table('vh_share_order_log')
            ->field('sub_order_no, order_type')
            ->where($o_w)
            ->select();
        $share_u =[];
        if (!empty($order_c)) {
            foreach ($order_c as $val) {
                switch ($val['order_type']) {
                    case '0':
                        $share_u['flash'][] = $val['sub_order_no'];
                        break;
                    case '1':
                        $share_u['second'][] = $val['sub_order_no'];
                        break;
                    case '2':
                        $share_u['cross'][] = $val['sub_order_no'];
                        break;
                    case '3':
                        $share_u['leftover'][] = $val['sub_order_no'];
                        break;
                }
            }
        }
        $order_count = 0;
        $order_sum = 0;
        if (!empty($share_u)) {
                if (!empty($share_u['flash'])) {
                    $order = Db::connect('orders')->table('vh_flash_order')
                        ->field('COUNT(id) as order_count, SUM(payment_amount) as order_sum')
                        ->where([
                            ['sub_order_status', 'in', '1,2,3'],
                            ['sub_order_no', 'in', $share_u['flash']]
                        ])
                        ->find();
                    $order_count += $order['order_count'];
                    $order_sum += $order['order_sum'];
                }
                // 秒发总订单数量，金额
                if (!empty($share_u['second'])) {
                    $order = Db::connect('orders')->table('vh_second_order')
                        ->field('COUNT(id) as order_count, SUM(payment_amount) as order_sum')
                        ->where([
                            ['sub_order_status', 'in', '1,2,3'],
                            ['sub_order_no', 'in', $share_u['second']]
                        ])
                        ->find();
                    $order_count += $order['order_count'];
                    $order_sum += $order['order_sum'];
                }
                // 跨境总订单数量，金额
                if (!empty($share_u['cross'])) {
                    $order = Db::connect('orders')->table('vh_cross_order')
                        ->field('COUNT(id) as order_count, SUM(payment_amount) as order_sum')
                        ->where([
                            ['sub_order_status', 'in', '1,2,3'],
                            ['sub_order_no', 'in', $share_u['cross']]
                        ])
                        ->find();
                    $order_count += $order['order_count'];
                    $order_sum += $order['order_sum'];
                }
                // 尾货总订单数量，金额
                if (!empty($share_u['leftover'])) {
                    $order = Db::connect('orders')->table('vh_tail_order')
                        ->field('COUNT(id) as order_count, SUM(payment_amount) as order_sum')
                        ->where([
                            ['sub_order_status', 'in', '1,2,3'],
                            ['sub_order_no', 'in', $share_u['leftover']]
                        ])
                        ->find();
                    $order_count += $order['order_count'];
                    $order_sum += $order['order_sum'];
                }
        }
        $c_c['order_count'] = $order_l->total() ?? 0;
        $c_c['order_sum'] = $order_sum;
        // 分页
        $result['total'] = $order_l->total() ?? 0;
        $result['list'] = $order_l->getCollection() ?? [];

        $result['cc'] = $c_c;
        return throwResponse($result);
    }

    /**
     * 商品分享人统计
     * @return \think\Response
     */
    public function getShareGroup(): \think\Response
    {
        $list = PeriodsShareRecord::field('share_id, share_name')
            ->group('share_id')
            ->select()
            ->toArray();
        return throwResponse($list);
    }

    /**
     * 商品分享人统计
     * @return \think\Response
     */
    public function getShareOrderGroup(Request $request): \think\Response
    {
        $v_uid = $request->header('vinehoo-uid');

        // 除开售前主管（梅林丽）可直接看到所有人的客户信息，则其他人都无权访问只能看见自己的信息。
        $where = ['id' => 0];
        if (!empty($v_uid)) {
            $where = $v_uid != 210 ? ['source_user' => $v_uid] : [];
        }

        $list = Db::connect('orders')->table('vh_share_order_log')
            ->field('source_user, share_name')
            ->where($where)
            ->group('source_user')
            ->select()
            ->toArray();
        return throwResponse($list);
    }

}