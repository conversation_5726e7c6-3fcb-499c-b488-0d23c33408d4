<?php

namespace app\controller\v3;

use app\BaseController2;
use app\service\v3\PeriodsPoolService;


/**
 * 产品池主表
 * Class PeriodsPool
 * @package app\controller\v3
 */
class PeriodsPool extends BaseController2
{

    public function initialize()
    {
        $this->service = new PeriodsPoolService;
    }

    public function __call($method, $args)
    {
        #region 数据处理
        $param = $this->request->param();

        if (!in_array($method, $this->remove_header)) {
            $vh_uid             = $this->request->header('vinehoo-uid', null);
            $base64_vh_vos_name = $this->request->header('vinehoo-vos-name', null);

            ($vh_uid !== null) && $param['vh_uid'] = $vh_uid;
            ($base64_vh_vos_name !== null) && $param['vh_vos_name'] = base64_decode($base64_vh_vos_name);
        }
        if (in_array($method, ['uiPersonnel', 'pictureSubmit', 'documentPersonnel', 'editorSubmit', 'uiPictureDistribution', 'qualificationSubmit', 'purchaseSubmit', 'qualification', 'editor', 'pictureDistribution', 'documentDistribution', 'auditPurchase', 'auditLastPurchase', 'document', 'purchaseExec'])) {
            $param['method'] = $method;
            return $this->service->update($param);
        }

        #endregion

        return $this->service->$method($param);
    }

}