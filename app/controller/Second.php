<?php

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\model\PeriodsPurchasedSync;
use app\model\PeriodsSecondMerchants;
use app\service\elasticsearch\ElasticSearchService;
use app\service\es\Es;
use app\service\Other;
use app\service\Package;
use app\service\Periods;
use app\service\Products;
use app\service\Second as PeriodsSecond;
use app\Request;
use app\service\v3\PeriodsPoolService;
use app\validate\Buyer;
use app\validate\Operations;
use app\validate\SecondCopywriter;
use Elasticsearch\ClientBuilder;
use think\Exception;
use think\facade\Db;
use think\facade\Log;
use think\response\Json;


class Second extends BaseController
{

    /**
     * 添加尾货商品文案
     *
     * @param Request $request
     * @return false|string|\think\Response
     */
    public function create(Request $request)
    {
        // 参数
        $params = $request->post();
        // 获取商品id
        $params['id'] = $this->getGeneratorID(1);
        if (!$params['id']) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '生成秒发文案失败');
        }
        // 操作信息
        $params['creator_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['creator_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['creator_name']) {
            $params['creator_name'] = base64_decode($params['creator_name']);
        }
        // 参数验证
        $validate             = new SecondCopywriter();
        $params['creator_id'] = $request->header('vinehoo-uid', '0');
        $validate_params      = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }

        $params['created_time'] = time();

        $periods_ser = new Periods(1);
        // 获取参数配置默认值
        $params = $periods_ser->get_params_default_value($params);
        // 获取商品产品评分饮用建议
        $product_list = [];
        // 产品简码
        $params['short_code']            = '';
        $params['product_main_category'] = '';
        //        if ($params['product_id'] != '') {
        //            $params['product_id'] = trim($params['product_id'], ',');
        //            $product_ids = explode(',', $params['product_id']);
        //            // 查询产品信息
        //            $product_url = env('ITEM.WINE_WIKI_URL').'/wiki/v3/product/fieldsdatarr';
        //            $field  = 'id, short_code, tasting_notes, score, prize, drinking_suggestion, product_category';
        //            $product_info = post_url($product_url, ['ids' => $product_ids, 'fields' => $field]);
        //            if (!is_null(json_decode($product_info))) {
        //                $result_pro = json_decode($product_info, true);
        //                if ($result_pro['error_code'] == 0) {
        //                    $product_list = $result_pro['data']['list'];
        //                } else {
        //                    return  throwResponse(null, ErrorCode::EXEC_ERROR, $result_pro['error_msg']);
        //                }
        //            }
        //            if (!empty($product_list)) {
        //                foreach ($product_list as &$value) {
        //                    $value['period'] = $params['id'];
        //                    $value['product_id'] = $value['id'];
        //                    $value['created_time'] = time();
        //                    $params['short_code'] .= $value['short_code']. ',';
        //                    $params['product_main_category'] .= $value['product_category_name']. ',';
        //                    unset($value['id'], $value['product_category_name'], $value['product_category']);
        //                }
        //                unset($value);
        //            }
        //        }
        if ($params['short_code'] != '') {
            $params['short_code'] = trim($params['short_code'], ',');
        }
        if ($params['product_main_category'] != '') {
            $params['product_main_category'] = trim($params['product_main_category'], ',');
        }
        // 手动创建
        $params['is_manual_create'] = 1;
        // 添加跨境商品
        $second = new PeriodsSecond();
        $result = $second->create($params);

        if (!$result) {
            return throwResponse($result, ErrorCode::EXEC_ERROR, '产品闪购添加失败');
        }

        // 添加商品产品评分饮用建议
        if (!empty($params['product_info'])) {
            $other_ser = new Other();
            $other_ser->createProductInfo($result, $params['product_info']);
        }

        return throwResponse($result);
    }

    /**
     * 添加编辑采购信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateBuyerInfo(Request $request)
    {
        // 参数
        $params = $request->post();
        // 参数验证
        $validate        = new Buyer();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }

        $second = new PeriodsSecond();

        // 添加采购信息
        $result = $second->update($params);

        if (!$result['status']) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, '采购信息添加失败');
        }

        return throwResponse($result['data']);
    }

    /**
     * 添加编辑运营信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update(Request $request)
    {
        // 参数
        $params = $request->post();
        // 参数验证
        $validate        = new Operations();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }

        // 验证商品 id
        $update_validate = $this->updateValidate($params);
        if (!empty($update_validate)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $update_validate);
        }

        $second = new PeriodsSecond();
        // 验证绑定产品
        $period_info = $second->getOne((int)$params['id'], 'product_id, buyer_review_status, supplier_id, payee_merchant_id');
        if (empty($period_info)) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, '未查询到期数信息');
        }
        if (!empty($params['onsale_time'])) {
            if (empty($period_info['supplier_id'])) {
                return throwResponse(null, ErrorCode::EXEC_ERROR, '供应商不能为空');
            }
            if (empty($period_info['payee_merchant_id'])) {
                return throwResponse(null, ErrorCode::EXEC_ERROR, '收款商户不能为空');
            }
        }
        $old_product_id = explode(',', $period_info['product_id']);
        $product_id     = explode(',', $params['product_id']);
        $is_del_product = 0;
        foreach ($old_product_id as $v) {
            if (empty($v)) {
                continue;
            }
            if (!in_array($v, $product_id)) {
                $is_del_product = 1;
            }
        }
        if ($is_del_product == 1 && $period_info['buyer_review_status'] == 3) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, '采购审核后不允许删除绑定产品');
        }

        // 添加运营信息 时间戳转换
        if (isset($params['predict_shipment_time'])) {
            $params['predict_shipment_time'] = strtotime($params['predict_shipment_time']);
        }
        if (isset($params['onsale_time'])) {
            $params['onsale_time'] = strtotime($params['onsale_time']);
        }
        if (isset($params['sell_time'])) {
            $params['sell_time'] = strtotime($params['sell_time']);
        }
        if (isset($params['sold_out_time'])) {
            $params['sold_out_time'] = strtotime($params['sold_out_time']);
        }

        // 限购规则转 json 字符串
        if (isset($params['quota_rule']) && !empty($params['quota_rule'])) {
            $params['quota_rule'] = json_encode($params['quota_rule']);
        }

        // 如果前端返回图片域名，则删除域名
        if (isset($params['banner_img']) && $params['banner_img'] != '') {
            $params['banner_img'] = str_replace(env('ALIURL'), "", $params['banner_img']);
        }
        if (isset($params['product_img']) && $params['product_img'] != '') {
            $params['product_img'] = str_replace(env('ALIURL'), "", $params['product_img']);
        }
        if (isset($params['video_cover']) && $params['video_cover'] != '') {
            $params['video_cover'] = str_replace(env('ALIURL'), "", $params['video_cover']);
        }
        if (isset($params['horizontal_img']) && $params['horizontal_img'] != '') {
            $params['horizontal_img'] = str_replace(env('ALIURL'), "", $params['horizontal_img']);
        }
        // 查询产品简码，大类
        if ($params['product_id'] != '') {
            $other_ser                       = new Other();
            $product_info                    = $other_ser->getProductList($params);
            $params['short_code']            = $product_info['short_code'];
            $params['product_main_category'] = $product_info['product_main_category'];
        }
        $result = $second->update($params);

        if (!$result['status']) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, '运营信息添加失败');
        }

        (new PeriodsPoolService())->syncPeriods($params);

        // 添加商品产品评分饮用建议
        if (!empty($params['product_info'])) {
            $other_ser = new Other();
            $other_ser->createProductInfo((int)$params['id'], $params['product_info']);
        }

        $periods_ser = new Periods(1);
        // 更新 json 文件
        $periods_ser->create_period_json((int)$params['id'], 1,0,-1);
        // 更新 CDN
        $periods_ser::CDNrefreshObject((int)$params['id']);

        return throwResponse($result['data']);
    }

    /**
     * 商品详细
     *
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(Request $request)
    {
        // 参数
        $params       = $request->get();
        $params['id'] = (int)$params['id'];
        // 验证商品 id
        $update_validate = $this->updateValidate($params);
        if (!empty($update_validate)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $update_validate);
        }

        $second = new PeriodsSecond();

        // 详细信息
        $result = $second->getOne($params['id'], '*');
        if (empty($result)) {
            return throwResponse($result, ErrorCode::PARAM_ERROR, '期数不存在');
        }
        // 套餐信息
        $package_ser        = new Package(1);
        $where['period_id'] = $params['id'];
        $result['package']  = $package_ser->getPackage($where);
        // 产品信息
        $products_ser           = new Products();
        $result['product_list'] = $products_ser->getListById($result['product_id'], $params['id']);
        // 是否存在上架记录
        $periods                    = new Periods(0);
        $result['is_onsale_record'] = 0;
        $onsale_record              = $periods->getOnSaleRecord($params['id'], 1);
        if ($onsale_record) {
            $result['is_onsale_record'] = 1;
        }
        $label_ser = new \app\service\Label();
        // 标签查询
        $result['label_arr'] = [];
        if (!empty($result['label'])) {
            $result['label_arr'] = $label_ser->getLabelNameByIds($result['label']);
        }
        return throwResponse($result);
    }

    /**
     * 更新产品参数验证
     *
     * @param array $params
     * @return string
     */
    protected function updateValidate(array $params): string
    {
        // 提示信息
        $message = '';

        // 商品 id 验证
        if (!isset($params['id']) || empty($params['id'])) {
            $message .= '请选择商品';
        }

        return $message;
    }


    /**
     * 秒发 ES 列表
     * @param Request $request
     * @return \think\Response
     */
    public function getSecondES(Request $request): \think\Response
    {

        $from = (int)$request->get('page', 0);
        $size = (int)$request->get('limit', 15);

        if ($from > 0) {
            $from = ($from - 1) * $size;
        } else {
            $from = 0;
        }

        //        $esWhere['bool']['must'] = [];
        $param     = $request->get();
        $second_id = $param['second_id'] ?? 1;
        // 秒发频道
        //        $esWhere['bool']['must'][] = ['match' => ['periods_type' => 1]];
        // 兼容商家秒发
        $esWhere['bool']['must'][] = ['terms' => ['periods_type' => [1, 9]]];
        $esWhere['bool']['must'][] = ['terms' => ['second_ids' => [$second_id]]];

        // 默认查询，待售中，在售中，售完不下架
        //        $esWhere['bool']['must'][] = ['match' => ['onsale_status' => 2]];
        $esWhere['bool']['should'][]             = ['match' => ['onsale_status' => 1]];
        $esWhere['bool']['should'][]             = ['match' => ['onsale_status' => 2]];
        $esWhere['bool']['should'][]             = ['match' => ['sellout_sold_out' => 1]];
        $esWhere['bool']['minimum_should_match'] = 1;


        if (isset($param['key']) && $param['key'] != '') {
            // 价格搜索
            if ($param['key'] == 'price') {
                $between_price = explode('-', $param['value']);
                if ((int)$between_price[0] <= 0) {
                    $between_price[0] = 0;
                }
                if (!empty($between_price)) {
                    $esWhere['bool']['must'][] = [
                        'range' => [
                            'price' => [
                                'gte' => $between_price[0],
                                'lte' => $between_price[1]
                            ]
                        ]
                    ];
                }
            } else {
                $param['value']            = $param['value'] ?? '';
                $esWhere['bool']['must'][] = ['match_phrase' => [$param['key'] => $param['value']]];
            }
        }
        //        // 国家查询
        //        if (isset($param['country']) && $param['country'] != '') {
        //            $esWhere['bool']['must'][] = ['match_phrase' => ['country' => $param['country']]];
        //        }
        //        // 分类查询
        //        if (isset($param['product_category']) && $param['product_category'] != '') {
        //            $esWhere['bool']['must'][] = ['match_phrase' => ['product_category' => $param['product_category']]];
        //        }
        //        // 关键词查询
        //        if (isset($param['product_keyword']) && $param['product_keyword'] != '') {
        //            $esWhere['bool']['must'][] = ['match_phrase' => ['product_keyword' => $param['product_keyword']]];
        //        }
        // 排序
        $sort = [['sort' => 'desc'], ['onsale_time' => 'desc']];
        // 指定字段
        $field = [
            'id', 'country', 'title', 'brief', 'banner_img', 'horizontal_img', 'sort', 'second_ids',
            'purchased', 'vest_purchased', 'price', 'market_price', 'periods_type', 'product_img'
        ];

        $params = [
            'index'   => 'vinehoo.periods',
            'type'    => '_doc',
            '_source' => $field,
            'body'    => [
                'query' => $esWhere,
                'sort'  => $sort,
                'size'  => $size,
                'from'  => $from
            ]
        ];
        // es 配置
        $hosts = [
            [
                'host' => env('ES.HOST', '127.0.0.1'),
                'port' => env('ES.PORT', 9200),
                'user' => env('ES.USER', 'root'),
                'pass' => env('ES.PASS', 'vinehoo666')
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client    = $es_client->create()->setHosts($hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data       = get_es_result($es_data);
        $ids        = array();
        $label_data = [];
        // 遍历列表取出限购数量
        if (!empty($data['list'])) {
            foreach ($data['list'] as &$val) {
                $ids[]        = $val['id'];
                $label_data[] = [
                    'periods'      => $val['id'],
                    'periods_type' => $val['periods_type'],
                ];
                if (isset($val['quota_rule']) && !empty($val['quota_rule'])) {
                    $val['quota_number'] = json_decode($val['quota_rule'], true)['quota_number'] ?? 0;
                }
                // 题图
                if (isset($val['banner_img']) && $val['banner_img'] != '') {
                    $val['banner_img'] = explode(",", $val['banner_img']);
                    foreach ($val['banner_img'] as &$v) {
                        $v = env('ALIURL') . $v;
                    }
                    $val['banner_img'] = $val['banner_img'][0] ?? '';
                    unset($v);
                }
                // 产品图
                if (isset($val['product_img']) && $val['product_img'] != '') {
                    $val['product_img'] = explode(",", $val['product_img']);
                    foreach ($val['product_img'] as &$v) {
                        $v = env('ALIURL') . $v;
                    }
                    unset($v);
                }
                // 秒发竖图
                if (isset($val['horizontal_img']) && $val['horizontal_img'] != '') {
                    $val['horizontal_img'] = explode(",", $val['horizontal_img']);
                    foreach ($val['horizontal_img'] as &$v) {
                        $v = env('ALIURL') . $v;
                    }
                    $val['horizontal_img'] = $val['horizontal_img'][0] ?? '';
                    unset($v);
                }
                // 视频封面图增加域名
                if (isset($val['video_cover']) && $val['video_cover'] != '') {
                    $val['video_cover'] = env('ALIURL') . $val['video_cover'];
                }
                $val['is_gift']      = 0;
                $val['is_reduction'] = 0;
                // 商品标签
                $val['label'] = [];
            }
            unset($val);
            // 查询秒发套餐立减
            $package_ser = new Package(1);
            $pl          = $package_ser->getPackageByPeriods($ids);
            // 查询满减
            $goods_label_url = env("ITEM.COMMODITIES_SERVICES") .
                '/commodities_server/v3/second_home/get_full_reduction_label';

            $body        = json_encode(['data' => $label_data]);
            $haeder      = ['vinehoo-uid:' . $request->header('vinehoo-uid')];
            $label_res   = curlRequest($goods_label_url, $body, $haeder);
            $goods_label = $label_res['data'] ?? [];

            if (!empty($pl)) {
                foreach ($data['list'] as &$val) {
                    // 立减标签
                    foreach ($pl as &$v) {
                        if ($val['id'] == $v['period_id']) {
                            if ($v['preferential_reduction'] == intval($v['preferential_reduction'])) {
                                $formattedNumber = intval($v['preferential_reduction']);
                            } else {
                                $formattedNumber = number_format($v['preferential_reduction'], 2);
                            }
                            $val['label'][] = '立减' . $formattedNumber;
                        }
                    }
                    // 满减标签
                    if (isset($goods_label['list'][$val['id']]) && !empty($goods_label['list'][$val['id']])) {
                        foreach ($goods_label['list'][$val['id']] as $v1) {
                            $val['label'][] = $v1['title'];
                        }
                    }
                }
                unset($val, $v, $v1);
            }
        }
        $result['list']  = $data['list'];
        $result['total'] = $data['total'];
        return throwResponse($result);
    }

    /**
     * 秒发可筛选项
     * @param Request $request
     * @return \think\Response
     */
    public function getSecondPeriodsFilter(Request $request): \think\Response
    {
        // 获取筛选项字段
        $field     = ['id', 'country', 'product_category', 'product_keyword'];
        $param     = $request->get();
        $second_id = $param['second_id'] ?? 1;
        // 秒发频道
        //        $esWhere['bool']['must'][] = ['match' => ['periods_type' => 1]];
        // 兼容商家秒发
        $esWhere['bool']['must'][] = ['terms' => ['periods_type' => [1, 9]]];
        $esWhere['bool']['must'][] = ['terms' => ['second_ids' => [$second_id]]];

        // 默认查询，待售中，在售中，售完不下架
        //        $esWhere['bool']['must'][] = ['match' => ['onsale_status' => 2]];
        $esWhere['bool']['should'][]             = ['match' => ['onsale_status' => 1]];
        $esWhere['bool']['should'][]             = ['match' => ['onsale_status' => 2]];
        $esWhere['bool']['should'][]             = ['match' => ['sellout_sold_out' => 1]];
        $esWhere['bool']['minimum_should_match'] = 1;

        $params = [
            'index'   => 'vinehoo.periods',
            'type'    => '_doc',
            '_source' => $field,
            'body'    => [
                'query' => $esWhere,
            ]
        ];
        // es 配置
        $hosts = [
            [
                'host' => env('ES.HOST', '127.0.0.1'),
                'port' => env('ES.PORT', 9200),
                'user' => env('ES.USER', 'root'),
                'pass' => env('ES.PASS', 'vinehoo666')
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client    = $es_client->create()->setHosts($hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data                       = get_es_result($es_data);
        $result['product_category'] = [];
        $result['country']          = [];
        $result['product_keyword']  = [];
        if (!empty($data['list'])) {
            foreach ($data['list'] as $val) {
                //                $val['country'] = explode(",", $val['country']);
                if (!empty($val['country'])) {
                    foreach ($val['country'] as $v) {
                        if (in_array($v, $result['country'])) {
                            continue;
                        }
                        if ($v) {
                            array_push($result['country'], $v);
                        }
                    }
                    unset($v);
                }
                //                $val['product_category'] = explode(",", $val['product_category']);
                if (!empty($val['product_category'])) {
                    foreach ($val['product_category'] as $v) {
                        if (in_array($v, $result['product_category'])) {
                            continue;
                        }
                        if ($v) {
                            array_push($result['product_category'], $v);
                        }
                    }
                    unset($v);
                }
                if (!empty($val['product_keyword'])) {
                    foreach ($val['product_keyword'] as $v) {
                        if (in_array($v, $result['product_keyword'])) {
                            continue;
                        }
                        if ($v) {
                            array_push($result['product_keyword'], $v);
                        }
                    }
                    unset($v);
                }
            }
        }

        // 价格配置
        $price_url       = env('ITEM.WINE_WIKI_URL') . '/wiki/v3/screen/index?type=4';
        $price_list      = get_url($price_url);
        $price_list      = get_interior_http_response($price_list);
        $price_list      = $price_list['list'] ?? [];
        $price_list_name = [];
        if (!empty($price_list)) {
            foreach ($price_list as $value) {
                $price_list_name[] = $value['name'];
            }
        }
        $re[0]['name'] = '国家';
        $re[0]['key']  = 'country';
        $re[0]['list'] = $result['country'] ?? [];
        $re[1]['name'] = '类别';
        $re[1]['key']  = 'product_category';
        $re[1]['list'] = $result['product_category'] ?? [];
        $re[2]['name'] = '价格';
        $re[2]['key']  = 'price';
        $re[2]['list'] = $price_list_name;
        $re[3]['name'] = '关键词';
        $re[3]['key']  = 'product_keyword';
        $re[3]['list'] = $result['product_keyword'] ?? [];
        return throwResponse($re);
    }

    /**
     * 秒发商品筛选列表
     * @param Request $request
     * @return \think\Response
     */
    public function getSecondFilterGoods(Request $request): \think\Response
    {
        $from = (int)$request->get('page', 0);
        $size = (int)$request->get('limit', 15);

        if ($from > 0) {
            $from = ($from - 1) * $size;
        } else {
            $from = 0;
        }


        $param = $request->get();
        //是否新用户：0-否，1是（未登录默认1）
        $uid         = $request->header('vinehoo-uid');
        $is_new_user = getIsNewUser($uid);

        // 兼容商家秒发
        $esWhere['bool']['must'][] = ['match' => ['periods_type' => 1]];
        // 获取商品ES筛选条件
        $esWhere = PeriodsSecond::getFilterCriteria($param, $esWhere);
        // 在售中
        $esWhere['bool']['must'][] = ['match' => ['onsale_status' => 2]];
        // 非渠道销售
        $esWhere['bool']['must'][] = ['match' => ['is_channel' => 0]];
        // 排序
        $sort = [['sort' => 'desc'], ['onsale_time' => 'desc']];
        // 指定字段
        $field  = [
            'id', 'country', 'title', 'brief', 'banner_img', 'horizontal_img', 'sort', 'second_ids',
            'purchased', 'vest_purchased', 'price', 'market_price', 'periods_type', 'product_img'
        ];
        $params = [
            'index'   => 'vinehoo.periods',
            'type'    => '_doc',
            '_source' => $field,
            'body'    => [
                'query' => $esWhere,
                'sort'  => $sort,
                'size'  => $size,
                'from'  => $from
            ]
        ];
        // es 配置
        $hosts = [
            [
                'host' => env('ES.HOST', '127.0.0.1'),
                'port' => env('ES.PORT', 9200),
                'user' => env('ES.USER', 'root'),
                'pass' => env('ES.PASS', 'vinehoo666')
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client    = $es_client->create()->setHosts($hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data       = get_es_result($es_data);
        $ids        = array();
        $label_data = [];
        $calcPrice  = [];
        // 遍历列表取出限购数量
        if (!empty($data['list'])) {
            foreach ($data['list'] as &$val) {
                $calcPrice[] = [
                    "id"          => $val['id'],
                    "period_type" => $val['periods_type'],
                    "price"       => $val['price'],
                ];

                $ids[]        = $val['id'];
                $label_data[] = [
                    'periods'      => $val['id'],
                    'periods_type' => $val['periods_type'],
                ];
                if (isset($val['quota_rule']) && !empty($val['quota_rule'])) {
                    $val['quota_number'] = json_decode($val['quota_rule'], true)['quota_number'] ?? 0;
                }
                // 题图
                if (isset($val['banner_img']) && $val['banner_img'] != '') {
                    $val['banner_img'] = explode(",", $val['banner_img']);
                    foreach ($val['banner_img'] as &$v) {
                        $v = env('ALIURL') . $v;
                    }
                    $val['banner_img'] = $val['banner_img'][0] ?? '';
                    unset($v);
                }
                // 产品图
                if (isset($val['product_img']) && $val['product_img'] != '') {
                    $val['product_img'] = explode(",", $val['product_img']);
                    foreach ($val['product_img'] as &$v) {
                        $v = env('ALIURL') . $v;
                    }
                    unset($v);
                }
                // 秒发竖图
                if (isset($val['horizontal_img']) && $val['horizontal_img'] != '') {
                    $val['horizontal_img'] = explode(",", $val['horizontal_img']);
                    foreach ($val['horizontal_img'] as &$v) {
                        $v = env('ALIURL') . $v;
                    }
                    $val['horizontal_img'] = $val['horizontal_img'][0] ?? '';
                    unset($v);
                }
                // 视频封面图增加域名
                if (isset($val['video_cover']) && $val['video_cover'] != '') {
                    $val['video_cover'] = env('ALIURL') . $val['video_cover'];
                }
                $val['is_gift']      = 0;
                $val['is_reduction'] = 0;
                // 商品标签
                $val['label'] = [];
            }
            unset($val);

            //查询商品新人价
            $newcomer = [];
            if ($is_new_user == 1 && !empty($calcPrice)) {
                $newcomer = getCalcPrice($calcPrice, $uid);
            }

            // 查询秒发套餐立减
            $package_ser = new Package(1);
            $pl          = $package_ser->getPackageByPeriods($ids);
            // 查询满减
            $goods_label_url = env("ITEM.COMMODITIES_SERVICES") .
                '/commodities_server/v3/second_home/get_full_reduction_label';

            $body        = json_encode(['data' => $label_data]);
            $haeder      = ['vinehoo-uid:' . $uid];
            $label_res   = curlRequest($goods_label_url, $body, $haeder);
            $goods_label = $label_res['data'] ?? [];

            foreach ($data['list'] as &$val) {
                //新人价
                if (!empty($newcomer[$val['id']]['lowest_price'])) {
                    $val['price'] = floatval($newcomer[$val['id']]['lowest_price']);
                }
                if (!empty($pl)) {
                    // 立减标签
                    foreach ($pl as &$v) {
                        if ($val['id'] == $v['period_id']) {
                            if ($v['preferential_reduction'] == intval($v['preferential_reduction'])) {
                                $formattedNumber = intval($v['preferential_reduction']);
                            } else {
                                $formattedNumber = number_format(floatval($v['preferential_reduction']), 2);
                            }
                            $val['label'][] = '立减' . $formattedNumber;
                        }
                    }
                }
                // 满减标签
                if (isset($goods_label['list'][$val['id']]) && !empty($goods_label['list'][$val['id']])) {
                    foreach ($goods_label['list'][$val['id']] as $v1) {
                        $val['label'][] = $v1['title'];
                    }
                }
            }
            unset($val, $v, $v1);
        }

        $result['list']  = $data['list'];
        $result['total'] = $data['total'];
        return throwResponse($result);
    }

    /**
     * 秒发商品筛选列表
     * @param Request $request
     * @return \think\Response
     */
    public function getSecondTopFilter(Request $request): \think\Response
    {
        $list = [
            [
                "id"          => "1",
                "image"       => env('ALIURL') . "/vinehoo/vos/marketing/ganbai.png",
                "second_name" => "白葡萄酒",
                "sort"        => 1,
            ],
            [
                "id"          => "2",
                "image"       => env('ALIURL') . "/vinehoo/vos/marketing/xiaozhuo.png",
                "second_name" => "红葡萄酒",
                "sort"        => 2,
            ],
            [
                "id"          => "3",
                "image"       => env('ALIURL') . "/vinehoo/vos/marketing/qipao.png",
                "second_name" => "起泡香槟",
                "sort"        => 3,
            ],
            [
                "id"          => "4",
                "image"       => env('ALIURL') . "/vinehoo/vos/marketing/liejiu.png",
                "second_name" => "烈酒",
                "sort"        => 4,
            ],
            [
                "id"          => "5",
                "image"       => env('ALIURL') . "/vinehoo/vos/marketing/other.png",
                "second_name" => "其他",
                "sort"        => 5,
            ],
        ];
        return throwResponse(['list' => $list]);
    }

    /**
     * @方法描述:获取秒发筛选项
     * <AUTHOR>
     * @Date   2023/06/29
     * @return \think\Response
     */
    public function getSecondLeftFilter(Request $request)
    {
        /*
        // 获取筛选项字段
        $field = ['id', 'country', 'winery', 'regions'];
        $param = $request->get();
        //筛选ID
        $second_id = $param['second_id'] ?? 1;
        // 兼容商家秒发
        $esWhere['bool']['must'][] = ['match' => ['periods_type' => 1]];
        // 非渠道商品
        $esWhere['bool']['must'][] = ['match' => ['is_channel' => 0]];
        // 默认查询在售中
        $esWhere['bool']['must'][] = ['match' => ['onsale_status' => 2]];
        // 获取商品ES筛选条件
        $esWhere = PeriodsSecond::getFilterCriteria($second_id, $esWhere);

        $params = [
            'index' => 'vinehoo.periods',
            'type' => '_doc',
            '_source' => $field,
            'body' => [
                'query' => $esWhere,
            ]
        ];
        // es 配置
        $hosts = [
            [
                'host' => env('ES.HOST', '127.0.0.1'),
                'port' => env('ES.PORT', 9200),
                'user' => env('ES.USER', 'root'),
                'pass' => env('ES.PASS', 'vinehoo666')
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);
        // $product_category = [];
        $country = [];
        $winery = [];
        $regions = [];
        if (!empty($data['list'])) {
            foreach ($data['list'] as $val) {
                // 国家
                if (!empty($val['country'])) {
                    foreach ($val['country'] as $v) {
                        if (!empty($v) && !in_array($v, $country)) {
                            array_push($country, $v);
                        }
                    }
                    unset($v);
                }
                // 产品类型
                // if (!empty($val['product_category'])) {
                //     foreach ($val['product_category'] as $v) {
                //         if (!empty($v) && !in_array($v, $product_category)) {
                //             array_push($product_category, $v);
                //         }
                //     }
                //     unset($v);
                // }
                // 酒庄
                if (!empty($val['winery'])) {
                    foreach ($val['winery'] as $v) {
                        if (!empty($v) && !in_array($v, $winery)) {
                            array_push($winery, $v);
                        }
                    }
                    unset($v);
                }
                // 产区
                if (!empty($val['regions'])) {
                    foreach ($val['regions'] as $v) {
                        if (!empty($v) && !in_array($v, $regions)) {
                            array_push($regions, $v);
                        }
                    }
                    unset($v);
                }
            }
        }*/
        $param = $request->get();
        //筛选ID
        $second_id = $param['second_id'] ?? 1;
        $result    = Db::name('periods_second_sub_classes')
            ->where([
                ['main_id', '=', $second_id],
                ['status', '=', 1],
            ])
            ->field('name,keywords,list')
            ->select()->toArray();
        foreach ($result as &$v) {
            $v['list'] = explode(',', $v['list']);
            $v['key']  = $v['keywords'];
            unset($v['keywords']);
        }

        return throwResponse($result);
    }

    public function syncSale(Request $request)
    {
        $param = $request->param();

        try {
            Log::write("syncSale_1 param: " . json_encode($param));
            Log::write("syncSale_2 param: ");
            if (!intval($param['onsale_time'])) return throwResponse();//未上架的不处理

            if ($param['is_channel']) {
                $is_hidden = 1; //渠道商品隐藏
            } else {
                $is_hidden = in_array($param['onsale_status'], [1, 2]) ? 0 : 1;//int64	是否隐藏（0：不隐藏，1：隐藏）
            }

            //region 查询产品
            $product_ids = [];
            $packages    = Es::name('periods_set')->where([
                ['period_id', '=', $param['id']],
                ['is_hidden', '=', '0'],
            ])->field('associated_products')->select()->toArray();
            foreach ($packages as $package) {
                $associated_products = json_decode($package['associated_products'], true);
                $product_ids         = array_merge($product_ids, array_column($associated_products, 'product_id'));
            }

            $product_list = Es::name('panshi.products')
                ->where([
                    ['id', 'in', array_unique($product_ids)],
                ])
                ->field('grape,chateau_name_cn,regions_name_cn')
                ->select()->toArray();

            foreach ($product_list as $k => $item) {
                $product_list[$k] = implode(',', array_values($item));
            }
            $product_label_string = $this->obliqueLineToMinus(implode(',', array_values($product_list)));

            $recommend_labels = Db::name('recommend_label')
                ->where([
                    ['id', 'in', explode(',', $param['label'])],
                    ['type', '=', 2],
                    ['is_delete', '=', 0],
                ])->column('name'); //商品标签

            if (($param['price'] > 0) && ($param['price'] <= 200)) {
                $recommend_labels[] = '0至200';
            } elseif (($param['price'] > 0) && ($param['price'] <= 500)) {
                $recommend_labels[] = '200至500';
            } elseif ($param['price'] > 500) {
                $recommend_labels[] = '500以上';
            }

            $labels = array_unique($this->flatten([
                explode(',', $this->obliqueLineToMinus($param['country'])), //国家
                explode(',', $this->obliqueLineToMinus($param['product_category'])), //类别
                explode(',', $this->obliqueLineToMinus(implode(',', $recommend_labels))), //标签
                explode(',', $product_label_string), //标签
            ]));
            //endregion 查询产品

            $push_data = [
                'item_id'     => "second-{$param['id']}", // string 项目id(全局唯一,使用来源加减号加数据id,例如秒发商品12:)
                'categories'  => ["秒发"], //[]string
                'comment'     => $this->obliqueLineToMinus($param['title']), //string
                'is_hidden'   => $is_hidden, //int64	是否隐藏（0：不隐藏，1：隐藏）
                'labels'      => array_values(array_filter($labels)), //[]string	标签(项目个性化标签,例如:小甜水)
                'create_time' => intval($param['onsale_time']), //	int64	上新时间(时间戳)
            ];

            $url        = env('ITEM.RECOMMEND_URL') . '/go-recommend/v3/item/modify';
            $code       = httpCurl($url, 'post', json_encode($push_data));
            $userResult = json_decode($code, true);

            Log::write("syncSale 同步数据: request- url: {$url} data:" . json_encode($push_data) . ' ret_data-' . $code);

            if ($userResult['error_code'] != '0') {
                Log::error("syncSale 同步数据失败: request-" . json_encode($push_data) . ' ret_data-' . $code);
                throw new Exception('syncSale 同步数据失败:' . $userResult['error_msg']);
            }
        } catch (\Exception $e) {
            Log::error("syncSale 同步数据失败:" . $e->getMessage());
        }

        return throwResponse();
    }

    //斜线转成-号
    public function obliqueLineToMinus($string)
    {
        $arr = [
            '/'  => '-',
            '\\' => '-',
        ];

        $string = str_replace(array_keys($arr), array_values($arr), $string);

        return $string;
    }

    //多维转一维数组
    public function flatten($arr)
    {
        $result = [];
        foreach ($arr as $value) {
            if (is_array($value)) {
                $result = array_merge($result, $this->flatten($value));
            } else {
                $result[] = $value;
            }
        }
        return $result;
    }

    /**
     * 查询秒发闪购
     * @param Request $request
     * @return \think\Response
     */
    public function getSecondFlashList(Request $request): \think\Response
    {

        $from = (int)$request->get('page', 0);
        $size = (int)$request->get('limit', 15);

        if ($from > 0) {
            $from = ($from - 1) * $size;
        } else {
            $from = 0;
        }

        $title = $request->get('title', '');
        if (empty($title)) {
            return throwResponse([]);
        }
        $esWhere['bool']['must'][] = ['match' => ['title' => $title]];

        //        $esWhere['bool']['must'] = [];
        $param = $request->get();
        //        $second_id = $param['second_id'] ?? 1;
        // 秒发频道
        //        $esWhere['bool']['must'][] = ['match' => ['periods_type' => 1]];
        // 兼容商家秒发
        //        $esWhere['bool']['must'][] = ['terms' => ['periods_type' => [1, 9]]];
        // 秒发闪购
        $esWhere['bool']['must'][] = ['terms' => ['periods_type' => [0, 1]]];
        //        $esWhere['bool']['must'][] = ['terms' => ['second_ids' => [$second_id]]];

        // 默认查询，待售中，在售中，售完不下架
        //        $esWhere['bool']['must'][] = ['match' => ['onsale_status' => 2]];
        $esWhere['bool']['should'][]             = ['match' => ['onsale_status' => 1]];
        $esWhere['bool']['should'][]             = ['match' => ['onsale_status' => 2]];
        $esWhere['bool']['should'][]             = ['match' => ['sellout_sold_out' => 1]];
        $esWhere['bool']['minimum_should_match'] = 1;

        // 排序
        $sort = [['periods_type' => 'desc'], ['sort' => 'desc']];
        // 指定字段
        $field = [
            'id', 'title', 'periods_type', 'banner_img', 'price', 'saled_count', 'is_hidden_price',
            'onsale_status', 'product_label', 'top_label', 'left_top_label'
        ];

        $params = [
            'index'   => 'vinehoo.periods',
            'type'    => '_doc',
            '_source' => $field,
            'body'    => [
                'query' => $esWhere,
                'sort'  => $sort,
                'size'  => $size,
                'from'  => $from
            ]
        ];
        // es 配置
        $hosts = [
            [
                'host' => env('ES.HOST', '127.0.0.1'),
                'port' => env('ES.PORT', 9200),
                'user' => env('ES.USER', 'root'),
                'pass' => env('ES.PASS', 'vinehoo666')
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client    = $es_client->create()->setHosts($hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);
        $ids  = [];
        // 遍历列表取出限购数量
        if (!empty($data['list'])) {
            foreach ($data['list'] as &$val) {
                if (isset($val['banner_img']) && $val['banner_img'] != '') {
                    $val['banner_img'] = explode(",", $val['banner_img']);
                    foreach ($val['banner_img'] as &$v) {
                        $v = env('ALIURL') . $v;
                    }
                    $val['banner_img'] = $val['banner_img'][0] ?? '';
                    unset($v);
                }
            }
        }
        $result['list']  = $data['list'];
        $result['total'] = $data['total'];
        return throwResponse($result);
    }

    /*
     * @方法描述: 聚合搜索
     * <AUTHOR>
     * @Date 2023/5/27 14:39
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function search(Request $request)
    {
        #region 获取入参
        $param = $request->param();
        validate()->rule([
            'page|当前页'     => ">:0",
            'limit|每页条数'  => ">:0",
            'type|搜索类型'   => "require|in:0,1,2,3",
            'keywords|关键字' => "require",
        ])->check($param);

        $page     = $param['page'] ?? 1;
        $page     = intval($page);
        $limit    = 15; //$param['limit'] ?? 15;
        $limit    = intval($limit);
        $begin    = ($page - 1) * $limit;
        $type     = $param['type'];
        $keywords = $param['keywords'];
        $h_client = $this->request->header('vinehoo-client', '');
        #endregion

        #region 初始化参数
        $empty_obj  = (object)[];
        $ret_item   = [
            'genre'   => '',
            'goods'   => $empty_obj,
            'auction' => $empty_obj,
            'party'   => $empty_obj,
            'posts'   => $empty_obj,
            'news'    => $empty_obj,
            'wine'    => $empty_obj,
        ];
        $list       = [];
        $total      = 0;
        $image_host = env("ALIURL");
        #endregion

        //2-内容，3-酒会
        if ($type == 0) {

            $custom_analyzer = false;
//            $search_keywords = Db::name('search_keywords')->column('name');
//            foreach ($search_keywords as $search_keyword) {
//                if (false !== strpos($keywords, $search_keyword)) {
//                    $custom_analyzer = 'ik_max_word';
//                }
//            }


            $type_str = 'goods';
            #region 查询商品 (闪购,秒发,跨境,尾货) 数据处理
            $where    = $where2 = [
                ["onsale_status", '=', 2], //上架状态:0=待上架, 1=待售中, 2=在售中, 3=已下架, 4=已售罄
                ["is_channel", '=', 0], //渠道销售（0：否，1：是）',
                ["is_delete", '=', 0], //上架状态:0=待上架, 1=待售中, 2=在售中, 3=已下架, 4=已售罄
            ];
            $where[]  = ["periods_type", 'in', [1]]; //频道:0=闪购, 1=秒发, 2=跨境, 3=尾货, 4=兔头实物, 5=兔头优惠券, 9=商家秒发, 11=拍卖
            $where2[] = ["periods_type", 'in', [0, 1, 2, 3]]; //频道:0=闪购, 1=秒发, 2=跨境, 3=尾货, 4=兔头实物, 5=兔头优惠券, 9=商家秒发, 11=拍卖
            $where_or = [];
//            if ($custom_analyzer) {
//                $keywords_where = ['query' => $keywords, 'analyzer' => $custom_analyzer];
//            } else {
//                $keywords_where = $keywords;
//            }
            $keywords_where = ['query' => $keywords, 'minimum_should_match' => '100%'];

            $product     = Es::name(Es::PRODUCTS)->where([['grape_name', '==', $keywords]])->field('grape_name,short_code')->select()->toArray();
            $short_codes = array_column($product, 'short_code');

            $where_or[] = ['title', '=', $keywords_where];
            $where_or[] = ['brief', '=', $keywords_where];
            $where_or[] = ['regions', '==', $keywords];
            $where_or[] = ['product_category', '==', $keywords];
            $where_or[] = ['country', '==', $keywords];
            $where_or[] = ['short_code', 'in', $short_codes];

            $items = Es::name('periods')->where($where)->whereOr($where_or)->field('id,title')->limit(0, 30)->select()->toArray();

            $field     = 'id,title,periods_type,purchased,vest_purchased,marketing_attribute,product_img,price,saled_count,is_hidden_price,onsale_status,quota_rule,limit_number'; //product_label,top_label,left_top_label
            $match_num = count($items);

            if ($match_num < 30) {
                //匹配推荐标签
                $recommend_labels = Db::name('recommend_label')->where([
                    'type'      => 1, //类型：1-搜索标签 2-商品标签
                    'is_delete' => 0, //删除状态：0-正常 1-已删除
                ])->order('id desc')->column('name');

                $txt = implode(',', array_column($items, 'title'));

                foreach ($recommend_labels as $label) {
                    if ((false !== strpos($txt, $label)) && ($keywords != $label)) {
                        //匹配成功 加入标签
                        if ($custom_analyzer) {
                            $where_or[] = ['title', '=', ['query' => $label, 'analyzer' => $custom_analyzer]];
                        } else {
                            $where_or[] = ['title', '=', $label];
                        }
                    }
                }

                $goods_ids   = array_column($items, 'id');
                $where2[]    = ['id', 'not in', $goods_ids];
                $label_total = Es::name('periods')->where($where2)->whereOr($where_or)->count();

                $goods_page_ids   = array_chunk($goods_ids, intval($limit));
                $current_page_ids = $goods_page_ids[intval($page - 1)] ?? [];

                $has_label_goods = count($current_page_ids) != $limit;

                $match_goods_list = $label_goods_list = [];
                if (!empty($current_page_ids)) {
                    $match_goods_list = Es::name('periods')->where([
                        ['id', 'in', $current_page_ids]
                    ])->field($field)->select()->toArray();
                }

                if ($has_label_goods) {
                    //根据标签查询数据
                    $begin = intval($begin - $match_num);
                    if ($begin < 0) {
                        $limit += $begin;
                        $begin = 0;
                    }
                    $label_goods_list = Es::name('periods')->where($where2)->whereOr($where_or)->field($field)->limit($begin, $limit)->select()->toArray();
                }

                $goods_list = array_merge($match_goods_list, $label_goods_list);

                $total = intval($label_total + $match_num);
            } else {
                $goods_list = Es::name('periods')->where($where)->whereOr($where_or)->field($field)->limit($begin, $limit)->select()->toArray();
                $total      = Es::name('periods')->where($where)->whereOr($where_or)->count();
            }

            #获取新人价


            $vh_uid = request()->header('vinehoo-uid', null);
            if ($vh_uid) {
                //查询是否新人
                $user        = \Curl::getUserInfo($vh_uid, 'uid,nickname,is_new_user');
                $is_newcomer = boolval($user['is_new_user']);
            } else {
                //未登录就算新人
                $is_newcomer = true;
            }
            if ($is_newcomer) {
                $periods = [];
                foreach ($goods_list as $item) {
                    $periods[] = [
                        "id"          => $item['id'],//商品id
                        "period_type" => $item['periods_type'],//商品频道
                        "price"       => $item['price']//原始金额
                    ];
                }

                $calc_price = \Curl::calcPrice(compact('is_newcomer', 'periods'))['list'] ?? [];
                $calc_price = array_column($calc_price, 'lowest_price', 'id');
            }

            //数据处理
            foreach ($goods_list as $item) {
                if ($item['product_img']) {
                    $item['product_img'] = $image_host . (explode(',', $item['product_img'])[0]);
                }
                $item['purchased'] = $item['saled_count'] = $item['purchased'] + $item['vest_purchased'];
                $item['price']     = $calc_price[$item['id']] ?? $item['price'];
                unset($item['vest_purchased']);

                $temp_item            = $ret_item;
                $temp_item['genre']   = $type_str;
                $temp_item[$type_str] = $item;
                $list[]               = $temp_item;
            }
            #endregion
        } elseif ($type == 1) {
            $type_str = 'auction';
            #region查询拍卖

            $field = 'id,title,onsale_status,product_img as banner_img,price,sell_time,closing_auction_time,pageviews';
            $where = function ($query) use ($keywords) {
                $query->where('title', 'like', "%$keywords%");
                $query->where('onsale_review_status', '=', 3);
                $query->where('onsale_status', 'in', [1, 2]);
                $query->where('is_delete', '=', '0');
            };


            try {
                $auction_list = Db::connect('auction')->name('goods')->where($where)->order('sort', 'desc')->limit($begin, $limit)->column($field);
                $total        = Db::connect('auction')->name('goods')->where($where)->count();
            } catch (\Exception $e) {
                print_r($e);
                die;
            }

            //数据处理
            foreach ($auction_list as $item) {
                if ($item['onsale_status'] == 2) {
                    $item['sell_time'] = auctionEctime($item['closing_auction_time']) . ' 结束';
                } else {
                    $item['sell_time'] = auctionSctime($item['sell_time']) . ' 开始';
                }
                unset($item['closing_auction_time']);
                unset($item['onsale_status']);
                $item['closing_auction_time'] =
                $item['periods_type'] = 11; //拍品
                if ($item['banner_img']) {
                    $item['banner_img'] = $image_host . (explode(',', $item['banner_img'])[0]);
                }

                $temp_item            = $ret_item;
                $temp_item['genre']   = $type_str;
                $temp_item[$type_str] = $item;
                $list[]               = $temp_item;
            }
            #endregion
        } elseif ($type == 2) {
            #region查询内容
            $url        = env('ITEM.COMMODITIES_SERVICES') . '/commodities_server/v3/second_home/get_content?page=' . $page . '&keywords=' . $keywords;
            $code       = httpCurl($url, 'get', []);
            $userResult = json_decode($code, true);

            $total        = $userResult['total'] ?? 0;
            $content_list = $userResult['list'] ?? [];

            foreach ($content_list as $item) {
                $type_str = $item['genre'];
                if (!(in_array($h_client, ['miniapp', 'h5']) && ($type_str == 'post'))) { //类型为内容时，H5、小程序不能返回帖子，因为没有社区功能的
                    $item_data = $item[$type_str];
                    $type_str  = ($type_str == 'post') ? 'posts' : $type_str;

                    //数据处理
                    unset($item_data['type']);
                    unset($item_data['uid']);
                    $item_data['banner_img'] = explode(',', $item_data['banner_img'])[0] ?? '';

                    $temp_item            = $ret_item;
                    $temp_item['genre']   = $type_str;
                    $temp_item[$type_str] = $item_data;
                    $list[]               = $temp_item;
                }
            }
            #endregion
        } elseif ($type == 3) {
            $type_str = 'party';
            #region查询酒会
            $field = 'id,title,thumb_image as banner_img';
            $where = function ($query) use ($keywords) {
                $query->where('title', 'like', "%$keywords%");
                $query->where('is_vh_show', '=', 1);
                $query->where('status', '=', 1);
            };

            $party_list = Db::connect('wineparty')->name('wine_party')->where($where)->order('sort', 'desc')->limit($begin, $limit)->column($field);
            $total      = Db::connect('wineparty')->name('wine_party')->where($where)->count();

            $party_packages = Db::connect('wineparty')->name('wine_party_package')
                ->where('party_id', 'in', array_column($party_list, 'id'))
                ->where('is_show', '=', 1)
                ->column('id,party_id,money,limitnum,apply_num');

            $packages_group = [];
            foreach ($party_packages as $package) {
                $packages_group[$package['party_id']][] = $package;
            }

            //数据处理
            foreach ($party_list as $item) {
                $package_group = $packages_group[$item['id']] ?? [];
                if ($item['banner_img']) {
                    $item['banner_img'] = $image_host . (explode(',', $item['banner_img'])[0]);
                }
                $item['price']           = $package_group[0]['money'] ?? 0;
                $item['remaining_quota'] = intval(array_sum(array_column($package_group, 'limitnum')) - array_sum(array_column($package_group, 'apply_num')));

                $temp_item            = $ret_item;
                $temp_item['genre']   = $type_str;
                $temp_item[$type_str] = $item;
                $list[]               = $temp_item;
            }
            #endregion
        }

        return throwResponse(compact('total', 'list'));
    }

    /**
     * @方法描述: 修改商家秒发期数关联酒云期数的真实销量
     * <AUTHOR>
     * @Date 2023/6/16 13:25
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function syncMerchantSales(Request $request)
    {
        #region 获取入参
        $param = $request->param();
        validate()->rule([
            'period|商家秒发期数'       => "require|integer",
            'sub_order_no|子订单号'     => "require",
            'sub_order_status|子订状态' => "require",
            'order_qty|套餐数量'        => "require",
            'package_id|套餐ID'         => "require",
        ])->check($param);
        #endregion

        if (in_array($param['sub_order_status'], [1, 2, 3])) {
            //验证订单是否加过销量了
            if (!PeriodsPurchasedSync::where('sub_order_no', $param['sub_order_no'])->find()) {

                $package             = Es::name(Es::PERIODS_PACKAGE)->where([['id', '==', $param['package_id']]])->field('associated_products')->find();
                $associated_products = json_decode($package['associated_products'], true);
                $package_nums        = intval(array_sum(array_column($associated_products, 'nums')));
                $purchased           = intval($package_nums * $param['order_qty']);
                $period              = intval($param['period']);

                #region
                $second_period_id = PeriodsSecondMerchants::where('id', $period)->value('join_period_id');

                if ($second_period_id) {
                    $second_period                     = \app\model\PeriodsSecond::where('id', $second_period_id)->find();
                    $second_period->merchant_purchased = $second_period->merchant_purchased + $purchased;
                    $second_period->purchased          = $second_period->purchased + $purchased;
                    $second_period->save();
                }

                (new PeriodsPurchasedSync)->save([
                    'sub_order_no'  => $param['sub_order_no'],
                    'vmall_period'  => $period,
                    'second_period' => $second_period_id,
                    'purchased'     => $purchased,
                ]);
            }
        }

        #endregion

        return throwResponse();
    }

    /**
     * 秒发商品PC筛选列表
     * @param Request $request
     * @return \think\Response
     */
    public function SecondFilterList(Request $request): \think\Response
    {
        $from = (int)$request->get('page', 0);
        $size = (int)$request->get('limit', 15);

        if ($from > 0) {
            $from = ($from - 1) * $size;
        } else {
            $from = 0;
        }


        $param = $request->get();
        //是否新用户：0-否，1是（未登录默认1）
        $uid         = $request->header('vinehoo-uid');
        $is_new_user = getIsNewUser($uid);

        //产品类型
        $second_id = 0;
        if (!empty($param['product_category'])) {
            switch ($param['product_category']) {
                case "白葡萄酒":
                    $second_id = 1;
                    break;
                case "红葡萄酒":
                    $second_id = 2;
                    break;
                case "起泡香槟":
                    $second_id = 3;
                    break;
                case "烈酒":
                    $second_id = 4;
                    break;
                case "其他":
                    $second_id = 5;
                    break;
            }
        }
        if ($second_id > 0) {
            // 获取商品ES筛选条件
            $esWhere = PeriodsSecond::getFilterCriteria(['second_id' => $second_id]);
        }

        // 国家
        if (!empty($param['country'])) {
            $esWhere['bool']['must'][] = ['match_phrase' => ['country' => $param['country']]];
        }

        // 关键字
        if (!empty($param['product_keyword'])) {
            $esWhere['bool']['must'][] = ['match_phrase' => ['product_keyword' => $param['product_keyword']]];
        }

        // 兼容商家秒发
        $esWhere['bool']['must'][] = ['match' => ['periods_type' => 1]];
        // 在售中
        $esWhere['bool']['must'][] = ['match' => ['onsale_status' => 2]];
        // 非渠道销售
        $esWhere['bool']['must'][] = ['match' => ['is_channel' => 0]];

        // 排序
        $sort = [['sort' => 'desc'], ['onsale_time' => 'desc']];
        if (!empty($param['sort_type']) && $param['sort_type'] != "sort") {
            if ($param['sort_type'] == 'purchased') {
                $sort = [
                    [
                        '_script' => [
                            'type' => 'number',
                            'script' => [
                                'lang' => 'painless',
                                'source' => "doc['purchased'].value + doc['vest_purchased'].value"
                            ],
                            'order' => $param['order'],
                        ]
                    ]
                ];
            } else {
                $sort = [[$param['sort_type'] => $param['order']]];
            }
        }

        // 指定字段
        // $field  = [
        //     'id', 'country', 'title', 'brief', 'banner_img', 'horizontal_img', 'sort', 'second_ids',
        //     'purchased', 'vest_purchased', 'price', 'market_price', 'periods_type', 'product_img'
        // ];
        $params = [
            'index'   => 'vinehoo.periods',
            'type'    => '_doc',
            // '_source' => $field,
            'body'    => [
                'query' => $esWhere,
                'sort'  => $sort,
                'size'  => $size,
                'from'  => $from
            ]
        ];
        // es 配置
        $hosts = [
            [
                'host' => env('ES.HOST', '127.0.0.1'),
                'port' => env('ES.PORT', 9200),
                'user' => env('ES.USER', 'root'),
                'pass' => env('ES.PASS', 'vinehoo666')
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client    = $es_client->create()->setHosts($hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data       = get_es_result($es_data);
        $ids        = array();
        $label_data = [];
        $calcPrice  = [];
        // 遍历列表取出限购数量
        if (!empty($data['list'])) {
            foreach ($data['list'] as &$val) {
                $calcPrice[] = [
                    "id"          => $val['id'],
                    "period_type" => $val['periods_type'],
                    "price"       => $val['price'],
                ];

                $ids[]        = $val['id'];
                $label_data[] = [
                    'periods'      => $val['id'],
                    'periods_type' => $val['periods_type'],
                ];
                if (isset($val['quota_rule']) && !empty($val['quota_rule'])) {
                    $val['quota_number'] = json_decode($val['quota_rule'], true)['quota_number'] ?? 0;
                }
                // 题图
                if (isset($val['banner_img']) && $val['banner_img'] != '') {
                    $val['banner_img'] = explode(",", $val['banner_img']);
                    foreach ($val['banner_img'] as &$v) {
                        $v = env('ALIURL') . $v;
                    }
                    $val['banner_img'] = $val['banner_img'][0] ?? '';
                    unset($v);
                }
                // 产品图
                if (isset($val['product_img']) && $val['product_img'] != '') {
                    $val['product_img'] = explode(",", $val['product_img']);
                    foreach ($val['product_img'] as &$v) {
                        $v = env('ALIURL') . $v;
                    }
                    unset($v);
                }
                // 秒发竖图
                if (isset($val['horizontal_img']) && $val['horizontal_img'] != '') {
                    $val['horizontal_img'] = explode(",", $val['horizontal_img']);
                    foreach ($val['horizontal_img'] as &$v) {
                        $v = env('ALIURL') . $v;
                    }
                    $val['horizontal_img'] = $val['horizontal_img'][0] ?? '';
                    unset($v);
                }
                // 视频封面图增加域名
                if (isset($val['video_cover']) && $val['video_cover'] != '') {
                    $val['video_cover'] = env('ALIURL') . $val['video_cover'];
                }
                $val['is_gift']      = 0;
                $val['is_reduction'] = 0;
                // 商品标签
                $val['label'] = [];
            }
            unset($val);

            //查询商品新人价
            $newcomer = [];
            if ($is_new_user == 1 && !empty($calcPrice)) {
                $newcomer = getCalcPrice($calcPrice, $uid);
            }

            // 查询秒发套餐立减
            $package_ser = new Package(1);
            $pl          = $package_ser->getPackageByPeriods($ids);
            // 查询满减
            $goods_label_url = env("ITEM.COMMODITIES_SERVICES") .
                '/commodities_server/v3/second_home/get_full_reduction_label';

            $body        = json_encode(['data' => $label_data]);
            $haeder      = ['vinehoo-uid:' . $uid];
            $label_res   = curlRequest($goods_label_url, $body, $haeder);
            $goods_label = $label_res['data'] ?? [];

            foreach ($data['list'] as &$val) {
                //新人价
                if (!empty($newcomer[$val['id']]['lowest_price'])) {
                    $val['price'] = floatval($newcomer[$val['id']]['lowest_price']);
                }
                if (!empty($pl)) {
                    // 立减标签
                    foreach ($pl as &$v) {
                        if ($val['id'] == $v['period_id']) {
                            if ($v['preferential_reduction'] == intval($v['preferential_reduction'])) {
                                $formattedNumber = intval($v['preferential_reduction']);
                            } else {
                                $formattedNumber = number_format(floatval($v['preferential_reduction']), 2);
                            }
                            $val['label'][] = '立减' . $formattedNumber;
                        }
                    }
                }
                // 满减标签
                if (isset($goods_label['list'][$val['id']]) && !empty($goods_label['list'][$val['id']])) {
                    foreach ($goods_label['list'][$val['id']] as $v1) {
                        $val['label'][] = $v1['title'];
                    }
                }
            }
            unset($val, $v, $v1);
        }

        $result['list']  = $data['list'];
        $result['total'] = $data['total'];
        return throwResponse($result);
    }

    /**
     * @方法描述: 根据简码查询待上架期数
     * <AUTHOR>
     * @Date 202412/17
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPeriodsByShortCode(Request $request)
    {
        $param = $request->param();

        if (empty($param['short_code']) || !is_array($param['short_code'])) {
            return throwResponse([], ErrorCode::PARAM_ERROR, '简码不能为空');
        }

        $periods_product = Db::name('periods_product_inventory')
            ->alias('pi')
            ->leftJoin('periods_second p', 'pi.period = p.id')
            ->where([
                ['pi.short_code', 'in', $param['short_code']],
                ['pi.periods_type', '=', 1],
                ['pi.is_use_comment', '=', 0],
                ['p.onsale_status', '=', 0],
                ['pi.warehouse_id', '=', 13],
            ])
            ->column('p.id,p.title,pi.short_code,pi.bar_code,pi.product_name,pi.en_product_name,pi.capacity');
        $list = [];
        foreach ($periods_product as $v) {
            $list[$v['short_code']][] = $v;
        }

        $result = [
            'list' => $list,
        ];

        return throwResponse($result);
    }

}
