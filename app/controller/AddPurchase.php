<?php

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\model\PeriodsAddPurchase;
use app\Request;
use think\facade\Log;
use const http\Client\Curl\Versions\CURL;

class AddPurchase extends BaseController
{
    /**
     * 添加加购套餐
     * @param Request $request
     * @return \think\Response
     */
    public function addPeriods(Request $request): \think\Response
    {
        $params = $request->post();
        $service = new \app\service\AddPurchase();
        $re = $service->addPurchase($params);
        if ($re == "-1") {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '该期数套餐已经存在');
        }
        return throwResponse($re);
    }

    /**
     * 更新加购套餐
     * @param Request $request
     * @return \think\Response
     */
    public function updateAP(Request $request): \think\Response
    {
        $params = $request->post();
        if (!isset($params['id']) || $params['id'] == '') {
            return throwResponse(null, ErrorCode::PARAM_ERROR, '请选择更新配置');
        }
        $service = new \app\service\AddPurchase();
        $re = $service->updateAP($params);
        return throwResponse($re);
    }

    /**
     * 查询加购商品
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getAddPurchase(Request $request): \think\Response
    {
        $params = $request->get();
        $service = new \app\service\AddPurchase();
        $re = $service->getAddPurchase($params);
        return throwResponse($re);
    }

    /**
     * 根据条件指定配置
     * @param Request $request
     * @return \think\Response
     */
    public function changeStatus(Request $request): \think\Response
    {
        $params = $request->post();
        $service = new \app\service\AddPurchase();

        Log::write("changeStatus:  监听修改状态: ". json_encode($params) );

        try {
            \Curl::exhibProductsSync([
                'period'        => $params['period'],
                'onsale_status' => $params['onsale_status'],
                'table'         => $params['table'] ?? '',
            ]);
        } catch (\Exception $e) {
            Log::error('exhib sync : ' . $e->getMessage());
        }
        
        if (isset($params['onsale_status'])) {
            switch ($params['onsale_status']) {
                case 1://商品待售
                    $service->goodsForSaleHandle($params);
                    break;
                case 2://商品在售
                    $service->goodsOnSaleHandle($params);
                    break;
                case 3://商品下架
                    $service->goodsOffShelfHandle($params);
                    break;
            }
        }

        // 验证闪购、秒发
        if (empty($params['table']) || !in_array($params['table'], ["vh_periods_flash", "vh_periods_second"])) {
            return throwResponse(true);
        }

        // 更新加购配置停用/启用
        if (isset($params['onsale_status']) && $params['onsale_status'] != 2) {
            $where = ['period' => $params['period']];
            $data = ['is_ap' => 0];
            $re = $service->apDisabled($where, $data);
            // 更新秒发筛选标签商品状态
            $service->updateSecondFiltersGoods($params);
            return throwResponse($re);
        }
        
        // 更新预计发货时间
        if (isset($params['predict_shipment_time']) && $params['predict_shipment_time'] != '') {
            $where = ['period' => $params['period']];
            $data = ['predict_shipment_time' => $params['predict_shipment_time']];
            $re = $service->apDisabled($where, $data);
            return throwResponse($re);
        }
        // 套餐隐藏
        if (isset($params['package_id']) && isset($params['is_hidden']) && $params['is_hidden'] == 1) {
            $where = ['package_id' => $params['package_id']];
            $data = ['is_ap' => 0];
            $re = $service->apDisabled($where, $data);
            return throwResponse($re);
        }

        return throwResponse(true);
    }

    /**
     * @方法描述: 更新排序值
     * <AUTHOR>
     * @Date 2023/6/9 9:41
     * @param Request $request
     * @return \think\Response
     */
    public function updateSort(Request $request)
    {
        $param = $request->post();
        return throwResponse(PeriodsAddPurchase::where('id', $param['id'])->update(['sort' => $param['sort']]));
    }

    /**
     * 订金期数下架自动退款
     * <AUTHOR>
     * @Date 2023/12/15
     * @param Request $request
     * @return \think\Response
     */
    public function depositRefund(Request $request): \think\Response
    {
        $params = $request->param();
        $service = new \app\service\AddPurchase();
        $re = $service->depositRefund($params);
        if ($re !== true) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $re);
        }
        return throwResponse();
    }
}