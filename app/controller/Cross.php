<?php
declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\ErrorCode;
use app\service\Cross as PeriodsCross;
use app\Request;
use app\service\Package;
use app\service\Periods;
use app\service\Products;
use app\service\v3\PeriodsPoolService;
use app\validate\Buyer;
use app\validate\Copywriter;
use app\validate\Operations;
use app\service\Other;


class Cross extends BaseController {

    /**
     * 添加跨境商品文案
     *
     * @param Request $request
     * @return false|string|\think\Response
     */
    public function create(Request $request) {
        // 参数
        $params = $request->post();
        // 获取商品id
        $params['id'] = $this->getGeneratorID(1);
        if (!$params['id']) {
            return throwResponse([], ErrorCode::EXEC_ERROR, '生成跨境文案失败');
        }
        // 参数验证
        $validate = new Copywriter();
        // 操作信息
        $params['creator_id'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['creator_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['creator_name']) {
            $params['creator_name'] = base64_decode($params['creator_name']);
        }
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        $params['created_time'] = time();
        $periods_ser = new Periods(2);
        // 获取参数配置默认值
        $params = $periods_ser->get_params_default_value($params);
        // 获取商品产品评分饮用建议
        $product_list = [];
        // 产品简码
        $params['short_code']  = '';
        $params['product_main_category'] = '';
//        if ($params['product_id'] != '') {
//            $params['product_id'] = trim($params['product_id'], ',');
//            $product_ids = explode(',', $params['product_id']);
//            // 查询产品信息
//            $product_url = env('ITEM.WINE_WIKI_URL').'/wiki/v3/product/fieldsdatarr';
//            $field  = 'id, short_code, tasting_notes, score, prize, drinking_suggestion, product_category';
//            $product_info = post_url($product_url, ['ids' => $product_ids, 'fields' => $field]);
//            if (!is_null(json_decode($product_info))) {
//                $result_pro = json_decode($product_info, true);
//                if ($result_pro['error_code'] == 0) {
//                    $product_list = $result_pro['data']['list'];
//                } else {
//                    return  throwResponse(null, ErrorCode::EXEC_ERROR, $result_pro['error_msg']);
//                }
//            }
//            if (!empty($product_list)) {
//                foreach ($product_list as &$value) {
//                    $value['period'] = $params['id'];
//                    $value['product_id'] = $value['id'];
//                    $value['created_time'] = time();
//                    $params['short_code'] .= $value['short_code']. ',';
//                    $params['product_main_category'] .= $value['product_category_name']. ',';
//                    unset($value['id'], $value['product_category_name'], $value['product_category']);
//
//                }
//                unset($value);
//            }
//        }
        if ($params['short_code'] != '') {
            $params['short_code'] = trim($params['short_code'], ',');
        }
        if ($params['product_main_category'] != '') {
            $params['product_main_category'] = trim($params['product_main_category'], ',');
        }
        // 手动创建
        $params['is_manual_create'] = 1;
        
        // 添加跨境商品
        $cross = new PeriodsCross();
        $result = $cross->create($params);

        if (!$result) {
            return throwResponse($result, ErrorCode::EXEC_ERROR, '产品闪购添加失败');
        }

        // 添加商品产品评分饮用建议
        if (!empty($params['product_info'])) {
            $other_ser = new Other();
            $other_ser->createProductInfo($result, $params['product_info']);
        }

        return throwResponse($result);

    }

    /**
     * 添加编辑采购信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateBuyerInfo(Request $request) {
        // 参数
        $params = $request->post();
        // 参数验证
        $validate = new Buyer();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }

        $cross = new PeriodsCross();

        // 添加采购信息
        $result = $cross->update($params);

        if (!$result['status']) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, '添加采购信息失败');
        }

        return throwResponse($result['data']);

    }

    /**
     * 添加编辑运营信息
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update(Request $request) {
        // 参数
        $params = $request->post();
        // 参数验证
        $validate = new Operations();
        $validate_params = $validate->check($params);
        if (!$validate_params) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $validate->getError());
        }
        // 验证商品 id
        $update_validate = $this->updateValidate($params);
        if (!empty($update_validate)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $update_validate);
        }
        $cross = new PeriodsCross();

        // 添加运营信息 时间戳转换
        if (isset($params['predict_shipment_time'])) {
            $params['predict_shipment_time'] = strtotime($params['predict_shipment_time']);
        }
        if (isset($params['onsale_time'])) {
            $params['onsale_time'] = strtotime($params['onsale_time']);
        }
        if (isset($params['sell_time'])) {
            $params['sell_time'] = strtotime($params['sell_time']);
        }
        if (isset($params['sold_out_time'])) {
            $params['sold_out_time'] = strtotime($params['sold_out_time']);
        }
        // 限购规则转 json 字符串
        if (isset($params['quota_rule']) && !empty($params['quota_rule'])) {
            $params['quota_rule'] = json_encode($params['quota_rule']);
        }

        // 如果前端返回图片域名，则删除域名
        if (isset($params['banner_img']) && $params['banner_img'] != '') {
            $params['banner_img'] = str_replace(env('ALIURL'),"", $params['banner_img']);
        }
        if (isset($params['product_img']) && $params['product_img'] != '') {
            $params['product_img'] = str_replace(env('ALIURL'),"", $params['product_img']);
        }
        if (isset($params['video_cover']) && $params['video_cover'] != '') {
            $params['video_cover'] = str_replace(env('ALIURL'),"", $params['video_cover']);
        }

        // 验证绑定产品
        $period_info = $cross->getOne((int)$params['id'], 'product_id, buyer_review_status, supplier_id, payee_merchant_id');
        if (empty($period_info)) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, '未查询到期数信息');
        }
        if (!empty($params['onsale_time'])) {
            if (empty($period_info['supplier_id'])) {
                return throwResponse(null, ErrorCode::EXEC_ERROR, '供应商不能为空');
            }
            if (empty($period_info['payee_merchant_id'])) {
                return throwResponse(null, ErrorCode::EXEC_ERROR, '收款商户不能为空');
            }
        }
        $old_product_id = explode(',', $period_info['product_id']);
        $product_id = explode(',', $params['product_id']);
        $is_del_product = 0;
        foreach ($old_product_id as $v) {
            if (empty($v)) {
                continue;
            }
            if (!in_array($v, $product_id)) {
                $is_del_product = 1;
            }
        }
        if ($is_del_product == 1 && $period_info['buyer_review_status'] == 3) {
            return throwResponse(null, ErrorCode::EXEC_ERROR, '采购审核后不允许删除绑定产品');
        }

        // 查询产品简码，大类
        if ($params['product_id'] != '') {
            $other_ser = new Other();
            $product_info = $other_ser->getProductList($params);
            $params['short_code'] = $product_info['short_code'];
            $params['product_main_category'] = $product_info['product_main_category'];
        }
        $result = $cross->update($params);

        if (!$result['status']) {
            return throwResponse($result['data'], ErrorCode::EXEC_ERROR, $result['msg']);
        }

        (new PeriodsPoolService())->syncPeriods($params);

        // 添加商品产品评分饮用建议
        if (!empty($params['product_info'])) {
            $other_ser = new Other();
            $other_ser->createProductInfo((int)$params['id'], $params['product_info']);
        }

        $periods_ser = new Periods(2);
        // 更新 json 文件
        $periods_ser->create_period_json((int)$params['id'], 2,0,-1);
        // 更新 CDN
        $periods_ser::CDNrefreshObject((int)$params['id']);

        return throwResponse($result['data']);

    }


    /**
     * 商品详细
     *
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(Request $request)
    {
        // 参数
        $params = $request->get();
        $params['id'] = (int) $params['id'];
        // 验证商品 id
        $update_validate = $this->updateValidate($params);
        if (!empty($update_validate)) {
            return throwResponse([], ErrorCode::PARAM_ERROR, $update_validate);
        }

        $cross = new PeriodsCross();

        // 详细信息
        $result = $cross->getOne($params['id'], '*');
        if (empty($result)) {
            return throwResponse($result, ErrorCode::PARAM_ERROR, '期数不存在');
        }
        // 套餐信息
        $package_ser = new Package(2);
        $where = [
            ['period_id', '=', $params['id']],
            ['pid', '=', 0],
        ];
        $result['package'] = $package_ser->getPackage($where);
        // 产品信息
        $products_ser = new Products();
        $result['product_list'] = $products_ser->getListById($result['product_id'], $params['id']);
        // 是否存在上架记录
        $periods = new Periods(0);
        $result['is_onsale_record'] = 0;
        $onsale_record = $periods->getOnSaleRecord($params['id'], 2);
        if ($onsale_record) {
            $result['is_onsale_record'] = 1;
        }
        $label_ser = new \app\service\Label();
        // 标签查询
        $result['label_arr'] = [];
        if (!empty($result['label'])) {
            $result['label_arr'] = $label_ser->getLabelNameByIds($result['label']);
        }
        return throwResponse($result);

    }


    /**
     * 更新商品参数验证
     *
     * @param array $params
     * @return string
     */
    protected function updateValidate(array $params): string {
        // 提示信息
        $message = '';

        // 商品 id 验证
        if (!isset($params['id']) || empty($params['id']) ||
            !is_int($params['id'])) {
            $message .= '请选择商品';
        }

        return $message;
    }

}
