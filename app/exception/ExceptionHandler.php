<?php
namespace app\exception;

use think\exception\Handle;
use think\facade\Log;
use think\Response;
use Throwable;

/*
 * 重写Handle的render方法，实现自定义异常消息
 */
class ExceptionHandler extends Handle
{
    private $code;
    private $msg;
    private $errorCode;
    private $data;
    private $status;
    public function render($request, Throwable $e): Response
    {
        if(env('APP_DEBUG') === true){
            return parent::render($request,  $e);
        }

        if ($e instanceof BaseException) {
            //如果是自定义异常，则控制http状态码，不需要记录日志
            //因为这些通常是因为客户端传递参数错误或者是用户请求造成的异常
            //不记录日志
            $this->code = $e->getCode();
            $this->msg = $e->getMessage();
            $this->errorCode = $e->errorCode;
            $this->data = $e->data;
            $this->status = $e->status;
        } else {

            // 如果是服务器未处理的异常，将http状态码设置为500，并记录日志
//            if(env('APP_DEBUG')){
//                // 调试状态下需要显示TP默认的异常页面，因为TP的默认页面
//                // 很容易看出问题
//                return parent::render($request,$e);
//            }
            $this->code = 200;
            $this->errorCode = 10002;

            $this->msg = $e->getMessage();
            
            $this->recordErrorLog($e);
        }
        
        $result = [
            'error_code' => $this->errorCode,
            'error_msg'  => $this->msg,
            'requestUrl' => $request = $request->url(),
            'data'       => empty($this->data) ? (object)$this->data : $this->data,
        ];
        return json($result, $this->code);
    }

    /*
     * 将异常写入日志
     */
    private function recordErrorLog($e)
    {
        Log::record($e->getMessage());
    }
}