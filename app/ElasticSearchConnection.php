<?php

namespace app;

use Elasticsearch\Client;
use Elasticsearch\ClientBuilder;

class ElasticSearchConnection
{
    /**
     * 配置参数
     * @var array
     */
    protected $config = [];

    public function __construct()
    {
        $host = getConfigs("db.elasticsearch","vinehoo.accounts");
        if (!empty($host)) {
            $this->config = $host;
        }
    }

    /**
     * es 连接
     *
     * @return Client
     */
    public function connection(): Client
    {
        $hosts = [
            // This is effectively equal to: "https://username:password!#$?*<EMAIL>:9200/"
            [
                'host' => $this->config['host'],
                'port' => $this->config['port'],
                'scheme' => 'http',
                'user' => $this->config['username'],
                'pass' => $this->config['password']
            ],

        ];
        return ClientBuilder::create()->setHosts($hosts)->build();
    }

}
