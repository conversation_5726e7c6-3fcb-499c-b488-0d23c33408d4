<?php
// 应用公共文件

use app\model\GeneratorId;
use think\Response;
use app\ElasticSearchConnection;
use think\facade\Log;
use app\service\es\Es;
use think\facade\Db;

if (!function_exists('throwResponse')) {
    /**
     * 接口获取返回方法
     * @param int $code 状态码
     * @param string $error_msg 错误信息
     * @param array $options 返回参数
     * @param array $header 返回头信息
     * @return Response
     */
    function throwResponse($options = [], int $code = 0, string $error_msg = '', array $header = [], $response_code = 200): Response
    {
        $data['error_code'] = intval($code);
        $data['error_msg']  = $error_msg;
        $data['data']       = $options;
        return Response::create($data, 'json', $response_code)->header($header);
    }
}

if (!function_exists('serviceReturn')) {
    /**
     * service 返回数据
     * @param bool $status
     * @param string $data
     * @param string $msg
     * @return array
     */
    function serviceReturn(bool $status = true, $data = '', string $msg = ''): array
    {
        return ['status' => $status, 'data' => $data, 'msg' => $msg];
    }
}

if (!function_exists('get_url')) {
    /**
     * 通过URL获取页面信息
     * @param $url 地址
     * @return mixed 返回页面信息
     */
    function get_url($url, $header = [])
    {
        $start_time = microtime(true);
        $ch = curl_init();
        //设置访问的url地址
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        //不输出内容
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $result = curl_exec($ch);
        curl_close($ch);
        $end_time = microtime(true);
        $execution_time = ($end_time - $start_time);
        //判断是否超过一秒
        if ($execution_time > 0.5) {
            //file_put_contents(app()->getRuntimePath() . '/log/console.log', sprintf("时间:%s,请求路由:%s,耗时:%s秒\n", date('Y-m-d H:i:s'), $url, $execution_time), FILE_APPEND);
        }
        return $result;
    }
}

if (!function_exists('post_url')) {
    /**
     * 模拟POST提交
     * @param string $url 地址
     * @param array | string $data 提交的数据
     * @return string 返回结果
     */
    function post_url($url, $data, $header = [])
    {
        $start_time = microtime(true);
        if (is_array($data)) {
            $data = http_build_query($data);
        }
        // 启动一个CURL会话
        $curl = curl_init();
        // 要访问的地址
        curl_setopt($curl, CURLOPT_URL, $url);
        // 对认证证书来源的检查
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        // 从证书中检查SSL加密算法是否存在
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        // 模拟用户使用的浏览器
        curl_setopt($curl, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
        // 发送一个常规的Post请求
        curl_setopt($curl, CURLOPT_POST, 1);
        // Post提交的数据包
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        // 设置超时限制 防止死循环
        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        // 显示返回的Header区域内容
        curl_setopt($curl, CURLOPT_HEADER, 0);
        // 获取的信息以文件流的形式返回
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        $tmpInfo = curl_exec($curl); // 执行操作
//        if(curl_errno($curl))
//        {
//            echo 'Errno'.curl_error($curl);//捕抓异常
//        }
        curl_close($curl); // 关闭CURL会话
        $end_time = microtime(true);
        $execution_time = ($end_time - $start_time);
        if ($execution_time > 0.5) {
            //file_put_contents(app()->getRuntimePath() . '/log/console.log', sprintf("时间:%s,请求路由:%s,耗时:%s秒\n", date('Y-m-d H:i:s'), $url, $execution_time), FILE_APPEND);
        }
        return $tmpInfo; // 返回数据
    }

}

if (!function_exists('get_interior_http_response')) {
    /**
     * 解析内部访问数据
     * @param $json
     * @return mixed
     * <AUTHOR>
     */
    function get_interior_http_response($json)
    {
        $result = json_decode($json, true);
        if ($result['error_code'] == '0') {
            return $result['data'];
        } else {
            return null;
        }
    }
}

if (!function_exists('uuid')) {
    function uuid()
    {
        $chars = md5(uniqid(mt_rand(), true));
        $uuid  = substr($chars, 0, 8) . '-'
            . substr($chars, 8, 4) . '-'
            . substr($chars, 12, 4) . '-'
            . substr($chars, 16, 4) . '-'
            . substr($chars, 20, 12);
        return $uuid;
    }
}

if (!function_exists('getAdminUser')) {
    /**
     * 获取用户信息
     * @param $uid 用户id
     * @return mixed
     */
    function getAdminUser($uid)
    {
        $url = env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info';
        $url .= '?admin_id=' . $uid;
        return get_url($url);
    }
}

if (!function_exists('getConfigs')) {
    /**
     * Description:获取nacos配置
     * @param string $dataId
     * @param string $group
     * @return mixed
     */
    function getConfigs($dataId = 'misc', $group = 'vinehoo.conf')
    {
//        $url =
//            env('NACOS.URL') . '?dataId=' . $dataId . '&group=' . $group . '&tenant=' .
//            env('NACOS.TENANT') . '&password=' . env('NACOS.PASSWORD')
//            . '&username=' . env('NACOS.USERNAME');
        $url          = env('ITEM.CONFIG_CENTER_URL') . '/config/v3/nacos/readstring' . '?dataid=' . $dataId . '&group=' . $group;
        $url_header[] = 'vinehoo-client: tp6-commodities';
        $url_header[] = 'vinehoo-client-version: v3';
        $result       = get_url($url, $url_header);
        if (!is_null(json_decode($result))) {
            $result         = json_decode($result, true);
            $result['data'] = json_decode($result['data'], true);
            if (isset($result['status']) && $result['status'] >= 400) {
                $data['error_code'] = ErrorCode::PARAM_ERROR;
                $data['error_msg']  = '未获取到nacos配置:' . $result['message'];
                $data['data']       = [];
                $result             = json_encode($data, JSON_UNESCAPED_UNICODE);
                print_r($result);
                die;
            }
        }
        return $result['data'];
    }
}

if (!function_exists('deleteDomain')) {
    /**
     * 删除绝对路径域名
     * @param string $url 带域名路径
     * @return false|string
     */
    function deleteDomain(string $url)
    {
        if (empty($url)) {
            return $url;
        }
        return substr($url, strpos($url, '.com') + 4);
    }
}

/**
 * Description:ES获取一条数据
 * Author: zrc
 * Date: 2021/8/12
 * Time: 18:33
 * @param $id
 * @param $index
 * @return Response
 */
function esGetOne($id, $index)
{
    try {
        $es     = new ElasticSearchConnection();
        $result = $es->connection()->get(['id' => $id, 'index' => $index, 'type' => '_doc']);
        return $result['_source'];
    } catch (Exception $e) {
        return null;
    }
}

if (!function_exists('getUserInfoByIds')) {
    /**
     * 获取User信息
     * @param $uid 用户id
     * @return mixed
     */
    function getUserInfoByIds($uid, $field = '')
    {
        $url    = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
        $url    .= '?admin_id=' . $uid . '&field=' . $field;
        $result = get_url($url);
        var_dump($result);
        exit;
    }
}
function cryptionDeal($type, $orig_data)
{
    if ($type == 1) {
        $url = env('ITEM.CRYPTION_ADDRESS') . '/v1/encrypt';
    } else {
        $url = env('ITEM.CRYPTION_ADDRESS') . '/v1/decrypt';
    }
    $crypt_data = array(
        'orig_data' => $orig_data,
        'from'      => 'php-mall',
        'uid'       => "曹红军",
        'operator'  => "曹红军"
    );
    $crypt      = httpPostString($url, json_encode($crypt_data));
    if (isset($crypt['data'])) {
        return $crypt['data'];
    }
    return [];
}

/**
 * http post请求，参数为json字符串
 * @param $url
 * @param $data_string
 * @return bool|string
 */
function httpPostString(string $url, string $data_string)
{
    if (empty($header)) {
        $header = array(
            "content-type: application/json",
            "vinehoo-client: tp6-commodities",
        );
    }
    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL            => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING       => "",
        CURLOPT_MAXREDIRS      => 10,
        CURLOPT_TIMEOUT        => 3,
        CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST  => "POST",
        CURLOPT_POSTFIELDS     => $data_string,
        CURLOPT_HTTPHEADER     => $header
    ));
    $response = curl_exec($curl);
    $err      = curl_error($curl);
    curl_close($curl);
    if ($err) {
        return ['error_code' => 10002, 'error_msg' => $err, 'data' => []];
    } else {
        return json_decode($response, true);
    }
}

if (!function_exists('get_es_result')) {
    /**
     * 解析 es 返回数据
     * @param array $es_data
     * @return array
     * <AUTHOR>
     */
    function get_es_result(array $es_data): array
    {
        $data['total'] = 0;
        $data['list']  = [];
        if ($es_data['hits']['total']['value'] < 1) {
            return $data;
        }
        $data['total'] = $es_data['hits']['total']['value'];
        foreach ($es_data['hits']['hits'] as $val) {
            $data['list'][] = $val['_source'];
        }
        return $data;
    }

    /**
     * Description:日期友好显示新规则
     * Author: zrc
     * Date: 2021/9/13
     * Time: 17:24
     * @param null $time
     * @return string
     */
    function mdate($time = NULL)
    {
        if (is_string($time)) {
            $time = strtotime($time);
        }
        $text = '';
        $time = $time === NULL || $time > time() ? time() : intval($time);
        $t    = time() - $time; //时间差 （秒）
        $y    = date('Y', $time) - date('Y', time());//是否跨年
        if ($t < 60 * 10) {
            $text = '刚刚';
        } else if (60 * 10 <= $t && $t < 60 * 60) {
            $text = floor($t / 60) . '分钟前'; //一小时内
        } else if (60 * 60 <= $t && $t < 60 * 60 * 24) {
            $text = floor($t / (60 * 60)) . '小时前'; // 一天内
        } else if (60 * 60 * 24 <= $t && $t < 60 * 60 * 24 * 7) {
            $text = floor($t / (60 * 60 * 24)) . '天前'; //几天前
        } else if (60 * 60 * 24 * 7 <= $t && $t < 60 * 60 * 24 * 30) {
            $text = '1周前'; // 几周前(统一都显示1周前)
        } else if (60 * 60 * 24 * 30 <= $t && $t < 60 * 60 * 24 * 365) {
            $text = '1月前'; //几月前(统一都显示1月前)
        } else {
            $text = '1年前';//几年前(统一都显示1年前)
        }
        return $text;
    }

}
/**
 * Description:curl请求
 * Author: zrc
 * Date: 2023/4/24
 * Time: 16:16
 * @param $url
 * @param array $data
 * @param array $haeder
 * @param string $method
 * @param int $timeout
 * @param bool $sync
 * @return mixed
 */
function curlRequest($url, $data = [], $haeder = [], $method = 'POST', $timeout = 30, $sync = True)
{
    if ($method == 'POST' && !is_array($data) && !in_array('Content-Type:application/json', $haeder)) {
        $haeder[] = 'Content-Type:application/json';
    }
    $ch = curl_init();
    if (is_array($data)) {
        $data = http_build_query($data);
    }
    if ($method == 'POST') {
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, True);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    } else {
        if (!empty($data)) curl_setopt($ch, CURLOPT_URL, "{$url}?{$data}");
        else curl_setopt($ch, CURLOPT_URL, $url);
    }
    if (strlen($url) > 5 && strtolower(substr($url, 0, 5)) == "https") {
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    }
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    if ($haeder) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $haeder);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, $sync);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_NOBODY, !$sync);
    $return = curl_exec($ch);
    curl_close($ch);
    #记录请求日志
    $param = is_array($data) ? json_encode($data) : $data;
    Log::info("请求URL：{$url}，请求参数：{$param}，响应参数：" . $return);
    return json_decode($return, true);
}

/**
 * Description:curl请求
 * Author: zrc
 * Date: 2023/4/24
 * Time: 16:16
 * @param $url
 * @param array $data
 * @param array $haeder
 * @param string $method
 * @param int $timeout
 * @param bool $sync
 * @return mixed
 */
function curl_request($url, $data = [], $haeder = [], $method = 'POST', $timeout = 30, $sync = True)
{
    if ($method == 'POST' && !is_array($data) && !in_array('Content-Type:application/json', $haeder)) {
        $haeder[] = 'Content-Type:application/json';
    }
    $ch = curl_init();
    if (is_array($data)) {
        $data = http_build_query($data);
    }
    if ($method == 'POST') {
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, True);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    } else {
        if (!empty($data)) curl_setopt($ch, CURLOPT_URL, "{$url}?{$data}");
        else curl_setopt($ch, CURLOPT_URL, $url);
    }
    if (strlen($url) > 5 && strtolower(substr($url, 0, 5)) == "https") {
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    }
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    if ($haeder) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $haeder);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, $sync);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_NOBODY, !$sync);
    $return = curl_exec($ch);
    curl_close($ch);
    #记录请求日志
    $param = is_array($data) ? json_encode($data) : $data;
    Log::info("请求URL：{$url}，请求参数：{$param}，响应参数：" . $return);
    return $return;
}

function httpCurl($url, $http = 'get', $data = [], $timeout = 10, $headers = [])
{
    $headers[] = 'Content-Type: application/json;charset=utf-8';

    $curl = curl_init(); //初始化
    curl_setopt($curl, CURLOPT_URL, $url); //设置抓取的url
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // 跳过证书检查
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false); // 从证书中检查SSL加密算法是否存在
    curl_setopt($curl, CURLOPT_HEADER, false); //设置头文件的信息作为数据流输出
    curl_setopt($curl, CURLOPT_HTTPHEADER, $headers); //设置头信息
    if ($http == 'post') {
        curl_setopt($curl, CURLOPT_POST, true); //设置post方式提交
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data); //设置post数据
    }
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); //设置获取的信息以文件流的形式返回，而不是直接输出。
    curl_setopt($curl, CURLOPT_TIMEOUT, $timeout); //设置超时时间
    $response = curl_exec($curl); //执行命令
    curl_close($curl); //关闭URL请求
    // print_r($response); //显示获得的数据
    return $response;
}

/**
 * 时间转换计算今明几点开始
 */
if (!function_exists('auctionSctime')) {
    function auctionSctime($start_time)
    {
        $t = time();
        // 今日凌晨时间
        $td = strtotime(date('Y-m-d 23:59:59', $t));
        // 今日开始时间
        $tsd = strtotime(date('Y-m-d 00:00:00', $t));
        // 明天开始时间
        $tmor = strtotime("+1 day", $tsd);
        // 明天凌晨时间
        $tmtd = strtotime(date('Y-m-d 23:59:59', $tmor));
        // 计算时间
        if ($start_time >= $tsd && $start_time <= $td) {
            return '今天 ' . date('H:i:s', $start_time);
        } elseif ($start_time >= $tmor && $start_time <= $tmtd) {
            return '明天 ' . date('H:i:s', $start_time);
        }
        return date('m月d日 H:i:s', $start_time);
    }
}

/**
 * 时间转换计算今明周几点开始
 */
if (!function_exists('auctionEctime')) {
    function auctionEctime($end_time)
    {
        $t = time();
        // 今日凌晨时间
        $td = strtotime(date('Y-m-d 23:59:59', $t));
        // 今日开始时间
        $tsd = strtotime(date('Y-m-d 00:00:00', $t));
        // 明天开始时间
        $tmor = strtotime("+1 day", $tsd);
        // 明天凌晨时间
        $tmtd = strtotime(date('Y-m-d 23:59:59', $tmor));
        // 后天开始时间
        $htd = strtotime("+1 day", $tmor);
        // 后天凌晨时间
        $hetd = strtotime(date('Y-m-d 23:59:59', $htd));
        // 计算时间
        $week_arr = ['日', '一', '二', '三', '四', '五', '六'];
        if ($end_time >= $tsd && $end_time <= $td) {
            return '今天 ' . date('H:i:s', $end_time);
        } elseif ($end_time >= $tmor && $end_time <= $tmtd) {
            return '明天 ' . date('H:i:s', $end_time);
        } elseif ($end_time >= $htd && $end_time <= $hetd) {
            return '周' . $week_arr[date('w', $end_time)] . ' ' . date('H:i:s', $end_time);
        }
        return date('m月d日 H:i:s', $end_time);
    }
}


/**
 * 查询新人价/最低价
 */
function getCalcPrice($calcPeriods, $uid)
{
    $body     = json_encode([
        'is_newcomer' => true,
        'periods'     => $calcPeriods,
    ]);
    $url      = env('ITEM.RECOMMEND_URL') . '/go-recommend/v3/calc/calcPrice';
    $haeder   = ['vinehoo-uid:' . $uid];
    $res      = curlRequest($url, $body, $haeder);
    $newcomer = [];
    if (!empty($res['data']['list'])) {
        foreach ($res['data']['list'] as $v) {
            $newcomer[$v['id']] = $v;
        }
    }
    return $newcomer;
}

/**
 * 获取用户信息
 * @param $uids
 * @param $files
 * @return array
 */
function getUserInfoByUids($uids, $files = '')
{
    //请求地址
    $base = env('ITEM.USER_URL');
    $url  = $base . '/user/v3/profile/getUserInfo';
    //数据组装
    $body      = [
        'uid'   => $uids,
        'field' => $files
    ];
    $userInfo  = curlRequest($url, $body, [], "GET");
    $userInfos = [];
    if ((isset($userInfo['data']) && !empty($userInfo['data']))) {
        $data = $userInfo['data']['list'] ?? [];
        foreach ($data as $v) {
            if (!empty($v['uid'])) {
                $userInfos[$v['uid']] = $v;
            } else {
                $userInfos[] = $v;
            }

        }
    }
    return $userInfos;
}

/**
 * 获取用户信息
 * @param $uids
 * @param $files
 * @return array
 */
function getIsNewUser($uid)
{
    $is_new_user = 1;
    if (!empty($uid) && intval($uid) > 0) {
        $is_new_user = 0;
        $res         = getUserInfoByUids($uid, 'uid,is_new_user');
        $user        = $res[$uid] ?? [];
        if (!empty($user)) {
            if (!empty($user['is_new_user']) && $user['is_new_user'] == 1) {
                $is_new_user = 1;
            }
        }
    }
    return $is_new_user;
}


/**
 * 生成自增id
 * @param int $id
 * @return false|mixed
 */
function getGeneratorID(int $id)
{
    GeneratorId::startTrans();
    $g_id = GeneratorId::where('id', $id)->lock(true)->value('g_id');
    $r_id = $g_id + 1;
    $inc  = GeneratorId::where('id', $id)->inc('g_id', 1)->update();
    if ($inc) {
        GeneratorId::commit();
        return $r_id;
    } else {
        GeneratorId::rollback();
    }
    return false;
}

/**
 * 字符串截取
 * @Date 2023/07/11 15:44
 * @param int $len 长度
 * @param string $str 字符串
 * @return Response
 */
if (!function_exists('getsre')) {
    function getsre($str, $len)
    {
        preg_match_all('/./us', $str, $strlrn);
        $match = $strlrn[0];
        if (sizeof($match) < $len) {
            return $str;
        } else {
            $finalstr = '';
            for ($i = 0; $i < $len; $i++) {
                $finalstr .= $match[$i];
            }
            return $finalstr . '...';
        }
    }
}

function treeToList($tree, $children = 'children', &$list = [])
{
    if (is_array($tree)) {
        foreach ($tree as $key => $value) {
            $reffer = $value;
        }
        if (isset($sreffer[$children])) {
            unset($sreffer[$children]);
            treeToList($value[$children], $children, $list);
        }
        $list[] = $reffer;
    }
    return $list;
}

if (!function_exists('multiple_image_full_path')) {
    function multiple_image_full_path(string $urls): array
    {
        if (empty($urls)) return [];
        $product_imgs = explode(',', $urls);
        foreach ($product_imgs as &$img) {
            $img = image_full_path($img);
        }
        return $product_imgs;
    }
}
if (!function_exists('image_full_path')) {
    /**
     * @方法描述: 单图返回全路径
     * <AUTHOR>
     * @Date 2022/12/8 12:41
     * @param $value
     * @return string
     */
    function image_full_path($value)
    {
        if (!$value) return '';
        $host = env('ALIURL');
        if (empty($host)) return $value;
        return (false === strpos($value, $host)) ? $host . $value : $value;
    }
}
/**
 * 获取期数所有简码已售数量
 * @param array $param 请求参数[["period"=>122294,"period_type"=>0]]
 * @param $files
 * @return array
 */
function GetSaleBottleNums($param)
{
    $url = env('ITEM.USER_CACHE_URL') . '/commodities/GetSaleBottleNums';
    $res = curlRequest($url, json_encode($param));

    return $res['data'] ?? [];
}

/**
 * 获取期数所有简码已售未发货数量
 * @param array $param 请求参数[["period"=>122294,"period_type"=>0]]
 * @param $files
 * @return array
 */
function GetUnshippedBottleNums($param)
{
    $url = env('ITEM.USER_CACHE_URL') . '/commodities/GetUnshippedBottleNums';
    $res = curlRequest($url, json_encode($param));

    return $res['data'] ?? [];
}

/**
 * 收款公司id与code兑换
 * @param string $value 值
 * @param int $type 类型：0=ID，1=code
 * @return int|string
 */
function payeeMerchantIdCodeExchange($value, $type = 0)
{
    $result = '';
    if ($type == 1) {
        switch ($value) {
            case '002'://重庆云酒佰酿电子商务有限公司
                $result = 1;
                break;
            case '029'://佰酿云酒（重庆）科技有限公司
            case '001'://佰酿云酒（重庆）科技有限公司
                $result = 2;
                break;
            case '008'://渝中区微醺酒业商行
                $result = 5;
                break;
            case '032'://海南一花一世界科技有限公司
                $result = 10;
                break;
        }

    } else if ($type == 0) {
        switch ($value) {
            case 1://重庆云酒佰酿电子商务有限公司
                $result = '002';
                break;
            case 2://佰酿云酒（重庆）科技有限公司
                $result = '001';
                break;
            case 5://渝中区微醺酒业商行
                $result = '008';
                break;
            case 10://海南一花一世界科技有限公司
                $result = '032';
                break;
        }

    } else {
        switch ($value) {
            case 1://重庆云酒佰酿电子商务有限公司
                $result = '002';
                break;
            case 2://佰酿云酒（重庆）科技有限公司
                $result = '029';
                break;
            case 5://渝中区微醺酒业商行
                $result = '008';
                break;
            case 10://海南一花一世界科技有限公司
                $result = '032';
                break;
        }
    }

    return $result;
}

/**
 * 收款公司列表
 * @return array
 */
function payeeMerchantList()
{
    $merchants = [
        '重庆云酒佰酿电子商务有限公司' => 1,
        '佰酿云酒（重庆）科技有限公司' => 2,
        '渝中区微醺酒业商行' => 5,
        '海南一花一世界科技有限公司' => 10,
    ];

    return $merchants;
}

/**
 * 获取收款公司名称/ID
 * @return array
 */
function getPayeeMerchantNameID($val, $type = 0)
{
    if ($type == 0) {
        $merchants = [
            1 => '重庆云酒佰酿电子商务有限公司',
            2 => '佰酿云酒（重庆）科技有限公司',
            5 => '渝中区微醺酒业商行',
            10 => '海南一花一世界科技有限公司',
        ];
        return $merchants[$val] ?? '';
        
    } else {
        $merchants = [
            '重庆云酒佰酿电子商务有限公司' => 1,
            '佰酿云酒（重庆）科技有限公司' => 2,
            '渝中区微醺酒业商行' => 5,
            '海南一花一世界科技有限公司' => 10,
        ];
        return $merchants[$val] ?? 0;
    }
}

/**
 * 代发期数预计发货时间计算
 * @param int $predict_shipment_time 预计发货时间
 * @return int
 */
function SupplierShipPredictShipmentTimeLogic($predict_shipment_time)
{
    /*如果预计发货时间小于当前时间：
    18点之前下单的用户，预计发货为当前时间+24小时，18点之后下单的用户，预计发货时间为当前时间+48小时。
    如果预计发货时间大于当前时间：
    但是不大于30个小时，预计发货时间为当前时间+48小时；否则使用商品设置的预计发货时间*/
    $time = time();
    if ($predict_shipment_time <= $time) {
        if (intval(date('H')) < 18) {
            $predict_shipment_time = intval($time+(24*3600));
        } else {
            $predict_shipment_time = intval($time+(48*3600));
        }
    } else {
        if (intval($predict_shipment_time - $time) < intval(30*3600)){
            $predict_shipment_time = intval($time+(48*3600));
        }
    }
    return $predict_shipment_time;
}

/**
 * 发送企业微信消息推送队列
 * @param array $content 消息内容
 * @param string $access_token 机器人token
 * @return array
 */
function SendWeChatRobot($content, $access_token = '', $at = '')
{
    if (empty($access_token)) {
        $test_access_token = 'a5df4c1d-0f20-4f0c-8410-c91dca6d7695';//'f7e52882-58f9-4dc9-bf3d-0fde51aaa2ee';
        $access_token = env('COMMODITIES.ONSALE_GDWJ_TOKEN') ?? $test_access_token;
    }

    if (is_array($content)) {
        $content = json_encode($content, JSON_UNESCAPED_UNICODE);
    }
    $d_str = base64_encode(json_encode([
        'access_token' => $access_token,
        'type'         => 'text',
        'at'           => $at,
        'content'      => base64_encode($content),
    ]));

    // 推送请求参数
    $push_data = [
        'exchange_name' => 'dingtalk',
        'routing_key'   => 'dingtalk_sender',
        'data'          => $d_str,
    ];

    $url_header = [
        'vinehoo-client: tp6-commodities',
        'Content-Type: application/json;charset=utf-8'
    ];
    return curlRequest(env('item.QUEUE_URL'), json_encode($push_data), $url_header);
}

/**
 * 操作商品边框
 * @param int $id 期数
 * @param array $special_activity_data 当前商品活动数据
 * @param string $newBorder 新边框图，为空修改为原始边框图
 * @return array
 */
function UpdatePeriodsBorder($id, $special_activity_data, $newBorder = '')
{
    // 默认为空
    empty($special_activity_data['activity_name']) && $special_activity_data['activity_name'] = '';
    empty($special_activity_data['activity_url']) && $special_activity_data['activity_url'] = '';
    empty($special_activity_data['list_back']) && $special_activity_data['list_back'] = '';
    empty($special_activity_data['title_map']) && $special_activity_data['title_map'] = '';

    $body = [];
    if ($newBorder != '') {// 修改为新边框
        $special_activity_data['new_title_map'] = $newBorder;

        foreach ($special_activity_data as $k => $v) {
            $body[$k] = str_replace(env('ALIURL'), '', $v);
        }

    } else {//修改为原始边框图
        if (!empty($special_activity_data['new_title_map'])) {
            $special_activity_data['new_title_map'] = '';

            foreach ($special_activity_data as $k => $v) {
                $body[$k] = str_replace(env('ALIURL'), '', $v);
            }
        }
    }

    if (!empty($body)) {
        Es::name("periods")->update([
            'id'                    => $id,
            'special_activity_data' => $body,
        ]);
    }

    if (!empty($special_activity_data['new_title_map'])) {
        unset($special_activity_data['new_title_map']);
    }
    return $special_activity_data;
}

/**
 * @方法描述:数据加解密
 * <AUTHOR>
 * @Date   2021/09/07
 * @param array $param 参数
 * @param string $type 类型：D解密，E加密
 * @param int $i 请求次数
 * @return Response
 */
function CryptionDeals($param, $type = 'D', $i = 1)
{
    $url  = $type == 'D' ? '/v1/decrypt' : '/v1/encrypt';

    $data = [
        'orig_data' => array_values($param['orig_data']),
        'from'      => 'outbound',
        'uid'       => !empty($param['uid']) ? strval($param['uid']) : '18623372439',
        'operator'  => !empty($param['uid']) ? strval($param['operator']) : '甘高寒'
    ];
    $res = curlRequest(env('ITEM.CRYPTION_ADDRESS') . $url, json_encode($data)) ?? '';
    #失败重试
    if (empty($res['data']) && $i < 3) {
        $i++;
        $res = cryptionDeal($param, $type, $i);
    }
    return $res['data'] ?? [];
}

/**
 * @方法描述:获取渠道期数加密ID
 * <AUTHOR>
 * @Date   2023/11/22
 * @param int $id 期数
 * @return Response
 */
function getChannelEncryptId($id)
{
    $time = time();
    $enauth = $id . '_' . $time;
    $en_auth = CryptionDeals(['orig_data' => [$enauth]], 'E');
    $endata = json_encode([
        'id' => $id,
        'auth' => $en_auth[$enauth] ?? '',
    ]);
    $en_data = CryptionDeals(['orig_data' => [$endata]], 'E');

    return $en_data[$endata] ?? '';
}


/**
 * 迭代器方法，实现无限分类树
 * @param array $array 原始数据包
 * @return array
 */
function buildTree($array, $children = 'children', $pid = 'pid')
{
    if (empty($array)) {
        return [];
    }
    $map        = array();
    $fotmatTree = array();
    foreach ($array as &$vo) {
        $map[$vo['id']]            = &$vo;
        $map[$vo['id']][$children] = array();
    }
    unset($vo);

    foreach ($array as &$item) {
        $parent = &$map[$item[$pid]];
        if (empty($parent)) {
            $fotmatTree[] = &$item;
        } else {
            $parent[$children][] = &$item;

        }
    }
    unset($map);
    return $fotmatTree;
}

function tree_to_array ($tree , $children = 'children',  &$list = []){
    if (is_array($tree)) {
        foreach ($tree as $key => $value) {
            $reffer = $value;
            unset($reffer[$children]);
            if (!empty($value[$children])) {
                tree_to_array($value[$children], $children, $list);
            }
            $list[] = $reffer;
        }
    }
    return $list;
}

/**
 * @方法描述:获取中台管理员信息
 * <AUTHOR>
 * @Date 2023/12/14
 * @param int $admin_id 手机号
 * @param string $fields 字段
 * @return array [分页起始值,返回条数]
 */
function getMiddleAdminInfo($admin_id, $fields = '')
{
    $url = env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info';
    $body = [
        'admin_id' => $admin_id,
        'field' => !empty($fields) ? $fields : 'id,realname,userid,dept_id,roles',
    ];

    $res = curlRequest($url, $body, [], "GET");
    return $res['data'][$admin_id] ?? [];
}

/**
 * @方法描述:重写查找子字符串在字符串中第一次出现的位置
 * <AUTHOR>
 * @param array $keyword 字符串
 * @param array $key 关键字
 * @return int|false 
 */
function strpos_str($keyword, $key)
{
    return strpos(strval($keyword), strval($key));
}

/**
 * @方法描述:解析专题活动期数
 * <AUTHOR>
 * @param string $activity_periods 活动期数
 * @return array ['aid'=>1,'periods'=>1]
 */
function analysisActivityPeriods($activity_periods)
{
    $result = ['aid' => 0, 'periods' => 0];
    $act_periods = explode('000000', $activity_periods);
    if (empty($act_periods) || count($act_periods) < 2) {
        return $result;
    }

    $periodsstr = str_split($act_periods[1]);
    $periods = '';
    foreach ($periodsstr as $v) {
        if (empty($periods)) {
            if ($v == 0) {
                $act_periods[0] .= $v;
            } else {
                $periods = $v;
            }
        } else {
            $periods .= $v;
        }
    }

    foreach ($act_periods as $k => $v) {
        if ($k > 1) {
            $periods .= $v;
        }
    }

    return ['aid' => $act_periods[0], 'periods' => $periods];
}

function getMillisecondsTimestamp() {
    list($microseconds, $seconds) = explode(' ', microtime());
    return round(($seconds * 1000) + ($microseconds * 1000));
}

//非代发，非烈酒，首次采购瓶数=实际瓶数*1.2倍且需要是6的倍数
function FirstQuantityCalculation($number) {
    $number = intval(ceil($number * 1.2));
    // for ($i = 1; $i <= 6; $i++) {
	// 	if ($number%6 == 0) {
	// 		break;
	// 	}
	// 	$number++;
	// }
    return $number;
}

// 获取两个时间戳相隔天数
function getTimeDiffDays($t1, $t2) {
    $tt1 = strtotime(date('Y-m-d', $t1));
    $tt2 = strtotime(date('Y-m-d', $t2));
    if ($tt1 > $tt2) {
        return intval(($tt1 - $tt2) / 86400);
    } else {
        return intval(($tt2 - $tt1) / 86400);
    }
}

/**
 * 远程文件保存本地
 * @param string $fileUrl  文件远程路径
 * @param string $fileName  文件名称
 * @return string $path 文件保存路径
 */
function saveLocal($fileUrl, $fileName = '')
{
    if (strpos_str($fileUrl, 'http') === false) {
        $fileUrl = env('ALIURL') . $fileUrl;
    }
    if (empty($fileName)) {
        $fileName = substr($fileUrl, strrpos($fileUrl, '/') + 1);
    }
    $path = app()->getRuntimePath() . $fileName;
    
    file_put_contents($path, file_get_contents($fileUrl));

    return $path;
}

#浏览器下载
function excelDownload($filePath, $fileName)
{
    // Set Header
    header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    header('Content-Disposition: attachment;filename="' . $fileName . '"');
    header('Content-Length: ' . filesize($filePath));
    header('Content-Transfer-Encoding: binary');
    header('Cache-Control: must-revalidate');
    header('Cache-Control: max-age=0');
    header('Pragma: public');

    ob_clean();
    flush();

    if (copy($filePath, 'php://output') === false) {
        // Throw exception
    }

    // Delete temporary file
    @unlink($filePath);
}

//自动添加期数到营销版块
function AutomaticallyAddPeriod($period,$periods_type,$card,$column,$title,$card_filter,$column_filter)
{
    $url = env('ITEM.COMMODITIES_SERVICES') . '/commodities_server/v3/marketing/AutomaticallyAddPeriod';
    $data = json_encode([
        'period' => intval($period),
        'period_type' => intval($periods_type),
        'card' => $card,
        'card_filter' => $card_filter,
        'column' => $column,
        'column_filter' => $column_filter,
        'title' => $title,
    ]);
    return curlRequest($url, $data);
}

function getmicrotime()
{
    list($usec, $sec) = explode(' ', microtime());
    return sprintf('%d%03d', (int)$sec, (int)(($usec - floor($usec)) * 1000));
}

//判断是否烈酒\白酒
function is_baijueLiejiu($period)
{
    $exist = Db::table('vh_commodities.vh_periods_product_inventory')
        ->alias('pi')
        ->leftJoin('vh_wiki.vh_products ps','ps.id=pi.product_id')
        ->leftJoin('vh_wiki.vh_product_type pt','pt.id=ps.product_type')
        ->where('pt.id in(21,22) or pt.fid in(21,22)')
        ->where('pi.period', $period)
        ->where('pi.is_use_comment', 0)
        ->value('pi.id');
    return $exist;
}

/**
 * @方法描述: 上传文件到OSS
 * <AUTHOR>
 * @Date 2024/11/25
 * @param $file_path 文件路径
 * @param $upload_path 上传路径
 * @return bool|string
 */
function uploadFile($file_path, $upload_path)
{
    // 阿里云 oss 账号
    $accessKeyId = env('OSS.ACCESSKEYID');
    $accessKeySecret = env('OSS.ACCESSKEYSECRET');
    // oss 数据中心域名
    $endpoint = env('OSS.ENDPOINT');
    // oss 存储空间名称
    $bucket = env('OSS.BUCKET');

    try {
        $ossClient = new \OSS\OssClient($accessKeyId, $accessKeySecret, $endpoint);
        $result = $ossClient->putObject($bucket, $upload_path, file_get_contents($file_path));

    } catch (Exception $e) {
        return "上传OSS失败: " . $e->getMessage();
    }
    return true;
}

/**
 * @方法描述:url请求重试
 * @param string $url 请求url
 * @param array $data 请求参数
 * @param array $haeder 请求头
 * @param string $method 请求的方式
 * @param int $timeout 超时时间，单位s
 * @return Response
 */
function curlRetryRequest($url, $data = [], $haeder = [], $method = 'POST', $timeout = 3, $i = 0)
{
    if (empty($haeder)){
        $haeder = array(
            "vinehoo-client: tp6-commodities",
        );
    }
    $res = curl_request($url, $data, $haeder, $method, $timeout) ?? '';
    $res = json_decode($res,true);
    if (!isset($res['error_code']) && $i<3) {
        $i++;
        $res = curlRetryRequest($url, $data, $haeder, $method, $timeout, $i);
    }
    return $res;
}

/**
 * 根据标签名称查询用户ID
 * @param array $label_name
 * @return array
 */
function getUserByLabelName($label_name) {
    $data = json_encode(['label_name' => $label_name]);
    $result = curlRetryRequest(env('ITEM.USER_URL').'/user/v3/label/userIdByLabel', $data);
    return $result['data']['user_id'] ?? [];
}

/**
 * @方法描述: 数组其他根据指定数组排序
 * <AUTHOR>
 * @Date 2024/12/27
 * @param array $data 原始数组
 * @param array $sort 排序数组
 * @param string $field 排序字段
 * @return array
 */
function arrayByarraySort(array $data, array $sort, string $field = 'id')
{
    $data = array_column($data, null, $field);
    $result = [];
    foreach ($sort as $value) {
        if (!empty($data[$value])) {
            $result[] = $data[$value];
        }
    }
    return $result;
}

/**
 * @方法描述: 把所有的在售商品按照规则添加标签
 * <AUTHOR>
 * @Date 2025/5/22
 * @param int $label_id 标签ID
 * @return string
 */
function addLabelByRule($label_id)
{
    $url = env('ITEM.COMMODITIES_SERVICES') . '/commodities_server/v3/marketing/SaleGoodsByRuleAddLabel';
    $data = json_encode(['label_id' => intval($label_id)]);
    return curlRequest($url, $data);
}

/**
 * @方法描述: 获取毫秒时间戳
 * <AUTHOR>
 * @Date 2025/5/23
 * @return int
 */
function getMillisecond()
{
    list($s1, $s2) = explode(' ', microtime());
    return (float)sprintf('%.0f', (floatval($s1) + floatval($s2)) * 1000);
}

/**
 * @方法描述: 移除隐藏套餐，自选套餐新增子套餐
 * <AUTHOR>
 * @Date 2025/6/10
 * @param array $package
 * @return array
 */
function addChildPackage($package)
{
    $packageList = [];
    foreach ($package as $v) {
        if ($v['is_hidden_package'] == 1) {
            if ($v['source_package_id'] > 0) {
                if (!empty($packageList[$v['source_package_id']])) {
                    $packageList[$v['source_package_id']]['package_list'][] = $v;
                } else {
                    $packageList[$v['source_package_id']] = [
                        'package_list' => [$v],
                    ];
                }
            }
            continue;
        }

        $v['package_list'] = [];
        if (!empty($packageList[$v['id']]['package_list']) && $v['is_custom_package'] == 1) {
            $v['package_list'] = $packageList[$v['id']]['package_list'];
        }
        $packageList[$v['id']] = $v;
    }
    return array_values($packageList);
}