<?php
declare (strict_types = 1);

namespace app;

use app\model\GeneratorId;
use app\model\PeriodsGeneratorId;
use app\model\PeriodsSetGeneratorId;
use think\App;
use think\exception\ValidateException;
use think\Validate;

/**
 * 控制器基础类
 */
abstract class BaseController
{
    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * 是否批量验证
     * @var bool
     */
    protected $batchValidate = false;

    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [];

    /**
     * 构造方法
     * @access public
     * @param  App  $app  应用对象
     */
    public function __construct(App $app)
    {
        $this->app     = $app;
        $this->request = $this->app->request;

        // 控制器初始化
        $this->initialize();
    }

    // 初始化
    protected function initialize()
    {}

    /**
     * 验证数据
     * @access protected
     * @param  array        $data     数据
     * @param  string|array $validate 验证器名或者验证规则数组
     * @param  array        $message  提示信息
     * @param  bool         $batch    是否批量验证
     * @return array|string|true
     * @throws ValidateException
     */
    protected function validate(array $data, $validate, array $message = [], bool $batch = false)
    {
        if (is_array($validate)) {
            $v = new Validate();
            $v->rule($validate);
        } else {
            if (strpos($validate, '.')) {
                // 支持场景
                [$validate, $scene] = explode('.', $validate);
            }
            $class = false !== strpos($validate, '\\') ? $validate : $this->app->parseClass('validate', $validate);
            $v     = new $class();
            if (!empty($scene)) {
                $v->scene($scene);
            }
        }

        $v->message($message);

        // 是否批量验证
        if ($batch || $this->batchValidate) {
            $v->batch(true);
        }

        return $v->failException(true)->check($data);
    }

    /**
     * 生成商品 id
     * @return mixed|null |null
     */
    protected function periodsGeneratorID() {
        $generator_model = new PeriodsGeneratorId();
        $geren = $generator_model::create(['id' => null]);
        return $geren->id ?? null;
    }

    /**
     * 生成商品套餐 id
     * @return mixed|null |null
     */
    protected function periodsGetGeneratorID() {
        $generator_model = new PeriodsSetGeneratorId();
        $geren = $generator_model::create(['id' => null]);
        return $geren->id ?? null;
    }

    /**
     * 生成自增id
     * @param int $id
     * @return false|mixed
     */
    public function getGeneratorID(int $id) {
        GeneratorId::startTrans();
        $g_id = GeneratorId::where('id', $id)->value('g_id');
        $r_id = $g_id + 1;
        $inc = GeneratorId::where('id', $id)->inc('g_id', 1)->update();
        if ($inc) {
            GeneratorId::commit();
            return $r_id;
        } else {
            GeneratorId::rollback();
        }
        return false;
    }
}
