<?php
// +----------------------------------------------------------------------
// | 控制台配置
// +----------------------------------------------------------------------
return [
    // 指令定义
    'commands' => [
        'putshelf' => 'app\command\PutShelf',
        // 添加首页排序数据
        'createIndexRedis' => 'app\command\CreateIndexRedis',
        'createPeriodsJson' => 'app\command\CreatePeriodsJson',
        'updateESPPP' => 'app\command\UpdateESPeriodsPackagePrice',
        'CommentCount' => 'app\command\CommentCount',
        'createVmallSecondRedis' => 'app\command\CreateVmallSecondRedis',
        'PeriodsStatis' => 'app\command\PeriodsStatis',
        'PeriodsAutoSort' => 'app\command\PeriodsAutoSort',
        'ppho' => 'app\command\PeriodsPageviewHoursOrders',
        'dc' => 'app\command\DataCleaning',

        // 订金消息推送
        'PeriodsDepositSms' => 'app\command\PeriodsDepositSms',

        // 销量TOP统计
        'salesVolumeTop' => 'app\command\SalesVolumeTop',

        // 定金补偿
        'DepositCompensate' => 'app\command\DepositCompensate',

        // 更新浏览量
        'UpdatePeriodPageviews' => 'app\command\UpdatePeriodPageviews',
        // 更新预计发货时间
        'UpdatePeriodPredictShipmentTime' => 'app\command\UpdatePeriodPredictShipmentTime',

        //尾货在售商品随机排序
        'SortShuffleAndUpdate' => 'app\command\SortShuffleAndUpdate',

        //批量上架尾货商品（需要打上渠道标识）
        'batch_on_sale_leftover_channel' => 'app\command\BatchOnSaleLeftoverChannelCommand',

    ],
];
