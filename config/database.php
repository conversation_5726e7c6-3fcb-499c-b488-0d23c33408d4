<?php

return [
    // 默认使用的数据库连接配置
    'default'         => env('DATABASE_COMMODITIES.driver', 'mysql'),

    // 自定义时间查询规则
    'time_query_rule' => [],

    // 自动写入时间戳字段
    // true为自动识别类型 false关闭
    // 字符串则明确指定时间字段类型 支持 int timestamp datetime date
    'auto_timestamp'  => true,

    // 时间字段取出后的默认时间格式
    'datetime_format' => 'Y-m-d H:i:s',

    // 时间字段配置 配置格式：create_time,update_time
    'datetime_field'  => '',

    // 数据库连接配置信息
    'connections'     => [
        'mysql' => [
            // 数据库类型
            'type'            => env('DATABASE_COMMODITIES.TYPE', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_COMMODITIES.HOSTNAME', ''),
            // 数据库名
            'database'        => env('DATABASE_COMMODITIES.DATABASE', ''),
            // 用户名
            'username'        => env('DATABASE_COMMODITIES.USERNAME', ''),
            // 密码
            'password'        => env('DATABASE_COMMODITIES.PASSWORD', ''),
            // 端口
            'hostport'        => env('DATABASE_COMMODITIES.HOSTPORT', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_COMMODITIES.CHARSET', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_COMMODITIES.PREFIX', 'vh_'),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('DATABASE_COMMODITIES.DEBUG', 'false'),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        'mysql_follow' => [// 从数据库配置
            // 数据库类型
            'type'            => env('DATABASE_COMMODITIES_FOLLOW.TYPE', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_COMMODITIES_FOLLOW.HOSTNAME', ''),
            // 数据库名
            'database'        => env('DATABASE_COMMODITIES_FOLLOW.DATABASE', ''),
            // 用户名
            'username'        => env('DATABASE_COMMODITIES_FOLLOW.USERNAME', ''),
            // 密码
            'password'        => env('DATABASE_COMMODITIES_FOLLOW.PASSWORD', ''),
            // 端口
            'hostport'        => env('DATABASE_COMMODITIES_FOLLOW.HOSTPORT', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_COMMODITIES_FOLLOW.CHARSET', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_COMMODITIES_FOLLOW.PREFIX', 'vh_'),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('DATABASE_COMMODITIES_FOLLOW.DEBUG', 'false'),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        'v2mysql' => [
            // 数据库类型
            'type'            => 'mysql',
            // 服务器地址
            'hostname'        => '**************',
            // 数据库名
            'database'        => 'wy_mall',
            // 用户名
            'username'        => 'vinehoodev',
            // 密码
            'password'        => 'vinehoo666',
            // 端口
            'hostport'        => 3306,
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => 'utf8',
            // 数据表前缀
            'prefix'          =>  'wy_',

            // 开启字段缓存
            'fields_cache'    => false,
        ],
        'orders' => [
            // 数据库类型
            'type'            => env('DATABASE_ORDERS.TYPE', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_ORDERS.HOSTNAME', ''),
            // 数据库名
            'database'        => env('DATABASE_ORDERS.DATABASE', ''),
            // 用户名
            'username'        => env('DATABASE_ORDERS.USERNAME', ''),
            // 密码
            'password'        => env('DATABASE_ORDERS.PASSWORD', ''),
            // 端口
            'hostport'        => env('DATABASE_ORDERS.HOSTPORT', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_ORDERS.CHARSET', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_ORDERS.PREFIX', 'vh_'),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('DATABASE_ORDERS.DEBUG', 'false'),
            // 开启字段缓存
            'fields_cache'    => false,
        ],

        // wiki
        'wiki' => [
            // 数据库类型
            'type'            => env('DATABASE_WIKI.TYPE', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_WIKI.HOSTNAME', ''),
            // 数据库名
            'database'        => env('DATABASE_WIKI.DATABASE', ''),
            // 用户名
            'username'        => env('DATABASE_WIKI.USERNAME', ''),
            // 密码
            'password'        => env('DATABASE_WIKI.PASSWORD', ''),
            // 端口
            'hostport'        => env('DATABASE_WIKI.HOSTPORT', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_WIKI.CHARSET', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_WIKI.PREFIX', 'vh_'),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('DATABASE_WIKI.DEBUG', 'false'),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        'supplychain' => [
            // 数据库类型
            'type'            => env('DATABASE_SUPPLYCHAIN.TYPE', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_SUPPLYCHAIN.HOSTNAME', ''),
            // 数据库名
            'database'        => env('DATABASE_SUPPLYCHAIN.DATABASE', ''),
            // 用户名
            'username'        => env('DATABASE_SUPPLYCHAIN.USERNAME', ''),
            // 密码
            'password'        => env('DATABASE_SUPPLYCHAIN.PASSWORD', ''),
            // 端口
            'hostport'        => env('DATABASE_SUPPLYCHAIN.HOSTPORT', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_SUPPLYCHAIN.CHARSET', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_SUPPLYCHAIN.PREFIX', 'vh_'),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('DATABASE_SUPPLYCHAIN.DEBUG', 'false'),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        //拍卖
        'auction' => [
            // 数据库类型
            'type'            => env('DATABASE_AUCTION.TYPE', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_AUCTION.HOSTNAME', ''),
            // 数据库名
            'database'        => env('DATABASE_AUCTION.DATABASE', ''),
            // 用户名
            'username'        => env('DATABASE_AUCTION.USERNAME', ''),
            // 密码
            'password'        => env('DATABASE_AUCTION.PASSWORD', ''),
            // 端口
            'hostport'        => env('DATABASE_AUCTION.HOSTPORT', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_AUCTION.CHARSET', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_AUCTION.PREFIX', 'vh_'),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('DATABASE_AUCTION.DEBUG', 'false'),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        //酒会
        'wineparty' => [
            // 数据库类型
            'type'            => env('DATABASE_WINEPARTY.TYPE', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_WINEPARTY.HOSTNAME', ''),
            // 数据库名
            'database'        => env('DATABASE_WINEPARTY.DATABASE', ''),
            // 用户名
            'username'        => env('DATABASE_WINEPARTY.USERNAME', ''),
            // 密码
            'password'        => env('DATABASE_WINEPARTY.PASSWORD', ''),
            // 端口
            'hostport'        => env('DATABASE_WINEPARTY.HOSTPORT', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_WINEPARTY.CHARSET', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_WINEPARTY.PREFIX', 'vh_'),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('DATABASE_WINEPARTY.DEBUG', 'false'),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        'marketing' => [
            // 数据库类型
            'type'            => env('DATABASE_MARKETING.TYPE', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_MARKETING.HOSTNAME', ''),
            // 数据库名
            'database'        => env('DATABASE_MARKETING.DATABASE', ''),
            // 用户名
            'username'        => env('DATABASE_MARKETING.USERNAME', ''),
            // 密码
            'password'        => env('DATABASE_MARKETING.PASSWORD', ''),
            // 端口
            'hostport'        => env('DATABASE_MARKETING.HOSTPORT', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_MARKETING.CHARSET', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_MARKETING.PREFIX', 'vh_'),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('DATABASE_MARKETING.DEBUG', 'false'),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        'work' => [
            // 数据库类型
            'type'            => env('DATABASE_CUSTOMER_SERVICE.TYPE', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_CUSTOMER_SERVICE.HOSTNAME', ''),
            // 数据库名
            'database'        => env('DATABASE_CUSTOMER_SERVICE.DATABASE', ''),
            // 用户名
            'username'        => env('DATABASE_CUSTOMER_SERVICE.USERNAME', ''),
            // 密码
            'password'        => env('DATABASE_CUSTOMER_SERVICE.PASSWORD', ''),
            // 端口
            'hostport'        => env('DATABASE_CUSTOMER_SERVICE.HOSTPORT', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_CUSTOMER_SERVICE.CHARSET', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_CUSTOMER_SERVICE.PREFIX', 'vh_'),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('DATABASE_CUSTOMER_SERVICE.DEBUG', 'false'),
            // 开启字段缓存
            'fields_cache'    => false,
        ],

        // 更多的数据库配置信息
        'mongodb' => [
            // 数据库类型
            'type'            => env('MONGODB.TYPE', 'mongo'),
            // 服务器地址
            'hostname'        => env('MONGODB.HOSTNAME', 'localhost'),
            // 数据库名
            'database'        =>  env('MONGODB.DATABASE', 'periods_pool'),
            // 用户名
            'username'        => env('MONGODB.USERNAME', ''),
            // 密码
            'password'        => env('MONGODB.PASSWORD', ''),
            // 端口
            'hostport'        => env('MONGODB.HOSTPORT', '30317'),
            // 数据库连接参数
            'params'          => env('MONGODB.DEPLOY') == 1?["replicaSet"=>env('MONGODB.REPLICASET')]:[],
            // 数据库编码默认采用utf8
            'charset'         => env('MONGODB.CHARSET', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('MONGODB.PREFIX', 'vh_'),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => env('MONGODB.DEPLOY', 0),
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => env('MONGODB.RW_SEPARATE', false),
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => 6,
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
            'read_master' => true,
        ]

    ],
];
