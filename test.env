APP_DEBUG = false

# oss域名
ALIURL = "https://images.vinehoo.com"
#公社前端是否展示
IS_DISPLAY=1

[ITEM]
#物流模块
LOGISTICS_URL = "http://tp6-logistics"
#松鸽拍卖消息
SONGGE_MESSAGE_URL="http://go-songge-message"
#松鸽拍卖出价
SONGGE_AUCTION_SERVER_URL = "http://go-songgge-auction-bid"
#松鸽拍卖订单
SONGGE_AUCTION_ORDERS_URL = "http://tp6-songge-auction-order"
#松鸽拍卖商品
SONGGE_AUCTION_GOODS_URL = "http://tp6-songge-auction"
#松鸽用户模块
SONGGE_USER_URL = "http://tp6-songge-user"
#埋点
MAIDIAN_URL="https://callback.vinehoo.com/maidian"
#权限
AUTHORITY_URL ="http://tp6-authority"
#tp6-oss
OSS_URL= "http://tp6-oss.vinehoo-prod"
#萌牙分发
DISTRIBUTE_URL = "http://distribute.wms-prod"
#萌牙仓库
AREA_URL = "http://area.wms-prod"
#萌芽出库
OUTBOUND_URL = "http://outbound.wms-prod"
#门店
STORE_URL= "https://osms.vinehoo.com/osms"
#优惠券模块
COUPON_URL = "http://tp6-coupon"
#营销模块
MARKET_URL = "http://tp6-marketing-reduction"
#产品模块
COMMODITIES_URL = "http://tp6-commodities"
#加密模块
CRYPTION_ADDRESS = "http://vinehoo-des-server-go.vinehoo-prod"
# 用户模块
USER_URL = "http://tp6-user"
#订单模块
ORDERS_URL = "http://tp6-orders"
#活动模块
ACTIVITIES_URL = "http://tp6-activities"
#队列模块
QUEUE_URL = "http://go-queueservice/services/v3/queue/push"
#秒级计划任务模块
SLS_URL= "http://node-second-level-scheduler"
#工单模块
WORK_URL = "http://tp6-work"
#满赠模块
FULLGIFT_URL = "http://tp6-fullgift"
# 推送萌芽
PUSH_ORDERS_URL = "http://tp6-pushorders"
# 推送T+
PUSH_T_PLUS_URL = "http://tp6-push-t-plus"
#超时任务模块
TIMEING_SERVICE_URL= "http://go-timing-service"
#支付模块
PAYMENT_URL = "http://tp6-payment"
#秒级计划任务模块
SLS_URL= "http://node-second-level-scheduler"
#钉钉消息推送及接口服务
DINGTALK_APPROVAL_URL = "http://node-dingtalk-system-notice"
#钉钉消息通知
DINTALK_SYSTEM_NOTICE_URL = "http://node-dingtalk-system-notice"
#社区模块
COMMUNITY_URL = "http://tp6-community"
#直播websocket
LIVE_ROBOT_URL = "https://callback.vinehoo.com/websocketapi"
LIVE_ROOM_URL = "https://callback.vinehoo.com/websocket"
#直播订单同步系统
LIVE_ORDER_SYNC_URL = "http://tp6-live-order-sync"
#直播
LIVE_URL="http://tp6-live"
#微信授权
WECHART_URL="http://go-wechat"
#磐石系统
WINE_WIKI_URL= "http://tp6-wine-wiki"
#vinehoo库存扣减退换服务
VINEHOO_INVENTORY_INOUT_URL="http://go-vinehoo-inventory-inout"
#物流管理
LOGISTICS_URL = "http://tp6-logistics.vinehoo-prod"
#酒会
WINEPARTY_URL = "http://tp6-wineparty"
ACTIVITIES_MANAGEMENT_URL = "http://go-activities-management"
#配置中心
CONFIG_CENTER_URL= "http://go-config-center"
#vinehoo订单金额计算
CALC-ORDERS_PRICE="go-calc-orders-price.vinehoo-prod"
#敏感词
BADWORDS_URL="http://go-badwords.vinehoo-prod"
#酒闻模板
NEWS_URL = "http://tp6-news"
#商品模块
COMMODITIES_URL = "http://tp6-commodities"
#短视频模块
SHORT_VIDEO_URL = "http://tp6-shortvideo"
#内容审核模块
AUDIT_URL = "http://tp6-content-audit"
#营销配置模块
MARKET_CONF_URL = "http://tp6-marketing-conf"
# 发票模块
INVOICE_URL = "http://tp6-invoice"
# 致信链
NFT_URL= "http://go-vinehoo-nft"
#短信模块
SMS_URL = "http://tp6-sms"
ACTIVITIES_MANAGEMENT_URL= "http://go-activities-management"
#app推送
APPPUSH_URL = http://tp6-app-push
#运营马甲服务
VEST_URL="http://go-vest-service"
#采购模块
PURCHASE_MANAGEMENT_URL = "http://tp6-purchase-management"
#T+库存查询
ADMIN_MODULE_ADDRESS ="https://callback.vinehoo.com/push-t-plus"
#用户缓存模块
USER_CACHE_URL= "http://go-user-cache"
#api批量请求
BATCH_REQUESTS_URL= "http://go-batch-requests"
# 兔头兑换优惠券
COUPON_RABBITS_ISSUE_URL = "http://go-coupons-rabbits-issue"
# 核销码
WINE_PARTY_URL = "https://callback.vinehoo.com/wineparty/"
#h5地址
H5_URL= "https://uh5.vinehoo.com"
#商品马甲管理
GOODS_VEST_URL="http://tp6-vest"
#秒发
VMALL_URL="http://tp6-vmall"
#商家秒发库存模块
VMALL_STOCK_URL="http://go-vmall-stockchange"
#计算距离服务
DISTANCE_URL="http://go-distance-calc"
#mysql多表联查
MYSQL_BATCH_SEARCH= "http://go-mysql-batch-search"
#营销注册拉新推广活动
INVITE_NEW_ACTIVITY_URL = "https://h5.vinehoo.com/pages/invite-activity/invite-activity"
#营销酒会推广活动
WINE_PARTY_ACTIVITY_URL = "https://h5.vinehoo.com/pages/activity/activity"
#订单推送萌芽
PUSH_ORDERS_URL="http://tp6-pushorders"
#支付商户号选择
payment_distribution_url= "http://go-payment-distribution"
#erp 制单人管理
ERP_PREPARED_URL = "http://tp6-erp-prepared"
#go语言社区
COMMUNITY_ES_URL = "http://go-community"
#go-短信发送
MARKINGKET_SMS_SEND_URL ="http://go-markingket-sms-send-service"
#ERP
ERP_URL ="http://tp6-erp"
#新门店
NEW_STORE_URL = "http://tp6-stores.osms-prod"
#拍卖订单
auction_orders_url= "http://tp6-auction-order"
#IP查询位置服务
NALI_IP_URL="http://go-nali-ip"
#供应链
SUPPLYCHAIN_URL = "http://tp6-supplychain"
#拍卖消息通知
MESSAGE_URL="http://go-message"
#拍卖订单模块
AUCTION_ORDERS_URL = "http://tp6-auction-order"
#拍卖订单商品
AUCTION_GOODS_URL = "http://tp6-auction"
#拍卖server
AUCTION_SERVER_URL = "http://go-auction-bid"
#AUCTION_SERVER_URL= "http://node-auction:8001"
#拍卖短链接
H5_URL_SHORT = "vinehoo.com"
#京东物流
JD_LOGISTICS_URL = "http://go-jd-logistics"
#萌芽对外服务
MENGYA_URL = "http://go-mengya.wms-prod"
#订单微服务模块
ORDERS_MICRO_SERVICE_URL = "http://go-orders-micro-service"
#GO发票
GO_INVOICE_URL = "http://go-invoice"
#供应商产品
SUPPLIER_PRODUCT_URL = "http://tp6-supplier-product"

MARKETING_INVITE_NEWUSERS_URL = "http://tp6-marketing-invite-newusers"
#推荐服务
RECOMMEND_URL="http://go-recommend"

COMMODITIES_SERVICES="http://go-commodities-micro-service"
#流程服务
FLOW_URL = "http://go-flow"
#相似度查询
SIMILAR_URL = "https://callback.vinehoo.com/py3-similar"
#学堂
ACADEMY_URL = "http://tp6-academy"

#直播订单
[LIVE-ORDER-SYNC]
appid = wx91084bb0a5b66d53
logistics = 2
delivery_id = SF
orderpagesize = 50
ordergetTime = 60
ordergetTime = 3600

#短视频
[SHORTVIDEO]
#分享地址
SHARE_URL = "http://uh5.vinehoo.com/agreement/videoDetail?id="
#阿里云视频点播配置
VIDEO_ACCESSKEYID = "LTAI4GCZW2qdgVhkpsnfWw54"
VIDEO_ACCESSKEYSECRET = "******************************"





[WINE_WIKI]
#磐石资料库
ALIBABACLOUD_ACCESSKEYID = LTAI5tQtYyes9Jp648hW6Yn3
ALIBABACLOUD_ACCESSKEYSECRET = ******************************
ALIBABACLOUD_ENDPOINT = cn-shanghai
ALIBABACLOUD_CAPTCHAAPPID = 2096409785
ALIBABACLOUD_EXAMPLE = imagesearch-cn-tl32lnfyn001
ALIBABACLOUD_EXAMPLE_NAME = vinehoosearchimages
WIKI_IMAGE_HOST = https://images.vinehoo.com
#批量新增 是否同步到T+  TRUE 同步 FALSE 不同步
ERP_SYSN_BUTCH = "TRUE"
#关单卫检审批ID
product_attachment_verify_id="C4UFHhdTTkrKEwfevf1hact5DQjHKwXTdHeEjQDDt"

#物流
[LOGISTICS]
TIMEOUT = 10
#物流调用（公网）
TP6_LOGISTICS_ADDRESS_COMMON = "https://callback.vinehoo.com/logistics"
V2_MALL = "https://gateway.wine-talk.cn/store"
IS_PUSH_APP = 1
#派送通知，模板ID
MINIAPP_DELIVERY_TEMPLATE_ID="lPrWbNj9YOxXKDmzH5h0w-s6pC2NjyFNiij2ems8Yo0"
#签收通知模板ID
MINIAPP_SIGN_TEMPLATE_ID="VOqqPT6cu3sRh2tsxFMIPStRBboo1EdUsL9fiSImP0E"
#跳转小程序类型：developer 为开发版；trial 为体验版；formal 为正式版；默认为正式版
MINIAPP_STATE="formal"

#后台日志
[ADMINLOG]
VINEHOO_CLIENT = admin-log

[WINECABINET]
#酒柜
APPID = wx124e816631c22df3
SECRET = 0ce244df98c2aba6531cdc4569baa778

[COMMUNITY]
posts_token = 4978b602251958b063ebb7fad20f6f47c9b988f6017fcee42c2104203de67ad7
battle_token = 75d86830454edf7649c81ebd7f95f9392dc48e51e98647884a9746f81448b459
#酒评首次评价获得兔头数量
WINE_EVALUATION_RABBIT = 20

#app推送
[APPPUSH]
base_url = https://restapi.getui.com/v2
appid = fnVgFFV3ebAK9dVthdlQ95
mastersecret = 4RmuNkWJpB9A7AZ6THSf68
appsecret = qXMzko16do7A9Tv18x1W46
appkey = STewQtheRz75M0UCePzGO4
environment = 正式

#订单
[ORDERS]
#跨境超额锁定时间-7天
CROSS_EXCESS_LOCK_TIMING = "168h"
#支付倒计时-300秒
pay_time_out = "300"
#拍卖订单超时时间
AUCTION_ORDER_TIMEOUT = "172800"
#银联验签key
key = "rRfKYjr7AFiykDnfBmeitWnTa6t2iQb74MBKpcH4eMGnnce3"
#基准快递费收取标准：小于99元收取9元快递费
MIN_MONEY=99
COURIER_FEE=9
#订单号前缀
ORDER_MAIN=VHM
ORDER_SON=VHS
ORDER_GD=VHG
WINEPARTY=VHP
RABBIT=VHR
COURCE=VHC
REFUND=REF
EARNEST=VHE
ORDER_DEPOSIT=VHD
#萌芽实体仓编号
STORE_CODE = "xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa"
#自动确认收货时间-15天
receipt_time = "15"
#暂存费用计算
free_ts_day = "30"
ts_rate = "0.01"
#拼团分享链接
#group_share_url = "http://test-wine.wineyun.com"
#跨境南沙仓正式环境地址
nan_sha_url = "https://service.etopideal.com/ocp2/rest"
#跨境银联申报支付单配置项
#支付单申报请求地址
declare_url = "https://cb.chinapay.com/overseasPay/B2cCustoms"
Version = ********
MerId = ***************
TranType = 0008
ProductType = ********
BusiType = 0001
CurryNo = CNY
BankInstNo = ***************
#支付单申报异步回调地址
MerBgUrl = "https://callback.vinehoo.com/orders/orders/v3/push/declareNotify"
BillMode = 1
CertType = 01
#支付单申报查询地址
QueryUrl = "https://cb.chinapay.com/overseasQuery/CustomsQuery"
#售后需支付订单超时时间
AFTER_SALES_ORDER_TIMEOUT = "30m"
#订单发货微信模板配置
WEIXINTEMPLATE_CURLOPT_URL = https://api.weixin.qq.com/cgi-bin/message/template/send
WEIXINTEMPLATE_TEMPLATE_ID = -DAXyKhrA1yXsrVcdveFOQcSRWusOp9R1fHm7kSgNBU
WEIXINTEMPLATE_REMARK = "您的快递已发货，点击进入订单详情查看"
#超时订单支付异步回调审批ID
time_out_order_create_verify_code = "3WKi3vzovEDk8jDPGJCNNoi8k8iFhHovHQ3fT2WJ"
#订单价格异常机器人token
order_price_unusual_token = "5c0f6e6a-8cbe-44c9-8909-fb4b5e530b79"
#代付倒计时-1800秒
replace_pay_time_out = "1800"
#代付超时时间-30分
replace_pay_time_out_m = "30m"
#科技新增销售单审批ID
ordinary_sale_order_verify_id_001 = "C4RambRN6SxGxNLCdQdRw6TA3e7WaA6Fm3cajFqnM"
#电子新增销售单审批ID
ordinary_sale_order_verify_id_002 = "3WKibAXdjasfvYBiXpLhmwKqYBsix3J8aCR37PbU"
#销售退货导出审批id
ordinary_sales_return_order_export_verify_id = "3WLJP4avjLPi7U66tH7pgrTDxtC39jXhebnGm1w5"
#科技样酒销售单审批ID
sample_liquor_verify_id_001 = "C4UCJJKGVPqEAUL7v72CSdJ1m9YUjaZGAdbYJ6rtE"
#销售退货批量导入审批ID
sales_return_batch_import_id = "3WKibHe5ijj5J9TxT8qBzYhZ6RYcPcN3yxNyk5M3"
#保证金金额
EARNEST_MONEY = "100"
#跨境小助手机器人token
cross_token = "25377ece-f484-4f4e-9e2d-8fbe4318166b"
#中台制单客户名称
CUSTOMER = "酒云研酒所-重庆点零售,佰酿美誉,佰酿美誉贸易（重庆）有限公司,MKT-酒云教育,深圳蜜思园酒业有限公司,猿小姐的甜水铺,吉顺（重庆）,佰酿美酒APP,佰酿云酒（重庆）科技有限公司,酒云研酒所,酒云研酒所-美团订单,酒云研酒所-饿了么,快团团-酒云网VINEHOO,其他,渝中区微醺酒业商行,快团团-兔子福利社"
#科技销售退款审批ID
ordinary_sales_return_order_verify_id = "3WKibHeUnMX5TjiY6h96f3x6gUVLTTE3JmxeuHfD"
#未发货提醒审批
un_ship_verify_id = "3WLJ7Cae1snwPm6sP9KJUHP5MLGc86hyJ2gYK3gv"
#小程序订阅消息配置
MINIPROGRAM_STATE = "formal"
#直播订单超时时间
LIVE_ORDER_TIMEOUT = "120"
#线下转账审批ID
offline_transfer_verify_id = "C4UE6WHiGomgH2QL8eFCed9rucDo84a4igWbCB1qK"
#对公转账支付倒计时-172800秒
transfer_pay_time_out = "172800"
#对公转账支付超时时间-2天
transfer_pay_time_out_h = "48h"
#银联对公转账验签key
upg_key = "YDPekCbzPrGrazNehpJpArp5dEn5eC7rJWENk88KweT7FbTE"



#优惠券
[COUPON]
APPROVAL_PROCESS_CODE = "PROC-4153BC23-35C8-40CC-9CF6-DA7A34B55A80"
HTTP_REQUEST_VINEHOO_CLIENT = "coupon"
HTTP_REQUEST_VINEHOO_CLIENT_VERSION = "v3"
WEIXINTEMPLATE_TEMPLATE_ID = EYWiPXcb-0_uUDgeUpqp6UCo_K1juB4qC1JwfShpD4E
MINIPROGRAM_STATE = "formal"
PACKAGE_APPROVAL_PROCESS_CODE = "8TeikhKkQuuKxXVcRjrPdEfgRVRg5zsoHXmW3"
DELETE_PROCESS_CODE = "C4Ranfrd7PPuqmgfxFhtdMyc8hmLdRcyGC6HoMF7Y"
#企业微信发送优惠券审批code
WECHART_PROCESS_CODE = "3WKhv4bWCm1bezWhDPJnDqwvVy33yMRydt7A3ahY"
#机器人token
DINTALK_TOKEN = "8d1f223e-5a72-493e-8af8-54f821555836"
#监听开始时间点
MONITOR_TIME = "2023-02-14 00:00:00"


#钉钉审批
[APPROVAL]
CORP_ID = ding8ea462d639c047e6ffe93478753d9884
APP_KEY = dinglvbcw4cgiagxlily
APP_SECRET = tFSFhIq-UxiWYeq2QGgYTEW-lBZRJ_0MwmSCQ9GFZ1xggG1osmddDIC9E3XVAwJk
TOKEN = 44Ru9qI3wjXk
AES_KEY = K4LlA0RsItSvqc2MhLKeW6IT4ArJPu7eOumSLvVivkl
TEST_URL = 123.com
HTTP_REQUEST_VINEHOO_CLIENT = "approval"

#支付
[PAYMENT]
api_url = https://qr.chinaums.com/netpay-route-server/api/
h5_url = https://qr.chinaums.com/netpay-portal/webpay/pay.do
key = rRfKYjr7AFiykDnfBmeitWnTa6t2iQb74MBKpcH4eMGnnce3
msgSrc = WWW.CQYJBN.COM
msgSrcId = 12F2
signType = SHA256
random_max = 10
dingtalk_refund = 5b12b3a315d28b2d451e5305822ad1cb77cf7309a2b535d79fe46ddd417da4bd
cashier_appid = wxfc6864f04e3d19d3
cashier_secret = d023d48f25d5e0736e0395c3c87c7e10
vinehoo_appid = wx3e0b582d1f902659
vinehoo_secret = 90127c2a982011758c4466e35b4df317
wineparty_appid = wxa761ee456d6ded89
#支付回调（公网）
store_url = "https://osms.vinehoo.com/osms/api/store/notify/umspay"
orders_url = "https://callback.vinehoo.com/orders/orders/v3/order/notify"
#老外卖家回调（公网）
laowine_url = "https://laowine.vinehoo.com/laowine/laowine/api/v1/paySuccess"
laowine_appid = wx9a7aa0b4317a6296
laowine_secret = df150ebfc825f4b1172630d029d4775e
KejiNotify = http://tp6-orders/orders/v3/order/notify
#保证金
earnest_url = https://tp6-auction-order/auction-order/v3/earnest/notifyDeal


#钉钉推送
[SENDER]
HTTP_REQUEST_VINEHOO_CLIENT = dingtalk-sender
CORP_ID = ding8ea462d639c047e6ffe93478753d9884
APP_KEY = dinglvbcw4cgiagxlily
APP_SECRET = tFSFhIq-UxiWYeq2QGgYTEW-lBZRJ_0MwmSCQ9GFZ1xggG1osmddDIC9E3XVAwJk
TOKEN = 44Ru9qI3wjXk
AES_KEY = K4LlA0RsItSvqc2MhLKeW6IT4ArJPu7eOumSLvVivkl
TEST_URL = 123.com


#直播
[LIVE]
accesskey_user = <EMAIL>
accesskey_id  = LTAI4FzbtXvSksW9sezPekyG
accesskey_secret = ******************************
domain_push = livepush.wineyun.com
domain_name = live.wineyun.com
source_url = http://wineyun.com/
app_name = vinehoo
#app_name = live-wineyun
static_url = http://************:10381
socket_url = wss://callback.vinehoo.com/websocket

#淘宝天猫订单同步
[TAOBAO_SYNC]
APPKEY = ********
APPSECRET = 450dd2a5a9a7d92b15a2c2c40d92e079
REDIRECT_URL =  https://callback.vinehoo.com/tp6-taobao-sync/taobao/auth/notify
TIMEOUT = 10
HOTLIMIT = 3
JUSHITAIP = *************

#发票
[INVOICE]
appId = 13fb0cf43135a4940c233fe16de1c05cb0eca6e0bc85819be0c78a7850d314ff
contentPassword = 56B27A5C3D404069
XSF_NSRSBH = 91500240MA5UA0LK57
XSF_MC = 重庆云酒佰酿电子商务有限公司
XSF_YHZH = 民生银行重庆江北支行155175348
XSF_DZDH = "重庆市石柱土家族自治县南宾镇城南居委白岩组(工业孵化楼509-41) 023-63360736"
client_id = 160810214421
client_secret = e0eacd983971634327ae1819ea8b6214
base_amount = 11300
alcohol_rate = 0.13
farmer_rate = 0.09
cocktail_rate = 0.06

#酒会
[WINEPARTY]
GAODE_KEY = "0c09e15cb33d08fe1b4d808090ad5e9c"
OPERATIONAL_DATA_DAILY="01ddd1f1-02bf-4ed7-a196-5d3426554476"

#银联验签key
key = rRfKYjr7AFiykDnfBmeitWnTa6t2iQb74MBKpcH4eMGnnce3
WINEPARTY=VHP
OWN = 5
WINE = 12
#分享链接
SHARE_URL = ""
#审批ID
PROCESS_CODE = "PROC-DD9907B8-6521-475F-B189-7CCCF442BAFF"


#权限
[AUTHORITY]
ADMIN_KEY = d7e958de4a6071ba
DD_APPID  = dinglvbcw4cgiagxlily
DD_SECRET = tFSFhIq-UxiWYeq2QGgYTEW-lBZRJ_0MwmSCQ9GFZ1xggG1osmddDIC9E3XVAwJk

[SYNCJD]
#京东订单同步
JD_URL=http://116.196.110.129
URL = https://callback.vinehoo.com/jd-sync
T_URL=http://tp6-push-t-plus
JDCODE_URL=https://gateway.wine-talk.cn/jdd
MQ_SYNC_EXCHANGE_NAME = thirdparty_orders
MQ_SYNC_ROUTING_KEY = thirdparty_queue_jd
ROBOT_TOKEN = 1b8fcfd6109f86ac36ef774a3ce8f84dced11812a07e4acfafc4223461d21ad4


[CONTENTAUDIT]
#阿里云视频点播配置
VIDEO_ACCESSKEYID = "LTAI4GCZW2qdgVhkpsnfWw54"
VIDEO_ACCESSKEYSECRET = "******************************"
#钉钉机器人推送token
DINTALK_TOKEN = "7c230703f092681ae46862f64c732ceda94bc01bef84c5dd8cb1ccfa77878636"

#商城
[COMMODITIES]
ONSALE_DINGTALK_TOKEN = "2cb6042c85382ad0447ce6341c690548eb39500f470f39313da4604b86d2e77a"
RECEIVER = "26cda82b-0bec-42d4-bc4a-c0cf46a4665d"
ONSALE_GDWJ_TOKEN = "f7e52882-58f9-4dc9-bf3d-0fde51aaa2ee"
INVENTORY_PROCESS_CODE  = "C4Woq1TPNnhBxLYmnoixN4TPiAzc5Rdhc5DNHUoQm"

[V2]
#V2用户模块
USER_V2_URL = https://gateway.wine-talk.cn/consumer

[TMALL]
app_key = ********
secret = 450dd2a5a9a7d92b15a2c2c40d92e079
format = json
v = 2.0
auth_url = http://*************:30383/taobao/auth/index
#测试
; qimen_url = http://pre-gw.api.taobao.com/top/router/rest
#正式
qimen_url = http://gw.api.taobao.com/router/rest
store_code = xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa
owner_id = 2807304908
xtjr_owner_id = 452890329
tmall_store_code = RMCWBNYJ01
fic_code = 301
order_dingtalk_access_token = 8fa34597792a694cc005adf448c76ad70e4fbe4762023c306e664f1eb9b74c5b
product_dingtalk_access_token = 0dd34c3f6dc571709acc9789cc2b81a20598b15adaca4f3a5bc12ef11cd72780
rabbimq_exchange_name = thirdparty_orders
rabbimq_route_key = thirdparty_queue_tmall
receiver_mobile = 15023814212
receiver_name = 胡进慧
receiver_address = '金新街道锦绣路888号供销产业集团东二门(希望大道和文昌路交叉口）佰酿云酒仓库'
receiver_area = 通州区
receiver_city = 南通市
receiver_province = 江苏省
receiver_country = 中国
receiver_zip_code = 226300


[PDD_CONFIG]
#拼多多
CLIENT_ID = 8c865e8c79f24cb1b9dcfd7cadb2b5a2
CLIENT_SECRET = 20ae08c34975b4ad97ec60d64c6576d40fc53fe3
REDIRECTURI = https://callback.vinehoo.com/pdd-sync/pdd/notify/auth


[SENDMAIL]
#公社发送邮件
host = smtp.qiye.aliyun.com
email = <EMAIL>
code = "Vinehoo@2021"

[WEIXIN]
APPID = wx62e0392115afb308
SECRET = bd542834b58306e86d1f7d3a4525c608

#微信账号
[WCHAT]
ACCOUNT = "eyIxIjp7ImFwcGlkIjoid3g3Njc5Y2Y5NjY1YTg1N2JkIiwic2VjcmV0IjoiMDFiM2NjNjZhMzUxYzBlNzgzYzVmOTE4MTg2YzExNzMifSwiMyI6eyJhcHBpZCI6Ind4M2UwYjU4MmQxZjkwMjY1OSIsInNlY3JldCI6IjkwMTI3YzJhOTgyMDExNzU4YzQ0NjZlMzViNGRmMzE3In0sIjQiOnsiYXBwaWQiOiJ3eGU5NTI0ZmNiNDdjMmU0YzkiLCJzZWNyZXQiOiI2NWM3OTQxZTE1MGM5MTc3NTI3MDRlOWEzZjEwZjAzNyJ9LCI4Ijp7ImFwcGlkIjoid3hhNzYxZWU0NTZkNmRlZDg5Iiwic2VjcmV0IjoiNTk1OGU1OGRlM2U5ZTcyNjhmNDMwMjNjZGFkNzk5ZmIifSwiOSI6eyJhcHBpZCI6Ind4NjJlMDM5MjExNWFmYjMwOCIsInNlY3JldCI6ImJkNTQyODM0YjU4MzA2ZTg2ZDFmN2QzYTQ1MjVjNjA4In0sIjEwIjp7ImFwcGlkIjoid3hlYmU0YjVhNTU3NzgyZTJjIiwic2VjcmV0IjoiOGNkZTY1MWZkYWQwMzlkODAwYTk2OTUzNDljNGEyZjQifSwiMTEiOnsiYXBwaWQiOiJ3eDg4NWIxNGJhYjhhNjExOTUiLCJzZWNyZXQiOiJiYzJkZjdiMDI1MGIyNjdlYzdmMzk1MTBkN2EyNGUxOCJ9LCIxMiI6eyJhcHBpZCI6Ind4ZjYwM2U3M2Q4YzM2MTZmYSIsInNlY3JldCI6IjljODRkZGQyZDg4OTRlYTY1OTI1NzMwZmVhMTNjYmNmIn0sIjEzIjp7ImFwcGlkIjoid3g3OTM4NDRmMTAyYTMwMmU0Iiwic2VjcmV0IjoiYzJjMGNkMzU5ZjU4ZjYyMTg2YmZmMjY1YjZkZmFmMDMifX0="

#小红书
[XIAOHONGSHU_SYNC]
APPID = e840e096a975478984d4
APPSECRET = c7931a95f48312d7918cde5de2c47b8e
AUTH_CALLBACK_URL = https://callback.vinehoo.com/xiaohongshu-sync/xiaohongshu/v3/auth/callback
MQ_SYNC_EXCHANGE_NAME = thirdparty_orders
MQ_SYNC_ROUTING_KEY = thirdparty_queue_xiaohongshu
WAREHOUSE_CODE = 330
STORE_MANAGER = ***********

[SMS]
APPROVAL_PROCESS_CODE = "PROC-DD9F72D5-D84A-452C-B005-E5C906AC8570"
#营销短信账号极密码
TY_MARKETING_USER_NAME = "酒云网"
TY_MARKETING_PASSWORD = "239233"
#通知短信账号及密码
TY_NOTICE_USER_NAME = "jyw"
TY_NOTICE_PASSWORD = "888666"
#大汉通信云短信账号及密码
DH3T_ACCOUNT = dh49797
DH3T_PASSWORD = ux1o7Dbh
DH3T_MD5_PASSWORD = 661ef8f2d29371402c960ae1040df68b

DH3T_MARKET_ACCOUNT = dh40723
DH3T_MARKET_PASSWORD = 4xYDwdNq
DH3T_MARKET_MD5_PASSWORD = 7f41e70871fc273450ca60bffa7bb592

[KEJI_WECHANT]
#科技微信支付
app_id = wx38f674425e3a8635
sub_app_id = wxe9524fcb47c2e4c9
miniapp_id = wx38f674425e3a8635
sub_miniapp_id = wx3e0b582d1f902659
appid = wx38f674425e3a8635
sub_appid = wxb205cdb4f9089c80
mch_id = **********
sub_mch_id = **********
key = wgs1z56ejH2y4dAVLUiFkDOMaPZbNtpT

[KEJI_LAOWINE_WECHANT]
app_id = wx38f674425e3a8635
sub_app_id = wx9a7aa0b4317a6296
miniapp_id = wx38f674425e3a8635
sub_miniapp_id = wx9a7aa0b4317a6296
appid = wx38f674425e3a8635
sub_appid = wxb205cdb4f9089c80
mch_id = **********
sub_mch_id = **********
key = wgs1z56ejH2y4dAVLUiFkDOMaPZbNtpT

[KEJI_ALI]
app_id = ****************
pid = ****************
private_key = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCWQr3W8UmRf3bHkCTwN+N7f/FU6+xO5gSZFbJ1ri/rcNQv8L3Gv5Kw+hn8xHk1vRumq9UQQAwJe+Hss5M1AKwCQFUmFVnfAGukgQaxB8rpIyd6UH3f/+Ro8YSUCILH40tpXOoFkFHUJzhJaM0WyqgzS+naP8qkNHI/+2ybH6j6WeIoAf4qIXLe7DA+p4AJo4C2IJu5x+nqqvuLMTHZ2ybNaqRjJxK1NfZsSyu1cAp8t3OO17w6D+4S66/HdAlcoSwKTVjNTThL0hfLlaxgYiqtiSLKBtfxUuJTqGiP+WYKQQpxDm0OL4W2/ALPg7zmL7ip9X2GDhslxPTrrGndXHQfAgMBAAECggEACdQ91NSdyl27KFy+fdQ6FjcNU0/HenXDcTwhG1+UlOMhji1CTMD1WIxTrNwxTZmcOG/fFRwLbYdkBPbON1Ze6hrJNx+IbHT7lZw3Etd+p3d8Op0PTtrPVyoNUR2paGfu4+qHCGwjXixemza2y7nf/NMHbhZkLA+eNZ89wHv2ssGaMr3pifpe3NQLXnUPVIwIJfZeR49RBpSj4IkpvhkqdE6xKVlXHxaFLOmplJ0qzhiFezYEMSCzojJ9Df2g6LJnmEUiP5sRJOlp6A4V2OzHoCeBkTXtwbcrAYedSE2Q9LTJrLDe+dWDKQqspA7BgX8W4eX2OiPed6OBpw3pEwje+QKBgQDymolK+s3CoDrUxZDYE93/HjpanQw8VlvnMPS0xiJRmGXJXI7nNFZYycpA6ineUjZSXuS+q5H4lFT0SU1G2NHQP0S5kMjPeLu44hG2+x60cGOmJ8I7zfJPq0FfatZ7gk3aq75rAOPmZc0Qx8v3BFOtVnjEgPMxrzWg4gG8F2XxpQKBgQCejtadkw6bc7nD8e1JNxQcIx/8FePU4NMF81gexVI1cKp6Z1z2v8Z1uBIOTubmwtGmuhnXVIPD/xbOkrZhoxUHb0RtCAIvYS1Aqw26Vh457iKT0gK+6e99DxNyAqFCLVE3NONYiZf/qitSXfuiwSjgF8f06I4vqHvIsC3s9JqbcwKBgQCOWDgYI2JZQNRkBWvALKAD0YOhqSYFvRH9a48ZBmwafhWy+Tjr1ZyYSgX+4qYgSRKQcBc8/MQQG1BhaSULXap1yrmTO61ndkqG5zZHq75PVuZRFmDz4Bm9vxkDWFdybnkpxb1bYVFW3QJeRyyYyhQiz2rauWJ4E3qe5BzqdLwe9QKBgEQgfYhhClKyy6ushFcECmj4kqKeyTDLlQAmicNW5za8/RLw3JnM1rn36BIrxrx6Eti/d6D02FTbgvIpSaH0D3INVeo/Gak/6NCzboeUvxIHBuZawwFCxLsIQkOna6BillXRLKn7HMsmU8zlQtywgB6hOTI6xfUmQGF/iYwZoWCPAoGAOSrMPmsp3M4O3vyYgeBCPMhU7sqCPOXyN9T7JM5qnC5DpJgDPWpcLyws3AUYLedBoCd7wrnfWvmYqHFYqRUZL0wPZeNkFyTWpT4m+mFw73aCJBwgCFhgIiYy3Vl1BpYpN9an/OsAwlCgr8tdlOIs/p38W8pB7ZthZ5mzRFc+2Mw="
ali_public_key = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoBqXeUeLl9pvB/LqBRjiWNdMMDcvGX9c53LPR0sAaHcrdIzuhdfwXnxjfnBt8nK6ATlLj+XGBFzTlvmj98LI8HXV138wqNlwliYsoI58Qj1OEKmsXTUDhVDvFq/elXDDygfgM0cmrCncL504wjHsWdOciWhBVO3R8Ku3CP1c3eCcqHuMlL+blmzshEZSBbQn707fSufRn62tnuc5MJ2tH0ql69yvK78G/eIf5DMbkua0boogi7D08KgmfB16f+nRxVG4ay1hpVp9+XVzui+RKcWV1VWmddPw6qE9WAoiv0Ab6aBeoXL8ltGbWCPUTUXMhU5pTWu3VoZxLKWAxJuQgQIDAQAB"

[WORK_ORDER]
REFUNDS = "C4RZbLeRodWQJkGxyvGS7uAkhAAdADs7Kf6PWawMS"
EXCHANGE_GOODS = "3WKi3gmwbNADnbcXtcDnG11xjwDGwZLZbtH5G4Lc"
REISSUE = "C4RZbLeRnuLAJNQMzAkoeCUPWoBravWS1UXTRG9cH"
REISSUE_COUPON = "ZvdTXGcVFWsjCDCAFuCvF9DpBVhVTV2f2xwvrd"

[U8C]
URL = http://***********:8088/u8cloud/api/
USER = admin
PASSWD = admin123456
SYSTEM = 001


#拍卖订单
[AUCTION_ORDER]
#保证金订单超时时间
EARNEST_MONEY_TIMEOUT = "600"
#银联验签key
key = "rRfKYjr7AFiykDnfBmeitWnTa6t2iQb74MBKpcH4eMGnnce3"
#订单号前缀
EARNEST=VHE
REFUND=REF
ORDER=VHA
#保证金金额
EARNEST_MONEY = "0.01"
#拍卖订单超时时间
AUCTION_ORDER_TIMEOUT = "172800"
#订单自动确认收货时间
AUTO_RECEIPT_TIME = "1296000"
#萌芽实体仓编号
STORE_CODE = "xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa"
#银行验证appcode
APPCODE = "0714a6e611be4cb2ab9a8e34ef8ee861"
#委托保证金手续费30%
ENTRUST_COMMISSION = "0.03"
#XXX未支付消息通知
AUCTION_ORDER_MESSAGE_PUSH_TIMEOUT = "43200"
#机器人token
DINTALK_TOKEN = "4ba6ec56-d1df-43aa-a052-861d2a8ae5d5"


[KEJI_AUCTION_ALI]
app_id = 2021003173633385
pid = 2021003173633385
private_key = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCGP6XPZJktNWOB5sekVpk2QNg5UFRRTG1csz8rUwu0k7BlmPML034ccE0AnUAKRWMF1Apnq/UeDC7+OgxkPN/6GgUHg3GMp9G7DzZHWbXsAIXtdWMmmKHFDaSUaaRfelCZOOUnd8Cq4Bc/dQCwRfaK3KvB3HxsPCigzk/INV4X6SzFE926gie9rDPXmihPmtv3ZFI9gjZZxi40TdIXLCloR+zjmhYtNSvAVlcIRCLxlqJOlyaOIHcHdFVGjTLvDFbjwE2ZR9tYqfz3U5SMUXczlROVartefKJjwlYdsktv5asa00u/zQoomXc5RJJLdjlyxewV4GR5RjxvxnUabIerAgMBAAECggEAV5n+y7dcUOjOtvxt9+nebk2gCr5FVwulLWF17gJx1/Krn5BPwL7QQyRwyt7R5CHk/O+hnobSGzpeC9y7K0mh6eTCtWVrWhpEPGkvxR1gHdE1k38PJSdiIGDHrWTGMttw7pr2xWtaWOaDpQ/nVB9DQte+aHFE66fR5yTZYM6aSMUYMVEOvxX2RWiKZMxha7BX8cljHd8nhHECNbJ4KJZxQv3gZr/gVZ6QL5t2FmBQK9p1jyltn0nxaUeBY5HiDL5hX9pMKwsZCj/KmjBwsxore09j+gVQH9rTD4rqGWbBM01gQ0uT8zTFAFZuYzmLHN+SUN/Y1iiOlJhnRqF+7XoYCQKBgQC/z5/61hFYlqGm3HlL4h5klO/o0ZhcNK6WV1Lr1f8abXW1lJDOJVumJSaX5/9rlGaDR/5piUVgyvd7UjhQW6dA/YBdrR2rnBG/8q4p8gXE5Ibd+awOKEguH/ZVd4Oiw/MsqrBjN8QZ3MHnaSqgVR8f4BjMcP1GaiJat5t6en0xnQKBgQCzLKyFeg47c+Mh+0X8dZ3rYOPt6yqk+9yt2KdqrND6ySQNFAvqMB6XhZo1AeFLS0kTQNACoNQ149aojMG0XVAb8zfEElAyyBbV4chBvNY820uJThWGCXnuODUuqcflZr/PslzY6igNv2vLNccFsj/CMI4wSqw4MammcyX4oCHf5wKBgGQT3nKv0mkYXsuC6T6wgg8UroAZK5egXR9P/JHUG8AcMHhHRa5pS4JKi/JiN6BX04zXuWQzHSG9hJaxdXRn/cIpAhreQkbRrWkrI7f68Quj+MJEjILufYqjXpGFRAdembhKbrG13PlpdzpYYsc44n5tHEM/CMb+hWIzvkmPM1b5AoGBALMXrV5BkSZGALs/sUXWvsG+eDWhAWAlzAAM9dK/6Kc0FA/qgLpV3M4xoeDn/yzGlMVk3EY0QcmX8kFJaHkDaIZ3hr86WuVXcNPaudNTkIUF4avd28+9ex3tGuOImyEmogVV0E6VRvHu6y0UlNmRwP82j3mfuiL1KRESOdsZHKLzAoGAOdHU0xiA0HK/iv/yIlfxTLfZws1x1nAWYEM0Dvfpnd0rS3Rjt1zVInoHmRSyOBg7BJciNZB4oDooJTYs2/TS/mNXa7GXsCgfWkaCSzyaFde3OZxAwEDo9BJn1VQYp4h6bntJUdCV7Tk0KWHGDuFMDfDQzGL7+cJBzVi5B6BLquo="
ali_public_key = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq3oleyI77yCktO5IaUxqYw3mAjmcSXihM6ufa7Fs8bHFkXk0UoDus11ziOHFdeyU4b/dJFOPHzahloFYfb6SGOiIcBys/FBL1q3QjXhMbHus9CGu2H1khtFSvVm4mhD7vHo0uA5pRwXR30r6vNy/PVE9iP6icDFVfkUF/SlJSpqafn8s95KNTHmjsIDcOAXQajMmLeVefBu/kgL/i6iJ3bKQXJy4mfeazANg7pP3WpSr60xtpnhx+EcFX573d4niPAUcHT29bVhQxNoQZjrY4nqvxsSNpBi+MGBpDi7w+/ESP/CuDuFXyBm5FodagxogeHSysWu9zsLHIboI2LW/aQIDAQAB"

[PAY_NOTIFY_URL]
cross = "https://callback.vinehoo.com/orders/orders/v3/order/notify"
auction_order = "https://callback.vinehoo.com/auction-order/auction-order/v3/order/notify"
auction_earnest_order = "https://callback.vinehoo.com/auction-order/auction-order/v3/earnest/notifyDeal"
laowine_order = https://laowine.vinehoo.com/laowine/laowine/api/v1/paySuccess
auction_songGe_order = https://callback.vinehoo.com/tp6-songge-auction-order/sg-auction-order/v3/order/notify


[PAY_UPDATE_URL]
cross = http://tp6-orders/orders/v3/order/updatePaymentMethod
auction_order = http://tp6-auction-order/auction-order/v3/order/commonOrderUpdate
auction_earnest_order = http://tp6-auction-order/auction-order/v3/earnest/updatePaymentMethod
auction_songGe_order = https://callback.vinehoo.com/tp6-songge-auction-order/sg-auction-order/v3/order/commonOrderUpdate

[KEJI_AUCTION_WECHANT]
app_id = wx38f674425e3a8635
sub_app_id = wxe9524fcb47c2e4c9
miniapp_id = wx38f674425e3a8635
sub_miniapp_id = wx3e0b582d1f902659
appid = wx38f674425e3a8635
sub_appid = wxb205cdb4f9089c80
mch_id = **********
sub_mch_id = 1636974049
key = wgs1z56ejH2y4dAVLUiFkDOMaPZbNtpT

#得物订单同步
[DEWU]
HOST = "https://openapi.dewu.com"
APP_KEY = "25cd5434c528456f9992cdfdddef03fd"
APPSECRET = "1f2cc4d7227b433bb77c60de368622808303ba9347744e9888c31670393eb627"

[SUPPLYCHAIN]
#存货价格 企业微信审批id 价格本修改审批-测试
COM_APPROVE_INVENTORY_PRICE = "C4UAWdyV4v5oYKBNrjkwUvP6hGA1hMGo5k3RcwoU3"
#供应商新增
COM_APPROVE_PARTNER_ENTITY = "C4RcZXKLVxzF4ddXhKAHv1mxD1VKfAekY1WmX4aGF"

#闪送
[SHANSONG]
APP_KEY=sscmnV8WrGZkm4o51
APPSECRET=8zA3wpNxCTGjMTyAQsK9AzoCDEl078h8
HOST = "http://open.ishansong.com"
NOTIFYURL="https://callback.vinehoo.com/vmall/vmall/v3/shansong/callback"
REDIRECTURL="https://callback.vinehoo.com/vmall/vmall/v3/shansong/redirect"
API_VERSION="/openapi/developer/v5/"

[EXTEND]
SPECIAL_ACTIVITY_ID = 42
ACCESS_TOKEN = "01ddd1f1-02bf-4ed7-a196-5d3426554476"

[QYWXSXYYY]
AGENTID=1000007
CORPID=ww7abe3785c0ccdd34

[ENTERPRISE_WECHAT_APPROVAL]
#预览图片
IMAGES_URL = "https://activity.vinehoo.com/activities-v3/auctionGoodsCheck?id="


[AUCTION_ALI_PAY]
APPID = 2021004109692561
APP_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoQwNFqnHinVFrHel9RqhloP1mLqedJ5YTIGd9HI1zLfC/WabLmaiEbGw6qprL60PlrA2sp3tLgvYDVWcnqfEdTMtRGDrCnUNrZUwv+0PNKtNznHL41c8OrY2VzGvqQ/NoBQ6oopUNInPtuwmV3BPS7TqlfwEqvHStv5B0F4PuJUCDt7h0i7K6ER4maUDLDc/1o3JeECRqSRvS15lMzOaBjYVLY1Ema7mZGXXdY3LLRWVPluhUvSwmuLOF3m8pMK34Fqh22O2VnLICISkcpQRvU+hQt/Z2TDNwUM9wHpeI/AY8cilFzXkRVSgxEOusLH/kzd6ybHtpvKieABfB74Y4QIDAQAB"
APP_PRIVATE_KEY = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQChDA0WqceKdUWsd6X1GqGWg/WYup50nlhMgZ30cjXMt8L9ZpsuZqIRsbDqqmsvrQ+WsDayne0uC9gNVZyep8R1My1EYOsKdQ2tlTC/7Q80q03OccvjVzw6tjZXMa+pD82gFDqiilQ0ic+27CZXcE9LtOqV/ASq8dK2/kHQXg+4lQIO3uHSLsroRHiZpQMsNz/Wjcl4QJGpJG9LXmUzM5oGNhUtjUSZruZkZdd1jcstFZU+W6FS9LCa4s4XebykwrfgWqHbY7ZWcsgIhKRylBG9T6FC39nZMM3BQz3Ael4j8BjxyKUXNeRFVKDEQ66wsf+TN3rJse2m8qJ4AF8HvhjhAgMBAAECggEBAJ8gHDKfcPVC6KP/2O/072KcYmgVRQnvh0mY7OyiiLwNelINNxnC+TJIg4lq99u0wOlglbspRjgwJbsPM2CkXf57oaelp7e+btRrzC5nWpXtCUUJta6c/Hr6nXjMNEf0wHUCZ+lAivEali2k6kGNTBx+1yd5S1do944ubfMnbZsvtssiAkkvgFgwYrQ7CtobVwIMWuzLXE2n6Puyx/4KmAD3IAh0yHnhdIbCraahHAMJUFXZ2CDufsVYWuNZO6Osyrh+p/sHdOfRfgE0d+577w3s3cR4aUJNeTKRHaTnxTFKr9tg/XdPmgLBiwWeI8gXn71/hbtBh63LnWF5DgxoVv0CgYEA0hWHE28sfoqfPRl8rPu95w6cln3QJrY7HHr/nfnegW7xypHVCpi+nlHlfx0YGZiHGYJfoxgbwSOYb0SOPQMaOqC0RjPmzPJnQmSyd7InCAsJaI4H8tppmQMZPv2V+CbD3DqGdsEkS7VzmC7oyQKYR2j16Bg0cv1nBKXw0w8PBtsCgYEAxD7XBhS5r07OOq4JjzZJ5OYFmkBabMFoFOLVVz3gX3LhJyj+aC0jMPrV8RIVOqWSEEkEEeQNhTA4bNf81orlnlhVSNdoq7SU0rExf2ywtp74aJ+xMi5w4eNaImmirmcHTOGCOuQRva7Et2qSNggAlabXZIfhRi4bvlgyVJEx9fMCgYBxXLR6xC7aSasLqUkhfuZGXBgbDODZQtQ61hRBBqgOZ/OJP5AaG7ogbEjOgsCSbz/XgAZjV4sCeVa0E+Y8/gOVR0p/51nPla4qoXO8KyHssSfuh1W6sijxLD7fQq8+LIzbtakBQ892Gv+5SvbKIOBTCExJZWZH9n00gOamvSV6qQKBgQC0JDb/hwDYMs/OP7nYs23rZ1bXPKGE2ZjwKzTJC6yuEWcqqk+2U1DNgkPnMcE4/4lv14ab4aqjixsQlseqLQAQdWd8IPgdPfuq2CrmnGAukwjWCVkABm9qe2de1XcW/s5NvWftJTQ/2XEMKRCj9n6K4QcxaiXZZTPka1NwG4QwDwKBgQCQfPlPK3B8v7Qdi0l1yjQqfj2OoJJGLaVM5rx+NyimpE/UxtERk5UeKpMj5AF91gBNNuTzvJz0f0FGVhl1g0q5UJePjxDFLWOg/VSqH70RhRLrAUgco1qCVQU8uUtIg388Evchm99K4i4EZf9lIeJUAPXX6A4f/ykXF6xPe6ThHQ=="

[MARKETING]
COUPON_MONITOR_TOKEN = "a9450712-b89f-4578-81e6-d7f37c256fa9"

[FLOW_SERVICE]
ROLE_ID = 67

FIELD_CTL_ROLE_ID = "2,39,14,44"

[FLOW_SERVICE]
FLOW_CODE = "8aae343f-f3b2-4658-a57b-67ccf1075b33"

[TMALL_KJ]
app_key = ********
secret = 450dd2a5a9a7d92b15a2c2c40d92e079
format = json
v = 2.0
auth_url = http://*************:30383/taobao/auth/index
qimen_url = http://gw.api.taobao.com/router/rest
store_code = xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa
owner_id = 28073049081
qjd_owner_id = 419938814
tmall_store_code = RMCWYJKJTMGJC01
fic_code = 361
order_dingtalk_access_token = 8fa34597792a694cc005adf448c76ad70e4fbe4762023c306e664f1eb9b74c5b
product_dingtalk_access_token = 0dd34c3f6dc571709acc9789cc2b81a20598b15adaca4f3a5bc12ef11cd72780
rabbimq_exchange_name = thirdparty_orders
rabbimq_route_key = thirdparty_queue_tmall
receiver_mobile = 15023814212
receiver_name = 胡进慧
receiver_address = '金新街道锦绣路888号供销产业集团东二门(希望大道和文昌路交叉口）佰酿云酒仓库'
receiver_area = 通州区
receiver_city = 南通市
receiver_province = 江苏省
receiver_country = 中国
receiver_zip_code = 226300

[APP]
DEFAULT_TIMEZONE = Asia/Shanghai

# 松鸽拍卖
[DATABASE_SONGGE_AUCTION]
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = songge
USERNAME =  vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_SONGGE_USER]
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = songge_user
USERNAME =  vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[CACHE_AUCTION]
DRIVER = redis
HOST = r-8vbyf9qn03iwmy7w5n.redis.zhangbei.rds.aliyuncs.com
PASSWORD = NRDSYa5e6EWZuJ3d
PORT = 6379
prefix = songge.
db = 15

[DATABASE_AUTHORITY]
#权限
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_authority
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
DEBUG = true
PREFIX = vh_


[DATABASE_COMMUNITY]
#社区
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_community
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = true

[DATABASE_WINECABINET]
#酒柜
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_winecabinet
USERNAME = vinehoocellardev
PASSWORD = qKXum07U3sbvnl1h
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = flase

[DATABASE_INVOICE]
#发票
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_invoice
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = flase


[DATABASE_LIVE_ORDER_SYNC]
#直播订单
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_live_order_sync
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_CONTENT_AUDIT]
#内容审核
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_content_audit
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_APPPUSH]
#推送
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_app_push
USERNAME = vinehoodev
#USERNAME =
PASSWORD = ziAJWCLwOVs29NbB
#PASSWORD =
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_SHORTVIDEO]
#短视频
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_short_video
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = true

[DATABASE_DINGTALK]
#钉钉
TYPE = mysql
driver = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_dingtalk
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_COMMODITIES]
#商品
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_commodities
USERNAME =  vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = true

[DATABASE_MARKETING]
#营销
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_marketing
USERNAME =  vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8
PREFIX = vh_
DEBUG = false

[DATABASE_USER]
#用户
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_user
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
DEBUG = false
PREFIX = vh_

[DATABASE_WIKI]
#磐石
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_wiki
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8
PREFIX = vh_
DEBUG = true

[DATABASE_WINEPARTY]
#酒会
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_wineparty
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = true

[DATABASE_ORDERS]
#订单
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_orders
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_NEWS]
#酒闻
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_news
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
DEBUG = false


[DATABASE_SMS]
#短信群发
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_sms
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
DEBUG = false


[DATABASE_LOGISTICS]
#日志
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_logistics
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_LIVE]
#直播
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_live
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8
PREFIX = vh_
DEBUG = false

[DATABASE_TAOBAO]
#淘宝天猫订单同步
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_thirdparty_stores
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8
PREFIX = vh_
DEBUG = true

#分销
[DATABASE_DISTRIBUTION]
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_distribution
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8
DEBUG = true
prefix = vh_

#v2用户数据库
[DATABASE_WY_V2]
TYPE = mysql
HOSTNAME = rm-m5e1gld569948pyfeko.mysql.rds.aliyuncs.com
DATABASE = wy_user
USERNAME = wy_dw
PASSWORD = 4DAOKz8RSq6f7svy
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

#v2商城数据库
[DATABASE_MALL_V2]
TYPE = mysql
HOSTNAME = rm-m5ee0xm27w9da26p2ko.mysql.rds.aliyuncs.com
DATABASE = wy_mall
USERNAME = wy_dw
PASSWORD = 4DAOKz8RSq6f7svy
HOSTPORT = 3306
CHARSET = utf8
DEBUG = true
prefix = wy_

#京东订单同步数据库
[DATABASE_SYNC_JD]
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_thirdparty_stores
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_PURCHASE_MANAGEMENT]
#采购管理
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_purchase_mangement
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_PUSHORDERS]
#推送萌芽
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_pushorders
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = true

[DATABASE_PUSH_T_PLUS]
#推送T+
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_push_t_plus
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_T_PLUS]
#T+ 002 024 029账套
TYPE = sqlsrv
HOSTNAME = **************
DATABASE = UFTData864980_000002
DATABASE_024 = UFTData684772_000024
DATABASE_029 = UFTData633239_000029
DATABASE_008 = UFTData350044_000008
DATABASE_031 = UFTData633239_000031
USERNAME = sa
PASSWORD = CD1008...
HOSTPORT = 1433
CHARSET = utf8
DEBUG = true

[DATABASE_DATA_STATISTICS]
#数据统计
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_data_statistics
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_CUSTOMER_SERVICE]
#工单
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_customer_service
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_PAYMENT]
#支付
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_payment
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_TMALL]
#天猫国际
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_thirdparty_stores
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8
PREFIX = vh_
DEBUG = true


[DATABASE_THIRDPARTY_STORES]
#拼多多
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_thirdparty_stores
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8
PREFIX = vh_
DEBUG = true

[DATABASE_VMALL]
#秒发后台
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_vmall
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_COMMUNE]
#公社后台
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_commune
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = wy_
DEBUG = false

[DATABASE_ERP]
#ERP
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_erp
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false


[DATABASE_vh_data_statistics]
TYPE = mysql
driver = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_data_statistics
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_U8C]
#U8C
TYPE = sqlsrv
HOSTNAME = rm-8vbuizsf257qnlj3tgo.mssql.zhangbei.rds.aliyuncs.com
DATABASE = U8CLOUD
USERNAME = vinehoodev
PASSWORD = Nmqo5Hf&BsQWBodkaV8g
HOSTPORT = 1433
CHARSET = utf8
DEBUG = true

#供应链
[DATABASE_SUPPLYCHAIN]
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_supplychain
USERNAME =  vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

#拍卖
[DATABASE_AUCTION]
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_auction
USERNAME =  vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

#渝欧
[DATABASE_YUOU_ORDER_SYNC]
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_yuou_order_sync
USERNAME =  vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[DATABASE_TEMP_ACTIVITY]
# 临时活动
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_temp_activity
USERNAME = vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = true


[NACOS]
URL = "http://rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com:8848/nacos/v1/cs/configs"
TENANT = "d9fd09cf-6569-4a2d-a623-5df9999dd91a"
USERNAME = "nacos"
PASSWORD = "vinehoo666"


[MONGODB]
TYPE = mongo
HOSTNAME = "dds-8vb46b057fe339e42.mongodb.zhangbei.rds.aliyuncs.com,dds-8vb46b057fe339e41.mongodb.zhangbei.rds.aliyuncs.com"
USERNAME = "root"
PASSWORD = "7slZVTv4WRwj03Da"
HOSTPORT = 3717
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = true
RW_SEPARATE = true
DEPLOY = 1
REPLICASET = "mgset-507888801"
READPREFERENCE = "secondaryPreferred"

[ES]
HOST = es-cn-7mz2retry0008zrzt.elasticsearch.aliyuncs.com
PORT = 9200
USER = elastic
PASS = BYI8uuJGQuo45smj
PREFIX = "vinehoo."
SCHEME = "http"


[CACHE]
DRIVER = redis
HOST = r-8vbyf9qn03iwmy7w5n.redis.zhangbei.rds.aliyuncs.com
PASSWORD = NRDSYa5e6EWZuJ3d
PORT = 6379
prefix = vinehoo.
db = 1

[CACHE_VMALL]
DRIVER = redis
HOST = r-8vbyf9qn03iwmy7w5n.redis.zhangbei.rds.aliyuncs.com
PASSWORD = NRDSYa5e6EWZuJ3d
PORT = 6379
PREFIX = vmall.
DB = 3

[CACHE_ORDER]
DRIVER = redis
HOST = r-8vbyf9qn03iwmy7w5n.redis.zhangbei.rds.aliyuncs.com
PASSWORD = NRDSYa5e6EWZuJ3d
PORT = 6379
prefix = "vinehoo."
db = 8

[REDIS_GRAPH]
DRIVER = redis
HOST="*************"
PASSWORD="6a#s^x9^@zqzPvGxPLM9FxpD"
PORT=30079
prefix= vinehoo.
db = 1

[NEO4J]
HOST = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
PORT = 7687
USER = neo4j
PASS = vinehoo666

[RABBIMQ]
IP = ************
PORT = 5672
NAME = "admin"
PSWORD = "XevRzHH8JayDYXa6h7wGeLCH8Z"
VHOST = wineyun-prod

[OSS]
ACCESSKEYID = LTAI5tCTTF2TderhYBGjCHQ6
ACCESSKEYSECRET = ******************************
ENDPOINT = oss-cn-zhangjiakou.aliyuncs.com
BUCKET = vinehoo
ALIURL = https://images.vinehoo.com
ROLEARN = acs:ram::1083400064674077:role/oss-sts
ROLESESSIONNAME = oss-sts
REGIONID = cn-zhangjiakou
CALLBACK_URL="https://callback.vinehoo.com/oss/v3/callback"

[TMALL_SNACK]
app_key = ********
secret = 450dd2a5a9a7d92b15a2c2c40d92e079
format = json
v = 2.0
auth_url = http://*************:30383/taobao/auth/index
qimen_url = http://gw.api.taobao.com/router/rest
store_code = xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa
owner_id = 114968496
qjd_owner_id = 419938814
tmall_store_code = TMBNKY
fic_code = 011
order_dingtalk_access_token = b4db52ac-63d2-402b-bd95-3a606232a477
product_dingtalk_access_token = b4db52ac-63d2-402b-bd95-3a606232a477
rabbimq_exchange_name = thirdparty_orders
rabbimq_route_key = thirdparty_queue_tmall
receiver_mobile = 15023814212
receiver_name = 胡进慧
receiver_address = '金新街道锦绣路888号供销产业集团东二门(希望大道和文昌路交叉口）佰酿云酒仓库'
receiver_area = 通州区
receiver_city = 南通市
receiver_province = 江苏省
receiver_country = 中国
receiver_zip_code = 226300

# 订单
[MAILER_CONFIG]
# email
host = smtp.qiye.aliyun.com
email = <EMAIL>
email_name = 系统邮件
code = "kP96d27Fr"

# 松鸽拍卖（个人拍卖小程序）微信支付
[SG_KEJI_AUCTION_WECHANT]
app_id = wx38f674425e3a8635
sub_app_id = wxe9524fcb47c2e4c9
miniapp_id = wx38f674425e3a8635
sub_miniapp_id = wxf603e73d8c3616fa
appid = wx38f674425e3a8635
sub_appid = wxb205cdb4f9089c80
mch_id = **********
sub_mch_id = 1636974049
key = wgs1z56ejH2y4dAVLUiFkDOMaPZbNtpT

# 酒展通微信支付
[WINE_EXHIBITION_WECHANT]
app_id = wx38f674425e3a8635
sub_app_id = wxe9524fcb47c2e4c9
miniapp_id = wx38f674425e3a8635
sub_miniapp_id = wx793844f102a302e4
appid = wx38f674425e3a8635
sub_appid = wxb205cdb4f9089c80
mch_id = **********
sub_mch_id = 1668286167
key = wgs1z56ejH2y4dAVLUiFkDOMaPZbNtpT

[SONGGE_AUCTION_ORDER]
#保证金订单超时时间
EARNEST_MONEY_TIMEOUT = "300"
#银联验签key
key = "rRfKYjr7AFiykDnfBmeitWnTa6t2iQb74MBKpcH4eMGnnce3"
#订单号前缀
EARNEST=SGE
REFUND=SGR
ORDER=SGA
#保证金金额
EARNEST_MONEY = "0.01"
#拍卖订单超时时间
AUCTION_ORDER_TIMEOUT = "172800"
AUCTION_ORDER_TIMEOUT_BAK = "172800"
#订单倒计时12小时未支付消息通知
AUCTION_ORDER_MESSAGE_PUSH_TIMEOUT = "43200"
AUCTION_ORDER_MESSAGE_PUSH_TIMEOUT_BAK = "43200"
#萌芽实体仓编号
STORE_CODE = "xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa"
#订单自动确认收货时间
AUTO_RECEIPT_TIME = "259200"
#委托保证金手续费30%
ENTRUST_COMMISSION = "0.03"
#萌芽实体仓编号
STORE_CODE = "xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa"
#拍卖群助手
DINTALK_TOKEN = "4ba6ec56-d1df-43aa-a052-861d2a8ae5d5"


#商品从数据库
[DATABASE_COMMODITIES_FOLLOW]
TYPE = mysql
HOSTNAME = rr-8vbwq6b79kqq5sj26.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_commodities
USERNAME =  vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8
PREFIX = vh_
DEBUG = true

#学堂
[DATABASE_ACADEMY]
TYPE = mysql
HOSTNAME = rm-8vb8nfr498cxlyhdu.mysql.zhangbei.rds.aliyuncs.com
DATABASE = vh_academy
USERNAME =  vinehoodev
PASSWORD = ziAJWCLwOVs29NbB
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false