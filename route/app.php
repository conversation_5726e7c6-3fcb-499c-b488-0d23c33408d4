<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

Route::get('/', function () {
    return 'ok-tp6-companies';
});

/**
 * 产品管理
 */
Route::group('databank_old/v3', function () {
    // 添加产品
    Route::post('products/create', 'Products/create');
    // 更新产品
    Route::post('products/update', 'Products/update');
    // 产品单位列表
    Route::get('products/unit', 'Products/getUnit');
    // 产品类别列表
    Route::get('products/category', 'Products/getCategory');
    // 根据类别 id 获取类型列表
    Route::get('products/type', 'Products/getTypeByFID');
    // 根据产品 id 获取产品详细信息
    Route::get('products/info', 'Products/getProductInfoById');
    // 根据条件和字段返回商品详细信息
    Route::get('products/query', 'Products/getProductByShortCode');
    // 商品属性
    Route::get('products/getProperty', 'Products/getGoodsProperty');
    // 产品列表
    Route::get('products/list', 'Products/esList');
});

/**
 * 商品管理
 */
Route::group('commodities/v3', function () {
    /**
     * 商品通用接口
     */
    // 更新商品信息
    Route::post('periods/update', 'Periods/update');
    // 更新商品上下架时间信息
    Route::post('periods/updateTimes', 'Periods/updatePeriodsTime');
    // 更新商家秒发商品上下架时间信息
    Route::post('periods/vmallUpdatePeriodsTime', 'Periods/vmallUpdatePeriodsTime');
    // 更新商品不重要信息，单个字段信息
    Route::post('periods/updateUncUsed', 'Periods/updateUncommonlyUsed');
    // es商品列表
    Route::get('periods/list', 'Flash/esList');
    // es前端商品列表
    Route::get('periods/periodsList', 'Flash/esFrontEndList');
    // 活动专题页-热门推荐
    Route::get('periods/esHotRecommendations', 'Flash/esHotRecommendations');
    // 添加商品备注
    Route::post('periods/createRemark', 'Periods/createRemark');
    // 商品备注列表
    Route::get('periods/remarkList', 'Periods/remarkList');
    Route::get('periods/remarkUserList', 'Periods/remarkUserList');
    // 商品评论
    Route::post('periods/createComment', 'Periods/createComment');
    // 商品详细评论列表
    Route::get('periods/commentList', 'Periods/commentList');
    // 用户评论状态
    Route::post('periods/getCommentUserStatus', 'Periods/getCommentUserStatus');
    // 添加商品产品库存
    Route::post('periods/createPeriodsProductInventory', 'Periods/createPeriodsProductInventory');
    // 删除商品产品库存
    Route::post('periods/deletePeriodsProductInventory', 'Periods/deletePeriodsProductInventory');
    // 增减库存已购
    Route::post('periods/periodsPlusMinus', 'Periods/periodsPlusMinus');
    // 马甲、背心、小编列表
    Route::get('periods/vestRecordList', 'Periods/vestRecordList');
    // 用户收藏商品
    Route::post('periods/collection', 'Periods/periodsCollection');
    // 用户收藏列表
    Route::get('periods/collectionList', 'Periods/collectionList');
    // 查看用户是否收藏期数
    Route::get('periods/existsCollection', 'Periods/existsCollection');
    // 批量删除用户收藏期数
    Route::post('periods/delCollection', 'Periods/delCollection');
    // 库存列表
    Route::get('periods/inventoryList', 'Periods/periodsInventoryList');
    // 修改期数库存
    Route::post('periods/updateInventory', 'Periods/updateInventory');
    // 更新商品采购
    Route::post('periods/updatePurchaseInfo', 'Periods/updatePurchaseInfo');
    // 更新期数收款商户
    Route::post('periods/updatePayeeMerchant', 'Periods/updatePayeeMerchant');
    // 期数点赞
    Route::post('periods/praise', 'Periods/praise');
    // 热门推荐，猜你喜欢
    Route::get('periods/recommendList', 'Periods/recommendList');
    // 根据 id 获取es期数详细
    Route::get('periods/getESPeriodInfoById', 'Periods/getESPeriodInfoById');
    // 期数上架获取期数详细钉钉消息消息推送
    Route::get('periods/onSaleDingdingPush', 'Periods/onSaleDingdingPush');
    // 自增期数浏览量
    Route::post('periods/incPageviews', 'Periods/incPageviews');
    // 首页缓存
    Route::get('periods/indexListSort', 'Periods/indexListSort');
    // v2 商品数据更新同步 v3
    Route::post('periods/v2SyncUpdateV3Periods', 'Periods/v2SyncUpdateV3Periods');
    //商品转换率
    Route::get('periods/accessStatistics', 'Periods/getAccessStatisticsCount');
    //商品导入到待上架列表
    Route::post('periods/importByExcel', 'Periods/importByExcel');
    // 跨境商品导入到待上架列表
    Route::post('periods/importCrossByExcel', 'Periods/importCrossByExcel');
    // 查询加购商品
    Route::get('ap/getAddPurchase', 'AddPurchase/getAddPurchase');
    // 添加加购商品
    Route::post('ap/addPeriods', 'AddPurchase/addPeriods');
    // 更新加购商品配置
    Route::post('ap/updateAP', 'AddPurchase/updateAP');
    // 监听期数频道
    Route::post('ap/changeStatus', 'AddPurchase/changeStatus');
    // 监听期数频道
    Route::post('ap/updateSort', 'AddPurchase/updateSort');
    //查询期数关单卫检状态
    Route::get('periods/getCustomsOrderHealthInspect', 'Periods/getCustomsOrderHealthInspect');
    // 获取渠道期数加密ID
    Route::get('periods/getChannelEncryptId', 'Periods/getChannelEncryptId');
    // 订金期数下架自动退款（定时任务自动回调）
    Route::post('ap/depositRefund', 'AddPurchase/depositRefund');
    // 验证渠道期数加密ID
    Route::post('periods/verifyChannelEncryptId', 'Periods/verifyChannelEncryptId');
    // 保存期数操作记录
    Route::post('periods/SaveOperationRecord', 'Periods/SaveOperationRecord');
    // 期数操作记录列表
    Route::get('periods/OperationRecordList', 'Periods/OperationRecordList');
    // 更新代发期数发货时间
    Route::post('periods/updateDfDeliveryTime', 'Periods/updateDfDeliveryTime');
    // 【闪购、秒发、食品】仓扣减库存后如果库存为0或者【库存不足】推送萌牙失败，需要包含此简码的在售的闪购非渠道期数修改发货时间
    Route::post('periods/stockUpdateDeliveryTime', 'Periods/stockUpdateDeliveryTime');
    // 取消在售期数渠道标识
    Route::post('periods/cancelChannel', 'Periods/cancelChannel');
    Route::get('periods/cancelChannel', 'Periods/cancelChannel');
    // 期数查询同时在售的期数
    Route::get('periods/getSamePeriods', 'Periods/getSamePeriods');
    // 展示文案通过记录
    Route::get('periods/showingText', 'Periods/showingText');

    /**
     * 闪购
     */
    // 添加闪购文案
    Route::post('flash/create', 'Flash/create');
    // 添加编辑采购信息
    Route::post('flash/updateBuyerInfo', 'Flash/updateBuyerInfo');
    // 添加编辑运营信息
    Route::post('flash/update', 'Flash/update');
    // 详细信息
    Route::get('flash/detail', 'Flash/detail');
    // 更新 es 数据
    Route::post('flash/esUpdate', 'Flash/esUpdate');
    // 前端闪购默认列表
    Route::get('flash/esPeriods', 'Flash/esPeriods');
    // 闪购批量字段获取
    Route::get('flash/getFlashFieldAll', 'Flash/getFlashFieldAll');
    /**
     * 跨境
     */
    // 添加跨境文案
    Route::post('cross/create', 'Cross/create');
    // 添加编辑采购信息
    Route::post('cross/updateBuyerInfo', 'Cross/updateBuyerInfo');
    // 添加编辑运营信息
    Route::post('cross/update', 'Cross/update');
    // 详细信息
    Route::get('cross/detail', 'Cross/detail');
    /**
     * 秒发
     */
    // 添加秒发文案
    Route::post('second/create', 'Second/create');
    // 添加编辑采购信息
    Route::post('second/updateBuyerInfo', 'Second/updateBuyerInfo');
    // 添加编辑运营信息
    Route::post('second/update', 'Second/update');
    // 详细信息
    Route::get('second/detail', 'Second/detail');
    // 秒发es列表
    Route::get('second/getSecondES', 'Second/getSecondES');
    // 秒发商品筛选列表
    Route::get('second/getSecondFilterGoods', 'Second/getSecondFilterGoods');
    // 获取 秒发 es 可筛选列表
    Route::get('second/getSecondPeriodsFilter', 'Second/getSecondPeriodsFilter');
    // 获取 秒发一级筛选列表
    Route::get('second/getSecondTopFilter', 'Second/getSecondTopFilter');
    // 获取 秒发左侧 es 可筛选列表（新）
    Route::get('second/getSecondLeftFilter', 'Second/getSecondLeftFilter');
    // 获取秒发闪购
    Route::get('second/getSecondFlashList', 'Second/getSecondFlashList');
    // 秒发商品PC筛选列表
    Route::get('second/SecondFilterList', 'Second/SecondFilterList');
    // 根据简码查询待上架期数
    Route::post('second/getPeriodsByShortCode', 'Second/getPeriodsByShortCode');

    /**
     * 尾货
     */
    // 添加尾货文案
    Route::post('leftover/create', 'Leftover/create');
    // 添加编辑采购信息
    Route::post('leftover/updateBuyerInfo', 'Leftover/updateBuyerInfo');
    // 添加编辑运营信息
    Route::post('leftover/update', 'Leftover/update');
    // 详细信息
    Route::get('leftover/detail', 'Leftover/detail');
    // 闪购批量字段获取
    Route::get('leftover/getLeftoverFieldAll', 'Leftover/getLeftoverFieldAll');
    // 批量上架尾货商品（需要打上渠道标识）
    Route::post('leftover/batchOnSaleChannel', 'Leftover/batchOnSaleChannel');

    /**
     * 兔头商品
     */
    // 添加尾货文案
    Route::post('rabbit/create', 'Rabbit/create');
    // 添加编辑采购信息
    Route::post('rabbit/updateBuyerInfo', 'Rabbit/updateBuyerInfo');
    // 添加编辑运营信息
    Route::post('rabbit/update', 'Rabbit/update');
    // 详细信息
    Route::get('rabbit/detail', 'Rabbit/detail');
    /**
     * 兔头优惠券
     */
    // 添加兔头优惠券
    Route::post('rabbitCoupon/create', 'RabbitCoupon/create');
    // 更新兔头优惠券
    Route::post('rabbitCoupon/update', 'RabbitCoupon/update');
    // 兔头优惠券详细
    Route::get('rabbitCoupon/detail', 'RabbitCoupon/detail');
    //兔头兑换优惠券
    Route::post('rabbitCoupon/exchange', 'RabbitCoupon/rabbitExchange');

    /**
     * 拍卖
     */
    // 添加拍卖文案
//    Route::post('auction/create', 'Auction/create');
//    // 添加编辑采购信息
//    Route::post('auction/updateBuyerInfo', 'Auction/updateBuyerInfo');
//    // 添加编辑运营信息
//    Route::post('auction/update', 'Auction/update');
//    // 详细信息
//    Route::get('auction/detail', 'Auction/detail');
//    // 添加用户拍卖出价记录
//    Route::post('auction/createDidRecord', 'Auction/createDidRecord');
//    // 查看出价记录
//    Route::get('auction/getBidRecord', 'Auction/getBidRecord');
//    // 更新出价运营信息
//    Route::post('auction/auctionSet', 'Auction/auctionSet');
//    // 添加用户关注
//    Route::post('auction/createAuctionFollow', 'Auction/createAuctionFollow');
//    // 拍卖加价加时
//    Route::post('auction/priceTimeInc', 'Auction/priceTimeInc');
//    // 拍卖首页商品
//    Route::get('auction/getAuctionIndex', 'Auction/getAuctionIndex');
//    // 删除我的拍卖关注商品
//    Route::post('auction/delAuctionFollow', 'Auction/delAuctionFollow');
//    // 我的拍卖关注列表
//    Route::get('auction/getMyAuctionFollow', 'Auction/getMyAuctionFollow');
//    // 首页拍卖商品详情
//    Route::get('auction/getIndexAuctionDetail', 'Auction/getIndexAuctionDetail');
//    // 获取用户已拍下待下单列表
//    Route::get('auction/getUserAuction', 'Auction/getUserAuction');
//    // 更新用户拍卖创建订单
//    Route::post('auction/updateUserAuction', 'Auction/updateUserAuction');
//    // 获取用户竞拍中拍品
//    Route::get('auction/getUserAuctionNow', 'Auction/getUserAuctionNow');


    /**
     * 营销商品
     */
    // 添加拼团
    Route::post('marketing/addGroup', 'Periods/addGroup');
    // 删除拼团
    Route::post('marketing/delGroup', 'Periods/delGroup');
    // 更新拼团
    Route::post('marketing/upGroup', 'Periods/upGroup');
    // 拼团列表
    Route::get('marketing/getGroupList', 'Periods/getGroupList');
    // 拼团详细
    Route::get('marketing/getGroupInfo', 'Periods/getGroupInfo');
    // 添加新人
    Route::post('marketing/addNewcomer', 'Periods/addNewcomer');
    // 删除新人
    Route::post('marketing/delNewComer', 'Periods/delNewComer');
    // 更新新人
    Route::post('marketing/upNewcomer', 'Periods/upNewcomer');
    // 新人列表
    Route::get('marketing/getNewcomerList', 'Periods/getNewcomerList');
    // 新人详细
    Route::get('marketing/getNewcomerInfo', 'Periods/getNewcomerInfo');
    // 获取启用分享的拼团信息
    Route::get('marketing/getShareInfo', 'Periods/getShareInfo');


    /**
     * 违禁词
     */
    // 添加违禁词
    Route::post('bannedWord/create', 'bannedWord/create');
    // 删除违禁词
    Route::post('bannedWord/delete', 'bannedWord/delete');
    // 违禁词列表
    Route::get('bannedWord/list', 'bannedWord/list');
    /**
     * 套餐
     */
    // 添加商品套餐
    Route::post('package/create', 'Package/create');
    // 更新商品套餐
    Route::post('package/update', 'Package/update');
    // 验证套餐库存是否充足
    Route::get('package/validateInventory', 'Package/validateInventory');
    // 返回套餐库存
    Route::post('package/sendBackInventory', 'Package/sendBackInventory');
    // 期数套餐列表
    Route::get('package/list', 'Package/packageList');
    // 期数套餐+商品列表
    Route::get('package/productList', 'Package/packageProductList');
    // 商品套餐库存售罄更新
    Route::post('package/examinationPI', 'Package/examinationPackageInventory');
    // 查询商品套餐库存
    Route::get('package/getPeriodsPackageInventory', 'Package/getPeriodsPackageInventory');
    // 查询商家秒发商品套餐库存
    Route::get('package/getVmallPeriodsPackageInventory', 'Package/getVmallPeriodsPackageInventory');
    // 查询商品套餐产品库存详细
    Route::post('package/getPeriodsPackageProductInventory', 'Package/getPeriodsPackageProductInventory');
    // 获取期数单个套餐
    Route::get('package/getPeriodsPackageOne', 'Package/getPeriodsPackageOne');
    //秒发详情
    Route::get('package/getPeriodsSecondPackageInventory', 'Package/getPeriodsSecondPackageInventory');
    // 获取剩余订货量及预计发货时间
    Route::get('package/getPeriodsRemainingOrderQuantity', 'Package/getPeriodsRemainingOrderQuantity');
    // 获取专题活动期数套餐价格
    Route::get('package/getActivityPeriodsPrice', 'Package/getActivityPeriodsPrice');
    // 跨境套餐根据SKU拆分为不同的子套餐
    Route::get('package/split', 'Package/split');
    // 删除套餐
    Route::post('package/del', 'Package/del');

    // 自选专题活动创建自选套餐
    Route::post('package/createCustomActivityPackage', 'Package/createCustomActivityPackage');
    // 根据期数获取支持自选套餐
    Route::get('package/getCustomActivityPackage', 'Package/getCustomActivityPackage');
    
    

    /**
     * 审核
     */
    // 文案提交
    Route::post('review/copywriterSubmit', 'Periods/copywriterSubmit');
    // 商品内容采购审核
    Route::post('review/review', 'Periods/review');
    // 运营上架审核
    Route::post('review/onSale', 'Periods/onSale');
    // 商品上下架
    Route::post('review/offSale', 'Periods/offSale');
    // 删除期数
    Route::post('period/delete', 'Periods/delete');
    // 驳回理由查询
    Route::get('review/getReviewLog', 'Periods/getReviewLog');
    // 文案主管审核
    Route::post('review/copyWriterReview', 'Periods/copyWriterReview');
    // 文案提交主管审核
    Route::post('review/submitCopyWriter', 'Periods/submitCopyWriter');
    // 查询未推送萌牙订单
    Route::get('period/getunpushorder', 'Periods/getunpushorder');

    /**
     * 其它
     */
    // 获取可筛选商品国家、类型、关键词
    Route::get('filter/list', 'Other/filterList');
    // 仓库列表
//    Route::get('fictitious/list', 'Other/fictitiousList');
    Route::get('fictitious/list', 'ShippingWarehouse/getVirtualListByChannelType');
    // 添加仓库
    Route::post('fictitious/add', 'Other/fictitiousAdd');
    // 修改仓库
    Route::post('fictitious/up', 'Other/fictitiousUp');
    // 同步仓库数据
    Route::post('fictitious/syncFictitious', 'Other/syncFictitious');
    // 添加拼团库存数据
    Route::post('group/add', 'Other/createGroupOrderInventory');
    // 期数修改列表
    Route::get('periods/periodsStatusChangeRecord', 'Other/periodsStatusChangeRecord');
    // 复制期数
    Route::get('periods/copyPeriod', 'Other/copyPeriod');
    // 闪购同步到尾货
    Route::post('periods/syncLeftover', 'Other/syncLeftover');
    // 根据条件和字段返回商品详细信息
    Route::get('products/warehouse', 'Products/warehouse');
    // 生成商品 json 详细信息
    Route::get('period/createJson', 'Periods/createJson');
    // 检测 oss 文件是否存在
    Route::get('period/objectExist', 'Periods/objectExist');
    // 生成商品 json 详细信息 post 方式
    Route::post('period/createJsonPost', 'Periods/createJson');
    // 获取商品 json 信息
    Route::get('period/getPeriodJson', 'Periods/getPeriodJson');
    // 系统到点自动上架，秒级自动任务调用此接口，回调接口
    Route::post('periods/systemPutShelf', 'Periods/systemPutShelf');
    // 用户获取商品相关基本信息
    Route::get('getUserPeriodsInfo', 'Other/getUserPeriodsInfo');
    // 获取套餐产品基础信息
    Route::get('getPackageProductInfo', 'Other/getPackageProductInfo');
    // 更新套餐绑定产品
    Route::post('other/updatePackageProductInfo', 'other/updatePackageProductInfo');
    // 更新期数产品信息
    Route::post('other/updatePeriodsProductInfo', 'other/updatePeriodsProductInfo');
    // 更新期数产品信息
    Route::post('other/updatePeriodsProductInventory', 'other/updatePeriodsProductInventory');
    // 根据期数更新绑定产品信息
    Route::post('other/updatePeriodsProductByPeriod', 'other/updatePeriodsProductByPeriod');
    // 添加暂存模板
    Route::post('other/addTsTemplate', 'other/addTsTemplate');
    // 更新暂存模板
    Route::post('other/upTsTemplate', 'other/upTsTemplate');
    // 暂存模板列表
    Route::get('other/tsTemplateList', 'other/tsTemplateList');
    // 开启/关闭 暂存模板
    Route::post('other/enabledTsTemplate', 'other/enabledTsTemplate');
    // 更新供应商
    Route::get('other/updateSupplier', 'other/updateSupplier');

    /**
     * 评论池
     */
    // 评论列表
    Route::get('comment/list', 'Comment/getComment');
    // 根据简码获取期数评论
    Route::get('comment/getPeriodsByShortCode', 'Comment/getPeriodsByShortCode');
    // 根据期数查询简码
    Route::get('comment/getCommentByPeriodAndShortCode', 'Comment/getCommentByPeriodAndShortCode');
    // 更新评论
    Route::post('comment/upComment', 'Comment/upComment');
    // 审核评论
    Route::post('comment/periodCommentAudit', 'Comment/periodCommentAudit');
    // 获取评论用户信息
    Route::get('comment/getUserCommentInfo', 'Comment/getUserCommentInfo');
    // 喜欢评论
    Route::post('comment/like', 'Comment/likeComment');

    /**
     * 评论管理
     */
    // 后台管理添加评论
    Route::post('comment/createComment', 'Comment/createComment');
    // 后台添加评论自动任务
    Route::post('comment/automaticTaskComment', 'Comment/automaticTaskComment');
    // 后台评论管理列表
    Route::get('comment/getCommentList', 'Comment/getCommentList');
    // v2 评论数据变通同步 v3 评论
    Route::post('comment/v2SyncV3Comment', 'Comment/v2SyncV3Comment');


    /**
     * 发货仓管理
     */
    // 添加实体仓
    Route::post('warehouse/createPhysicalWarehouse', 'ShippingWarehouse/createPhysicalWarehouse');
    // 添加虚拟仓
    Route::post('warehouse/createVirtualWarehouse', 'ShippingWarehouse/createVirtualWarehouse');
    // 实体仓详细
    Route::get('warehouse/getPhysicalWarehouseInfo', 'ShippingWarehouse/getPhysicalWarehouseInfo');
    // 虚拟仓详细
    Route::get('warehouse/getVirtualWarehouseInfo', 'ShippingWarehouse/getVirtualWarehouseInfo');
    // 更新实体仓库
    Route::post('warehouse/upPhysicalWarehouse', 'ShippingWarehouse/upPhysicalWarehouse');
    // 更新虚拟仓详细
    Route::post('warehouse/upVirtualWarehouse', 'ShippingWarehouse/upVirtualWarehouse');
    // 获取实体仓列表
    Route::get('warehouse/getPhysicalWarehouseList', 'ShippingWarehouse/getPhysicalWarehouseList');
    // 获取虚拟仓列表
    Route::get('warehouse/getVirtualWarehouseList', 'ShippingWarehouse/getVirtualWarehouseList');
    Route::get('warehouse/corpList', 'ShippingWarehouse/corpList');
    // 查询所有实体仓下面的绑定了频道的虚拟仓需
    Route::get('warehouse/getPhysicalAndVirtualList', 'ShippingWarehouse/getPhysicalAndVirtualList');
    // 根据虚拟仓id（编码）查询虚拟仓列表
    Route::get('warehouse/getVirtualByVirtualId', 'ShippingWarehouse/getVirtualByVirtualId');
    // 临时库存查询添加
    Route::post('warehouse/addTempWarehouse', 'ShippingWarehouse/addTempWarehouse');
    // 更新库存数量
    Route::post('warehouse/updateTempWarehouseInventory', 'ShippingWarehouse/updateTempWarehouseInventory');
    // 获取临时仓库信息
    Route::get('warehouse/getTempWarehouse', 'ShippingWarehouse/getTempWarehouse');
    // 导入临时库存
    Route::post('warehouse/importWarehouse', 'ShippingWarehouse/importWarehouse');
    // 获取代发仓列表
    Route::get('warehouse/getDfWarehouseList', 'ShippingWarehouse/getDfWarehouseList');

    /**
     * es 查询
     */
    Route::get('es/getPeriodOperator', 'ElasticSearch/getPeriodOperator');
    // 查询期数 ES 套餐信息
    Route::get('es/getPeriodPackages', 'ElasticSearch/getPeriodPackages');
    // 根据期数查询订单
    Route::get('es/getOrderByPeriod', 'ElasticSearch/getOrderByPeriod');
    // 查询订单套餐产品
    Route::get('es/getOrderPackage', 'ElasticSearch/getOrderPackage');

    // 添加商家秒发商品
    Route::post('vmall/second/create', 'SecondMerchants/create');
    // 更新商家秒发商品
    Route::post('vmall/second/update', 'SecondMerchants/update');
    // 秒发商品详细
    Route::get('vmall/second/detail', 'SecondMerchants/detail');
    // 评论总数
    Route::post('comment/updateCommentCount', 'Comment/updateCommentCount');
    // 商家秒发列表
    Route::get('vmall/second/list', 'SecondMerchants/getSecondMerchants');
    // 商品详细
    Route::get('other/periodInfo', 'ElasticSearch/getPeriodInfo');
    // 商家商品上下架
    Route::post('review/vmallOffSale', 'Periods/vmallOffSale');
    // 获取产品
    Route::get('other/gppi', 'Other/getPeriodsProductInventory');
    // es 列表查询
    Route::get('es/periodsList', 'ElasticSearch/getPeriodList');
    // 商家库存变更上下架
    Route::post('vmall/vmallInventorySaleStatus', 'Periods/vmallInventorySaleStatus');
    // 根据套餐 id 获取套餐列表
    Route::get('es/getPackageList', 'ElasticSearch/getPackageList');
    // 更新期数采购人
    Route::post('periods/updatePeriodsBuyer', 'Periods/updatePeriodsBuyer');
    // 发送邮件
    Route::post('other/sendEmail', 'Other/sendEmail');
    // 给采购发送邮件队列回调
    Route::post('periods/sendEmailToBuyer', 'Periods/sendEmailToBuyer');
    // 根据期数获取期数拼团价格
    Route::get('other/getPeriodsGroupPrice', 'Other/getPeriodsGroupPrice');
    // 商家审核
    Route::post('vmall/operationAudit', 'SecondMerchants/operationAudit');
    // 库存预警运维钉钉群推送
    Route::post('other/pushInventoryAlert', 'Other/pushInventoryAlert');
    // 删除库存预警运维钉钉群推送
    Route::post('other/delInventoryAlert', 'Other/delInventoryAlert');
    // 获取 v2 期数基本信息
    Route::get('other/getV2GoodsBase', 'Other/getV2GoodsBase');
    // 复制商家秒发商品
    Route::get('vmall/copyPeriod', 'SecondMerchants/copyPeriod');
    // 更新商家秒发马甲已购
    Route::post('vmall/updateVestPurchased', 'SecondMerchants/updateVestPurchased');
    // 增加期数曝光率
    Route::post('other/exposureRate', 'Other/exposureRate');
    // 修改商家秒发成本
    Route::post('vmall/updateCost', 'SecondMerchants/updateCost');

    /**
     * 采购看板
     */
    Route::get('purchase/getPeriodsList', 'Purchase/getPeriodsList');
    Route::post('purchase/updateInventoryOrder', 'Purchase/updateInventoryOrder');
    Route::get('purchase/getInventoryOrderList', 'Purchase/getInventoryOrderList');
    Route::post('purchase/delRecord', 'Purchase/delRecord');
    Route::post('purchase/addPurchaseOrderno', 'Purchase/addPurchaseOrderno');
    Route::get('purchase/getPurchaseOrdernoList', 'Purchase/getPurchaseOrdernoList');
    Route::post('purchase/delPurchaseOrderno', 'Purchase/delPurchaseOrderno');
    Route::post('purchase/updateEstimatePurchase', 'Purchase/updateEstimatePurchase');
    Route::get('purchase/getProductHistoricalCostprice', 'Purchase/getProductHistoricalCostprice');
    Route::post('purchase/addRecord', 'Purchase/addRecord');
    Route::get('purchase/payeeMerchantList', 'Purchase/payeeMerchantList');

    /**
     * 分享访问数据统计
     */
    // 分享统计
    Route::get('statistics/getShareSta', 'Statistics/getShareSta');
    // 分享订单统计明细
    Route::get('statistics/getShareDetailList', 'Statistics/getShareDetailList');
    // 商品分享人
    Route::get('statistics/getShareGroup', 'Statistics/getShareGroup');
    // 订单分享人
    Route::get('statistics/getShareOrderGroup', 'Statistics/getShareOrderGroup');


    // 浏览量
    Route::get('periods/getPageviewsByHours', 'Periods/getPageviewsByHours');
    // 按天查询期数浏览量
    Route::get('periods/getPageviewsByDays', 'Periods/getPageviewsByDays');
    // 统计期数小时浏览量
    Route::get('periods/getPeriodPageviewsByHours', 'Periods/getPeriodPageviewsByHours');
    // 更新期数标签
    Route::post('periods/updateLabel', 'Periods/updateLabel');
    // 批量更新标签
    Route::post('periods/batchUpdateLabel', 'Periods/batchUpdateLabel');
    // 批量查询期数毛利率
    Route::post('periods/BatchQueryPeriodGrossProfitMargin', 'Periods/BatchQueryPeriodGrossProfitMargin');


    /**推荐标签 start**/
    Route::get('label/labelList', 'Label/labelList');//推荐标签列表
    Route::post('label/labelAdd', 'Label/labelAdd');//新增推荐标签
    Route::post('label/labelEdit', 'Label/labelEdit');//修改推荐标签
    Route::get('label/getLabelByName', 'Label/getLabelByName');// 查询标签
    /**推荐标签 end**/

    #region cct
    Route::post('second/syncSale', 'Second/syncSale');//秒发在售商品数据同步
    Route::get('second/search', 'Second/search');//秒发首页搜索
    Route::post('second/syncMerchantSales', 'Second/syncMerchantSales');//商家秒发期数销量同步

    //用户画像
    Route::post("userPortrait/add", "UserPortrait/add"); //添加
    Route::get("userPortrait/list", "UserPortrait/list"); //列表
    Route::get("userPortrait/activeUserPortrait", "UserPortrait/activeUserPortrait"); //用户是否展示
    Route::post("userPortrait/postUserPortrait", "UserPortrait/postUserPortrait"); //用户提交喜好标签
    Route::get("userPortraitLogs/listByUserPortrait", "UserPortraitLogs/listByUserPortrait"); //列表
    Route::post("userPortrait/feedback", "userPortrait/feedback"); //反馈
    Route::post("userPortrait/batchFeedback", "userPortrait/batchFeedback"); //反馈
    #endregion

    // 库存操作
    Route::post("inventory/update", "Inventory/update"); //库存调整
    Route::post("inventory/callback/update", "Inventory/callbackUpdate"); //库存调整审批回调
    Route::get("inventory/query/prompt", "Inventory/queryPrompt"); //查询修改库存提示

    //预约
    Route::post('periods/reservation', 'Periods/reservation');
    //获取用户是否预约
    Route::post('periods/isReservation', 'Periods/isReservation');
    //获取预约列表
    Route::get("periods/reservationList", "Periods/reservationList");
    //用户预约列表
    Route::get("periods/myReservation", "Periods/myReservation");
    //用户取消预约
    Route::post('periods/cancelReservation', 'Periods/cancelReservation');

    /* 运营投流 */
    //获取规则配置
    Route::get('evSort/config', 'EvSort/config');
    //获取用户额度
    Route::get('evSort/getUserLimit', 'EvSort/getUserLimit');
    //新增投流排序
    Route::post('evSort/addSort', 'EvSort/addSort');
    //修改投流排序
    Route::post('evSort/updateSort', 'EvSort/updateSort');
    //取消投流排序
    Route::post('evSort/cancelSort', 'EvSort/cancelSort');
    //投流列表
    Route::get('evSort/sortList', 'EvSort/sortList');
    //获取用户消费记录
    Route::get('evSort/getUserLimitRecord', 'EvSort/getUserLimitRecord');
    //获取当前生效的排序商品
    Route::get('evSort/getActiveSortIds', 'EvSort/getActiveSortIds');
    //重置所有用户余额
    Route::get('evSort/resetUserLimits', 'EvSort/resetUserLimits');
    //获取排序商品
    Route::get('evSort/getProductsByPage', 'EvSort/getProductsByPage');
    //权重配置
    Route::get('evSort/weightConfig', 'EvSort/weightConfig');
    //修改权限配置
    Route::post('evSort/updateWeightConfig', 'EvSort/updateWeightConfig');

})->middleware(\app\middleware\monitor::class);

Route::group("commodities", function () {
    Route::group("v3", function () {


        //搜索反馈记录
        Route::post("userQueryLog/add", "UserQueryLog/add"); //添加
        Route::post("userQueryLog/autoAddThemeActivity", "UserQueryLog/autoAddThemeActivity"); //添加
        Route::get("userQueryLog/userList", "UserQueryLog/userList"); //删除

        //产品池
        Route::get("period/batchLastSellTime", "UserQueryLog/batchLastSellTime"); //根据简码批量查询最后售卖时间和期数

        Route::get("PeriodsPool/shortCodesList", "PeriodsPool/shortCodesList"); //根据简码查询期数列表
        Route::post("periodsPool/onSale", "PeriodsPool/onSale"); //开售
        Route::get("periodsPool/list", "PeriodsPool/list"); //列表
        Route::get("periodsPool/logList", "PeriodsPool/logList"); //logs列表
        Route::get("periodsPool/supplierProductList", "PeriodsPool/supplierProductList"); //logs列表
        Route::get("periodsPool/findPeriods", "PeriodsPool/findPeriods"); //logs列表
        Route::post("periodsPool/purchaseSubmit", "PeriodsPool/purchaseSubmit"); //提交采购信息
        Route::post("periodsPool/del", "PeriodsPool/del"); //删除
        Route::post("periodsPool/qualification", "PeriodsPool/qualification"); //上传资质
        Route::post("periodsPool/editor", "PeriodsPool/editor"); //文案编辑
        Route::post("periodsPool/pictureDistribution", "PeriodsPool/pictureDistribution"); //图片分发
        Route::post("periodsPool/pictureSubmit", "PeriodsPool/pictureSubmit"); //图片分发
        Route::post("periodsPool/documentDistribution", "PeriodsPool/documentDistribution"); //文案分发

        Route::post("periodsPool/auditPurchase", "PeriodsPool/auditPurchase"); //采购审核
        Route::post("periodsPool/auditLastPurchase", "PeriodsPool/auditLastPurchase"); //采购主管终审
        Route::post("periodsPool/document", "PeriodsPool/document"); //文案主管审核
        Route::post("periodsPool/documentPersonnel", "PeriodsPool/documentPersonnel"); //文案主管审核分发
        Route::post("periodsPool/uiPersonnel", "PeriodsPool/uiPersonnel"); //UI负责人修改
        Route::post("periodsPool/purchaseExec", "PeriodsPool/purchaseExec"); //采购执行审核资质
        Route::post("periodsPool/operation", "PeriodsPool/operation"); //运营审核


        Route::post("periodsPool/add", "PeriodsPool/add"); //添加
        Route::post("periodsPool/edit", "PeriodsPool/edit"); //编辑
        Route::get("periodsPool/detail", "PeriodsPool/detail"); //详情

        Route::post("periodsPool/resend", "PeriodsPool/resend"); //重新提交
        Route::post("periodsPool/uiPictureDistribution", "PeriodsPool/uiPictureDistribution"); //图片分发 (附件上传)
        Route::post("periodsPool/qualificationSubmit", "PeriodsPool/qualificationSubmit"); //图片分发 (附件上传)
        Route::get("periodsPool/backlogQuantity", "PeriodsPool/backlogQuantity"); //图片分发 (附件上传)
        Route::post("periodsPool/editorSubmit", "PeriodsPool/editorSubmit"); //文案提交
        Route::post("periodsPool/editorUpdate", "PeriodsPool/editorUpdate"); //文案提交
        Route::post("periodsPool/operatOvertime", "PeriodsPool/operatOvertime"); //运营未排期
        Route::post("periodsPool/timeExpires", "periodsPool/timeExpires"); //运营未排期
        Route::get("periodsPool/getCompanyByProducts", "periodsPool/getCompanyByProducts"); //运营未排期

        //采购单表
        Route::get("purchaseOrderno/getPoNo", "PurchaseOrderno/getPoNo"); //获取PO单号
        Route::post("purchaseOrderno/add", "PurchaseOrderno/add"); //添加
        Route::get("purchaseOrderno/goodsGetFictitiousCount", "PurchaseOrderno/goodsGetFictitiousCount"); //查询萌芽库存


        Route::post("purchaseOrderno/edit", "PurchaseOrderno/edit"); //编辑
        Route::get("purchaseOrderno/list", "PurchaseOrderno/list"); //列表
        Route::post("purchaseOrderno/del", "PurchaseOrderno/del"); //删除
        Route::post("purchaseOrderno/cancel", "PurchaseOrderno/cancel"); //采购单延期下单
        Route::post("purchaseOrderno/sync", "PurchaseOrderno/sync"); //同步更新采购单
        Route::post("purchaseOrderno/reject", "PurchaseOrderno/reject"); //驳回采购单
        Route::post("purchaseOrderno/approved", "PurchaseOrderno/approved"); //同意采购单
        Route::post("purchaseOrderno/retrial", "PurchaseOrderno/retrial"); //复审采购单
        Route::post("purchaseOrderno/updateAnnex", "PurchaseOrderno/updateAnnex"); //更新附件
        Route::get("purchaseOrderno/getItems", "PurchaseOrderno/getItems"); //查询采购单产品信息
        Route::post("purchaseOrderno/retract", "PurchaseOrderno/retract"); //收回采购单
        Route::post("purchaseOrderno/uploadWaybill", "PurchaseOrderno/uploadWaybill"); //上传运单号
        Route::get("purchaseOrderno/getHistoryPrice", "PurchaseOrderno/getHistoryPrice");//查询历史采购价格
        Route::get("purchaseOrderno/getPaymentInfo", "PurchaseOrderno/getPaymentInfo");//获取采购付款单信息
        Route::post("purchaseOrderno/sendFkApproval", "PurchaseOrderno/sendFkApproval");//发起【采购付款单】企微审批
        Route::post("purchaseOrderno/fkApprovalCallback", "PurchaseOrderno/fkApprovalCallback");//采购付款单企微审批回调
        Route::post("purchaseOrderno/pushPurchaseOrderToWMS", "PurchaseOrderno/pushPurchaseOrderToWMS");//推送采购订单到wms
        
        
        //采购单表
        Route::post("purchaseOrdernoItems/add", "PurchaseOrdernoItems/add"); //添加
        Route::post("purchaseOrdernoItems/edit", "PurchaseOrdernoItems/edit"); //编辑
        Route::get("purchaseOrdernoItems/list", "PurchaseOrdernoItems/list"); //列表
        Route::get("purchaseOrdernoItems/detail", "PurchaseOrdernoItems/detail"); //详情
        Route::post("purchaseOrdernoItems/del", "PurchaseOrdernoItems/del"); //删除

        //产品销售跟进关注看板
        Route::get("productSaleFollow/list", "ProductSaleFollow/list"); //列表
        Route::get("productSaleFollow/shortCodeSearch", "ProductSaleFollow/shortCodeSearch"); //根据简码搜索
        Route::post("productSaleFollow/add", "ProductSaleFollow/add"); //添加
        Route::post("productSaleFollow/remove", "ProductSaleFollow/remove"); //移除
        Route::post("productSaleFollow/addRemark", "ProductSaleFollow/addRemark"); //添加备注
        Route::get("productSaleFollow/remarkList", "ProductSaleFollow/remarkList"); //备注列表
        Route::post("productSaleFollow/import", "ProductSaleFollow/import"); //导入
        Route::get("productSaleFollow/export", "ProductSaleFollow/export"); //导出

        //商品信息参数配置
        Route::get("parameters/list", "Parameters/list"); //列表
        Route::get("parameters/channellist", "Parameters/channelList"); //获取频道数据列表数据
        Route::post("parameters/save", "Parameters/save"); //保存数据


        //差评记录
        Route::post("negativeComment/add", "NegativeComment/add"); //添加
        Route::post("negativeComment/edit", "NegativeComment/edit"); //编辑
        Route::get("negativeComment/list", "NegativeComment/list"); //列表
        Route::get("negativeComment/detail", "NegativeComment/detail"); //详情
        Route::post("negativeComment/del", "NegativeComment/del"); //删除

        // 商品采购分账配置
        Route::get("periodsBuyer/list", "PeriodsBuyer/list");
        Route::post("periodsBuyer/save", "PeriodsBuyer/save");

        //运营人员关联
        Route::post("operateRelated/add", "OperateRelated/add"); //添加
        Route::post("buyerRelated/add", "BuyerRelated/add"); //添加
        Route::get("operateRelated/list", "OperateRelated/list"); //列表
        Route::post("buyerRelated/del", "BuyerRelated/del"); //删除
        Route::post("operateRelated/del", "OperateRelated/del"); //删除


    })->prefix("v3.");
});
