# 曝光排序接口文档

## 1. 获取规则配置

##### 简要描述
- 获取曝光排序的规则配置列表

##### 请求URL
- `/commodities/v3/evSort/config`

##### 请求方式
- GET

##### 返回示例
```json
{
    "error_code": 0,
    "error_msg": "",
    "data": {
        "list": [
            {
                "id": 1,
                "duration": 30,  //时长，分钟
                "rule": {
                    "1": 15,  //排到第一需要的额度
                    "2": 10,
                    "3": 9,
                    "4": 8,
                    "5": 7,
                    "6": 6,
                    "7": 5,
                    "8": 4,
                    "9": 3,
                    "10": 1
                }
            }
        ]
    }
}
```

## 2. 新增排序

##### 简要描述
- 为商品添加曝光排序

##### 请求URL
- `/commodities/v3/evSort/addSort`

##### 请求方式
- POST

##### 参数
|参数名|必选|类型|说明|
|:----    |:---|:----- |-----   |
|period |是  |int |商品期数   |
|sort |是  |int | 排序值    |
|config_id |是  |int | 配置ID    |

##### 返回示例
```json
{
    "error_code": 0,
    "error_msg": "",
    "data": []
}
```

## 3. 取消排序

##### 简要描述
- 取消商品的曝光排序

##### 请求URL
- `/commodities/v3/evSort/cancelSort`

##### 请求方式
- POST

##### 参数
|参数名|必选|类型|说明|
|:----    |:---|:----- |-----   |
|id |是  |int |排序记录ID   |

##### 返回示例
```json
{
    "error_code": 0,
    "error_msg": "",
    "data": []
}
```

## 4. 投流列表

##### 简要描述
- 获取曝光排序列表

##### 请求URL
- `/commodities/v3/evSort/sortList`

##### 请求方式
- GET

##### 参数
|参数名|必选|类型|说明|
|:----    |:---|:----- |-----   |
|status |否  |int |状态（0:所有商品 1:在投商品）   |
|page |否  |int |页码，默认1   |
|limit |否  |int |每页条数，默认10   |

##### 返回示例
```json
{
    "error_code": 0,
    "error_msg": "",
    "data": {
        "list": [
            {
                "id": 1,
                "period": 123,
                "sort": 1,
                "start_time": 1648888888,
                "end_time": 1648892488,
                "duration": 60,
                "limit": 15,
                "status": 1,
                "status_text": "进行中",
                "start_time_text": "2022-04-02 15:34:48",
                "end_time_text": "2022-04-02 16:34:48",
                "remaining_time": 3600,
                "remaining_time_text": "1小时"
            }
        ],
        "total": 1
    }
}
```

## 5. 获取用户额度

##### 简要描述
- 获取当前用户的额度信息

##### 请求URL
- `/commodities/v3/evSort/getUserLimit`

##### 请求方式
- GET

##### 返回示例
```json
{
    "error_code": 0,
    "error_msg": "",
    "data": {
        "limit": 100
    }
}
```

## 6. 获取用户消费记录

##### 简要描述
- 获取用户额度消费记录

##### 请求URL
- `/commodities/v3/evSort/getUserLimitRecord`

##### 请求方式
- GET

##### 参数
|参数名|必选|类型|说明|
|:----    |:---|:----- |-----   |
|page |否  |int |页码，默认1   |
|limit |否  |int |每页条数，默认10   |

##### 返回示例
```json
{
    "error_code": 0,
    "error_msg": "",
    "data": {
        "list": [
            {
                "id": 1,
                "type": 1,
                "type_text": "增加",
                "limit": 10,
                "describe": "取消商品期数123的排序，返还剩余额度",
                "create_time": 1648888888,
                "create_time_text": "2022-04-02 15:34:48"
            }
        ],
        "total": 1
    }
}
```
